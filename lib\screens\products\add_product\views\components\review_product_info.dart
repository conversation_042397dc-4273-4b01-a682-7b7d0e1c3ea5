import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_carousel_feild_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ReviewProductInfo extends StatelessWidget {
  const ReviewProductInfo({Key? key, required this.controller})
      : super(key: key);
  final AddProductController controller;
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return GetX<AddProductController>(
        init: AddProductController(),
        builder: (val) {
          return Padding(
            padding:  EdgeInsets.only(top: 24.h),
            child: ElbaabCarouselFeildWidget(
              aspectRatio: 5 / 4.7,
              children: [
                ElbaabFeildContainerWidget(
                  borderWidth: 0,
                  borderColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: kLeftSpace,
                        right: kRightSpace,
                        top: 10,
                        bottom: 16).r,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Row(
                          children: <Widget>[
                           
                            Expanded(
                              child: Text(
                                appLocal.informationTitleEnglish,
                                style: FontStyles.fontBold(fontSize: 12),
                              ),
                            ),
                             IconButton(
                              onPressed: () => controller.changeTab(
                                  isNext: false, isJumpPage: 0),
                              icon: Container(
                                height: 30.h,
                                width: 30.w,
                                decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.06),
                                    shape: BoxShape.circle),
                                child: Center(
                                  child: SvgPicture.string(
                                      SvgStrings.iconEditGray),
                                ),
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                               appLocal.productname,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded( child: getProductName(appLocal))
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                appLocal.description,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(child: getDescription(appLocal))
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                appLocal.productId,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded( child: getGtin(appLocal))
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                appLocal.categories,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(child: getCategory(appLocal))
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                appLocal.brand,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                              child: getBrand(appLocal),
                            )
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                              appLocal.keyword,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(child: getKeywords())
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                ElbaabFeildContainerWidget(
                  borderWidth: 0,
                  borderColor: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: kLeftSpace,
                        right: kRightSpace,
                        top: 10,
                        bottom: 16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Row(
                          children: <Widget>[
                            Expanded(
                              child: Text(
                                appLocal.informationTitleArabic,
                                style: FontStyles.fontBold(fontSize: 12),
                              ),
                            ),
                            IconButton(
                              onPressed: () => controller.changeTab(
                                  isNext: false, isJumpPage: 0),
                              icon: Container(
                                height: 30,
                                width: 30,
                                decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.06),
                                    shape: BoxShape.circle),
                                child: Center(
                                  child: SvgPicture.string(
                                      SvgStrings.iconEditGray),
                                ),
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                appLocal.productname,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                                child: getProductName(appLocal, isArabic: true))
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                appLocal.description,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                                child: getDescription(appLocal, isArabic: true))
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                appLocal.productId,
                                textAlign: TextAlign.right,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(child: getGtin(appLocal))
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                appLocal.categories,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                                child: getCategory(appLocal, isArabic: true),)
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                appLocal.brand,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                              child: getBrand(appLocal, isArabic: true),
                            )
                          ],
                        ),
                          Row(
                          children: [
                            Expanded(
                              child: Text(
                             appLocal.keyword,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded( child: getKeywords())
                          ],
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          );
        });
  }

  Widget getProductName(AppLocalizations localizations,
      {bool isArabic = false}) {
    String information = prefs.getString(productInformation) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (information.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(information);
      cacheValue = !isArabic ? info["name"] ?? "" : info["nameAr"] ?? "";
      serverValue = controller.product?.productName ?? "";
    }
    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue:
            !isArabic ? controller.txtProductName.text : controller.txtProductNameAr.text,
        errorCacheValue: localizations.youRemoveProductName,
        errorServerValue: localizations.productNameNotProvided);
  }

  Widget getDescription(AppLocalizations localizations,
      {bool isArabic = false}) {
    String information = prefs.getString(productInformation) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (information.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(information);
      cacheValue =
          isArabic ? info["descriptionAr"] ?? "" : info["description"] ?? "";
      serverValue = controller.product?.productDescription ?? "";
    }
    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: isArabic
            ? controller.txtProductDiscriptionAr.text
            : controller.txtProductDiscription.text,
        errorCacheValue: localizations.youRemoveProductDescription,
        errorServerValue: localizations.productDescriptionNotProvided);
  }

  Widget getGtin(AppLocalizations localizations) {
    String information = prefs.getString(productInformation) ?? "";
    String cacheProductGtin = "";
    String productGtin = "";
    if (information.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(information);
      cacheProductGtin = info["prductGtin"] ?? "";
      productGtin = controller.product?.productManufacturerId ?? "";
    }
    return controller.validateWidget(
        cacheValue: cacheProductGtin,
        serverValue: productGtin,
        feildValue: controller.productId.isNotEmpty
            ? controller.productId
            : controller.gtinNumber.value,
        errorCacheValue: localizations.youRemoveProductGtin,
        errorServerValue: localizations.productGtinNotProvided);
  }

  Widget getCategory(AppLocalizations localizations, {bool isArabic = false}) {
    String information = prefs.getString(productInformation) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (information.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(information);
      cacheValue =
          "${localizations.localeName == "en" ? info["category"] ?? "" : info["categoryAr"] ?? ""} -> ${localizations.localeName == "en" ? info["subCategory"] ?? "" : info["subCategoryAr"] ?? ""}";
      serverValue =
          "${controller.product?.productCategory?.categoryName ?? ""} -> ${controller.product?.productSubCategory?.subCategoryName ?? ""}";
    }
    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: !isArabic
            ? '${controller.category.value} -> ${controller.subCategory.value}'
            : '${controller.categoryAr.value} -> ${controller.subCategoryAr.value}',
        errorCacheValue: localizations.youRemoveProductCategory,
        errorServerValue: localizations.productCategoryNotProvided);
  }

  Widget getBrand(AppLocalizations localizations, {bool isArabic = false}) {
    String information = prefs.getString(productInformation) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (information.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(information);
      cacheValue = isArabic ? info["brandAr"] ?? "" : info["brand"] ?? "";
      serverValue = controller.product?.productBrand?.brandName ?? "";
    }
    return controller.validateWidget(
      cacheValue: cacheValue,
      serverValue: serverValue,
      feildValue: isArabic
          ? controller.manufactureAr.value
          : controller.manufacture.value,
      errorCacheValue: localizations.youRemoveProductBrand,
      errorServerValue: localizations.productBrandNotProvided,
    );
  }

  Widget getKeywords() {
    String information = prefs.getString(productInformation) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (information.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(information);
      cacheValue = info["keywords"] ?? "";

      for (int i = 0;
          i < (controller.product?.productKeyWords?.length ?? 0);
          i++) {
        if (i == (((controller.product?.productKeyWords?.length ?? 0)) - 1)) {
          serverValue =
              "$serverValue${controller.product?.productKeyWords?[i] ?? ""}";
        } else {
          serverValue =
              "$serverValue${controller.product?.productKeyWords?[i] ?? ""},";
        }
      }
    }
    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: controller.keywords,
        errorCacheValue: "You removed product keywords from information",
        errorServerValue: "Product Keywords Not Provided");
  }
}
