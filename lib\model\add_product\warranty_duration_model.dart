class WarrantyDurationModel {
  final String? typename;
  final List<WarrantyDurations>? warrantyDurations;
  final int? status;
  final String? message;

  WarrantyDurationModel({
    this.typename,
    this.warrantyDurations,
    this.status,
    this.message,
  });

  WarrantyDurationModel.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      warrantyDurations = (json['warrantyDurations'] as List?)?.map((dynamic e) => WarrantyDurations.fromJson(e as Map<String,dynamic>)).toList(),
      status = json['status'] as int?,
      message = json['message'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'warrantyDurations' : warrantyDurations?.map((e) => e.toJson()).toList(),
    'status' : status,
    'message' : message
  };
}

class WarrantyDurations {
  final String? typename;
  final String? warrantyDuration;
  final WarrantyDurations? warrantyDurationsAr;

  WarrantyDurations({
    this.typename,
    this.warrantyDurationsAr,
    this.warrantyDuration,
  });

  WarrantyDurations.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      warrantyDurationsAr = (json['ar'] as Map<String, dynamic>?) != null
          ? WarrantyDurations.fromJson(json['ar'] as Map<String, dynamic>)
          : null,
      warrantyDuration = json['warrantyDuration'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'warrantyDuration' : warrantyDuration,
    'ar' : warrantyDurationsAr
  };
}