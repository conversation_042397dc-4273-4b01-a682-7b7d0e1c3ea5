import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/provider/language_change.dart';
import 'package:overolasuppliers/widgets/elbaab_dotted_border_container.dart';
import 'package:provider/provider.dart';

class SelectLanguage extends StatefulWidget {
  const SelectLanguage({super.key});

  @override
  _SelectLanguageState createState() => _SelectLanguageState();
}

class _SelectLanguageState extends State<SelectLanguage>
    with SingleTickerProviderStateMixin, ServerResponse {
  
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: SafeArea(
          child: Column(children: [
            Expanded(
              flex: 2,
              child: AnimatedContainer(
                duration: Durations.long3,
                decoration: BoxDecoration(
                  image: DecorationImage(
                      image: appLocal.localeName == "en"
                          ? Image.asset('assets/images/usa_flag.png').image
                          : Image.asset('assets/images/uae_flag.png').image),
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      appLocal.selectLanguage,
                      style: FontStyles.fontSemibold(fontSize: 20),
                    ),
                    Text(
                      appLocal.selectLanguageDetail,
                      textAlign: TextAlign.center,
                      style: FontStyles.fontRegular(),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Column(children: [
                Container(
                  height: 60.h,
                  padding: EdgeInsets.symmetric(horizontal: 10.w),
                  margin: EdgeInsets.symmetric(horizontal: 20.w),
                  decoration: BoxDecoration(
                    color: AppColors.headerColorDark,
                    borderRadius: BorderRadius.circular(40),
                  ),
                  child: Row(
                    textDirection: TextDirection.ltr,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            Provider.of<LanguageChnangeProvider>(context,
                                    listen: false)
                                .changeLanguage(context, 'en');
                          },
                          child: AnimatedContainer(
                            duration: Durations.long4,
                            height: 50.h,
                            curve: Curves.linear,
                            decoration: BoxDecoration(
                              color: appLocal.localeName == "en"? AppColors.colorPrimary: Colors.transparent,
                              borderRadius: BorderRadius.circular(40),
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              "ENGLISH",
                              textAlign: TextAlign.center,
                              style: FontStyles.fontMedium(),
                            ),
                          ),
                        ),
                      ),
                       Expanded(
                        child: InkWell(
                          onTap: () {
                            Provider.of<LanguageChnangeProvider>(context,
                                    listen: false)
                                .changeLanguage(context, 'ar');
                          },
                          child: AnimatedContainer(
                            duration: Durations.long4,
                            height: 50.h,
                            curve: Curves.linear,
                            decoration: BoxDecoration(
                              color: appLocal.localeName == "ar"? AppColors.colorPrimary: Colors.transparent,
                              borderRadius: BorderRadius.circular(40),
                            ),
                            alignment: Alignment.center,
                            child: Text(
                          "العربية",
                              textAlign: TextAlign.center,
                              style: FontStyles.fontSemibold(),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                ElbaabDottedBorderContainer(
                  onTap: () {
                          prefs.setBool("isFirstLaunch", true);

                        Get.offAllNamed(RouteNames.splashScreen);

                    // GraphQlInitilize(this).runMutation(
                    //     context: context,
                    //     query: GraphQlQuries.updateUserLang,
                    //     type: "updateUserLang",
                    //     variables: {"lang": appLocal.localeName});
                  },
                  child: Text(
                    appLocal.confirm.toUpperCase(),
                    style: FontStyles.fontMedium(fontSize: 12),
                  ),
                )
              ]),
            )
          ]),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    Get.offAllNamed(RouteNames.splashScreen);
  }
}
