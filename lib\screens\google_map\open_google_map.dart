import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/other/popup_loader_component.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/widgets/elbaab_border_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class OpenGoogleMap extends StatefulWidget {
  const OpenGoogleMap({super.key});

  @override
  _OpenGoogleMapState createState() => _OpenGoogleMapState();
}

class _OpenGoogleMapState extends State<OpenGoogleMap> {
  LatLng? latlong;
  LatLng? currentLatLong;
  LatLng? selectedLatLong;

  final Completer<GoogleMapController> _controller = Completer();
  final Map<MarkerId, Marker> _markers = <MarkerId, Marker>{};

  CameraPosition _cameraPosition = const CameraPosition(
    target: LatLng(25.1972, 55.2744),
    zoom: 14.4746,
  );

  @override
  void initState() {
    super.initState();
    if (Get.arguments != null) {
      selectedLatLong = Get.arguments[0];
    }
    getCurrentLocation();
  }

 

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar:  ElbaabHeader(
        title: appLocal.setYourLocation,
        leadingBack: true,
      ),
      body: Stack(
        children: <Widget>[
          GoogleMap(
            mapType: MapType.terrain,
            myLocationButtonEnabled: false,
            rotateGesturesEnabled: true,
            zoomControlsEnabled: true,
            compassEnabled: true,
            scrollGesturesEnabled: true,
            markers: Set<Marker>.of(_markers.values),
            initialCameraPosition: _cameraPosition,
            onMapCreated: (controller) => _controller.complete(controller),
            onCameraMove: (CameraPosition position) {
              if (_markers.isNotEmpty) {
                MarkerId markerId =  const MarkerId('a');
                Marker? marker = _markers[markerId];
                Marker updatedMarker = marker!.copyWith(
                  positionParam: position.target,
                );
                latlong = position.target;
                setState(() {
                  _markers[markerId] = updatedMarker;
                });
              }
            },
          ),
          Positioned(
            bottom: 30,
            left: kLeftSpace,
            right: kRightSpace,
            child: ElbaabBorderButtonWidget(
              onPress: () => Get.back(result: latlong),
              text: appLocal.selectPinLocation,
            ),
          ),
          Positioned(
            bottom: 80,
            right: 16,
            child: FloatingActionButton(
              backgroundColor: Colors.white,
              elevation: 2,
              onPressed: () async {
                final GoogleMapController controller = await _controller.future;
                _cameraPosition =
                    CameraPosition(target: currentLatLong!, zoom: 17.0);
                controller.animateCamera(
                    CameraUpdate.newCameraPosition(_cameraPosition));
                MarkerId markerId =  const MarkerId("a");
                Marker marker = Marker(
                    markerId: markerId,
                    draggable: false,
                    position: currentLatLong!,
                    );
                _markers[markerId] = marker;
              },
              child:
                  Center(child: SvgPicture.string(SvgStrings.currentLocation)),
            ),
          ),
        ],
      ),
    );
  }

  Future getCurrentLocation() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission != PermissionStatus.granted) {
      LocationPermission permission = await Geolocator.requestPermission();
      if (permission != PermissionStatus.granted) {
        getLocation();
      } else {
        openAppSettings();
      }
      return;
    }
    getLocation();
  }

  getLocation() async {
    final GoogleMapController controller = await _controller.future;
    if (selectedLatLong == null) {
      PopupLoader.showLoadingDialog(Get.context!);
    } else {
      _cameraPosition = CameraPosition(target: selectedLatLong!, zoom: 17.0);
      controller.animateCamera(CameraUpdate.newCameraPosition(_cameraPosition));
      MarkerId markerId =  const MarkerId("a");
      Marker marker = Marker(
        markerId: markerId,
        draggable: false,
        position: selectedLatLong!,
        // icon: icon
      );
      _markers[markerId] = marker;
    }
    Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high);

    if (selectedLatLong == null) {
      PopupLoader.hideLoadingDialog();
    }
    setState(() {
      latlong = LatLng(position.latitude, position.longitude);
      currentLatLong = LatLng(position.latitude, position.longitude);
      if (latlong != null && selectedLatLong == null) {
        _cameraPosition = CameraPosition(target: latlong!, zoom: 17.0);
        controller
            .animateCamera(CameraUpdate.newCameraPosition(_cameraPosition));
        MarkerId markerId =  const MarkerId("a");
        Marker marker = Marker(
          markerId: markerId,
          draggable: false,
          position: latlong!,
        );
        _markers[markerId] = marker;
      }
    });
  }
}
