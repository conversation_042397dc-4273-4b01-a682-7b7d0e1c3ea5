import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en')
  ];

  /// No description provided for @changeLanguage.
  ///
  /// In en, this message translates to:
  /// **'Change Language'**
  String get changeLanguage;

  /// No description provided for @customerService.
  ///
  /// In en, this message translates to:
  /// **'Customer Service'**
  String get customerService;

  /// No description provided for @deleteProduct.
  ///
  /// In en, this message translates to:
  /// **'Deleted Products'**
  String get deleteProduct;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy And Policy'**
  String get privacyPolicy;

  /// No description provided for @accountAndMangment.
  ///
  /// In en, this message translates to:
  /// **'Accounts And Mangments'**
  String get accountAndMangment;

  /// No description provided for @appName.
  ///
  /// In en, this message translates to:
  /// **'Elbaab'**
  String get appName;

  /// No description provided for @your_country.
  ///
  /// In en, this message translates to:
  /// **'Your Country'**
  String get your_country;

  /// No description provided for @your_language.
  ///
  /// In en, this message translates to:
  /// **'Your Language'**
  String get your_language;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @youDontHaveAccess.
  ///
  /// In en, this message translates to:
  /// **'You dont have account?'**
  String get youDontHaveAccess;

  /// No description provided for @authenticateToSignIn.
  ///
  /// In en, this message translates to:
  /// **'Please authenticate to sign in'**
  String get authenticateToSignIn;

  /// No description provided for @verifyMobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Verify mobile number'**
  String get verifyMobileNumber;

  /// No description provided for @signup.
  ///
  /// In en, this message translates to:
  /// **'Signup'**
  String get signup;

  /// No description provided for @useBiometricAcess.
  ///
  /// In en, this message translates to:
  /// **'Use Biometric Access'**
  String get useBiometricAcess;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// No description provided for @restrictOption.
  ///
  /// In en, this message translates to:
  /// **'Adding more option for accepted and matched product is not allowed'**
  String get restrictOption;

  /// No description provided for @reset.
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get reset;

  /// No description provided for @rememberMe.
  ///
  /// In en, this message translates to:
  /// **'Remember Me'**
  String get rememberMe;

  /// No description provided for @clearCache.
  ///
  /// In en, this message translates to:
  /// **'Clear Cache'**
  String get clearCache;

  /// No description provided for @pushNotification.
  ///
  /// In en, this message translates to:
  /// **'Manage Notificatios'**
  String get pushNotification;

  /// No description provided for @reciveOrderEmails.
  ///
  /// In en, this message translates to:
  /// **'Recive Order Email'**
  String get reciveOrderEmails;

  /// No description provided for @biometricAccess.
  ///
  /// In en, this message translates to:
  /// **'Biometric Access'**
  String get biometricAccess;

  /// No description provided for @verifyMobileNumberContent.
  ///
  /// In en, this message translates to:
  /// **'To verify your mobile number, we\'ve sent a One Time Password (OTP) to your phone'**
  String get verifyMobileNumberContent;

  /// No description provided for @addPhotos.
  ///
  /// In en, this message translates to:
  /// **'Add Photos'**
  String get addPhotos;

  /// No description provided for @addOption.
  ///
  /// In en, this message translates to:
  /// **'Add Option'**
  String get addOption;

  /// No description provided for @addedColor.
  ///
  /// In en, this message translates to:
  /// **'ADDED Colors'**
  String get addedColor;

  /// No description provided for @brand.
  ///
  /// In en, this message translates to:
  /// **'Brand'**
  String get brand;

  /// No description provided for @completeSetup.
  ///
  /// In en, this message translates to:
  /// **'Complete Setup'**
  String get completeSetup;

  /// No description provided for @updateInfo.
  ///
  /// In en, this message translates to:
  /// **'Update Info'**
  String get updateInfo;

  /// No description provided for @addSize.
  ///
  /// In en, this message translates to:
  /// **'Add Size'**
  String get addSize;

  /// No description provided for @uploadColorPhoto.
  ///
  /// In en, this message translates to:
  /// **'Upload Color Photo'**
  String get uploadColorPhoto;

  /// No description provided for @category.
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// No description provided for @custom.
  ///
  /// In en, this message translates to:
  /// **'Custom'**
  String get custom;

  /// No description provided for @viewColors.
  ///
  /// In en, this message translates to:
  /// **'View Colors'**
  String get viewColors;

  /// No description provided for @productname.
  ///
  /// In en, this message translates to:
  /// **'Product Name'**
  String get productname;

  /// No description provided for @addColor.
  ///
  /// In en, this message translates to:
  /// **'Add Color'**
  String get addColor;

  /// No description provided for @categories.
  ///
  /// In en, this message translates to:
  /// **'Categories'**
  String get categories;

  /// No description provided for @variation.
  ///
  /// In en, this message translates to:
  /// **'Variations'**
  String get variation;

  /// No description provided for @subCategory.
  ///
  /// In en, this message translates to:
  /// **'Sub Category'**
  String get subCategory;

  /// No description provided for @acceptReturn.
  ///
  /// In en, this message translates to:
  /// **'Accept return'**
  String get acceptReturn;

  /// No description provided for @accept.
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get accept;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @customOption.
  ///
  /// In en, this message translates to:
  /// **'Custom Option'**
  String get customOption;

  /// No description provided for @addcustomOption.
  ///
  /// In en, this message translates to:
  /// **'Add Custom Option'**
  String get addcustomOption;

  /// No description provided for @reject.
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get reject;

  /// No description provided for @addStory.
  ///
  /// In en, this message translates to:
  /// **'Add Story'**
  String get addStory;

  /// No description provided for @addPrice.
  ///
  /// In en, this message translates to:
  /// **'Add Price'**
  String get addPrice;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @addYourBrandName.
  ///
  /// In en, this message translates to:
  /// **'Add Your Brand Name'**
  String get addYourBrandName;

  /// No description provided for @maxBoxDimension.
  ///
  /// In en, this message translates to:
  /// **'Maximum Box Dimensions'**
  String get maxBoxDimension;

  /// No description provided for @genrateVariations.
  ///
  /// In en, this message translates to:
  /// **'Genrate Variations'**
  String get genrateVariations;

  /// No description provided for @notificationOnMinQtyIsReached.
  ///
  /// In en, this message translates to:
  /// **'Notification on Min. Qty is reached'**
  String get notificationOnMinQtyIsReached;

  /// No description provided for @availableQuantity.
  ///
  /// In en, this message translates to:
  /// **'Available Quantity'**
  String get availableQuantity;

  /// No description provided for @aed.
  ///
  /// In en, this message translates to:
  /// **'AED'**
  String get aed;

  /// No description provided for @size.
  ///
  /// In en, this message translates to:
  /// **'Sizes'**
  String get size;

  /// No description provided for @returnCondition.
  ///
  /// In en, this message translates to:
  /// **'Return Condition'**
  String get returnCondition;

  /// No description provided for @productPolicy.
  ///
  /// In en, this message translates to:
  /// **'Product Policy'**
  String get productPolicy;

  /// No description provided for @viewListofBrand.
  ///
  /// In en, this message translates to:
  /// **'View List of brands'**
  String get viewListofBrand;

  /// No description provided for @chooseBrand.
  ///
  /// In en, this message translates to:
  /// **'Choose Brand'**
  String get chooseBrand;

  /// No description provided for @recentlySelected.
  ///
  /// In en, this message translates to:
  /// **'Recently Selected'**
  String get recentlySelected;

  /// No description provided for @selectCategory.
  ///
  /// In en, this message translates to:
  /// **'Select Category'**
  String get selectCategory;

  /// No description provided for @updateRefundStatus.
  ///
  /// In en, this message translates to:
  /// **'Update refund status'**
  String get updateRefundStatus;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @polices.
  ///
  /// In en, this message translates to:
  /// **'Polices'**
  String get polices;

  /// No description provided for @width.
  ///
  /// In en, this message translates to:
  /// **'Width'**
  String get width;

  /// No description provided for @length.
  ///
  /// In en, this message translates to:
  /// **'Length'**
  String get length;

  /// No description provided for @weight.
  ///
  /// In en, this message translates to:
  /// **'Weight'**
  String get weight;

  /// No description provided for @height.
  ///
  /// In en, this message translates to:
  /// **'Height'**
  String get height;

  /// No description provided for @requiredRefregiration.
  ///
  /// In en, this message translates to:
  /// **'Required refregiration'**
  String get requiredRefregiration;

  /// No description provided for @specification.
  ///
  /// In en, this message translates to:
  /// **'Specification'**
  String get specification;

  /// No description provided for @itemPerOrder.
  ///
  /// In en, this message translates to:
  /// **'Items per Order'**
  String get itemPerOrder;

  /// No description provided for @newSpecification.
  ///
  /// In en, this message translates to:
  /// **'New Specification'**
  String get newSpecification;

  /// No description provided for @oldSpecification.
  ///
  /// In en, this message translates to:
  /// **'Old Specification'**
  String get oldSpecification;

  /// No description provided for @driverName.
  ///
  /// In en, this message translates to:
  /// **'Driver Name'**
  String get driverName;

  /// No description provided for @deliveryCompany.
  ///
  /// In en, this message translates to:
  /// **'Delivery Company'**
  String get deliveryCompany;

  /// No description provided for @officeAddress.
  ///
  /// In en, this message translates to:
  /// **'Office Address'**
  String get officeAddress;

  /// No description provided for @confirmAll.
  ///
  /// In en, this message translates to:
  /// **'CONFIRM ALL'**
  String get confirmAll;

  /// No description provided for @send.
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// No description provided for @returnDuration.
  ///
  /// In en, this message translates to:
  /// **'Return Duration'**
  String get returnDuration;

  /// No description provided for @warrantyDuration.
  ///
  /// In en, this message translates to:
  /// **'Warranty Duration'**
  String get warrantyDuration;

  /// No description provided for @keyword.
  ///
  /// In en, this message translates to:
  /// **'Keywords'**
  String get keyword;

  /// No description provided for @buyerDetails.
  ///
  /// In en, this message translates to:
  /// **'Buyer Details'**
  String get buyerDetails;

  /// No description provided for @deliveryDetails.
  ///
  /// In en, this message translates to:
  /// **'Delivery Details'**
  String get deliveryDetails;

  /// No description provided for @ordered.
  ///
  /// In en, this message translates to:
  /// **'Ordered'**
  String get ordered;

  /// No description provided for @delivered.
  ///
  /// In en, this message translates to:
  /// **'Delivered'**
  String get delivered;

  /// No description provided for @confirmed.
  ///
  /// In en, this message translates to:
  /// **'Confirmed'**
  String get confirmed;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @rejected.
  ///
  /// In en, this message translates to:
  /// **'Rejected'**
  String get rejected;

  /// No description provided for @cancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// No description provided for @shipmentInformation.
  ///
  /// In en, this message translates to:
  /// **'Shipment Information'**
  String get shipmentInformation;

  /// No description provided for @shipmentFits.
  ///
  /// In en, this message translates to:
  /// **'Package Type'**
  String get shipmentFits;

  /// No description provided for @returned.
  ///
  /// In en, this message translates to:
  /// **'Returned'**
  String get returned;

  /// No description provided for @shipment.
  ///
  /// In en, this message translates to:
  /// **'Shipment'**
  String get shipment;

  /// No description provided for @paymentMethod.
  ///
  /// In en, this message translates to:
  /// **'Payment Method'**
  String get paymentMethod;

  /// No description provided for @returnedConfirmed.
  ///
  /// In en, this message translates to:
  /// **'Returned\n Confirmed'**
  String get returnedConfirmed;

  /// No description provided for @cancelledByCustomer.
  ///
  /// In en, this message translates to:
  /// **'Cancelled\n by customer'**
  String get cancelledByCustomer;

  /// No description provided for @shipped.
  ///
  /// In en, this message translates to:
  /// **'Shipped'**
  String get shipped;

  /// No description provided for @orderDetail.
  ///
  /// In en, this message translates to:
  /// **'Order Detail'**
  String get orderDetail;

  /// No description provided for @transferRequest.
  ///
  /// In en, this message translates to:
  /// **'Transfer Request'**
  String get transferRequest;

  /// No description provided for @youAreGoingToSend.
  ///
  /// In en, this message translates to:
  /// **'You are going to send'**
  String get youAreGoingToSend;

  /// No description provided for @vat.
  ///
  /// In en, this message translates to:
  /// **'VAT'**
  String get vat;

  /// No description provided for @myShop.
  ///
  /// In en, this message translates to:
  /// **'My shop'**
  String get myShop;

  /// No description provided for @orderNumber.
  ///
  /// In en, this message translates to:
  /// **'Order Number'**
  String get orderNumber;

  /// No description provided for @totalAmount.
  ///
  /// In en, this message translates to:
  /// **'Total Amount'**
  String get totalAmount;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @transactionID.
  ///
  /// In en, this message translates to:
  /// **'Transaction ID'**
  String get transactionID;

  /// No description provided for @withdrawalID.
  ///
  /// In en, this message translates to:
  /// **'WithDrawal ID'**
  String get withdrawalID;

  /// No description provided for @orderID.
  ///
  /// In en, this message translates to:
  /// **'Order ID'**
  String get orderID;

  /// No description provided for @amount.
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// No description provided for @more.
  ///
  /// In en, this message translates to:
  /// **'more'**
  String get more;

  /// No description provided for @sales.
  ///
  /// In en, this message translates to:
  /// **'Sales'**
  String get sales;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @transferredAmount.
  ///
  /// In en, this message translates to:
  /// **'Transferred Amount'**
  String get transferredAmount;

  /// No description provided for @topSellingItems.
  ///
  /// In en, this message translates to:
  /// **'Top Selling Items'**
  String get topSellingItems;

  /// No description provided for @initiateTransferRequest.
  ///
  /// In en, this message translates to:
  /// **'Initiate Transfer Request'**
  String get initiateTransferRequest;

  /// No description provided for @totalVat.
  ///
  /// In en, this message translates to:
  /// **'Total VAT'**
  String get totalVat;

  /// No description provided for @totalRevenue.
  ///
  /// In en, this message translates to:
  /// **' Total Revenue'**
  String get totalRevenue;

  /// No description provided for @productforFreeDelivery.
  ///
  /// In en, this message translates to:
  /// **'Product for free delivery'**
  String get productforFreeDelivery;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @verify.
  ///
  /// In en, this message translates to:
  /// **'Verify'**
  String get verify;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @pendingAmount.
  ///
  /// In en, this message translates to:
  /// **'Pending Amount'**
  String get pendingAmount;

  /// No description provided for @availableAmount.
  ///
  /// In en, this message translates to:
  /// **'Available Amount'**
  String get availableAmount;

  /// No description provided for @price.
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get price;

  /// No description provided for @miniQTY.
  ///
  /// In en, this message translates to:
  /// **'Minimum Quantity'**
  String get miniQTY;

  /// No description provided for @deliveryChargesFees.
  ///
  /// In en, this message translates to:
  /// **'Delivery Charges Fees'**
  String get deliveryChargesFees;

  /// No description provided for @reply.
  ///
  /// In en, this message translates to:
  /// **'Reply'**
  String get reply;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @remove.
  ///
  /// In en, this message translates to:
  /// **'Remove'**
  String get remove;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @details.
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// No description provided for @resetPasswordContent.
  ///
  /// In en, this message translates to:
  /// **'Enter your email to reset password'**
  String get resetPasswordContent;

  /// No description provided for @tryAnotherWay.
  ///
  /// In en, this message translates to:
  /// **'Or you can try another way'**
  String get tryAnotherWay;

  /// No description provided for @welcome.
  ///
  /// In en, this message translates to:
  /// **'Welcome,'**
  String get welcome;

  /// No description provided for @addMobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Add Mobile Number'**
  String get addMobileNumber;

  /// No description provided for @enterAMobileNumberToSafeguardYourAccount.
  ///
  /// In en, this message translates to:
  /// **'Enter a mobile number to safeguard your account.'**
  String get enterAMobileNumberToSafeguardYourAccount;

  /// No description provided for @resetPassword.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPassword;

  /// No description provided for @welcomeAgain.
  ///
  /// In en, this message translates to:
  /// **'Welcome again ,'**
  String get welcomeAgain;

  /// No description provided for @welcomeBack.
  ///
  /// In en, this message translates to:
  /// **'Welcome back!'**
  String get welcomeBack;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @bankAccount.
  ///
  /// In en, this message translates to:
  /// **'Bank Account'**
  String get bankAccount;

  /// No description provided for @activity.
  ///
  /// In en, this message translates to:
  /// **'Activity'**
  String get activity;

  /// No description provided for @creditCard.
  ///
  /// In en, this message translates to:
  /// **'Credit Card'**
  String get creditCard;

  /// No description provided for @setting.
  ///
  /// In en, this message translates to:
  /// **'SETTING'**
  String get setting;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @removeTermsConditionMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you Sure You Want To Remove Your Terms And Condition'**
  String get removeTermsConditionMessage;

  /// No description provided for @myInformation.
  ///
  /// In en, this message translates to:
  /// **'MY Information'**
  String get myInformation;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @order.
  ///
  /// In en, this message translates to:
  /// **'Orders'**
  String get order;

  /// No description provided for @howCanWeHelpYou.
  ///
  /// In en, this message translates to:
  /// **'How can we help you?'**
  String get howCanWeHelpYou;

  /// No description provided for @callUs.
  ///
  /// In en, this message translates to:
  /// **'Call US'**
  String get callUs;

  /// No description provided for @supportEmail.
  ///
  /// In en, this message translates to:
  /// **'<EMAIL>'**
  String get supportEmail;

  /// No description provided for @whatsapp.
  ///
  /// In en, this message translates to:
  /// **'WHATSAPP'**
  String get whatsapp;

  /// No description provided for @support.
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get support;

  /// No description provided for @minimumQtyAlert.
  ///
  /// In en, this message translates to:
  /// **'Minimum Qty Alert'**
  String get minimumQtyAlert;

  /// No description provided for @minimumQty.
  ///
  /// In en, this message translates to:
  /// **'Minimum QTY'**
  String get minimumQty;

  /// No description provided for @freeDeliveryTarget.
  ///
  /// In en, this message translates to:
  /// **'Free Delivery Target'**
  String get freeDeliveryTarget;

  /// No description provided for @alert.
  ///
  /// In en, this message translates to:
  /// **'Alert'**
  String get alert;

  /// No description provided for @freeDeliveryItems.
  ///
  /// In en, this message translates to:
  /// **'Free Delivery Items'**
  String get freeDeliveryItems;

  /// No description provided for @sold.
  ///
  /// In en, this message translates to:
  /// **'Sold'**
  String get sold;

  /// No description provided for @services.
  ///
  /// In en, this message translates to:
  /// **'Services'**
  String get services;

  /// No description provided for @review.
  ///
  /// In en, this message translates to:
  /// **'Review'**
  String get review;

  /// No description provided for @incomeAndExpenses.
  ///
  /// In en, this message translates to:
  /// **'Income & Expenses'**
  String get incomeAndExpenses;

  /// No description provided for @addProduct.
  ///
  /// In en, this message translates to:
  /// **'Add Product'**
  String get addProduct;

  /// No description provided for @myTotalIncome.
  ///
  /// In en, this message translates to:
  /// **'MY TOTAL INCOME'**
  String get myTotalIncome;

  /// No description provided for @matchThisProduct.
  ///
  /// In en, this message translates to:
  /// **'Match this product'**
  String get matchThisProduct;

  /// No description provided for @createNewProduct.
  ///
  /// In en, this message translates to:
  /// **'Create New Product'**
  String get createNewProduct;

  /// No description provided for @similarProduct.
  ///
  /// In en, this message translates to:
  /// **'Match Product'**
  String get similarProduct;

  /// No description provided for @myAccounts.
  ///
  /// In en, this message translates to:
  /// **'My Accounts'**
  String get myAccounts;

  /// No description provided for @sellerDashboard.
  ///
  /// In en, this message translates to:
  /// **'Seller Dashboard'**
  String get sellerDashboard;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @product.
  ///
  /// In en, this message translates to:
  /// **'Product'**
  String get product;

  /// No description provided for @updateNow.
  ///
  /// In en, this message translates to:
  /// **'Update Now'**
  String get updateNow;

  /// No description provided for @publishNow.
  ///
  /// In en, this message translates to:
  /// **'Publish Now'**
  String get publishNow;

  /// No description provided for @saveForLater.
  ///
  /// In en, this message translates to:
  /// **'Save As Draft'**
  String get saveForLater;

  /// No description provided for @shop.
  ///
  /// In en, this message translates to:
  /// **'Shop'**
  String get shop;

  /// No description provided for @addNewBranch.
  ///
  /// In en, this message translates to:
  /// **'Add new branch'**
  String get addNewBranch;

  /// No description provided for @addNewColor.
  ///
  /// In en, this message translates to:
  /// **'Add New Colour'**
  String get addNewColor;

  /// No description provided for @openGoogleMap.
  ///
  /// In en, this message translates to:
  /// **'OPEN GOOGLE MAP'**
  String get openGoogleMap;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @shopDescription.
  ///
  /// In en, this message translates to:
  /// **'Describe your shop.'**
  String get shopDescription;

  /// No description provided for @contactNumbers.
  ///
  /// In en, this message translates to:
  /// **'Contact Numbers'**
  String get contactNumbers;

  /// No description provided for @contactEmails.
  ///
  /// In en, this message translates to:
  /// **'Contact Emails'**
  String get contactEmails;

  /// No description provided for @addAnotherBranch.
  ///
  /// In en, this message translates to:
  /// **'Add another Branch'**
  String get addAnotherBranch;

  /// No description provided for @forgetPaswoord.
  ///
  /// In en, this message translates to:
  /// **'Forgot password?'**
  String get forgetPaswoord;

  /// No description provided for @termsAndCondition.
  ///
  /// In en, this message translates to:
  /// **'Terms And Conditions'**
  String get termsAndCondition;

  /// No description provided for @yourName.
  ///
  /// In en, this message translates to:
  /// **'Your Name'**
  String get yourName;

  /// No description provided for @newName.
  ///
  /// In en, this message translates to:
  /// **'New Name'**
  String get newName;

  /// No description provided for @dashboard.
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// No description provided for @deleteThisColor.
  ///
  /// In en, this message translates to:
  /// **'Delete This Color'**
  String get deleteThisColor;

  /// No description provided for @deleteThisProduct.
  ///
  /// In en, this message translates to:
  /// **'Delete This Product'**
  String get deleteThisProduct;

  /// No description provided for @valid.
  ///
  /// In en, this message translates to:
  /// **'VALID'**
  String get valid;

  /// No description provided for @addAnotherContactNumber.
  ///
  /// In en, this message translates to:
  /// **'Add Another Contact Number'**
  String get addAnotherContactNumber;

  /// No description provided for @addAnotherContactEmail.
  ///
  /// In en, this message translates to:
  /// **'Add Another Contact Email'**
  String get addAnotherContactEmail;

  /// No description provided for @addLocationMap.
  ///
  /// In en, this message translates to:
  /// **'Add Location Map'**
  String get addLocationMap;

  /// No description provided for @addLocationUsingMap.
  ///
  /// In en, this message translates to:
  /// **'Add Location Using Map'**
  String get addLocationUsingMap;

  /// No description provided for @targetPrice.
  ///
  /// In en, this message translates to:
  /// **'Target Price'**
  String get targetPrice;

  /// No description provided for @passwordHint.
  ///
  /// In en, this message translates to:
  /// **'••••••••'**
  String get passwordHint;

  /// No description provided for @emailfeildLabel.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get emailfeildLabel;

  /// No description provided for @newEmail.
  ///
  /// In en, this message translates to:
  /// **'New Email'**
  String get newEmail;

  /// No description provided for @newPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'New Phone Number'**
  String get newPhoneNumber;

  /// No description provided for @addInfoAboutYourShop.
  ///
  /// In en, this message translates to:
  /// **'Add Info About Your Shop'**
  String get addInfoAboutYourShop;

  /// No description provided for @createShop.
  ///
  /// In en, this message translates to:
  /// **'Create Shop'**
  String get createShop;

  /// No description provided for @createShopContent.
  ///
  /// In en, this message translates to:
  /// **'Now you can create your own shop and add your products'**
  String get createShopContent;

  /// No description provided for @continu.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continu;

  /// No description provided for @city.
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get city;

  /// No description provided for @notification.
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get notification;

  /// No description provided for @reviews.
  ///
  /// In en, this message translates to:
  /// **'Reviews'**
  String get reviews;

  /// No description provided for @qty.
  ///
  /// In en, this message translates to:
  /// **'Qty'**
  String get qty;

  /// No description provided for @color.
  ///
  /// In en, this message translates to:
  /// **'Colors'**
  String get color;

  /// No description provided for @pickUp.
  ///
  /// In en, this message translates to:
  /// **'Pick-up'**
  String get pickUp;

  /// No description provided for @estimatedDelivery.
  ///
  /// In en, this message translates to:
  /// **'Estimated delivery'**
  String get estimatedDelivery;

  /// No description provided for @phone.
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// No description provided for @productId.
  ///
  /// In en, this message translates to:
  /// **'Product ID'**
  String get productId;

  /// No description provided for @productEin.
  ///
  /// In en, this message translates to:
  /// **' Product EIN'**
  String get productEin;

  /// No description provided for @variantEin.
  ///
  /// In en, this message translates to:
  /// **'Variant EIN'**
  String get variantEin;

  /// No description provided for @securityAndMangments.
  ///
  /// In en, this message translates to:
  /// **'Accounts And Mangments'**
  String get securityAndMangments;

  /// No description provided for @appSettings.
  ///
  /// In en, this message translates to:
  /// **'App Settings'**
  String get appSettings;

  /// No description provided for @deactivateTheAccount.
  ///
  /// In en, this message translates to:
  /// **'Deactivate The Account'**
  String get deactivateTheAccount;

  /// No description provided for @changePassword.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get changePassword;

  /// No description provided for @changeYourEmail.
  ///
  /// In en, this message translates to:
  /// **'Change Your Email'**
  String get changeYourEmail;

  /// No description provided for @changeYourUserName.
  ///
  /// In en, this message translates to:
  /// **'Change Your User Name'**
  String get changeYourUserName;

  /// No description provided for @changeYourMobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Change Your Mobile Number'**
  String get changeYourMobileNumber;

  /// No description provided for @editAccount.
  ///
  /// In en, this message translates to:
  /// **'Edit Account'**
  String get editAccount;

  /// No description provided for @whyThisInformationRequired.
  ///
  /// In en, this message translates to:
  /// **'Why this information is required?'**
  String get whyThisInformationRequired;

  /// No description provided for @addNewProduct.
  ///
  /// In en, this message translates to:
  /// **'Add New Product'**
  String get addNewProduct;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @myOrders.
  ///
  /// In en, this message translates to:
  /// **'My Orders'**
  String get myOrders;

  /// No description provided for @myProfile.
  ///
  /// In en, this message translates to:
  /// **'My profile'**
  String get myProfile;

  /// No description provided for @faq.
  ///
  /// In en, this message translates to:
  /// **'Faq'**
  String get faq;

  /// No description provided for @faqs.
  ///
  /// In en, this message translates to:
  /// **'FAQS'**
  String get faqs;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @updatedDate.
  ///
  /// In en, this message translates to:
  /// **'Updated On'**
  String get updatedDate;

  /// No description provided for @uploadShopBanner.
  ///
  /// In en, this message translates to:
  /// **'Upload shop banner'**
  String get uploadShopBanner;

  /// No description provided for @logo.
  ///
  /// In en, this message translates to:
  /// **'Logo'**
  String get logo;

  /// No description provided for @iban.
  ///
  /// In en, this message translates to:
  /// **'IBAN'**
  String get iban;

  /// No description provided for @message.
  ///
  /// In en, this message translates to:
  /// **'Message'**
  String get message;

  /// No description provided for @returnedFeilds.
  ///
  /// In en, this message translates to:
  /// **'Returned Feilds'**
  String get returnedFeilds;

  /// No description provided for @bankName.
  ///
  /// In en, this message translates to:
  /// **'Bank Name'**
  String get bankName;

  /// No description provided for @accountHolderName.
  ///
  /// In en, this message translates to:
  /// **'Account Holder Name'**
  String get accountHolderName;

  /// No description provided for @bankAccountInfo.
  ///
  /// In en, this message translates to:
  /// **'Bank Account Information'**
  String get bankAccountInfo;

  /// No description provided for @information.
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get information;

  /// No description provided for @adminNotes.
  ///
  /// In en, this message translates to:
  /// **'Admin Notes'**
  String get adminNotes;

  /// No description provided for @trashProducts.
  ///
  /// In en, this message translates to:
  /// **'Deleted Products'**
  String get trashProducts;

  /// No description provided for @privacyAndPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy And Policy'**
  String get privacyAndPolicy;

  /// No description provided for @logoutMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to logout'**
  String get logoutMessage;

  /// No description provided for @returnRequest.
  ///
  /// In en, this message translates to:
  /// **'Requests'**
  String get returnRequest;

  /// No description provided for @productReviews.
  ///
  /// In en, this message translates to:
  /// **'Product Reviews'**
  String get productReviews;

  /// No description provided for @aboutTheShop.
  ///
  /// In en, this message translates to:
  /// **'About The Shop'**
  String get aboutTheShop;

  /// No description provided for @manageBranches.
  ///
  /// In en, this message translates to:
  /// **'Manage Branches'**
  String get manageBranches;

  /// No description provided for @updateBussinessInfo.
  ///
  /// In en, this message translates to:
  /// **'Update Business Info'**
  String get updateBussinessInfo;

  /// No description provided for @addBusinessInformation.
  ///
  /// In en, this message translates to:
  /// **'Add Business Information'**
  String get addBusinessInformation;

  /// No description provided for @updateBusinessInformation.
  ///
  /// In en, this message translates to:
  /// **'Update Business Information'**
  String get updateBusinessInformation;

  /// No description provided for @loginDescription.
  ///
  /// In en, this message translates to:
  /// **'we are happy to see you again'**
  String get loginDescription;

  /// No description provided for @signupDescription.
  ///
  /// In en, this message translates to:
  /// **'We are thrilled to have you on board again'**
  String get signupDescription;

  /// No description provided for @passwordFeildLabel.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get passwordFeildLabel;

  /// No description provided for @reEnterPassword.
  ///
  /// In en, this message translates to:
  /// **'Re-enter password'**
  String get reEnterPassword;

  /// No description provided for @resendOtp.
  ///
  /// In en, this message translates to:
  /// **'Resend OTP'**
  String get resendOtp;

  /// No description provided for @accountNumber.
  ///
  /// In en, this message translates to:
  /// **'Account Number'**
  String get accountNumber;

  /// No description provided for @returnedDate.
  ///
  /// In en, this message translates to:
  /// **'Returned Date'**
  String get returnedDate;

  /// No description provided for @notReceived.
  ///
  /// In en, this message translates to:
  /// **'Not Received? '**
  String get notReceived;

  /// No description provided for @secondsRemainToResendOtp.
  ///
  /// In en, this message translates to:
  /// **'Seconds Remaing To Resend Otp'**
  String get secondsRemainToResendOtp;

  /// No description provided for @createYourAccount.
  ///
  /// In en, this message translates to:
  /// **'Create Your Account'**
  String get createYourAccount;

  /// No description provided for @alreadyHaveAnAccount.
  ///
  /// In en, this message translates to:
  /// **'Already have an account? '**
  String get alreadyHaveAnAccount;

  /// No description provided for @emailHint.
  ///
  /// In en, this message translates to:
  /// **'ex : <EMAIL>'**
  String get emailHint;

  /// No description provided for @createAnAccount.
  ///
  /// In en, this message translates to:
  /// **'Create An Account'**
  String get createAnAccount;

  /// No description provided for @nameHint.
  ///
  /// In en, this message translates to:
  /// **'ex : Khalid Moh'**
  String get nameHint;

  /// No description provided for @morningMsg.
  ///
  /// In en, this message translates to:
  /// **'Morning! Ready to boost your sales?'**
  String get morningMsg;

  /// No description provided for @noonMsg.
  ///
  /// In en, this message translates to:
  /// **'Noon! Keep the orders rolling in!'**
  String get noonMsg;

  /// No description provided for @afterNoonMsg.
  ///
  /// In en, this message translates to:
  /// **'Afternoon! Push for more sales!'**
  String get afterNoonMsg;

  /// No description provided for @eveningMsg.
  ///
  /// In en, this message translates to:
  /// **'Evening! Review today\'s progress.'**
  String get eveningMsg;

  /// No description provided for @greetTimeError.
  ///
  /// In en, this message translates to:
  /// **'Time issue! Check your clock!'**
  String get greetTimeError;

  /// No description provided for @searchProductFeildHint.
  ///
  /// In en, this message translates to:
  /// **'Headset Earpods white wireless'**
  String get searchProductFeildHint;

  /// No description provided for @skipAddNewProduct.
  ///
  /// In en, this message translates to:
  /// **'Skip ( Add New Product )'**
  String get skipAddNewProduct;

  /// No description provided for @matchExsistingProduct.
  ///
  /// In en, this message translates to:
  /// **'Match an existing product on Elbaab.'**
  String get matchExsistingProduct;

  /// No description provided for @or.
  ///
  /// In en, this message translates to:
  /// **'OR'**
  String get or;

  /// No description provided for @searchType.
  ///
  /// In en, this message translates to:
  /// **'\n\n Check With :\n\n• Product Name\n\n• EIN code (Elbaab Identification Number)\n\n• Product GTIN (Global Trade Item Number)\n\n• Product UPC (Universal Product Code) \n\n• Product SKU (Stock Keeping Unit)\n'**
  String get searchType;

  /// No description provided for @verificationCode.
  ///
  /// In en, this message translates to:
  /// **'Verification Code'**
  String get verificationCode;

  /// No description provided for @settingOtpDailogMessage.
  ///
  /// In en, this message translates to:
  /// **'We have sent the code verification to'**
  String get settingOtpDailogMessage;

  /// No description provided for @verifyOtp.
  ///
  /// In en, this message translates to:
  /// **'Verify Otp'**
  String get verifyOtp;

  /// No description provided for @updateBankAccount.
  ///
  /// In en, this message translates to:
  /// **'Update bank account'**
  String get updateBankAccount;

  /// No description provided for @addNewBankAccount.
  ///
  /// In en, this message translates to:
  /// **'Add new bank account'**
  String get addNewBankAccount;

  /// No description provided for @waitWhileLoadShop.
  ///
  /// In en, this message translates to:
  /// **'Wait until the shop is loaded'**
  String get waitWhileLoadShop;

  /// No description provided for @pickupAddress.
  ///
  /// In en, this message translates to:
  /// **'Pickup Addresses'**
  String get pickupAddress;

  /// No description provided for @pickupAddressUsageContant.
  ///
  /// In en, this message translates to:
  /// **'Pick up address will be used for shipping. It is not visible to customers.'**
  String get pickupAddressUsageContant;

  /// No description provided for @options.
  ///
  /// In en, this message translates to:
  /// **'Options'**
  String get options;

  /// No description provided for @priceAndVariations.
  ///
  /// In en, this message translates to:
  /// **'Price & variations'**
  String get priceAndVariations;

  /// No description provided for @productNameFeildLableEnglish.
  ///
  /// In en, this message translates to:
  /// **'Product Name (English)'**
  String get productNameFeildLableEnglish;

  /// No description provided for @productNameFeildLableArbic.
  ///
  /// In en, this message translates to:
  /// **'Product Name (Arabic)'**
  String get productNameFeildLableArbic;

  /// No description provided for @productNameFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : T-shirt'**
  String get productNameFeildHint;

  /// No description provided for @productNameFeildReturnMessage.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this name'**
  String get productNameFeildReturnMessage;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email is required.'**
  String get email;

  /// No description provided for @invalidEmail.
  ///
  /// In en, this message translates to:
  /// **'Invalid Email.'**
  String get invalidEmail;

  /// No description provided for @invalidText.
  ///
  /// In en, this message translates to:
  /// **'Invalid Text You Insert.'**
  String get invalidText;

  /// No description provided for @phoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Phone number is required.'**
  String get phoneNumber;

  /// No description provided for @invalidPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Invalid phone number.'**
  String get invalidPhoneNumber;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password field is required.'**
  String get password;

  /// No description provided for @urlNotAccepted.
  ///
  /// In en, this message translates to:
  /// **'Link and URL are not accepted'**
  String get urlNotAccepted;

  /// No description provided for @tradeCertificateRequired.
  ///
  /// In en, this message translates to:
  /// **'Trade Certificate is Required'**
  String get tradeCertificateRequired;

  /// No description provided for @emiratesIDRequired.
  ///
  /// In en, this message translates to:
  /// **'Emirates ID Or Passport Copy Is Required'**
  String get emiratesIDRequired;

  /// No description provided for @invalidPassword.
  ///
  /// In en, this message translates to:
  /// **'Password must contain minimum six characters'**
  String get invalidPassword;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password field is required.'**
  String get confirmPassword;

  /// No description provided for @fieldRequired.
  ///
  /// In en, this message translates to:
  /// **'Field is Required'**
  String get fieldRequired;

  /// No description provided for @errEmpty.
  ///
  /// In en, this message translates to:
  /// **'Field is empty'**
  String get errEmpty;

  /// No description provided for @gtinFeildLabel.
  ///
  /// In en, this message translates to:
  /// **'GTIN OR UPC'**
  String get gtinFeildLabel;

  /// No description provided for @gtinFeildReturnMessage.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this Manufacturer ID'**
  String get gtinFeildReturnMessage;

  /// No description provided for @gtinFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : Manufacturer ID'**
  String get gtinFeildHint;

  /// No description provided for @errReachImageLimit.
  ///
  /// In en, this message translates to:
  /// **'You reach image limit For Single Color'**
  String get errReachImageLimit;

  /// No description provided for @errSelectImageForColor.
  ///
  /// In en, this message translates to:
  /// **'You will asign maximum 5 photos for each Color'**
  String get errSelectImageForColor;

  /// No description provided for @passwordDoesNotMatch.
  ///
  /// In en, this message translates to:
  /// **'Password Does Not Match'**
  String get passwordDoesNotMatch;

  /// No description provided for @productDescriptionFeildLableEnglish.
  ///
  /// In en, this message translates to:
  /// **'Product Description (English)'**
  String get productDescriptionFeildLableEnglish;

  /// No description provided for @productDescriptionFeildLableArbic.
  ///
  /// In en, this message translates to:
  /// **'Product Description (Arabic)'**
  String get productDescriptionFeildLableArbic;

  /// No description provided for @productDescriptionFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : high neck t-shirt with short seleeves'**
  String get productDescriptionFeildHint;

  /// No description provided for @productDescriptionFeildReturnMessage.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this description'**
  String get productDescriptionFeildReturnMessage;

  /// No description provided for @freeDeliveryTargetMessage.
  ///
  /// In en, this message translates to:
  /// **'If customer reaches this target amount or above, you will deliver the product for free'**
  String get freeDeliveryTargetMessage;

  /// No description provided for @locationRequired.
  ///
  /// In en, this message translates to:
  /// **'Your location is required so shipment company can pick up your item'**
  String get locationRequired;

  /// No description provided for @whyBankInfo.
  ///
  /// In en, this message translates to:
  /// **'To start receiving online payments'**
  String get whyBankInfo;

  /// No description provided for @pendingAmountAlertMessage.
  ///
  /// In en, this message translates to:
  /// **'Pending amount will be available after 30 days from Customer receiving Date'**
  String get pendingAmountAlertMessage;

  /// No description provided for @messageSent.
  ///
  /// In en, this message translates to:
  /// **'Your message has been sent successfully'**
  String get messageSent;

  /// No description provided for @addKeywordMessage.
  ///
  /// In en, this message translates to:
  /// **'To improve your chance of selling add keywords and use comma between them'**
  String get addKeywordMessage;

  /// No description provided for @availableQtyMessage.
  ///
  /// In en, this message translates to:
  /// **'This information will shown only for you so, you can monitor your stock'**
  String get availableQtyMessage;

  /// No description provided for @minimumQtyAlertMessage.
  ///
  /// In en, this message translates to:
  /// **'You will receive a notification when the number of item reaches to minimum quantity and will be displayed in the user app as (Remaining)'**
  String get minimumQtyAlertMessage;

  /// No description provided for @requiredBanner.
  ///
  /// In en, this message translates to:
  /// **'Shop Banner Image Is Required'**
  String get requiredBanner;

  /// No description provided for @requiredLogo.
  ///
  /// In en, this message translates to:
  /// **'Shop Logo Image Is Required'**
  String get requiredLogo;

  /// No description provided for @requiredTargetPrice.
  ///
  /// In en, this message translates to:
  /// **'You Enable Free Delivery Target But You Didn\'t Enter Price'**
  String get requiredTargetPrice;

  /// No description provided for @requiredTermsAndConditions.
  ///
  /// In en, this message translates to:
  /// **'Upload Your Shop Terms And Conditions'**
  String get requiredTermsAndConditions;

  /// No description provided for @requiredCity.
  ///
  /// In en, this message translates to:
  /// **'Upload Your Shop Terms And Conditions'**
  String get requiredCity;

  /// No description provided for @requiredMapLocation.
  ///
  /// In en, this message translates to:
  /// **'Select Pin Location Using Map'**
  String get requiredMapLocation;

  /// No description provided for @requiredNumberForBranch.
  ///
  /// In en, this message translates to:
  /// **'At least One Number Is Required For Branch'**
  String get requiredNumberForBranch;

  /// No description provided for @requiredEmailForBranch.
  ///
  /// In en, this message translates to:
  /// **'At least One Email Is Required For Branch'**
  String get requiredEmailForBranch;

  /// No description provided for @requiredOtpCode.
  ///
  /// In en, this message translates to:
  /// **'Enter Valid Otp'**
  String get requiredOtpCode;

  /// No description provided for @pleaseAcceptTermsAndConditions.
  ///
  /// In en, this message translates to:
  /// **'Please Accept Terms and Conditions'**
  String get pleaseAcceptTermsAndConditions;

  /// No description provided for @limitReached.
  ///
  /// In en, this message translates to:
  /// **'You have reached the limit'**
  String get limitReached;

  /// No description provided for @infoForDeliveryCompany.
  ///
  /// In en, this message translates to:
  /// **'This Info Is Important For Delivery Company, you can enter the approximate values'**
  String get infoForDeliveryCompany;

  /// No description provided for @isFreeDeliveryProduct.
  ///
  /// In en, this message translates to:
  /// **'Is Product For Free Delivery'**
  String get isFreeDeliveryProduct;

  /// No description provided for @isFreeDeliveryProductMessage.
  ///
  /// In en, this message translates to:
  /// **'If you enable, you will pay all delivery charges'**
  String get isFreeDeliveryProductMessage;

  /// No description provided for @freeReturnTitle.
  ///
  /// In en, this message translates to:
  /// **'Free return'**
  String get freeReturnTitle;

  /// No description provided for @freeReturnMessage.
  ///
  /// In en, this message translates to:
  /// **'You will pay the return fees'**
  String get freeReturnMessage;

  /// No description provided for @notFreeReturnTitle.
  ///
  /// In en, this message translates to:
  /// **'Not free return'**
  String get notFreeReturnTitle;

  /// No description provided for @notFreeReturnMessage.
  ///
  /// In en, this message translates to:
  /// **'Customer will pay the return fees.'**
  String get notFreeReturnMessage;

  /// No description provided for @adminReturnConditionNote.
  ///
  /// In en, this message translates to:
  /// **'Admin Reject Your Return Condition'**
  String get adminReturnConditionNote;

  /// No description provided for @adminReturnDurationNote.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this return duration'**
  String get adminReturnDurationNote;

  /// No description provided for @returnDurationFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : 5 Days'**
  String get returnDurationFeildHint;

  /// No description provided for @returnDurationFeildLabell.
  ///
  /// In en, this message translates to:
  /// **'Return duration'**
  String get returnDurationFeildLabell;

  /// No description provided for @returnDurationSecondFeildLabell.
  ///
  /// In en, this message translates to:
  /// **'Enter Return duration'**
  String get returnDurationSecondFeildLabell;

  /// No description provided for @adminWarrantyDurationNote.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this warranty duration'**
  String get adminWarrantyDurationNote;

  /// No description provided for @warrantyDurationFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : 5 Days'**
  String get warrantyDurationFeildHint;

  /// No description provided for @warrantyDurationFeildLabel.
  ///
  /// In en, this message translates to:
  /// **'Warranty duration'**
  String get warrantyDurationFeildLabel;

  /// No description provided for @warrantyDurationSecondFeildLabel.
  ///
  /// In en, this message translates to:
  /// **'Enter Warranty duration'**
  String get warrantyDurationSecondFeildLabel;

  /// No description provided for @adminPolicyReturnNote.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this product policy'**
  String get adminPolicyReturnNote;

  /// No description provided for @updateReturnData.
  ///
  /// In en, this message translates to:
  /// **'Update Return Data'**
  String get updateReturnData;

  /// No description provided for @policyFeildLabelEnglish.
  ///
  /// In en, this message translates to:
  /// **'Policy (English)'**
  String get policyFeildLabelEnglish;

  /// No description provided for @policyFeildLabelArabic.
  ///
  /// In en, this message translates to:
  /// **'Policy (Arabic)'**
  String get policyFeildLabelArabic;

  /// No description provided for @policyFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : Cotton Grown Using Natural Fertilisers And Pesticides. Moreover,'**
  String get policyFeildHint;

  /// No description provided for @selectSubCategroy.
  ///
  /// In en, this message translates to:
  /// **'Select Sub Category'**
  String get selectSubCategroy;

  /// No description provided for @adminRejectThisCategory.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this category'**
  String get adminRejectThisCategory;

  /// No description provided for @adminRejectThisBrand.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this brand'**
  String get adminRejectThisBrand;

  /// No description provided for @pleaseSelectBrandOrType.
  ///
  /// In en, this message translates to:
  /// **'Please select brand or type your own brand name'**
  String get pleaseSelectBrandOrType;

  /// No description provided for @adminRejectThisSubCategory.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this sub category'**
  String get adminRejectThisSubCategory;

  /// No description provided for @pleaseSelectCategorySubCategory.
  ///
  /// In en, this message translates to:
  /// **'Please select category and sub category'**
  String get pleaseSelectCategorySubCategory;

  /// No description provided for @enterYourBrandName.
  ///
  /// In en, this message translates to:
  /// **'Enter your brand\'s name here'**
  String get enterYourBrandName;

  /// No description provided for @enterYourBrandNameFeildError.
  ///
  /// In en, this message translates to:
  /// **'Enter your brand name if not found in the list'**
  String get enterYourBrandNameFeildError;

  /// No description provided for @searchBrandByName.
  ///
  /// In en, this message translates to:
  /// **'Search brand by name'**
  String get searchBrandByName;

  /// No description provided for @colorIcon.
  ///
  /// In en, this message translates to:
  /// **'Color Icon'**
  String get colorIcon;

  /// No description provided for @colorFamilyFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : red'**
  String get colorFamilyFeildHint;

  /// No description provided for @colorNameFeildLabel.
  ///
  /// In en, this message translates to:
  /// **'Add Color Name'**
  String get colorNameFeildLabel;

  /// No description provided for @colorNameFeildHint.
  ///
  /// In en, this message translates to:
  /// **'Color name ( light red , dark blue … )'**
  String get colorNameFeildHint;

  /// No description provided for @colorFamilyFeildReturnMessage.
  ///
  /// In en, this message translates to:
  /// **'Admin returned this color family'**
  String get colorFamilyFeildReturnMessage;

  /// No description provided for @selectColorFamily.
  ///
  /// In en, this message translates to:
  /// **'Select Color Family'**
  String get selectColorFamily;

  /// No description provided for @deleteImageAlertMesssgae.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this image'**
  String get deleteImageAlertMesssgae;

  /// No description provided for @adminRejectColorName.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this color name'**
  String get adminRejectColorName;

  /// No description provided for @alertMessageForRemoveColor.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove this color'**
  String get alertMessageForRemoveColor;

  /// No description provided for @alertMessageBeforeDiscardProductChanges.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to close? All changes will be discard.'**
  String get alertMessageBeforeDiscardProductChanges;

  /// No description provided for @discardChangesInProgress.
  ///
  /// In en, this message translates to:
  /// **'Discard changes is in-progress'**
  String get discardChangesInProgress;

  /// No description provided for @saveInfo.
  ///
  /// In en, this message translates to:
  /// **'Save Info?'**
  String get saveInfo;

  /// No description provided for @deleteProductFromCache.
  ///
  /// In en, this message translates to:
  /// **'Deleting product data from cache'**
  String get deleteProductFromCache;

  /// No description provided for @albums.
  ///
  /// In en, this message translates to:
  /// **'Albums'**
  String get albums;

  /// No description provided for @invalidImageFormate.
  ///
  /// In en, this message translates to:
  /// **'Invalid Image Format'**
  String get invalidImageFormate;

  /// No description provided for @confirmSlection.
  ///
  /// In en, this message translates to:
  /// **'Confirm Selection'**
  String get confirmSlection;

  /// No description provided for @imageCropper.
  ///
  /// In en, this message translates to:
  /// **'Image Cropper'**
  String get imageCropper;

  /// No description provided for @pinchZoomInout.
  ///
  /// In en, this message translates to:
  /// **'Pinch to zoom in/out'**
  String get pinchZoomInout;

  /// No description provided for @maxNumberItemsPerOrder.
  ///
  /// In en, this message translates to:
  /// **'Maximum number of items per order?'**
  String get maxNumberItemsPerOrder;

  /// No description provided for @itemPerOrderQuantityLessAvailable.
  ///
  /// In en, this message translates to:
  /// **'It should be less than available quantity'**
  String get itemPerOrderQuantityLessAvailable;

  /// No description provided for @zeroNotAcceptable.
  ///
  /// In en, this message translates to:
  /// **'Zero is not acceptable'**
  String get zeroNotAcceptable;

  /// No description provided for @itemPerOrderFeildLabel.
  ///
  /// In en, this message translates to:
  /// **'Items'**
  String get itemPerOrderFeildLabel;

  /// No description provided for @itemPerOrderFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : 2'**
  String get itemPerOrderFeildHint;

  /// No description provided for @specFeildLabelEnglish.
  ///
  /// In en, this message translates to:
  /// **'Title (English)'**
  String get specFeildLabelEnglish;

  /// No description provided for @specFeildLabelArabic.
  ///
  /// In en, this message translates to:
  /// **'Title (Arabic)'**
  String get specFeildLabelArabic;

  /// No description provided for @cityFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : Dubai'**
  String get cityFeildHint;

  /// No description provided for @updateDraftProduct.
  ///
  /// In en, this message translates to:
  /// **'Update Draft Product'**
  String get updateDraftProduct;

  /// No description provided for @alertMessageBeforeDeleteProduct.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want delete this product?'**
  String get alertMessageBeforeDeleteProduct;

  /// No description provided for @resetProductWillDiscardChanges.
  ///
  /// In en, this message translates to:
  /// **'Reset the product will discard all changes which you have applied'**
  String get resetProductWillDiscardChanges;

  /// No description provided for @kindlyAddBankAccount.
  ///
  /// In en, this message translates to:
  /// **'kindly add your bank information to get your financial dues'**
  String get kindlyAddBankAccount;

  /// No description provided for @itemPerOrderQuantityLessVariantAvailable.
  ///
  /// In en, this message translates to:
  /// **'It should be less than available variant quantity'**
  String get itemPerOrderQuantityLessVariantAvailable;

  /// No description provided for @adminRejectShipmentFitErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Oops! you havn\'t update shipment fits\nAdmin note: This product not fits on this size of shipment which you selected'**
  String get adminRejectShipmentFitErrorMessage;

  /// No description provided for @youRemoveProductName.
  ///
  /// In en, this message translates to:
  /// **'You removed product name from information'**
  String get youRemoveProductName;

  /// No description provided for @productNameNotProvided.
  ///
  /// In en, this message translates to:
  /// **'Product Name Not Provided'**
  String get productNameNotProvided;

  /// No description provided for @youRemoveProductDescription.
  ///
  /// In en, this message translates to:
  /// **'You removed product description from information'**
  String get youRemoveProductDescription;

  /// No description provided for @productDescriptionNotProvided.
  ///
  /// In en, this message translates to:
  /// **'Product Description Not Provided'**
  String get productDescriptionNotProvided;

  /// No description provided for @youRemoveProductGtin.
  ///
  /// In en, this message translates to:
  /// **'You removed product Gtin from information'**
  String get youRemoveProductGtin;

  /// No description provided for @productGtinNotProvided.
  ///
  /// In en, this message translates to:
  /// **'Product Gtin Not Provided'**
  String get productGtinNotProvided;

  /// No description provided for @youRemoveProductCategory.
  ///
  /// In en, this message translates to:
  /// **'You removed product category from information'**
  String get youRemoveProductCategory;

  /// No description provided for @productCategoryNotProvided.
  ///
  /// In en, this message translates to:
  /// **'Product Category Not Provided'**
  String get productCategoryNotProvided;

  /// No description provided for @youRemoveProductBrand.
  ///
  /// In en, this message translates to:
  /// **'You removed product brand from information'**
  String get youRemoveProductBrand;

  /// No description provided for @productBrandNotProvided.
  ///
  /// In en, this message translates to:
  /// **'Product Brand Not Provided'**
  String get productBrandNotProvided;

  /// No description provided for @backToShop.
  ///
  /// In en, this message translates to:
  /// **'Back to Shop'**
  String get backToShop;

  /// No description provided for @editShop.
  ///
  /// In en, this message translates to:
  /// **'Edit Shop'**
  String get editShop;

  /// No description provided for @badgeProductHidden.
  ///
  /// In en, this message translates to:
  /// **'Product is hidden'**
  String get badgeProductHidden;

  /// No description provided for @begdeModifyRequest.
  ///
  /// In en, this message translates to:
  /// **'Modified\nRequest'**
  String get begdeModifyRequest;

  /// No description provided for @alertShopDiscardChanges.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to discard shop info changes?'**
  String get alertShopDiscardChanges;

  /// No description provided for @productDetails.
  ///
  /// In en, this message translates to:
  /// **'Product Detail'**
  String get productDetails;

  /// No description provided for @hideThisItem.
  ///
  /// In en, this message translates to:
  /// **'Hide This Item'**
  String get hideThisItem;

  /// No description provided for @alertMessageForhideItem.
  ///
  /// In en, this message translates to:
  /// **'The product will not be visible to the customer'**
  String get alertMessageForhideItem;

  /// No description provided for @restoreLastSubmission.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to restore your last submission?'**
  String get restoreLastSubmission;

  /// No description provided for @restoreLastChanges.
  ///
  /// In en, this message translates to:
  /// **'Restore Last Changes'**
  String get restoreLastChanges;

  /// No description provided for @restoreLastChangesAlertMessage.
  ///
  /// In en, this message translates to:
  /// **'The product will back to last accepted version and all changes will be discarded'**
  String get restoreLastChangesAlertMessage;

  /// No description provided for @restoreProduct.
  ///
  /// In en, this message translates to:
  /// **'Restore Product'**
  String get restoreProduct;

  /// No description provided for @editProduct.
  ///
  /// In en, this message translates to:
  /// **'Edit Product'**
  String get editProduct;

  /// No description provided for @change.
  ///
  /// In en, this message translates to:
  /// **'Change >'**
  String get change;

  /// No description provided for @deleteItemSaveAlert.
  ///
  /// In en, this message translates to:
  /// **'Deleted items are saved for 30 days in Profile'**
  String get deleteItemSaveAlert;

  /// No description provided for @editPendingProductNotAllowed.
  ///
  /// In en, this message translates to:
  /// **'Edit on pending product is not allowed'**
  String get editPendingProductNotAllowed;

  /// No description provided for @informationTitleEnglish.
  ///
  /// In en, this message translates to:
  /// **'Information ( English )'**
  String get informationTitleEnglish;

  /// No description provided for @informationTitleArabic.
  ///
  /// In en, this message translates to:
  /// **'Information ( Arabic )'**
  String get informationTitleArabic;

  /// No description provided for @shipmentTitleEnglish.
  ///
  /// In en, this message translates to:
  /// **'Shipment ( English )'**
  String get shipmentTitleEnglish;

  /// No description provided for @shipmentTitleArabic.
  ///
  /// In en, this message translates to:
  /// **'Shipment ( Arabic )'**
  String get shipmentTitleArabic;

  /// No description provided for @policesTitleEnglish.
  ///
  /// In en, this message translates to:
  /// **'Polices ( English )'**
  String get policesTitleEnglish;

  /// No description provided for @policesTitleArabic.
  ///
  /// In en, this message translates to:
  /// **'Polices ( Arabic )'**
  String get policesTitleArabic;

  /// No description provided for @detailsTitleEnglish.
  ///
  /// In en, this message translates to:
  /// **'Details ( English )'**
  String get detailsTitleEnglish;

  /// No description provided for @detailsTitleArabic.
  ///
  /// In en, this message translates to:
  /// **'Details ( Arabic )'**
  String get detailsTitleArabic;

  /// No description provided for @pending.
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// No description provided for @returnedCost.
  ///
  /// In en, this message translates to:
  /// **'Returned Cost'**
  String get returnedCost;

  /// No description provided for @sellerPayDeliveryCost.
  ///
  /// In en, this message translates to:
  /// **'The Seller pay the  Delivery cost.'**
  String get sellerPayDeliveryCost;

  /// No description provided for @customerPayDeliveryCost.
  ///
  /// In en, this message translates to:
  /// **' The Customer pay the Delivery cost.'**
  String get customerPayDeliveryCost;

  /// No description provided for @notFreeForCustomer.
  ///
  /// In en, this message translates to:
  /// **'Not free for customer'**
  String get notFreeForCustomer;

  /// No description provided for @productReturned.
  ///
  /// In en, this message translates to:
  /// **'Product Returned'**
  String get productReturned;

  /// No description provided for @requiredUpdateOnSpec.
  ///
  /// In en, this message translates to:
  /// **'Oops! seems like you have not updated the product return specifications yet and check other return details'**
  String get requiredUpdateOnSpec;

  /// No description provided for @aleartCompeleteEditSpec.
  ///
  /// In en, this message translates to:
  /// **'You have not completed the edit. Please finish the edit first.'**
  String get aleartCompeleteEditSpec;

  /// No description provided for @returnSpecHighlighted.
  ///
  /// In en, this message translates to:
  /// **'All highlighted specificatoin returned by admin'**
  String get returnSpecHighlighted;

  /// No description provided for @specTitleEnglish.
  ///
  /// In en, this message translates to:
  /// **'Title ( English )'**
  String get specTitleEnglish;

  /// No description provided for @specTitleArabic.
  ///
  /// In en, this message translates to:
  /// **'Title ( Arabic )'**
  String get specTitleArabic;

  /// No description provided for @specTitleFeildHint.
  ///
  /// In en, this message translates to:
  /// **'Screen Size'**
  String get specTitleFeildHint;

  /// No description provided for @specValueEnglish.
  ///
  /// In en, this message translates to:
  /// **'Value ( English )'**
  String get specValueEnglish;

  /// No description provided for @specValueArabic.
  ///
  /// In en, this message translates to:
  /// **'Value ( Arabic )'**
  String get specValueArabic;

  /// No description provided for @specValueFeildHint.
  ///
  /// In en, this message translates to:
  /// **'4.7'**
  String get specValueFeildHint;

  /// No description provided for @unit.
  ///
  /// In en, this message translates to:
  /// **'Unit'**
  String get unit;

  /// No description provided for @adminnRetuenSizeUnit.
  ///
  /// In en, this message translates to:
  /// **'Admin returned this size unit'**
  String get adminnRetuenSizeUnit;

  /// No description provided for @alertMessageForDelete.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete'**
  String get alertMessageForDelete;

  /// No description provided for @allHideAleartMessage.
  ///
  /// In en, this message translates to:
  /// **'You can\'t hide all values'**
  String get allHideAleartMessage;

  /// No description provided for @hideSizeAlertMessgae.
  ///
  /// In en, this message translates to:
  /// **'Hide size will not show this size to the customers'**
  String get hideSizeAlertMessgae;

  /// No description provided for @deleteSizeAlertMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this size'**
  String get deleteSizeAlertMessage;

  /// No description provided for @updateReturnSizeAlert.
  ///
  /// In en, this message translates to:
  /// **'Oops! you havn\'t update Size Unit'**
  String get updateReturnSizeAlert;

  /// No description provided for @waitVariationLoading.
  ///
  /// In en, this message translates to:
  /// **'Wait product variation has been loading'**
  String get waitVariationLoading;

  /// No description provided for @hide.
  ///
  /// In en, this message translates to:
  /// **'Hide'**
  String get hide;

  /// No description provided for @show.
  ///
  /// In en, this message translates to:
  /// **'Show'**
  String get show;

  /// No description provided for @gtin.
  ///
  /// In en, this message translates to:
  /// **'GTIN'**
  String get gtin;

  /// No description provided for @productOptions.
  ///
  /// In en, this message translates to:
  /// **'Product Options'**
  String get productOptions;

  /// No description provided for @pleaseUploadImage.
  ///
  /// In en, this message translates to:
  /// **'Please upload images'**
  String get pleaseUploadImage;

  /// No description provided for @aleartMessageOnHideVariant.
  ///
  /// In en, this message translates to:
  /// **'Hide variant will not show this variation to the customers'**
  String get aleartMessageOnHideVariant;

  /// No description provided for @colorName.
  ///
  /// In en, this message translates to:
  /// **'Color Name'**
  String get colorName;

  /// No description provided for @hideAllVariantAlertMessage.
  ///
  /// In en, this message translates to:
  /// **'To hide all product variants, you cannot simply hide the product. Instead, you must either set the quantity of all variants to 0 or manage their visibility individually.'**
  String get hideAllVariantAlertMessage;

  /// No description provided for @youCanNotHideAllColor.
  ///
  /// In en, this message translates to:
  /// **'You can\'t hide all colors'**
  String get youCanNotHideAllColor;

  /// No description provided for @hideColorNotVisible.
  ///
  /// In en, this message translates to:
  /// **'Hide color will not show this color to the customers'**
  String get hideColorNotVisible;

  /// No description provided for @haventUpdateColor.
  ///
  /// In en, this message translates to:
  /// **'Oops! you havn\'t update colors'**
  String get haventUpdateColor;

  /// No description provided for @colorFamily.
  ///
  /// In en, this message translates to:
  /// **'color family'**
  String get colorFamily;

  /// No description provided for @registration.
  ///
  /// In en, this message translates to:
  /// **'Registration'**
  String get registration;

  /// No description provided for @returnedByAdmin.
  ///
  /// In en, this message translates to:
  /// **'Returned by the admin'**
  String get returnedByAdmin;

  /// No description provided for @submittedByAdmin.
  ///
  /// In en, this message translates to:
  /// **'Submitted by the supplier'**
  String get submittedByAdmin;

  /// No description provided for @acceptedByAdmin.
  ///
  /// In en, this message translates to:
  /// **'Accepted by the admin'**
  String get acceptedByAdmin;

  /// No description provided for @whatsappNotInstalled.
  ///
  /// In en, this message translates to:
  /// **'WhatsApp is not installed on the device'**
  String get whatsappNotInstalled;

  /// No description provided for @contactUs.
  ///
  /// In en, this message translates to:
  /// **'CONTACT US'**
  String get contactUs;

  /// No description provided for @fixProblemOrContactUs.
  ///
  /// In en, this message translates to:
  /// **'You can quickly fix your problem here or you can '**
  String get fixProblemOrContactUs;

  /// No description provided for @updateName.
  ///
  /// In en, this message translates to:
  /// **'Update Name'**
  String get updateName;

  /// No description provided for @userNameInUse.
  ///
  /// In en, this message translates to:
  /// **'Username in Use: Choose another. This one\'s taken'**
  String get userNameInUse;

  /// No description provided for @pleaseEnterDigitalCode.
  ///
  /// In en, this message translates to:
  /// **'Please enter the digital code that we have sent'**
  String get pleaseEnterDigitalCode;

  /// No description provided for @emailClaimed.
  ///
  /// In en, this message translates to:
  /// **'Oops! Email\'s claimed. Pick a new one!'**
  String get emailClaimed;

  /// No description provided for @pleaseEnterCompeleteCode.
  ///
  /// In en, this message translates to:
  /// **'Please enter complete code'**
  String get pleaseEnterCompeleteCode;

  /// No description provided for @mobileNumberChanged.
  ///
  /// In en, this message translates to:
  /// **'Mobile number has been changed successfully'**
  String get mobileNumberChanged;

  /// No description provided for @passwordCharaterLimit.
  ///
  /// In en, this message translates to:
  /// **'The password must be at least 6 characters'**
  String get passwordCharaterLimit;

  /// No description provided for @passwordFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex: 123456'**
  String get passwordFeildHint;

  /// No description provided for @newPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// No description provided for @confirmPass.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPass;

  /// No description provided for @currentPassword.
  ///
  /// In en, this message translates to:
  /// **'Current Password'**
  String get currentPassword;

  /// No description provided for @updateBussinessInfoAlert.
  ///
  /// In en, this message translates to:
  /// **'Updating business info will hide your shop untill the admin accept the new changes, Are you sure you want to continue?'**
  String get updateBussinessInfoAlert;

  /// No description provided for @bussinessNameFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : Aroundix'**
  String get bussinessNameFeildHint;

  /// No description provided for @bussinessNameFeildLabel.
  ///
  /// In en, this message translates to:
  /// **'Business Name'**
  String get bussinessNameFeildLabel;

  /// No description provided for @bussinessNameFeildReturnMessage.
  ///
  /// In en, this message translates to:
  /// **'Business Name Returned From Admin'**
  String get bussinessNameFeildReturnMessage;

  /// No description provided for @bussinessOwnerNameFeildLabel.
  ///
  /// In en, this message translates to:
  /// **'Owner/Manager Name'**
  String get bussinessOwnerNameFeildLabel;

  /// No description provided for @bussinessOwnerNameFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : Khalid Moh'**
  String get bussinessOwnerNameFeildHint;

  /// No description provided for @bussinessOwnerNameFeildHintReturnMessae.
  ///
  /// In en, this message translates to:
  /// **'Owner/ Manager Name Returned From Admin'**
  String get bussinessOwnerNameFeildHintReturnMessae;

  /// No description provided for @pleaseUploadColorImage.
  ///
  /// In en, this message translates to:
  /// **'Please upload color images'**
  String get pleaseUploadColorImage;

  /// No description provided for @addNewColorName.
  ///
  /// In en, this message translates to:
  /// **'Add new color name for this color family'**
  String get addNewColorName;

  /// No description provided for @pleaseSelectColorFamily.
  ///
  /// In en, this message translates to:
  /// **'Please select color family'**
  String get pleaseSelectColorFamily;

  /// No description provided for @colorNameAlreadyExist.
  ///
  /// In en, this message translates to:
  /// **'Color name already exist for this color family'**
  String get colorNameAlreadyExist;

  /// No description provided for @alertMessageDeleteOption.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this option'**
  String get alertMessageDeleteOption;

  /// No description provided for @customOptionFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex: memory size'**
  String get customOptionFeildHint;

  /// No description provided for @customOptionFeildLabelEnglish.
  ///
  /// In en, this message translates to:
  /// **'Title ( English )'**
  String get customOptionFeildLabelEnglish;

  /// No description provided for @customOptionFeildLabelArabic.
  ///
  /// In en, this message translates to:
  /// **'Title ( Arabic )'**
  String get customOptionFeildLabelArabic;

  /// No description provided for @customOptionFeildErrorValidTitle.
  ///
  /// In en, this message translates to:
  /// **'Please Enter A Valid Title'**
  String get customOptionFeildErrorValidTitle;

  /// No description provided for @customOptionFeildTitleAlreadyExsist.
  ///
  /// In en, this message translates to:
  /// **'Option Title Already In List'**
  String get customOptionFeildTitleAlreadyExsist;

  /// No description provided for @customOptionAdminReturnTitle.
  ///
  /// In en, this message translates to:
  /// **'Admin Has Returned This Title'**
  String get customOptionAdminReturnTitle;

  /// No description provided for @addNewOption.
  ///
  /// In en, this message translates to:
  /// **'Add new Option'**
  String get addNewOption;

  /// No description provided for @youHaveAlreadyAddedThisOption.
  ///
  /// In en, this message translates to:
  /// **'You have already added this option title to your product option.'**
  String get youHaveAlreadyAddedThisOption;

  /// No description provided for @errorAtleastAddtwoValues.
  ///
  /// In en, this message translates to:
  /// **'Please Enter At least Two Values '**
  String get errorAtleastAddtwoValues;

  /// No description provided for @errorAtleastAddtwoValue.
  ///
  /// In en, this message translates to:
  /// **'Please Enter At least Two Value'**
  String get errorAtleastAddtwoValue;

  /// No description provided for @somethingWrongOnOption.
  ///
  /// In en, this message translates to:
  /// **'Something wrong on this option'**
  String get somethingWrongOnOption;

  /// No description provided for @updateThisOptionFirst.
  ///
  /// In en, this message translates to:
  /// **'Update this first then edit other\'s'**
  String get updateThisOptionFirst;

  /// No description provided for @hideOptionAlertMessgae.
  ///
  /// In en, this message translates to:
  /// **'Hide option will not show this option to the customers'**
  String get hideOptionAlertMessgae;

  /// No description provided for @valueAlreadyInList.
  ///
  /// In en, this message translates to:
  /// **'Value Already In List'**
  String get valueAlreadyInList;

  /// No description provided for @customOptionValueFeildLabelEnglish.
  ///
  /// In en, this message translates to:
  /// **'Value ( English )'**
  String get customOptionValueFeildLabelEnglish;

  /// No description provided for @customOptionValueFeildLabelArabic.
  ///
  /// In en, this message translates to:
  /// **'Value ( Arabic )'**
  String get customOptionValueFeildLabelArabic;

  /// No description provided for @noSalesFound.
  ///
  /// In en, this message translates to:
  /// **'No Sales infos was found'**
  String get noSalesFound;

  /// No description provided for @shipmentCode.
  ///
  /// In en, this message translates to:
  /// **'Shipment Code'**
  String get shipmentCode;

  /// No description provided for @transfer.
  ///
  /// In en, this message translates to:
  /// **'Transfer'**
  String get transfer;

  /// No description provided for @amountLessThenAvailable.
  ///
  /// In en, this message translates to:
  /// **'Amount should be less or equal than the available amount'**
  String get amountLessThenAvailable;

  /// No description provided for @pleaseEnterAmount.
  ///
  /// In en, this message translates to:
  /// **'Please enter amount'**
  String get pleaseEnterAmount;

  /// No description provided for @available.
  ///
  /// In en, this message translates to:
  /// **'Available'**
  String get available;

  /// No description provided for @deliveryFee.
  ///
  /// In en, this message translates to:
  /// **'Delivery Fee'**
  String get deliveryFee;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @mapLocation.
  ///
  /// In en, this message translates to:
  /// **'Map Location'**
  String get mapLocation;

  /// No description provided for @number.
  ///
  /// In en, this message translates to:
  /// **'Number'**
  String get number;

  /// No description provided for @landNumber.
  ///
  /// In en, this message translates to:
  /// **'Land Number'**
  String get landNumber;

  /// No description provided for @customerContact.
  ///
  /// In en, this message translates to:
  /// **'Customer Contact'**
  String get customerContact;

  /// No description provided for @contactDetailsInfo.
  ///
  /// In en, this message translates to:
  /// **'Contact details will be shared with customers after placing orders '**
  String get contactDetailsInfo;

  /// No description provided for @alertMessageDeleteOptionValue.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this option value'**
  String get alertMessageDeleteOptionValue;

  /// No description provided for @emailAddress.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddress;

  /// No description provided for @orderedIn.
  ///
  /// In en, this message translates to:
  /// **'Ordered in'**
  String get orderedIn;

  /// No description provided for @invoiceSummery.
  ///
  /// In en, this message translates to:
  /// **'Invoice Summery'**
  String get invoiceSummery;

  /// No description provided for @shipmentInfo.
  ///
  /// In en, this message translates to:
  /// **'Shipment Info'**
  String get shipmentInfo;

  /// No description provided for @variantFieldsUpdated.
  ///
  /// In en, this message translates to:
  /// **'Variant Fields Updated'**
  String get variantFieldsUpdated;

  /// No description provided for @alertVariantPriceQty.
  ///
  /// In en, this message translates to:
  /// **'Default price and QTY have been applied to the empty variations fields.'**
  String get alertVariantPriceQty;

  /// No description provided for @alertOnPriceReturn.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this price'**
  String get alertOnPriceReturn;

  /// No description provided for @alertOnQtyReturn.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this product quantity'**
  String get alertOnQtyReturn;

  /// No description provided for @applyToAllVariant.
  ///
  /// In en, this message translates to:
  /// **'Apply To All Variations'**
  String get applyToAllVariant;

  /// No description provided for @alertMessageUpdateCCustomOption.
  ///
  /// In en, this message translates to:
  /// **'Oops! you havn\'t update custom options'**
  String get alertMessageUpdateCCustomOption;

  /// No description provided for @returnCost.
  ///
  /// In en, this message translates to:
  /// **'Return Cost'**
  String get returnCost;

  /// No description provided for @youRemovedProductAcceptReturnPolicies.
  ///
  /// In en, this message translates to:
  /// **'You removed product accept return from Policies'**
  String get youRemovedProductAcceptReturnPolicies;

  /// No description provided for @productAcceptReturnNotProvided.
  ///
  /// In en, this message translates to:
  /// **'Product Accept Return Not Provided'**
  String get productAcceptReturnNotProvided;

  /// No description provided for @youRemovedProductFreeDeliveryPolicies.
  ///
  /// In en, this message translates to:
  /// **'You removed product for free delivery from Policies'**
  String get youRemovedProductFreeDeliveryPolicies;

  /// No description provided for @productForFreeDeliveryNotProvided.
  ///
  /// In en, this message translates to:
  /// **'Product For Free Delivery Not Provided'**
  String get productForFreeDeliveryNotProvided;

  /// No description provided for @errorCacheValueReturnDuration.
  ///
  /// In en, this message translates to:
  /// **'You removed product return duration from policies'**
  String get errorCacheValueReturnDuration;

  /// No description provided for @errorServerValueReturnDuration.
  ///
  /// In en, this message translates to:
  /// **'Product Return Duration Not Provided'**
  String get errorServerValueReturnDuration;

  /// No description provided for @errorCacheValueWarrantyDuration.
  ///
  /// In en, this message translates to:
  /// **'You removed product warranty duration from policies'**
  String get errorCacheValueWarrantyDuration;

  /// No description provided for @errorServerValueWarrantyDuration.
  ///
  /// In en, this message translates to:
  /// **'Product Warranty Duration Not Provided'**
  String get errorServerValueWarrantyDuration;

  /// No description provided for @errorCacheValuePolicies.
  ///
  /// In en, this message translates to:
  /// **'You removed product policy from policies'**
  String get errorCacheValuePolicies;

  /// No description provided for @errorServerValuePolicies.
  ///
  /// In en, this message translates to:
  /// **'Product Policy Not Provided'**
  String get errorServerValuePolicies;

  /// No description provided for @productPrice.
  ///
  /// In en, this message translates to:
  /// **'Product Price'**
  String get productPrice;

  /// No description provided for @errorCacheValuePrice.
  ///
  /// In en, this message translates to:
  /// **'You removed product price from detail'**
  String get errorCacheValuePrice;

  /// No description provided for @errorServerValuePrice.
  ///
  /// In en, this message translates to:
  /// **'Product Price Not Provided'**
  String get errorServerValuePrice;

  /// No description provided for @errorCacheValueQty.
  ///
  /// In en, this message translates to:
  /// **'You removed product qty from detail'**
  String get errorCacheValueQty;

  /// No description provided for @errorServerValueQty.
  ///
  /// In en, this message translates to:
  /// **'Product QTY Not Provided'**
  String get errorServerValueQty;

  /// No description provided for @errorCacheValueMinQty1.
  ///
  /// In en, this message translates to:
  /// **'You turned off minimum qty alert'**
  String get errorCacheValueMinQty1;

  /// No description provided for @errorCacheValueMinQty.
  ///
  /// In en, this message translates to:
  /// **'You removed product minimum qty from detail'**
  String get errorCacheValueMinQty;

  /// No description provided for @errorServerValueMinQty.
  ///
  /// In en, this message translates to:
  /// **'Product Minimum QTY Not Provided'**
  String get errorServerValueMinQty;

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// No description provided for @selectLanguageDetail.
  ///
  /// In en, this message translates to:
  /// **'Want to change the app language? You can switch between English and Arabic for a better experience.'**
  String get selectLanguageDetail;

  /// No description provided for @agreeTermsAndCondition.
  ///
  /// In en, this message translates to:
  /// **'By creating an account, you agree to Elbaab '**
  String get agreeTermsAndCondition;

  /// No description provided for @phoneNumberAlreadyInuse.
  ///
  /// In en, this message translates to:
  /// **'Phone number already in use'**
  String get phoneNumberAlreadyInuse;

  /// No description provided for @signupSuccessMessage2.
  ///
  /// In en, this message translates to:
  /// **'Thank you for choosing our app'**
  String get signupSuccessMessage2;

  /// No description provided for @signupSuccessMessage1.
  ///
  /// In en, this message translates to:
  /// **'Your request is now under review. please check your email for the status '**
  String get signupSuccessMessage1;

  /// No description provided for @verifyEmailAddress.
  ///
  /// In en, this message translates to:
  /// **'Verify Email Address'**
  String get verifyEmailAddress;

  /// No description provided for @resetYourPassword.
  ///
  /// In en, this message translates to:
  /// **'reset your password'**
  String get resetYourPassword;

  /// No description provided for @verifyYourEmail.
  ///
  /// In en, this message translates to:
  /// **'verify your email'**
  String get verifyYourEmail;

  /// No description provided for @to.
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get to;

  /// No description provided for @weSentOtp.
  ///
  /// In en, this message translates to:
  /// **'we have sent a One Time Password (OTP) to your email'**
  String get weSentOtp;

  /// No description provided for @mobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Mobile number'**
  String get mobileNumber;

  /// No description provided for @aleartFillSpecFeild.
  ///
  /// In en, this message translates to:
  /// **'Please wait while we translate'**
  String get aleartFillSpecFeild;

  /// No description provided for @aleartOnRemoveSpec.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove this specification'**
  String get aleartOnRemoveSpec;

  /// No description provided for @readySpecTranslation.
  ///
  /// In en, this message translates to:
  /// **'Translation is ready to add'**
  String get readySpecTranslation;

  /// No description provided for @colorNameEnglish.
  ///
  /// In en, this message translates to:
  /// **'Color Name ( English )'**
  String get colorNameEnglish;

  /// No description provided for @colorNameArabic.
  ///
  /// In en, this message translates to:
  /// **'Color Name ( Arabic )'**
  String get colorNameArabic;

  /// No description provided for @aleartRememberBe.
  ///
  /// In en, this message translates to:
  /// **'Enter your credentials then login to remember your details for next time'**
  String get aleartRememberBe;

  /// No description provided for @updateTradeCertiificateDate.
  ///
  /// In en, this message translates to:
  /// **'Update Trade Certificate Date'**
  String get updateTradeCertiificateDate;

  /// No description provided for @tradeCerficateExpireDate.
  ///
  /// In en, this message translates to:
  /// **'Trade Certificate Expiry Date'**
  String get tradeCerficateExpireDate;

  /// No description provided for @uploadTradeCertificate.
  ///
  /// In en, this message translates to:
  /// **'Upload Trade Certificate'**
  String get uploadTradeCertificate;

  /// No description provided for @tradeLicenseReturned.
  ///
  /// In en, this message translates to:
  /// **'Trade License Returned'**
  String get tradeLicenseReturned;

  /// No description provided for @uploadEmiratesID.
  ///
  /// In en, this message translates to:
  /// **'Upload Emirates ID'**
  String get uploadEmiratesID;

  /// No description provided for @ownerIDReturned.
  ///
  /// In en, this message translates to:
  /// **'Owner ID Returned'**
  String get ownerIDReturned;

  /// No description provided for @adminNote.
  ///
  /// In en, this message translates to:
  /// **'Admin Note:'**
  String get adminNote;

  /// No description provided for @updateEmiratesIDDate.
  ///
  /// In en, this message translates to:
  /// **'Update Emirates ID Date'**
  String get updateEmiratesIDDate;

  /// No description provided for @emiratesIDExpiryDate.
  ///
  /// In en, this message translates to:
  /// **'Emirates ID Expiry Date'**
  String get emiratesIDExpiryDate;

  /// No description provided for @aleartUploadTradeCertificate.
  ///
  /// In en, this message translates to:
  /// **'Please update your Trade License'**
  String get aleartUploadTradeCertificate;

  /// No description provided for @aleartUploadId.
  ///
  /// In en, this message translates to:
  /// **'Please update your Emirates ID'**
  String get aleartUploadId;

  /// No description provided for @updateShop.
  ///
  /// In en, this message translates to:
  /// **'Update Shop'**
  String get updateShop;

  /// No description provided for @aleartOnStopShopEdit.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to stop editing'**
  String get aleartOnStopShopEdit;

  /// No description provided for @rejectTargetPrice.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this target price'**
  String get rejectTargetPrice;

  /// No description provided for @uploadTermsAndCondiition.
  ///
  /// In en, this message translates to:
  /// **'Upload Terms & Conditions Doc'**
  String get uploadTermsAndCondiition;

  /// No description provided for @businessSloganFeildLabel.
  ///
  /// In en, this message translates to:
  /// **'Business Slogan'**
  String get businessSloganFeildLabel;

  /// No description provided for @businessSloganFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex :Find every thing here'**
  String get businessSloganFeildHint;

  /// No description provided for @businessSloganFeildAdminReject.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this slogan'**
  String get businessSloganFeildAdminReject;

  /// No description provided for @businessDiscriiptionFeildAdminReject.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this description'**
  String get businessDiscriiptionFeildAdminReject;

  /// No description provided for @addBranchNumber.
  ///
  /// In en, this message translates to:
  /// **'Add Branch Number'**
  String get addBranchNumber;

  /// No description provided for @verificationCodeSent.
  ///
  /// In en, this message translates to:
  /// **'We have sent the code verfication to'**
  String get verificationCodeSent;

  /// No description provided for @branchPhoneNumberOtp.
  ///
  /// In en, this message translates to:
  /// **'Add your phone number. we will send you a verification code '**
  String get branchPhoneNumberOtp;

  /// No description provided for @numberAlreadyUse.
  ///
  /// In en, this message translates to:
  /// **'Number already in use on other branch'**
  String get numberAlreadyUse;

  /// No description provided for @changePhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Change Phone Number?'**
  String get changePhoneNumber;

  /// No description provided for @waitFechingCities.
  ///
  /// In en, this message translates to:
  /// **'Wait we are fetching cities'**
  String get waitFechingCities;

  /// No description provided for @pickupCity.
  ///
  /// In en, this message translates to:
  /// **'Pickup City'**
  String get pickupCity;

  /// No description provided for @adminReturned.
  ///
  /// In en, this message translates to:
  /// **'Admin returned'**
  String get adminReturned;

  /// No description provided for @pickupLocation.
  ///
  /// In en, this message translates to:
  /// **'Pickup Location'**
  String get pickupLocation;

  /// No description provided for @pleaseAddLocation.
  ///
  /// In en, this message translates to:
  /// **'Please add location of pickup address'**
  String get pleaseAddLocation;

  /// No description provided for @landNumberFeildlabel.
  ///
  /// In en, this message translates to:
  /// **'Land number'**
  String get landNumberFeildlabel;

  /// No description provided for @landNumberFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : 05xxxxxxxx'**
  String get landNumberFeildHint;

  /// No description provided for @landNumberFeildAdminReject.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this land number'**
  String get landNumberFeildAdminReject;

  /// No description provided for @contactFeildlabel.
  ///
  /// In en, this message translates to:
  /// **'Phone number'**
  String get contactFeildlabel;

  /// No description provided for @contactFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : 05xxxxxxxx'**
  String get contactFeildHint;

  /// No description provided for @pickupContactNumber.
  ///
  /// In en, this message translates to:
  /// **'Pickup Contact number'**
  String get pickupContactNumber;

  /// No description provided for @verified.
  ///
  /// In en, this message translates to:
  /// **'Verified'**
  String get verified;

  /// No description provided for @pickupFeildlabel.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get pickupFeildlabel;

  /// No description provided for @pickupFeildHint.
  ///
  /// In en, this message translates to:
  /// **'ex : Shop A-23, Tower 03 Khalid Street'**
  String get pickupFeildHint;

  /// No description provided for @pickupFeildAdminReject.
  ///
  /// In en, this message translates to:
  /// **'Pickup Address'**
  String get pickupFeildAdminReject;

  /// No description provided for @setYourLocation.
  ///
  /// In en, this message translates to:
  /// **'Set Your Location'**
  String get setYourLocation;

  /// No description provided for @selectPinLocation.
  ///
  /// In en, this message translates to:
  /// **'Select Pin Location'**
  String get selectPinLocation;

  /// No description provided for @addAnotherAddress.
  ///
  /// In en, this message translates to:
  /// **'Add another pickup address'**
  String get addAnotherAddress;

  /// No description provided for @branchAlreadyAssign.
  ///
  /// In en, this message translates to:
  /// **'Branch already assign to some orders'**
  String get branchAlreadyAssign;

  /// No description provided for @aleartOnDeleteAddress.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this pickup adress'**
  String get aleartOnDeleteAddress;

  /// No description provided for @adminRejectPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this phone number'**
  String get adminRejectPhoneNumber;

  /// No description provided for @adminRejectWhatsapppNumber.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this whatsapp number'**
  String get adminRejectWhatsapppNumber;

  /// No description provided for @whatsappNumber.
  ///
  /// In en, this message translates to:
  /// **'Whatsapp Number'**
  String get whatsappNumber;

  /// No description provided for @adminRejectContactEmail.
  ///
  /// In en, this message translates to:
  /// **'Admin rejected this email addrress'**
  String get adminRejectContactEmail;

  /// No description provided for @discardAllChanges.
  ///
  /// In en, this message translates to:
  /// **'Discard All Changes'**
  String get discardAllChanges;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'Days'**
  String get days;

  /// No description provided for @selectCategoryFirst.
  ///
  /// In en, this message translates to:
  /// **'Please select category first'**
  String get selectCategoryFirst;

  /// No description provided for @errorAddTitleForCustomOption.
  ///
  /// In en, this message translates to:
  /// **'Please Enter Title Or Remove Empty Custom Option'**
  String get errorAddTitleForCustomOption;

  /// No description provided for @errorEmptyDetsilsOnAddOption.
  ///
  /// In en, this message translates to:
  /// **'Please Enter Title With At least Two Values Before Done'**
  String get errorEmptyDetsilsOnAddOption;

  /// No description provided for @accountSettings.
  ///
  /// In en, this message translates to:
  /// **'Account Settings'**
  String get accountSettings;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @dataManagement.
  ///
  /// In en, this message translates to:
  /// **'Data Management'**
  String get dataManagement;

  /// No description provided for @photos.
  ///
  /// In en, this message translates to:
  /// **'Photos'**
  String get photos;

  /// No description provided for @pickupLocations.
  ///
  /// In en, this message translates to:
  /// **'Pickup Locations'**
  String get pickupLocations;

  /// No description provided for @pleaseEnterBranchNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter branch number'**
  String get pleaseEnterBranchNumber;

  /// No description provided for @personalInformation.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get personalInformation;

  /// No description provided for @security.
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get security;

  /// No description provided for @noIncomeYet.
  ///
  /// In en, this message translates to:
  /// **'No Income Yet'**
  String get noIncomeYet;

  /// No description provided for @salesTrend.
  ///
  /// In en, this message translates to:
  /// **'Sales Trend'**
  String get salesTrend;

  /// No description provided for @helloSupplier.
  ///
  /// In en, this message translates to:
  /// **'Hello {storeName}!'**
  String helloSupplier(Object storeName);

  /// No description provided for @addedImages.
  ///
  /// In en, this message translates to:
  /// **'( {imagesLength} / 5 ) Photos'**
  String addedImages(Object imagesLength);

  /// No description provided for @dynamicPrice.
  ///
  /// In en, this message translates to:
  /// **'AED {price}'**
  String dynamicPrice(Object price);

  /// No description provided for @dynamicPieceCount.
  ///
  /// In en, this message translates to:
  /// **'Piece {count}'**
  String dynamicPieceCount(Object count);

  /// No description provided for @productTab.
  ///
  /// In en, this message translates to:
  /// **'Products ({count})'**
  String productTab(Object count);

  /// No description provided for @pendingTab.
  ///
  /// In en, this message translates to:
  /// **'Pending ({count})'**
  String pendingTab(Object count);

  /// No description provided for @draftTab.
  ///
  /// In en, this message translates to:
  /// **'Draft ({count})'**
  String draftTab(Object count);

  /// No description provided for @qtyAlertTab.
  ///
  /// In en, this message translates to:
  /// **'Qty Alert ({count})'**
  String qtyAlertTab(Object count);

  /// No description provided for @outOfStocTab.
  ///
  /// In en, this message translates to:
  /// **'Out of Stock ({count})'**
  String outOfStocTab(Object count);

  /// No description provided for @variantsTab.
  ///
  /// In en, this message translates to:
  /// **'Variants ({count})'**
  String variantsTab(Object count);

  /// No description provided for @followersCount.
  ///
  /// In en, this message translates to:
  /// **'Followers {count}'**
  String followersCount(Object count);

  /// No description provided for @productReturnMessage.
  ///
  /// In en, this message translates to:
  /// **'Admin Notes: {returnMessage}\nCheck the returned feilds'**
  String productReturnMessage(Object returnMessage);

  /// No description provided for @variantSize.
  ///
  /// In en, this message translates to:
  /// **'Size ( {variantSizeUnit} ) : '**
  String variantSize(Object variantSizeUnit);

  /// No description provided for @adminReturn.
  ///
  /// In en, this message translates to:
  /// **'Admin Return ( {returnValue} ) : '**
  String adminReturn(Object returnValue);

  /// No description provided for @pickUpCity.
  ///
  /// In en, this message translates to:
  /// **'Emirate {pickUpCity} '**
  String pickUpCity(Object pickUpCity);

  /// No description provided for @emailChangeSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Your Email has been Updated successfully to: {email}'**
  String emailChangeSuccessfully(Object email);

  /// No description provided for @pendingOrderTab.
  ///
  /// In en, this message translates to:
  /// **'Pending {count} '**
  String pendingOrderTab(Object count);

  /// No description provided for @inProgressOrderTab.
  ///
  /// In en, this message translates to:
  /// **'InProgress {count} '**
  String inProgressOrderTab(Object count);

  /// No description provided for @historyOrderTab.
  ///
  /// In en, this message translates to:
  /// **'History {count} '**
  String historyOrderTab(Object count);

  /// No description provided for @newOrderTab.
  ///
  /// In en, this message translates to:
  /// **'New {count} '**
  String newOrderTab(Object count);

  /// No description provided for @newReurnOrderTab.
  ///
  /// In en, this message translates to:
  /// **'Return {count} '**
  String newReurnOrderTab(Object count);

  /// No description provided for @waitingOrderTab.
  ///
  /// In en, this message translates to:
  /// **'Waiting Pickup {count} '**
  String waitingOrderTab(Object count);

  /// No description provided for @shippedOrderTab.
  ///
  /// In en, this message translates to:
  /// **'Shipped Orders {count} '**
  String shippedOrderTab(Object count);

  /// No description provided for @deliverdOrderTab.
  ///
  /// In en, this message translates to:
  /// **'Delivered {count} '**
  String deliverdOrderTab(Object count);

  /// No description provided for @cancelledOrderTab.
  ///
  /// In en, this message translates to:
  /// **'Cancelled {count} '**
  String cancelledOrderTab(Object count);

  /// No description provided for @returnedOrderTab.
  ///
  /// In en, this message translates to:
  /// **'Returned {count} '**
  String returnedOrderTab(Object count);

  /// No description provided for @secondRemaing.
  ///
  /// In en, this message translates to:
  /// **'Seconds Remaing To Resend Otp {count} '**
  String secondRemaing(Object count);

  /// No description provided for @removingCityBranch.
  ///
  /// In en, this message translates to:
  /// **'Removing {city} city branch'**
  String removingCityBranch(Object city);
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar': return AppLocalizationsAr();
    case 'en': return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
