import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/alert_messages_constants.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/model/signupModel.dart';
import 'package:overolasuppliers/screens/autentications/login_widgets/custom_clippers/blue_top_clipper.dart';
import 'package:overolasuppliers/screens/autentications/login_widgets/custom_clippers/grey_top_clipper.dart';
import 'package:overolasuppliers/screens/autentications/login_widgets/custom_clippers/white_top_clipper.dart';
import 'package:overolasuppliers/screens/autentications/login_widgets/fade_slide_transition.dart';
import 'package:overolasuppliers/screens/autentications/login_widgets/header.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';
import 'package:overolasuppliers/widgets/elbaab_password_textfield.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SignUp extends StatefulWidget {
  const SignUp({Key? key}) : super(key: key);

  @override
  State<SignUp> createState() => _SignUpState();
}

class _SignUpState extends State<SignUp>
    with InputValidationUtil, SingleTickerProviderStateMixin
    implements ServerResponse {
  late final AnimationController _animationController;
  late final Animation<double> _headerTextAnimation;
  late final Animation<double> _formElementAnimation;
  late final Animation<double> _whiteTopClipperAnimation;
  late final Animation<double> _blueTopClipperAnimation;
  late final Animation<double> _greyTopClipperAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: kLoginAnimationDuration,
    );

    final fadeSlideTween = Tween<double>(begin: 0.0, end: 1.0);
    _headerTextAnimation = fadeSlideTween.animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(
        0.0,
        0.6,
        curve: Curves.easeInOut,
      ),
    ));
    _formElementAnimation = fadeSlideTween.animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(
        0.7,
        1.0,
        curve: Curves.easeInOut,
      ),
    ));

    final clipperOffsetTween = Tween<double>(
      begin: Get.height,
      end: 0.0,
    );
    _blueTopClipperAnimation = clipperOffsetTween.animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(
          0.2,
          0.7,
          curve: Curves.easeInOut,
        ),
      ),
    );
    _greyTopClipperAnimation = clipperOffsetTween.animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(
          0.35,
          0.7,
          curve: Curves.easeInOut,
        ),
      ),
    );
    _whiteTopClipperAnimation = clipperOffsetTween.animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(
          0.5,
          0.7,
          curve: Curves.easeInOut,
        ),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  String strName = '',
      strEmail = '',
      strRePassword = '',
      strPassword = '',
      termsAndConditions = '';

  RxBool isAcceptTerms = RxBool(false);

  RxString strError = ''.obs;

  ValueNotifier<bool> password = ValueNotifier(true);

  ValueNotifier<bool> repassword = ValueNotifier(true);

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late GraphQlInitilize _request;

  _getTermsAndConditions(BuildContext context) {
    _request.runQueryWithCache(
      context: context,
      query: GraphQlQuries.getTermsAndConditions,
      type: "TermsAndConditions",
    );
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    _request = GraphQlInitilize(this);
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        Future.delayed(const Duration(milliseconds: 1500),
            () => Get.offAllNamed(RouteNames.loginScreen));
        _animationController.reverse();
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: Stack(
          alignment: Alignment.topCenter,
          children: <Widget>[
            AnimatedBuilder(
              animation: _whiteTopClipperAnimation,
              builder: (_, Widget? child) {
                return ClipPath(
                  clipper: WhiteTopClipper(
                    yOffset: _whiteTopClipperAnimation.value,
                  ),
                  child: child,
                );
              },
              child: Container(color: Colors.white),
            ),
            AnimatedBuilder(
              animation: _greyTopClipperAnimation,
              builder: (_, Widget? child) {
                return ClipPath(
                  clipper: GreyTopClipper(
                    yOffset: _greyTopClipperAnimation.value,
                  ),
                  child: child,
                );
              },
              child: Container(color: AppColors.colorPrimary),
            ),
            AnimatedBuilder(
              animation: _blueTopClipperAnimation,
              builder: (_, Widget? child) {
                return ClipPath(
                  clipper: BlueTopClipper(
                    yOffset: _blueTopClipperAnimation.value,
                  ),
                  child: child,
                );
              },
              child: Container(
                color: AppColors.headerColorDark,
              ),
            ),
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: kPaddingL),
                child: Header(animation: _headerTextAnimation, isSignup: true),
              ),
            ),
            SafeArea(
              child: FadeSlideTransition(
                animation: _formElementAnimation,
                additionalOffset: 0.0,
                child: Container(
                  margin: const EdgeInsets.only(top: 280),
                  child: SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: kLeftSpace, right: kRightSpace),
                      child: Form(
                        key: _formKey,
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              ElbaaabInputTextField(
                                  onChanged: (v) => strName = v,
                                  hint: appLocal.nameHint,
                                  label: appLocal.yourName,
                                  autoTextDirection: true,
                                  validator: validateFieldEmpty,
                                  inputFormatter: '[a-zA-Zا-ي ]',
                                  inputType: TextInputType.name),
                              ElbaaabInputTextField(
                                  margin: const EdgeInsets.only(top: 16),
                                  onChanged: (v) => strEmail = v,
                                  hint: appLocal.emailHint,
                                  autoTextDirection: true,
                                  label: appLocal.emailfeildLabel,
                                  validator: validateEmail,
                                  inputType: TextInputType.emailAddress),
                              ValueListenableBuilder(
                                valueListenable: password,
                                builder: (context, value, child) {
                                  return ElbaabPasswordTextField(
                                      edgeInsets:
                                          const EdgeInsets.only(top: 16),
                                      onChanged: (v) => strPassword = v,
                                      hint: appLocal.passwordHint,
                                      label: appLocal.passwordFeildLabel,
                                      validator: validatePassword,
                                      obscure: value as bool,
                                      obscureState: () {
                                        password.value
                                            ? password.value = false
                                            : password.value = true;
                                      });
                                },
                              ),
                              ValueListenableBuilder(
                                valueListenable: repassword,
                                builder: (context, value, child) {
                                  return ElbaabPasswordTextField(
                                    edgeInsets: const EdgeInsets.only(top: 16),
                                    onChanged: (v) => strRePassword = v,
                                    hint: appLocal.passwordHint,
                                    validator: (v) => validatePassword(v,
                                        previousPassword: strPassword),
                                    label: appLocal.reEnterPassword,
                                    obscure: value as bool,
                                    obscureState: () => repassword.value
                                        ? repassword.value = false
                                        : repassword.value = true,
                                  );
                                },
                              ),
                              const SizedBox(height: 24),
                              InkWell(
                                onTap: () {
                                  if (termsAndConditions.isEmpty) {
                                    _getTermsAndConditions(context);
                                  } else {
                                    htmlViewer(context);
                                  }
                                },
                                child: Row(
                                  children: [
                                    Obx(
                                      () => Checkbox(
                                        value: isAcceptTerms.value,
                                        onChanged: (v) =>
                                            isAcceptTerms.value = v as bool,
                                        activeColor: AppColors.colorPrimary,
                                      ),
                                    ),
                                    Expanded(
                                      child: RichText(
                                        text: TextSpan(
                                            style: FontStyles.fontRegular(),
                                            children: <TextSpan>[
                                               TextSpan(
                                                text:appLocal.agreeTermsAndCondition,
                                              ),
                                              TextSpan(
                                                text: appLocal.termsAndCondition,
                                                style: TextStyle(
                                                  color: AppColors.colorPrimary,
                                                ),
                                              ),
                                            ]),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              ElbaabNetworkEroor(strError: strError),
                              ElbaabGradientButtonWidget(
                                edgeInsets: const EdgeInsets.all(16),
                                onPress: () async {
                                  if (_formKey.currentState!.validate()) {
                                    if (isAcceptTerms.value == false) {
                                      strError.value = (AlertMessagesConstant
                                          .pleaseAcceptTermsandConditions);
                                    } else {
                                      _request.runMutation(
                                          context: context,
                                          query: GraphQlQuries.register,
                                          variables: GraphQlVariables.signup(
                                              lang: Get.locale?.languageCode ?? "en",
                                              name: strName,
                                              email: strEmail,
                                              password: strPassword));
                                    }
                                  }
                                },
                                text: appLocal.createYourAccount,
                              ),
                              SizedBox(
                                height: 90,
                                child: Center(
                                  child: Wrap(
                                    crossAxisAlignment:
                                        WrapCrossAlignment.center,
                                    children: [
                                      Text(
                                        appLocal.alreadyHaveAnAccount,
                                        style: FontStyles.fontRegular(),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          Future.delayed(
                                              const Duration(
                                                  milliseconds: 1500),
                                              () => Get.toNamed(
                                                  RouteNames.loginScreen));
                                          _animationController.reverse();
                                        },
                                        child: Text(
                                          appLocal.login,
                                          style: FontStyles.fontRegular(
                                              color: AppColors.colorPrimary),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            ]),
                      ),
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  onError(error, String type) {
    BaseModel model = BaseModel.fromJson(error);
    strError.value = (model.message ?? "");
  }

  @override
  onSucess(response, String type) async {
    if (type == "TermsAndConditions") {
      // strError.value = (response["message"]);
      if (response["status"] == statusOK) {
        if (response['termsAndConditions'] != null) {
          termsAndConditions =
              response['termsAndConditions']["termsAndConditionsText"];
          htmlViewer(MyApp.navigatorKey.currentContext!);
        }
      }
    } else {
      SignUpModel model = SignUpModel.fromJson(response);
      if (model.status == statusOK) {
        GlobalMethods.enableFutureLoginCheck(
            email: strEmail, password: strPassword);
        prefs.setBool(isListenNavidationStream, true);
        Get.toNamed(RouteNames.emailVerificationScreen,
            arguments: [model.user!.id, false, strEmail]);
      } else {
        strError.value = (model.message);
      }
    }
  }

  htmlViewer(context) {
    showModalBottomSheet(
      context: MyApp.navigatorKey.currentContext!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.only(top: 100),
          child: ColoredBox(
            color: AppColors.backgroundColorDark,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const SizedBox(height: 20),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(Icons.cancel),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: HtmlWidget(
                        termsAndConditions,
                        onErrorBuilder: (context, element, error) =>
                            Text('$element error: $error'),
                        onLoadingBuilder: (context, element, loadingProgress) =>
                            const CircularProgressIndicator(),
                        renderMode: RenderMode.column,
                        textStyle: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
