import 'dart:ui';

import 'package:camera/camera.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_it/get_it.dart';
import 'package:overolasuppliers/helper/branch.io/initilize_branch_io.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/dio/dio_client_network.dart';
import 'package:overolasuppliers/helper/notification/notification_service.dart';
import 'package:overolasuppliers/helper/routes/register_routes.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/themes/app_theme.dart';
import 'package:overolasuppliers/provider/initialize_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

late SharedPreferences prefs;
late CameraDescription firstCamera;

Future<void> main() async {
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ),
  );

  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp();

  await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };

  final cameras = await availableCameras();
  firstCamera = cameras.first;

  prefs = await SharedPreferences.getInstance();

  NotificationService.instance.initilizeFireBase();

  setupDepedencies();
  await serviceLocatorInstance<DioClientNetwork>().initializeDioClientNetwork();
  await serviceLocatorInstance<InitilizeBranchIO>().init();

  ErrorWidget.builder = (details) => ErrorWidget(details.exception);

  runApp(const MyApp());
}

final serviceLocatorInstance = GetIt.instance;
void setupDepedencies() {
  serviceLocatorInstance
      .registerSingleton<DioClientNetwork>(DioClientNetwork());
  serviceLocatorInstance
      .registerSingleton<InitilizeBranchIO>(InitilizeBranchIO());
}

class MyApp extends StatefulWidget {
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String local = "en";
  @override
  void initState() {
    super.initState();
    local = prefs.getString("languageCode") ?? "en";
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;

    Size designSize;
    if (screenWidth < 600) {
      designSize = const Size(360, 690);
    } else {
      designSize = const Size(768, 1280);
    }
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    return ScreenUtilInit(
        designSize: designSize,
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return MultiProvider(
            providers: providerList(context),
            child: GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              child: GetMaterialApp(
                locale: Locale(local),
                navigatorKey: MyApp.navigatorKey,
                builder: (context, child) => child!,
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                supportedLocales: const [Locale('en'), Locale('ar')],
                theme: AppTheme.dark,
                defaultTransition: Transition.native,
                debugShowCheckedModeBanner: false,
                getPages: allPages(),
                initialRoute: RouteNames.splashScreen,
              ),
            ),
          );
        });
  }
}
