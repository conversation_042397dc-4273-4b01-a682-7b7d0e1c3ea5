class SupportContactModel {
  final String? typename;
  final int? status;
  final String? message;
  final Support? support;

  SupportContactModel({
    this.typename,
    this.status,
    this.message,
    this.support,
  });

  SupportContactModel.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      status = json['status'] as int?,
      message = json['message'] as String?,
      support = (json['support'] as Map<String,dynamic>?) != null ? Support.fromJson(json['support'] as Map<String,dynamic>) : null;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'status' : status,
    'message' : message,
    'support' : support?.toJson()
  };
}

class Support {
  final String? typename;
  final String? whatsUp;
  final String? phone;
  final String? email;

  Support({
    this.typename,
    this.whatsUp,
    this.phone,
    this.email,
  });

  Support.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      whatsUp = json['whatsUp'] as String?,
      phone = json['phone'] as String?,
      email = json['email'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'whatsUp' : whatsUp,
    'phone' : phone,
    'email' : email
  };
}