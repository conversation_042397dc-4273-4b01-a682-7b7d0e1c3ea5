import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ElbaabCarouselFeildWidget extends StatelessWidget {
  final List<Widget> children;
  final double aspectRatio;
  const ElbaabCarouselFeildWidget(
      {super.key, required this.children, this.aspectRatio = 17 / 5});

  @override
  Widget build(BuildContext context) {
    return CarouselSlider(
        items: children,
        options: CarouselOptions(
          aspectRatio: aspectRatio,
          viewportFraction: 0.85,
          initialPage: AppLocalizations.of(context)?.localeName == "en" ? 0 : 1,
          enableInfiniteScroll: false,
          autoPlayCurve: Curves.fastOutSlowIn,
          enlargeCenterPage: true,
          enlargeFactor: 0.3,
          scrollDirection: Axis.horizontal,
        ));
  }
}
