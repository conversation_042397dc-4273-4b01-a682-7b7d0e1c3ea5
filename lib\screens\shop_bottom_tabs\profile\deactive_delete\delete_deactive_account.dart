import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/provider/account_setting_info_provider.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';
import 'package:provider/provider.dart';

class DeleteAndDeactiveAccount extends StatelessWidget
    with InputValidationUtil
    implements ServerResponse {
  RxString headerText = 'Deactivate the account'.obs, strError = "".obs;
  RxInt viewIndex = 0.obs;
  String strPassword = "";
  late GraphQlInitilize _request;

  final GlobalKey<FormState> _formkey = GlobalKey<FormState>();
  bool isDeactive = false, isShopActive = false;

  DeleteAndDeactiveAccount({super.key});
  @override
  Widget build(BuildContext context) {
    _request = GraphQlInitilize(this);
    isShopActive = Provider.of<AccountSettingProvider>(context, listen: false)
            .getAccountSetting
            .accountSetting
            ?.isShopActive ??
        false;
    return Scaffold(
        appBar: ElbaabHeader(
          title: "",
          leadingBack: true,
          leadingSpace: 50,
          leadingWidget: Obx(
            () => Text(
              headerText.value,
              style: FontStyles.fontRegular(fontSize: 17),
            ),
          ),
        ),
        body: Form(
          key: _formkey,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Obx(
                () => viewIndex.value == 0 ? _selectType() : _verifyPassword()),
          ),
        ));
  }

  Widget _selectType() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "How can we help you?",
          style: FontStyles.fontRegular(
            color: Colors.white.withOpacity(0.85),
          ),
        ),
        const SizedBox(height: 40),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: ElbaabButtonWidget(
                onPress: () {
                  isDeactive = false;
                  viewIndex.value = 1;
                  headerText.value = "Delete account";
                },
                colors: AppColors.colorPrimary_40,
                text: "Delete account",
                textStyle: FontStyles.fontRegular(),
                height: 40,
              ),
            ),
            const SizedBox(width: 13),
            Expanded(
              flex: 1,
              child: ElbaabButtonWidget(
                onPress: () {
                  isDeactive = true;
                  headerText.value = isShopActive
                      ? "Deactivate the account"
                      : "Activate the account";
                  viewIndex.value = 1;
                },
                colors: AppColors.colorPrimary,
                text: isShopActive ? "Deactivate account" : "Active Account",
                textStyle: FontStyles.fontRegular(),
                height: 40,
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _verifyPassword() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isDeactive
                ? isShopActive
                    ? "Deactivate account"
                    : "Activate account"
                : "Delete the account",
            style: FontStyles.fontRegular(
              color: Colors.white.withOpacity(0.85),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            "For account security, enter the password",
            style: FontStyles.fontRegular(
                color: Colors.white.withOpacity(0.59), fontSize: 12),
          ),
          const SizedBox(height: 26),
          ElbaaabInputTextField(
            onChanged: (v) => strPassword = v,
            hint: "******",
            validator: validatePassword,
            label: "Your password",
          ),
          const SizedBox(height: 20),
          ElbaabNetworkEroor(strError: strError),
          const SizedBox(height: 100),
          ElbaabButtonWidget(
            onPress: () {
              strError.value = '';
              if (_formkey.currentState!.validate()) {
                if (isDeactive) {
                  if (isShopActive) {
                    _request.runQuery(
                        context: Get.context!,
                        query: GraphQlQuries.desactivateAccount,
                        type: "desactivateAccount",
                        variables: GraphQlVariables.deleteAccount(
                            userPassword: strPassword));
                  } else {
                    _request.runQuery(
                        context: Get.context!,
                        query: GraphQlQuries.activateAccount,
                        type: "activateAccount",
                        variables: GraphQlVariables.deleteAccount(
                            userPassword: strPassword));
                  }
                } else {
                  _request.runMutation(
                      context: Get.context!,
                      query: GraphQlQuries.deleteAccount,
                      type: "deleteAccount",
                      variables: GraphQlVariables.deleteAccount(
                          userPassword: strPassword));
                }
              }
            },
            colors: AppColors.colorPrimary,
            text: EnStrings.verify,
            height: 40,
          ),
        ],
      ),
    );
  }

  @override
  onError(error, String type) {
    strError.value = BaseModel.fromJson(error).message ?? "";
  }

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (model.status == statusOK) {
      if (type == "deleteAccount") {
        prefs.remove(merchantID);
        prefs.remove(ownerName);
        prefs.remove(authToken);
        userAuthToken = "";
        supplierID = "";
        prefs.setString("information", "");
        prefs.setString("customerContact", "");
        prefs.setString("pickupAddress", "");
        Get.offAllNamed(RouteNames.loginScreen);
      } else {
        if (type == "desactivateAccount") {
          Provider.of<UpdatedInfo>(Get.context!, listen: false)
              .shopStatusUpdate("Deactive");
        } else {
          Provider.of<UpdatedInfo>(Get.context!, listen: false)
              .shopStatusUpdate("Accepted");
        }
        Get.back(result: true);
      }
    } else {
      strError.value = model.message ?? "";
    }
  }
}
