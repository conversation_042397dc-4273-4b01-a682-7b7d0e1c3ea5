import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';
import 'package:overolasuppliers/provider/order_status_provider.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/controller/orders_controller.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/orders_loader.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/orders_row/new_orders_row.dart';
import 'package:provider/provider.dart';

class NewOrders extends StatefulWidget {
  const NewOrders({Key? key}) : super(key: key);

  @override
  State<NewOrders> createState() => _NewOrdersState();
}

class _NewOrdersState extends State<NewOrders> implements ServerResponse {
  Rx<OrdersModel> ordersModel = OrdersModel().obs;

  late GraphQlInitilize _graphQlInitilize;

  RxList<OrderItems> newOrders = RxList<OrderItems>([]);
  final controller = Get.find<OrdersController>();

  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _graphQlInitilize = GraphQlInitilize(this);
    scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) => getOrders());
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      if (ordersModel.value.ordersPaggination?.hasNextPage ?? false) {
        getOrders(
            page: (ordersModel.value.ordersPaggination?.page ?? 0) + 1,
            itemsNumber: 15,
            isRequiredLoader: true);
      }
    }
  }

  getOrders(
      {int page = 1, int itemsNumber = 15, bool isRequiredLoader = false}) {
         // Add 10 dummy items for testing
     
    _graphQlInitilize.runQuery(
        context: context,
        isRequiredLoader: isRequiredLoader,
        query: GraphQlQuries.getOrderItems,
        variables: GraphQlVariables.getOrderPaginated(
            orderStatus: "Pending", page: page, itemsNumber: itemsNumber));
  }

  @override
  Widget build(BuildContext context) {
    if (Provider.of<OrderStatusProvider>(context).notificationType ==
        NotificationOrderType.newOrders) {
      newOrders.clear();
      newOrders.addAll(controller.dummyList);
      Future.delayed(Durations.long2, () {
        controller.getOrdersCount();
        getOrders(isRequiredLoader: true);
        Provider.of<OrderStatusProvider>(context, listen: false)
            .orderRecive(notificationOrderType: NotificationOrderType.notFound);
      });
    }
    return RefreshIndicator(
      onRefresh: () async {
        controller.getOrdersCount();
        newOrders.clear();
        newOrders.addAll(controller.dummyList);
        getOrders(isRequiredLoader: true);
        await Future.delayed(const Duration(milliseconds: 500));
      },
      child: Obx(
        () => ordersModel.value.status != statusOK
            ? SingleChildScrollView(
                child: SizedBox(
                  height: Get.height - 200,
                  child: Center(
                    child: Text(
                      ordersModel.value.message ?? "",
                      style: FontStyles.fontSemibold(),
                    ),
                  ),
                ),
              )
            : ListView.builder(
                itemCount: newOrders.length,
                controller: scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.only(top: 16),
                itemBuilder: (context, index) {
                  return newOrders[index].id == null
                      ? const OrdersLoader()
                      : NewOrderRow(
                          orderItems: newOrders[index],
                          onRefresh: (v) {
                            if (v == true) {
                              Provider.of<OrderStatusProvider>(context,
                                      listen: false)
                                  .orderRecive(
                                      notificationOrderType:
                                          NotificationOrderType
                                              .waitingPickupOrders);
                            }
                            newOrders.clear();
                            newOrders.addAll(controller.dummyList);
                            controller.getOrdersCount();
                            getOrders();
                          },
                        );
                }),
      ),
    );
  }

  @override
  onError(error, String type) {
   ordersModel.value = OrdersModel.fromJson(error);
   
  }

  @override
  onSucess(response, String type) {
    ordersModel.value = OrdersModel.fromJson(response);
    if (ordersModel.value.status == statusOK) {
      newOrders.removeWhere((element) => element.id == null);
      newOrders.addAll(ordersModel.value.ordersPaggination?.items ?? []);
      print(newOrders.length);
     
    }
   
  }
}
