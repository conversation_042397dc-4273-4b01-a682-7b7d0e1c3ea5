import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/controller/shop_info_controller.dart';

class CustomerContactCard extends GetView<ShopInfoController> {
  final Function(int value) tabPosition;
  const CustomerContactCard({Key? key, required this.tabPosition})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    String customerContact = prefs.getString(shopCustomerContact) ?? "";
    return Container(
      color: AppColors.headerColorDark,
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: kLeftSpace),
            child: Row(
              children: <Widget>[
                Expanded(
                  child: Text(
                    "Customer Contact",
                    style: FontStyles.fontBold(fontSize: 12),
                  ),
                ),
                if (controller.shop != null && customerContact.isNotEmpty)
                  IconButton(
                    onPressed: () {
                      Alerts.alertView(
                          context: context,
                          title: "Alert",
                          content:
                              "Are you sure you want to discard customer contact changes?",
                          defaultActionText: "Yes",
                          cancelActionText: "No",
                          cancelAction: () {
                            Navigator.pop(context);
                          },
                          action: () {
                            prefs.setString(shopCustomerContact, "");
                            tabPosition(2);
                            controller.updateContactInfo(
                                shop: controller.shop!);
                            Get.back();
                          });
                    },
                    icon: SvgPicture.string(SvgStrings.iconDelete),
                  ),
                IconButton(
                  onPressed: () => tabPosition(2),
                  icon: Container(
                    height: 30,
                    width: 30,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.06),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: SvgPicture.string(SvgStrings.iconEditGray),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          _infoListTile(
              "Phone Number", "+971 ${controller.strContactNumber}", false,
              changes: getCustomerNumber()),
          if (controller.strWhatsappNumber.isNotEmpty ||
              (controller.shop?.shopContactDetails?.whatsUpPhoneNumber ?? "")
                  .isNotEmpty)
            _infoListTile(
                "Whatsapp Number", "+971 ${controller.strWhatsappNumber}", true,
                changes: getCustomerWhatsappNumber()),
          if (controller.strEmail.isNotEmpty ||
              (controller.shop?.shopContactDetails?.email ?? "").isNotEmpty)
            _infoListTile("Email Address", controller.strEmail, false,
                changes: getCustomerEmail()),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget getCustomerEmail() {
    String customerContact = prefs.getString(shopCustomerContact) ?? "";
    if (controller.shop != null) {
      if (customerContact.isNotEmpty) {
        Map<String, dynamic> contactInfo = jsonDecode(customerContact);
        String cacheEmail = contactInfo["email"] ?? "";
        String uploadedEmail = controller.shop?.shopContactDetails?.email ?? "";
        if (cacheEmail.isNotEmpty && uploadedEmail != cacheEmail) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                uploadedEmail.isEmpty ? "Email Not Provided" : uploadedEmail,
                style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                cacheEmail,
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else if (cacheEmail.isEmpty) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                uploadedEmail,
                style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                "You removed customer contact email",
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else {
          return Text(
            (controller.shop?.shopContactDetails?.email ?? ""),
            style: FontStyles.fontRegular(
              fontSize: 12,
              color: Colors.white.withOpacity(0.5),
            ),
          );
        }
      } else {
        return Text(
          (controller.shop?.shopContactDetails?.email ?? ""),
          style: FontStyles.fontRegular(
            fontSize: 12,
            color: Colors.white.withOpacity(0.5),
          ),
        );
      }
    } else {
      return Text(
        controller.strEmail,
        style: FontStyles.fontRegular(
          fontSize: 12,
          color: Colors.white.withOpacity(0.5),
        ),
      );
    }
  }

  Widget getCustomerWhatsappNumber() {
    String customerContact = prefs.getString(shopCustomerContact) ?? "";

    if (controller.shop != null) {
      if (customerContact.isNotEmpty) {
        Map<String, dynamic> contactInfo = jsonDecode(customerContact);
        String cachewhatsUpPhoneNumber =
            contactInfo["whatsUpPhoneNumber"] ?? "";
        String whatsUpPhoneNumber =
            controller.shop?.shopContactDetails?.whatsUpPhoneNumber ?? "";
        if (cachewhatsUpPhoneNumber.isNotEmpty &&
            whatsUpPhoneNumber != cachewhatsUpPhoneNumber) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "+971 ${whatsUpPhoneNumber.isEmpty
                        ? "WhatsApp Not Provided"
                        : whatsUpPhoneNumber}",
                style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                "+971 $cachewhatsUpPhoneNumber",
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else if (cachewhatsUpPhoneNumber.isEmpty) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "+971 $whatsUpPhoneNumber",
                style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                "You removed Whatsapp number from customer contact",
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else {
          return Text(
            "+971 ${controller.shop?.shopContactDetails?.whatsUpPhoneNumber ?? ""}",
            style: FontStyles.fontRegular(
              fontSize: 12,
              color: Colors.white.withOpacity(0.5),
            ),
          );
        }
      } else {
        return Text(
          "+971 ${controller.shop?.shopContactDetails?.whatsUpPhoneNumber ?? ""}",
          style: FontStyles.fontRegular(
            fontSize: 12,
            color: Colors.white.withOpacity(0.5),
          ),
        );
      }
    } else {
      return Text(
        "+971 ${controller.strWhatsappNumber}",
        style: FontStyles.fontRegular(
          fontSize: 12,
          color: Colors.white.withOpacity(0.5),
        ),
      );
    }
  }

  Widget getCustomerNumber() {
    String customerContact = prefs.getString(shopCustomerContact) ?? "";
    if (controller.shop != null) {
      if (customerContact.isNotEmpty) {
        Map<String, dynamic> contactInfo = jsonDecode(customerContact);
        String cachephoneNumber = contactInfo["phoneNumber"] ?? "";
        String phoneNumber =
            controller.shop?.shopContactDetails?.phoneNumber ?? "";
        if (cachephoneNumber.isNotEmpty && phoneNumber != cachephoneNumber) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "+971 $phoneNumber",
                style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                "+971 $cachephoneNumber",
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else {
          return Text(
            "+971 $phoneNumber",
            style: FontStyles.fontRegular(
              fontSize: 12,
              color: Colors.white.withOpacity(0.5),
            ),
          );
        }
      } else {
        return Text(
          "+971 ${controller.shop?.shopContactDetails?.phoneNumber ?? ""}",
          style: FontStyles.fontRegular(
            fontSize: 12,
            color: Colors.white.withOpacity(0.5),
          ),
        );
      }
    } else {
      return Text(
        "+971 ${controller.strContactNumber}",
        style: FontStyles.fontRegular(
          fontSize: 12,
          color: Colors.white.withOpacity(0.5),
        ),
      );
    }
  }

  Widget _infoListTile(String title, String value, bool requiredTopSpace,
      {bool isLink = false,
      double locationLat = 0,
      double locationLong = 0,
      Widget? changes}) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: 16, vertical: requiredTopSpace ? 5 : 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              title,
              style: FontStyles.fontRegular(fontSize: 12),
            ),
          ),
          Expanded(
            flex: 2,
            child: InkWell(
              onTap: () {
                if (isLink) {
                  if (locationLat != 0 && locationLong != 0) {
                    GlobalMethods.openMap(locationLat, locationLong);
                  } else {
                    GlobalMethods.launchInWebView(value);
                  }
                }
              },
              child: changes ??
                  Text(
                    isLink ? value.split('/').last : value,
                    style: FontStyles.fontRegular(
                      fontSize: 12,
                      decoration: isLink
                          ? TextDecoration.underline
                          : TextDecoration.none,
                      color: isLink
                          ? AppColors.colorPrimary
                          : Colors.white.withOpacity(0.5),
                    ),
                  ),
            ),
          )
        ],
      ),
    );
  }
}
