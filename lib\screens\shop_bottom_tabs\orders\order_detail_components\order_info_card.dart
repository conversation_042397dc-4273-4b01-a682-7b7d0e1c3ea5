import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/enums/enums.dart';
import 'package:overolasuppliers/helper/other/badge_decoration.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/helper/url_loader.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/order_detail_components/info_row.dart';

class OrderInfoCard extends StatelessWidget {
  final OrderType orderType;
  final OrderItems orderItems;
  const OrderInfoCard(
      {Key? key, required this.orderType, required this.orderItems})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    int confirmedIndex = (orderItems.orderItemStatus ?? []).indexWhere(
        (element) =>
            element.status == "On the way for pickup" ||
            element.status?.toLowerCase() == "confirmed");
    int shippedIndex = (orderItems.orderItemStatus ?? [])
        .indexWhere((element) => element.status == "shipped");
    int deliverIndex = (orderItems.orderItemStatus ?? [])
        .indexWhere((element) => element.status == "Delivered");
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.headerColorDark,
          borderRadius: BorderRadius.circular(12),
        ),
        foregroundDecoration: getTag(appLocal),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Code Section
            Text(
              orderItems.orderItemCode ?? "",
              style: FontStyles.fontMedium(fontSize: 18),
            ),
             Divider(color: Colors.white24, height: 32.h),

            // Documents Section
            if (orderType != OrderType.cancelByCustomerOrder && orderType != OrderType.newOrder)
              Row(
                children: [
                  _buildDocumentButton(
                    label: "Invoice Summary",
                    onTap: () => _openDocument(orderItems.invoice ?? ""),
                  ),
                   SizedBox(width: 10.w),
                  _buildDocumentButton(
                    label: "Shipment Info",
                    onTap: () => _openDocument(orderItems.shipmentQrCode ?? ""),
                  ),
                ],
              ),

             SizedBox(height: 10.h),

            // Order Timeline Section
            Container(
              padding: const EdgeInsets.all(16).r,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Order Timeline',
                    style: FontStyles.fontMedium(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  InfoRow(
                    image: SvgStrings.iconOrderd,
                    title: appLocal.ordered,
                    info: GlobalMethods.convertTimeFormate(
                        orderItems.createdAt ?? "",
                        format: "dd MMM, yyyy hh:mm a"),
                  ),
                  if (confirmedIndex != -1)
                    InfoRow(
                        image: SvgStrings.iconOrderConfirm,
                        title: appLocal.confirmed,
                        info: GlobalMethods.convertTimeFormate(
                            orderItems.orderItemStatus?[confirmedIndex]
                                    .createdAt ??
                                "",
                            format: "dd MMM, yyyy hh:mm a"),
                        padding: 8),
                  if (orderType == OrderType.cancelByCustomerOrder)
                    InfoRow(
                        image: SvgStrings.iconOrderCancelled,
                        title: appLocal.cancelled,
                        info: GlobalMethods.convertTimeFormate(
                            orderItems.orderItemStatus?.last.createdAt ?? "",
                            format: "dd MMM, yyyy hh:mm a"),
                        padding: 8),
                  if (shippedIndex != -1)
                    InfoRow(
                        image: SvgStrings.iconShipped,
                        title: appLocal.shipped,
                        info: GlobalMethods.convertTimeFormate(
                            orderItems
                                    .orderItemStatus?[shippedIndex].createdAt ??
                                "",
                            format: "dd MMM, yyyy hh:mm a"),
                        padding: 8),
                  if (deliverIndex != -1)
                    InfoRow(
                        image: SvgStrings.iconDelivery,
                        title: appLocal.delivered,
                        info: GlobalMethods.convertTimeFormate(
                            orderItems
                                    .orderItemStatus?[deliverIndex].createdAt ??
                                "",
                            format: "dd MMM, yyyy hh:mm a"),
                        padding: 8),
                  if (orderType == OrderType.rejectedOrders)
                    InfoRow(
                        image: SvgStrings.iconOrderRejected,
                        title: appLocal.rejected,
                        info: '11:30 PM, 20.09.2020',
                        padding: 8),
                  if (orderType == OrderType.returnOrder ||
                      orderType == OrderType.returnConfirmOrder)
                    InfoRow(
                        image: SvgStrings.iconReturned,
                        title: appLocal.returned,
                        info: '11:30 PM, 20.09.2020',
                        padding: 8),
                  if (orderType == OrderType.returnConfirmOrder)
                    InfoRow(
                        image: SvgStrings.iconReturnedConfirmed,
                        title: "Return Confirmed",
                        info: '11:30 PM, 20.09.2020',
                        padding: 8),
                ],
              ),
            ),

             SizedBox(height: 10.h),

            // Cost Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCostRow(
                    label: "Total Cost",
                    amount: "${orderItems.finalCost ?? 0}",
                    icon: SvgStrings.iconPriceTag,
                  ),
                  if (orderItems.shipmentCost == 0) ...[
                     SizedBox(height: 12.h),
                    Row(
                      children: [
                        Icon(
                          Icons.local_shipping_rounded,
                          color: AppColors.colorSecondary,
                          size: 20,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: Text(
                            "Free Shipping - Shipment cost covered by you",
                            style: FontStyles.fontMedium(
                              color: AppColors.colorSecondary,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentButton({
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.colorSecondary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.file_present_rounded,
                  color: AppColors.colorPrimary,
                  size: 20,
                ),
              ),
               SizedBox(height: 8.h),
              Text(
                label,
                style: FontStyles.fontMedium(
                  color: AppColors.colorPrimary_80,
                  fontSize: 14,
                  // decoration: TextDecoration.underline,
                ),
                textAlign: TextAlign.center,
              ),
               
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCostRow({
    required String label,
    required String amount,
    required String icon,
  }) {
    return Row(
      children: [
        SvgPicture.string(icon),
        SizedBox(width: 12.w),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: "$label: ",
                style: FontStyles.fontSemibold(),
              ),
              TextSpan(
                text: amount,
                style: FontStyles.fontBold(fontSize: 16),
              ),
              TextSpan(
                text: " AED",
                style: FontStyles.fontSemibold(fontSize: 12),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _openDocument(String url) {
    if (Platform.isIOS) {
      Get.to(
        () => UrlLoader(url: url),
        fullscreenDialog: true,
        transition: Transition.circularReveal,
      );
    } else {
      GlobalMethods.launchInWebView(url);
    }
  }

  BadgeDecoration getTag(AppLocalizations appLocal) {
    late BadgeDecoration tag;
    switch (orderType) {
      case OrderType.newOrder:
        tag = badgeWidget("New");
        break;
      case OrderType.confirmedOrder:
        tag = badgeWidget(appLocal.confirmed, color: AppColors.colorSecondary);
        break;
      case OrderType.shippedOrder:
        tag = badgeWidget(appLocal.shipped, color: AppColors.colorTangerine);
        break;
      case OrderType.cancelByCustomerOrder:
        tag = badgeWidget(appLocal.cancelledByCustomer,
            color: AppColors.colorSecondaryYellow, height: 1.5);
        break;
      case OrderType.deliveredOrder:
        tag = badgeWidget(appLocal.delivered, color: AppColors.colorSecondary);
        break;
      case OrderType.rejectedOrders:
        tag =
            badgeWidget(appLocal.rejected, color: AppColors.colorSecondary_Red);
        break;
      case OrderType.returnOrder:
        tag = badgeWidget(appLocal.returned, color: AppColors.colorTangerine);
        break;
      case OrderType.returnConfirmOrder:
        tag = badgeWidget(appLocal.returnedConfirmed,
            color: AppColors.colorTangerine, height: 1.5);
        break;
      default:
    }
    return tag;
  }

  badgeWidget(String title, {Color? color, double? height}) {
    Color badgeColor = AppColors.colotMidBlack;
    if (color != null) {
      badgeColor = color.withOpacity(0.1);
    }
    return BadgeDecoration(
      badgeColor: badgeColor,
      badgeSize: 90,
      textSpan: TextSpan(
        text: title,
        style: FontStyles.fontRegular(
          fontSize: 10,
          height: height ?? 2,
          color: color ?? Colors.white,
        ),
      ),
    );
  }
}
