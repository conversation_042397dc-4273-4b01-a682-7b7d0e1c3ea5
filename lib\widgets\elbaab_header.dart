import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:provider/provider.dart';

class ElbaabHeader extends StatelessWidget implements PreferredSizeWidget {
  final bool leadingBack;
  final Widget? leadingWidget;
  final Widget? trailingWidget;
  final String title;
  final Color? backgroundColor;
  final bool? notificationBell;
  final double leadingSpace;
  final double height;

  const ElbaabHeader(
      {Key? key,
      this.leadingBack = false,
      this.leadingSpace = 0,
      this.leadingWidget,
      this.trailingWidget,
      required this.title,
      this.backgroundColor,
      this.height = 60,
      this.notificationBell})
      : super(key: key);

  @override
  Size get preferredSize => Size.fromHeight(height);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.headerColorDark,
            AppColors.headerColorDark.withOpacity(0.8),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            top: -20,
            right: -20,
            child: Container(
              height: 80.h,
              width: 80.w,
              decoration: BoxDecoration(
                color: AppColors.colorPrimary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            bottom: -10,
            left: -10,
            child: Container(
              height: 50.h,
              width: 50.w,
              decoration: BoxDecoration(
                color: AppColors.colorPrimary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
            ),
          ),
          AppBar(
            centerTitle: true,
            automaticallyImplyLeading: false,
            backgroundColor: Colors.transparent,
            leadingWidth: leadingBack ? null : 90,
            leading: leadingBack
                ? IconButton(
                    icon: Icon(
                      Icons.chevron_left,
                      size: 35,
                      color: Colors.white.withOpacity(0.86),
                    ),
                    onPressed: () => Get.back(result: '1'),
                  )
                : Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 5),
                    child: leadingWidget ?? const SizedBox.shrink(),
                  ),
            elevation: 0,
            title: Text(
              title,
              style: FontStyles.fontRegular(fontSize: 17),
            ),
            actions: [
              if (trailingWidget != null) trailingWidget!,
              if (notificationBell ?? false)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: InkWell(
                    onTap: () => Get.toNamed(RouteNames.notificationsScreen),
                    child: Badge(
                      backgroundColor: Provider.of<UpdatedInfo>(context)
                                        .unReadNotificationCount ==
                                    0
                                ? Colors.transparent
                                : AppColors.colorDanger,
                      label: Text(
                          "${Provider.of<UpdatedInfo>(context).unReadNotificationCount > 0 ? Provider.of<UpdatedInfo>(context).unReadNotificationCount > 9 ? "9+" : Provider.of<UpdatedInfo>(context).unReadNotificationCount : ""}"),
                      textStyle: FontStyles.fontSemibold(fontSize: 10),
                      textColor: Colors.white,
                      child: SvgPicture.string(SvgStrings.iconBell),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
