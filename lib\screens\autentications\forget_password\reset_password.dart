import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';

class ResetPassword extends StatelessWidget
    with InputValidationUtil
    implements ServerResponse {
  final GlobalKey<FormState> _globalKey = GlobalKey<FormState>();
  late GraphQlInitilize _request;
  RxString strError = ''.obs;
  String value = "";

  ResetPassword({super.key});

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    if (Get.arguments != null) {
      value = Get.arguments[0];
    }
    _request = GraphQlInitilize(this);
    return Scaffold(
      appBar:  ElbaabHeader(
        title: appLocal.resetPassword,
        leadingBack: true,
      ),
      body: SingleChildScrollView(
        child: Container(
          height: Get.height - 250,
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _globalKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  appLocal.resetPasswordContent,
                  style: FontStyles.fontSemibold(fontSize: 20),
                ),
                const SizedBox(height: 24),
                ElbaaabInputTextField(
                  onChanged: (v) => value = v,
                  initialValue: Get.arguments[0],
                  isAllowSpace: false,
                  label: appLocal.emailfeildLabel,
                  charaterlimit: 50,
                  autoTextDirection: true,
                  hint: appLocal.emailHint,
                  inputFormatter: "[a-zA-Z0-9@+.ا-ي]",
                  validator: validateEmail,
                  inputType: TextInputType.emailAddress,
                ),
                Obx(
                  () => Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: strError.value.isEmpty
                          ? EdgeInsets.zero
                          : const EdgeInsets.all(8.0),
                      child: Text(
                        strError.value,
                        style: FontStyles.fontRegular(
                          fontSize: 12,
                          color: AppColors.colorDanger,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                ElbaabGradientButtonWidget(
                  onPress: () {
                    strError.value = "";
                    if (_globalKey.currentState!.validate()) {
                      if (GetUtils.isEmail(value)) {
                        _request.runMutation(
                          context: context,
                          query: GraphQlQuries.forgetPassword,
                          variables:
                              GraphQlVariables.forgetpassword(userEmail: value),
                          type: "EmailVerification",
                        );
                      } else {
                        _request.runMutation(
                            context: context,
                            query: GraphQlQuries.forgetPasswordByNumber,
                            variables: GraphQlVariables.forgetpassword(
                                userEmail: value));
                      }
                    }
                  },
                  text: appLocal.send,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {
    strError.value = BaseModel.fromJson(error).message ?? "";
  }

  @override
  onSucess(response, String type) {
    BaseModel baseModel = BaseModel.fromJson(response);
    if (baseModel.status == statusOK) {
      if (type == "EmailVerification") {
        prefs.setBool(isListenNavidationStream, true);
        Get.toNamed(RouteNames.emailVerificationScreen,
            arguments: [baseModel.userId ?? "", true, value]);
      } else {}
    } else {
      strError.value = baseModel.message ?? "";
    }
  }
}
