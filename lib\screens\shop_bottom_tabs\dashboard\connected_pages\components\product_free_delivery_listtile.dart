import 'package:flutter/material.dart';
import 'package:get/state_manager.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/product_by_shop_model.dart';

class ProductFreeDeliveryListTile extends StatelessWidget {
  final ShopProducts product;
  final Function(String id) onTap;
  ProductFreeDeliveryListTile(
      {Key? key, required this.product, required this.onTap})
      : super(key: key);

  RxBool isChecked = false.obs;

  @override
  Widget build(BuildContext context) {
    if (product.isFreeDeliveryItem ?? false) {
      isChecked.value = true;
    }
    return Container(
      height: 139,
      margin: const EdgeInsets.only(top: 16),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                height: 123,
                width: 130,
                decoration: BoxDecoration(
                  color: AppColors.headerColorDark,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Center(
                  child: SizedBox(
                    height: 96,
                    width: 96,
                    child: GlobalMethods.netWorkImage(
                        (product.productOptions?.productColors?.isNotEmpty ??
                                false)
                            ? product.productOptions?.productColors?.first
                                    .colorImages?.first ??
                                ""
                            : product.productImages?.first ?? "",
                        BorderRadius.circular(5),
                        BoxFit.cover),
                  ),
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.productName ?? "",
                      maxLines: 2,
                      style: FontStyles.fontMedium(
                          fontSize: 16, color: Colors.white.withOpacity(0.6)),
                    ),
                    Text(
                      product.productBrand?.brandName ?? "",
                      style: FontStyles.fontRegular(
                        color: Colors.white.withOpacity(0.6),
                        decoration: TextDecoration.underline,
                      ),
                    ),
                    SizedBox(
                      height: 30,
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              "${product.productPrice ?? ""} Aed",
                              style: FontStyles.fontBold(
                                fontSize: 12,
                              ),
                            ),
                          ),
                          Obx(
                            () => Checkbox(
                              value: isChecked.value,
                              checkColor: AppColors.colorSecondary,
                              fillColor: MaterialStateProperty.all(
                                  AppColors.headerColorDark),
                              onChanged: (value) => {
                                isChecked.value = value!,
                                onTap(product.id ?? "")
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (product.productOptions?.productColors?.isNotEmpty ??
                        false)
                      SizedBox(
                        height: 12,
                        child: ListView.builder(
                            itemCount:
                                product.productOptions?.productColors?.length,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (context, index) {
                              return Container(
                                height: 12,
                                width: 12,
                                margin: const EdgeInsets.only(right: 6),
                                child: GlobalMethods.netWorkImage(
                                    product.productOptions
                                            ?.productColors?[index].colorIcon ??
                                        "",
                                    BorderRadius.circular(6),
                                    BoxFit.cover),
                              );
                            }),
                      ),
                    const SizedBox(height: 6),
                  ],
                ),
              ),
            ],
          ),
          const Spacer(),
          Container(
            height: 1,
            margin: const EdgeInsets.only(left: 14),
            color: AppColors.headerColorDark,
          )
        ],
      ),
    );
  }
}