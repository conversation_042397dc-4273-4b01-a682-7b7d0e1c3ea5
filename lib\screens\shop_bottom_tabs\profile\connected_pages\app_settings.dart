import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/provider/account_setting_info_provider.dart';
import 'package:overolasuppliers/screens/autentications/login_widgets/enable_local_auth_sheet.dart';
import 'package:overolasuppliers/screens/products/add_product/model/account_setting_model.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';
import 'package:provider/provider.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AppSetting extends StatefulWidget {
  const AppSetting({super.key});

  @override
  State<AppSetting> createState() => _AppSettingtState();
}

class _AppSettingtState extends State<AppSetting> implements ServerResponse {
  RxBool biomatricAccess = false.obs,
      isRememberMe = false.obs,
      isReciveOrderEmail = false.obs;

  RxString strError = "".obs;
  late GraphQlInitilize _request;
  var localAuth = LocalAuthentication();

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    Future.delayed(Duration.zero, () {
      String localAuth = prefs.getString(localAuthEnable) ?? "false";
      String rememberMeOption = prefs.getString(rememberMe) ?? "false";
      biomatricAccess.value = localAuth == "false" ? false : true;
      isRememberMe.value = rememberMeOption == "false" ? false : true;
      AccountSettingModel accountSettingModel =
          Provider.of<AccountSettingProvider>(context, listen: false)
              .getAccountSetting;
      isReciveOrderEmail.value =
          accountSettingModel.accountSetting?.acceptOrderEmails ?? false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return WillPopScope(
      onWillPop: () {
        Get.back(result: "1");
        return Future.value(true);
      },
      child: Scaffold(
        appBar: ElbaabHeader(
          title: appLocal.appSettings,
          leadingBack: true,
        ),
        body: Obx(
          () => ListView(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
            children: [
              _buildSettingsSection(
                title: appLocal.accountSettings,
                children: [
                  _buildSettingsTile(
                    icon: Icons.remember_me,
                    title: appLocal.rememberMe,
                    trailing: Switch.adaptive(
                      value: isRememberMe.value,
                      onChanged: (v) {
                        prefs.setString(rememberMe, "$v");
                        isRememberMe.value = v;
                      },
                      activeColor: AppColors.colorPrimary,
                    ),
                  ),
                  _buildSettingsTile(
                    icon: Icons.fingerprint,
                    title: appLocal.biometricAccess,
                    trailing: Switch.adaptive(
                      value: biomatricAccess.value,
                      onChanged: (v) async {
                        if (v == false) {
                          prefs.setString(localAuthEnable, "false");
                          biomatricAccess.value = v;
                        } else {
                          String email = prefs.getString(localAuthEmail) ?? "";
                          String password =
                              prefs.getString(localAuthPassword) ?? "";
                          if (email.isEmpty && password.isEmpty) {
                            strError.value =
                                "We think you used the Clear Cache option to enable fingerprint or faceId just login again";
                          } else {
                            if (await localAuth.canCheckBiometrics) {
                              showModalBottomSheet<void>(
                                context: MyApp.navigatorKey.currentContext!,
                                builder: (BuildContext context) {
                                  return EnableLocalAuthModalBottomSheet(
                                      action: () {
                                    biomatricAccess.value = v;
                                    prefs.setString(localAuthEnable, "$v");
                                    prefs.setString(localAuthEmail, email);
                                    prefs.setString(
                                        localAuthPassword, password);
                                  });
                                },
                              );
                            }
                          }
                        }
                      },
                      activeColor: AppColors.colorPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _buildSettingsSection(
                title: appLocal.notifications,
                children: [
                  _buildSettingsTile(
                    icon: Icons.notifications,
                    title: appLocal.pushNotification,
                    onTap: () => AppSettings.openAppSettings(
                        type: AppSettingsType.notification),
                  ),
                  _buildSettingsTile(
                    icon: Icons.attach_email,
                    title: appLocal.reciveOrderEmails,
                    trailing: Switch.adaptive(
                      value: isReciveOrderEmail.value,
                      onChanged: (v) {
                        _request.runMutation(
                          context: context,
                          query: GraphQlQuries.changeAcceptOrderEmail,
                          variables: GraphQlVariables.changeAcceptOrderEmail(
                              acceptOrderEmail: v),
                          type: "changeAcceptOrderEmail$v",
                        );
                      },
                      activeColor: AppColors.colorPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _buildSettingsSection(
                title: appLocal.dataManagement,
                children: [
                  _buildSettingsTile(
                    icon: Icons.cached,
                    title: appLocal.clearCache,
                    onTap: () => Alerts.alertView(
                      context: context,
                      content:
                          "Click on yes that's mean you loss every thing in cache like your product detail and shop detail which is not uploaded and all login credential and biometric access",
                      action: () async {
                        Get.back();
                        prefs.clear();
                        biomatricAccess.value = false;
                        prefs.setString(localAuthEnable, "false");
                        prefs.setString(localAuthEmail, "");
                        prefs.setString(localAuthPassword, "");
                        prefs.remove(merchantID);
                        prefs.remove(ownerName);
                        prefs.remove(authToken);
                        prefs.setString("information", "");
                        prefs.setString("customerContact", "");
                        prefs.setString("pickupAddress", "");
                      },
                      cancelActionText: EnStrings.no,
                      cancelAction: () => Get.back(),
                    ),
                  ),
                ],
              ),
              if (strError.value.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: ElbaabNetworkEroor(strError: strError),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 8),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: AppColors.headerColorDark.withOpacity(0.5),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            
            children: [
              Positioned(
            top: -20,
            right: -20,
            child: Container(
              height: 60.h,
              width: 60.w,
              decoration: BoxDecoration(
                color: AppColors.colorPrimary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            bottom: -10,
            left: -10,
            child: Container(
              height: 40.h,
              width: 40.w,
              decoration: BoxDecoration(
                color: AppColors.colorPrimary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
            ),
          ),
              Column(
                children: children,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      onTap: onTap,
      leading: Icon(icon, color: AppColors.colorPrimary),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      trailing: trailing,
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type.contains("changeAcceptOrderEmail")) {
      String value = type.replaceAll("changeAcceptOrderEmail", "");
      if (value.toLowerCase() == "true") {
        isReciveOrderEmail.value = true;
      } else {
        isReciveOrderEmail.value = false;
      }
    }
  }
}
