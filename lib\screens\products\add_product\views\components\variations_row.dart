import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/product_variation_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class VariationsRow extends StatefulWidget {
  final bool isScrollable;
  const VariationsRow({Key? key, required this.isScrollable}) : super(key: key);

  @override
  State<VariationsRow> createState() => _VariationsRowState();
}

class _VariationsRowState extends State<VariationsRow>
    with InputValidationUtil {
  final controller = Get.find<AddProductController>();

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Obx(
      () => controller.arrVariations.isEmpty
          ? SizedBox(
              height: (context.height / 2).h,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 50, horizontal: 16).r,
                child: Row(children: [
                  SizedBox(
                    width: 60.w,
                    height: 60.h,
                    child: CircularProgressIndicator(
                      color: AppColors.colorPrimary,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      appLocal.waitVariationLoading,
                      style: FontStyles.fontMedium(),
                    ),
                  )
                ]),
              ),
            )
          : Form(
              key: controller.variationFormKey,
              child: SingleChildScrollView(
                physics: widget.isScrollable
                    ? const ScrollPhysics()
                    : const NeverScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: controller.arrVariations.map((variations) {
                    if (controller.variationViewLoading) {
                      controller.variationViewLoading = false;
                    }
                    return Container(
                      height: 262.h,
                      margin: EdgeInsets.only(top: 8.h),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: variations.variations?.map((variation) {
                                return VariationWidget(
                                  variation: variation,
                                  variations: variations,
                                );
                              }).toList() ??
                              [],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
    );
  }
}

class VariationWidget extends StatelessWidget with InputValidationUtil {
  final Variations variation;
  final Variants variations;

  VariationWidget({
    Key? key,
    required this.variation,
    required this.variations,
  }) : super(key: key);

  final controller = Get.find<AddProductController>();
  late AppLocalizations appLocal;

  @override
  Widget build(BuildContext context) {
    appLocal = AppLocalizations.of(context)!;
    RxBool isHide = (variation.isVariantVisible ?? false).obs;
    dynamic price = variation.variantPrice ?? 0;
    String quantity = "${variation.variantQte ?? 0}";
    String gtin = variation.variantManufacturerId ?? "";

    String serverValueGtin = "", serverValuePrice = "", serverValueQty = "";
    if (variation.id?.isNotEmpty ?? false) {
      int indexOf = (controller.product?.productVariants ?? [])
          .indexWhere((element) => element.id == variation.id);
      if (indexOf != -1) {
        serverValueGtin = controller
                .product?.productVariants?[indexOf].variantManufacturerId ??
            "";
        serverValuePrice =
            "${controller.product?.productVariants?[indexOf].variantPrice}";
        serverValueQty =
            "${controller.product?.productVariants?[indexOf].variantQte ?? 0}";
      }
    }

    if (price != 0) {
      price = controller.price;
    }
    if (quantity.isEmpty) {
      quantity = controller.qty;
    }
    Future.delayed(Durations.short4,
        () => controller.variationFormKey.currentState!.validate());
    return Container(
      height: 262.h,
      width: 271.w,
      decoration: BoxDecoration(
        color: AppColors.headerColorDark,
        borderRadius: BorderRadius.circular(10),
      ),
      margin: EdgeInsets.only(right: 15.r),
      child: Stack(
        children: [
          Positioned(
            bottom: 9,
            right: 9,
            left: 9,
            top: 20,
            child: Column(
              children: [
                _buildVariantInfo(),
                _buildPriceAndQuantityFields(
                    serverValuePrice, serverValueQty, quantity),
                const Spacer(),
                _buildGtinField(serverValueGtin, gtin),
              ],
            ),
          ),
          Obx(
            () => AnimatedContainer(
              curve: Curves.slowMiddle,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: isHide.value
                    ? Colors.transparent
                    : Colors.black.withOpacity(0.6),
              ),
              height: 310.h,
              width: isHide.value ? 0 : 271.w,
              duration: const Duration(seconds: 1),
              child: isHide.value
                  ? Container()
                  : const Icon(
                      Icons.visibility_off,
                      color: Colors.white,
                      size: 50,
                    ),
            ),
          ),
          Positioned(
            top: 5,
            right: appLocal.localeName == "en" ? 8 : null,
            left: appLocal.localeName == "en" ? null : 8,
            child: InkWell(
              onTap: () async {
                if ((variation.isVariantVisible ?? false)) {
                  bool haveMoreVariant = checkAvailableVariations(context);
                  if (!haveMoreVariant) {
                    return;
                  }
                  BottomSheets.showAlertMessageBottomSheet(
                          appLocal.aleartMessageOnHideVariant,
                          appLocal.hide,
                          context)
                      .then((value) {
                    if (value) {
                      isHide.value = false;
                      variation.isVariantVisible = false;
                    }
                  });
                } else {
                  isHide.value = true;
                  variation.isVariantVisible = true;
                }
                if (controller.product != null) {
                  variation.isUpdated = true;
                }
              },
              child: Obx(
                () => Text(
                  isHide.value ? appLocal.hide : appLocal.show,
                  style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: isHide.value
                        ? AppColors.colorSecondary_Red
                        : AppColors.colorBlue,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVariantInfo() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 70.w,
          height: 70.h,
          child: GlobalMethods.netWorkImage(
            (variations.colorIcon?.isEmpty ??
                    false || variations.colorIcon == null)
                ? controller.sliderImages.isNotEmpty
                    ? controller.sliderImages.first
                    : ""
                : variations.colorIcon ?? "",
            BorderRadius.circular(5),
            BoxFit.cover,
          ),
        ),
         SizedBox(width: 10.w),
        Expanded(
          child: SizedBox(
            height: 110.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if ((variation.variantEIN ?? "").isNotEmpty)
                  RichText(
                    text: TextSpan(
                      style: FontStyles.fontRegular(fontSize: 12),
                      children: [
                        const TextSpan(text: 'Variant EIN : '),
                        TextSpan(
                          text: variation.variantEIN ?? "",
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                if ((variations.colorFamily ?? "").isNotEmpty)
                  RichText(
                    text: TextSpan(
                      style: FontStyles.fontRegular(fontSize: 12),
                      children: [
                        TextSpan(text: '${appLocal.color} : '),
                        TextSpan(
                          text: appLocal.localeName == "ar"
                              ? variation.ar?.variantAttributes?.variantColor
                                      ?.colorFamily ??
                                  ""
                              : variations.colorFamily,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                if ((variations.colorName ?? "").isNotEmpty)
                  RichText(
                    text: TextSpan(
                      style: FontStyles.fontRegular(fontSize: 12),
                      children: [
                        TextSpan(text: '${appLocal.colorName} : '),
                        TextSpan(
                          text: appLocal.localeName == "ar"
                              ? variation.ar?.variantAttributes?.variantColor
                                      ?.colorName ??
                                  ""
                              : variations.colorName,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                if (variation
                        .variantAttributes?.variantSize?.size?.isNotEmpty ??
                    false)
                  RichText(
                    text: TextSpan(
                      style: FontStyles.fontRegular(fontSize: 12),
                      children: [
                        TextSpan(
                          text: appLocal.variantSize(appLocal.localeName == "ar"
                              ? variation.ar?.variantAttributes?.variantSize
                                      ?.unit ??
                                  ""
                              : variation
                                      .variantAttributes?.variantSize?.unit ??
                                  ""),
                        ),
                        TextSpan(
                          text:
                              variation.variantAttributes?.variantSize?.size ??
                                  "",
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                if (variation.variantAttributes?.variantCustomOptions != null)
                  Expanded(
                    child: ListView.builder(
                      itemCount: variation
                          .variantAttributes?.variantCustomOptions?.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        return RichText(
                          text: TextSpan(
                            style: FontStyles.fontRegular(fontSize: 12),
                            children: [
                              TextSpan(
                                  text:
                                     appLocal.localeName == "ar"? '${variation.ar?.variantAttributes?.variantCustomOptions?[index].attributeTitle} : '  : '${variation.variantAttributes?.variantCustomOptions?[index].attributeTitle} : '),
                              TextSpan(
                                text:appLocal.localeName == "ar"? '${variation.ar?.variantAttributes?.variantCustomOptions?[index].attributeValue}'  :  variation
                                        .variantAttributes
                                        ?.variantCustomOptions?[index]
                                        .attributeValue ??
                                    "",
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.5),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPriceAndQuantityFields(
      String serverValuePrice, String serverValueQty, String quantity) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          flex: 3,
          child: ElbaaabInputTextField(
            onChanged: (v) {
              variation.variantPrice = v;
              if (controller.product != null) {
                variation.isUpdated = true;
              }
            },
            error: false,
            validator: (v) {
              if (v == "0") {
                return appLocal.zeroNotAcceptable;
              } else {
                return validateFieldEmpty(
                  v,
                  errorMessage: "",
                  serverValue: serverValuePrice,
                  isReturend: (variation.isPriceReturned ?? false),
                );
              }
            },
            hint: 'ex : 200',
            charaterlimit: 7,
            decimalNumerLimit: 2,
            suffix: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.colorPrimary,
                borderRadius: appLocal.localeName == "ar"
                    ? const BorderRadius.only(
                        topLeft: Radius.circular(10),
                        bottomLeft: Radius.circular(10),
                      )
                    : const BorderRadius.only(
                        topRight: Radius.circular(10),
                        bottomRight: Radius.circular(10),
                      ),
              ),
              child: Center(
                child: Text(
                  appLocal.aed,
                  style: FontStyles.fontRegular(fontSize: 12),
                ),
              ),
            ),
            textDirection: appLocal.localeName == "ar"
                ? TextDirection.rtl
                : TextDirection.ltr,
            initialValue:
                "${variation.variantPrice == 0 ? "" : variation.variantPrice}",
            label: appLocal.price,
            inputType: const TextInputType.numberWithOptions(
                signed: true, decimal: true),
            inputFormatter: '[0-9.]',
          ),
        ),
        SizedBox(width: 6.w),
        Expanded(
          flex: 2,
          child: ElbaaabInputTextField(
            onChanged: (v) {
              variation.variantQte = int.parse(v.isEmpty ? "0" : v);
              if (controller.product != null) {
                variation.isUpdated = true;
              }
            },
            textDirection: appLocal.localeName == "ar"
                ? TextDirection.rtl
                : TextDirection.ltr,
            error: false,
            validator: (v) => validateFieldEmpty(
              v,
              errorMessage: "",
              serverValue: serverValueQty,
              isReturend: (variation.isQtyReturned ?? false),
            ),
            hint: appLocal.itemPerOrderFeildHint,
            initialValue: quantity == "0" ? "" : quantity,
            label: appLocal.qty,
            charaterlimit: 4,
            inputFormatter: '[0-9]',
            inputType: TextInputType.number,
          ),
        ),
      ],
    );
  }

  Widget _buildGtinField(String serverValueGtin, String gtin) {
    return ElbaaabInputTextField(
      onChanged: (v) {
        variation.variantManufacturerId = v;
        if (controller.product != null) {
          variation.isUpdated = true;
        }
      },
      error: false,
      textDirection:
          appLocal.localeName == "ar" ? TextDirection.rtl : TextDirection.ltr,
      validator: (v) => validateFieldEmpty(
        isOptionalFeild: !(variation.isGtinReturned ?? false),
        v,
        errorMessage: "",
        serverValue: serverValueGtin,
        isReturend: (variation.isGtinReturned ?? false),
      ),
      hint: 'ex: 123456789012',
      initialValue: gtin == "0" ? "" : gtin,
      label: 'GTIN',
      inputType: TextInputType.text,
    );
  }

  bool checkAvailableVariations(BuildContext context) {
    // Count how many values are false
    int falseCount = 0;
    int variationCount = 0;
    for (var variation in controller.arrVariations) {
      variationCount += (variation.variations ?? []).length;
      falseCount += (variation.variations ?? [])
          .where((element) => element.isVariantVisible == false)
          .length;
    }

    if (falseCount == (variationCount - 1)) {
      BottomSheets.showAlertMessageBottomSheet(
          appLocal.hideAllVariantAlertMessage, appLocal.alert, context);
      return false;
    } else {
      return true;
    }
  }
}
