import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/dio/api_requests.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/no_leading_space_formatter.dart';
import 'package:overolasuppliers/helper/media_picker.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/upload_image_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/banner_logo_preview.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/controller/shop_info_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_border_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';

class ShoopInformation extends GetView<ShopInfoController>
    with InputValidationUtil
    implements ServerResponse {
  final Function(int value) tabPosition;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  ShoopInformation({
    Key? key,
    required this.tabPosition,
  }) : super(key: key);
  RxString strError = ''.obs;
  late ApiRequest _request;
  List<String> arrParams = [];
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    _request = ApiRequest(this);
    Future.delayed(Duration.zero, () {
      if (controller.requiredPageAutoValidate()) {
        formKey.currentState!.validate();
      }
    });
    return SingleChildScrollView(
      child: Form(
        key: formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            Center(
              child: SvgPicture.string(SvgStrings.shopInfoTag),
            ),
            const SizedBox(height: 20),
            BannerLogoPreview(infoController: controller),
            ElbaaabInputTextField(
              margin: const EdgeInsets.only(
                  top: 16, left: kLeftSpace, right: kRightSpace),
              onChanged: (value) => controller.slogan = value,
              initialValue: controller.slogan,
              hint: appLocal.businessSloganFeildHint,
              label: appLocal.businessSloganFeildLabel,
              autoTextDirection: true,
              charaterlimit: 300,
              validator: (v) => validateFieldEmpty(
                v,
                errorMessage: appLocal.businessSloganFeildAdminReject,
                serverValue: controller.shop?.shopSlogan ?? "",
                isReturend:
                    (controller.history.returnValues?.contains("Shop Slogan") ??
                        false),
                isOptionalFeild: !(controller.history.returnValues
                        ?.contains("Shop Slogan") ??
                    false),
              ),
              formatter: [FilteringTextInputFormatter.singleLineFormatter],
              inputType: TextInputType.text,
            ),
            ElbaaabInputTextField(
              margin: const EdgeInsets.only(
                  top: 16, left: kLeftSpace, right: kRightSpace),
              onChanged: (value) => controller.description = value,
              initialValue: controller.description,
              height: 203,
              hint: appLocal.shopDescription,
              autoTextDirection: true,
              label: appLocal.description,
              charaterlimit: 300,
              requiredCounter: true,
              formatter: [
                FilteringTextInputFormatter.singleLineFormatter,
                NoLeadingSpaceFormatter()
              ],
              validator: (v) => validateFieldEmpty(
                v,
                errorMessage: appLocal.businessDiscriiptionFeildAdminReject,
                serverValue: controller.shop?.shopDescription ?? "",
                isReturend: (controller.history.returnValues
                        ?.contains("Shop Description") ??
                    false),
                isOptionalFeild: !(controller.history.returnValues
                        ?.contains("Shop Description") ??
                    false),
              ),
              inputType: TextInputType.multiline,
            ),
            Container(
              height: 50,
              margin:
                  const EdgeInsets.only(right: kRightSpace, left: kLeftSpace),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: GestureDetector(
                      onTap: () => BottomSheets.showAlertMessageBottomSheet(
                          appLocal.freeDeliveryTarget,
                          appLocal.freeDeliveryTarget,
                          context),
                      child: Wrap(
                        crossAxisAlignment: WrapCrossAlignment.center,
                        children: [
                          Text(
                            appLocal.freeDeliveryTarget,
                            style: FontStyles.fontRegular(),
                          ),
                          const SizedBox(width: 10),
                          Icon(
                            Icons.info,
                            color: AppColors.colorPrimary,
                            size: 20,
                          )
                        ],
                      ),
                    ),
                  ),
                  Obx(
                    () => Switch(
                      value: controller.freeDeliveryTaget.value,
                      activeColor: AppColors.colorPrimary,
                      onChanged: (v) =>
                          controller.freeDeliveryTaget.value = v,
                    ),
                  ),
                ],
              ),
            ),
            Obx(
              () => controller.freeDeliveryTaget.value
                  ? ElbaaabInputTextField(
                      margin: const EdgeInsets.only(
                          left: kLeftSpace, right: kRightSpace),
                      onChanged: (v) => controller.targetPrice = v,
                      initialValue: controller.targetPrice,
                      hint: 'ex : 500',
                      label: appLocal.targetPrice,
                      charaterlimit: 5,
                      autoTextDirection: true,
                      validator: (v) => validateFieldEmpty(
                        v,
                        errorMessage: appLocal.rejectTargetPrice,
                        serverValue:
                            "${controller.shop?.targetPriceForFdt ?? 0}",
                        isReturend: (controller.history.returnValues
                                ?.contains("Free Delivery Target Price") ??
                            false),
                      ),
                      formatter: [
                        FilteringTextInputFormatter.allow(RegExp('[0-9]'))
                      ],
                      suffix: Container(
                        height: 60,
                        width: 80,
                        decoration: BoxDecoration(
                          color: AppColors.colorPrimary,
                          borderRadius: appLocal.localeName == "en"? const BorderRadius.only(
                            topRight: Radius.circular(10),
                            bottomRight: Radius.circular(10),
                          ): const BorderRadius.only(
                            topLeft: Radius.circular(10),
                            bottomLeft: Radius.circular(10),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            appLocal.aed,
                            style: FontStyles.fontRegular(),
                          ),
                        ),
                      ),
                      inputType: const TextInputType.numberWithOptions(
                          signed: true, decimal: true),
                    )
                  : Container(),
            ),
            Padding(
              padding: const EdgeInsets.only(
                  top: 16, left: kLeftSpace, right: kRightSpace),
              child: Text(
                appLocal.termsAndCondition,
                style: FontStyles.fontMedium(),
              ),
            ),
            ElbaabFeildContainerWidget(
              containerHeight: 60,
              borderColor: (controller.history.returnValues
                          ?.contains("Shop Terms And Conditions") ??
                      false)
                  ? AppColors.colorDanger
                  : Colors.transparent,
              edgeInsets: const EdgeInsets.all(16),
              onPress: () => showMediaPicker(
                context,
                requiredDocumentPicker: true,
                requiredViewDocument: controller.termsUrl.isNotEmpty &&
                        controller.isRemoveTerms.value == false
                    ? true
                    : false,
                viewDocumentUrl: controller.termsUrl,
              ).then((value) {
                if (value != null) {
                   final tempDir = Directory.systemTemp;
                    final newPath =
                        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}.${value.path.split('.').last}';
                    final newFile = File(value.path).copySync(newPath);

                  controller.termCondition = newFile;
                  controller.uploadTermContidition.value =
                      newFile.path.split('/').last;
                }
              }),
              borderWidth: 1,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Container(
                      height: 28,
                      width: 28,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.12),
                        shape: BoxShape.circle,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(6.0),
                        child: SvgPicture.string(SvgStrings.iconDocument),
                      ),
                    ),
                    const SizedBox(width: 7),
                    Obx(
                      () => Expanded(
                        child: Text(
                          controller.uploadTermContidition.value,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: controller.uploadTermContidition.value ==
                                  appLocal.uploadTermsAndCondiition
                              ? FontStyles.fontRegular()
                              : FontStyles.fontRegular(
                                  color: AppColors.colorPrimary,
                                  decoration: TextDecoration.underline,
                                ),
                        ),
                      ),
                    ),
                    Obx(
                      () => controller.uploadTermContidition.value ==
                              appLocal.uploadTermsAndCondiition
                          ? Container()
                          : InkWell(
                              onTap: () => Alerts.alertView(
                                  context: context,
                                  title: appLocal.alert,
                                  content:
                                      appLocal.removeTermsConditionMessage,
                                  defaultActionText: appLocal.yes,
                                  cancelActionText: appLocal.no,
                                  cancelAction: () => Get.back(),
                                  action: () {
                                    Get.back();
                                    controller.isRemoveTerms.value = true;
                                    if (controller.termCondition != null) {
                                      controller.termCondition = null;
                                    }
                                    controller.termsUrl = "";
                                    controller.uploadTermContidition.value =
                                       appLocal.uploadTermsAndCondiition;
                                  }),
                              child: Icon(Icons.delete,
                                  color: AppColors.colorDanger),
                            ),
                    )
                  ],
                ),
              ),
            ),
            ElbaabNetworkEroor(strError: strError),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: kLeftSpace),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: ElbaabBorderButtonWidget(
                      text: appLocal.back,
                      onPress: () => Get.back(),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    flex: 1,
                    child: ElbaabGradientButtonWidget(
                      onPress: () {
                        if (controller.bannerUrl.isEmpty) {
                          if (controller.bannerImage == null) {
                            strError.value =
                                (appLocal.requiredBanner);
                          } else if (controller.logoImage == null) {
                            strError.value =
                                (appLocal.requiredLogo);
                          } else if (formKey.currentState!.validate()) {
                            List<File> arrFiles = [];
                            arrFiles.add(controller.bannerImage!);
                            arrParams.add("banner");
                            arrFiles.add(controller.logoImage!);
                            arrParams.add("logo");
                            if (controller.termCondition != null) {
                              arrFiles.add(controller.termCondition!);
                              arrParams.add("terms");
                            }
                            _request.uploadImage(
                                files: arrFiles,
                                parameters: arrParams,
                                type: "UPLOAD");
                          }
                        } else {
                          String information = prefs.getString(shopInfo) ?? "";
                          String banner = controller.shop?.shopBanner ?? "";
                          String logo = controller.shop?.shopLogo ?? "";
                          String terms =
                              controller.shop?.shopTermsAndConditions ?? "";
                       
                          if (information.isNotEmpty) {
                            Map<String, dynamic> info = jsonDecode(information);
                            if (controller.history.returnValues
                                    ?.contains("Shop Banner") ??
                                false) {
                              banner = info["banner"] ?? "";
                            }
                            if (controller.history.returnValues
                                    ?.contains("Shop Logo") ??
                                false) {
                              logo = info["logo"] ?? "";
                            }
                            if (controller.history.returnValues
                                    ?.contains("Shop Terms And Conditions") ??
                                false) {
                              terms = info["terms"] ?? "";
                            }
                          }

                          if ((controller.history.returnValues
                                      ?.contains("Shop Banner") ??
                                  false) &&
                              (controller.bannerImage == null &&
                                  banner ==
                                      (controller.shop?.shopBanner ?? ""))) {
                            strError.value = ("Please Update Your Banner");
                          } else if ((controller.history.returnValues
                                      ?.contains("Shop Logo") ??
                                  false) &&
                              controller.logoImage == null &&
                              logo == (controller.shop?.shopLogo ?? "")) {
                            strError.value = ("Please Update Your Shop Logo");
                          } else if ((controller.history.returnValues
                                      ?.contains("Shop Terms And Conditions") ??
                                  false) &&
                              controller.termCondition == null &&
                              terms ==
                                  (controller.shop?.shopTermsAndConditions ??
                                      "") &&
                              controller.isRemoveTerms.value == false) {
                            strError.value =
                                ("Please Update Your Shop Terms And Conditions");
                          } else {
                            List<File> arrFiles = [];
                            if (controller.bannerImage != null) {
                              arrFiles.add(controller.bannerImage!);
                              arrParams.add("banner");
                            }
                            if (controller.logoImage != null) {
                              arrFiles.add(controller.logoImage!);
                              arrParams.add("logo");
                            }
                            if (controller.termCondition != null) {
                              arrFiles.add(controller.termCondition!);
                              arrParams.add("terms");
                            }
                            if (arrFiles.isNotEmpty) {
                              _request.uploadImage(
                                  files: arrFiles,
                                  parameters: arrParams,
                                  type: "UPLOAD");
                            } else {
                              if (controller.shop != null) {
                                bool requiredUpdate = false;
                                if (controller.slogan !=
                                    (controller.shop?.shopSlogan ?? "")) {
                                  requiredUpdate = true;
                                } else if (controller.description !=
                                    (controller.shop?.shopDescription ?? "")) {
                                  requiredUpdate = true;
                                } else if (controller.freeDeliveryTaget.value !=
                                    (controller.shop?.freeDeliveryTarget ??
                                        false)) {
                                  requiredUpdate = true;
                                } else if (controller.freeDeliveryTaget.value ==
                                        true &&
                                    controller.targetPrice !=
                                        (controller.shop?.targetPriceForFdt ??
                                            "")) {
                                  requiredUpdate = true;
                                } else if ((controller
                                                .shop?.shopTermsAndConditions ??
                                            "")
                                        .isNotEmpty &&
                                    controller.isRemoveTerms.value == true) {
                                  requiredUpdate = true;
                                } else if (controller.termsUrl !=
                                    (controller.shop?.shopTermsAndConditions ??
                                        "")) {
                                  requiredUpdate = true;
                                }
                                if (formKey.currentState!.validate()) {
                                  if (requiredUpdate) {
                                    updateInfo(
                                        controller.bannerUrl,
                                        controller.logoUrl,
                                        controller.termsUrl);
                                  } else {
                                    tabPosition(1);
                                  }
                                }
                              } else {
                                updateInfo(controller.bannerUrl,
                                    controller.logoUrl, controller.termsUrl);
                              }
                            }
                          }
                        }
                      },
                      text: appLocal.next,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 60),
          ],
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == "UPLOAD") {
      UploadImageModel model = UploadImageModel.fromJson(response);
      if (model.status == statusOK) {
        if (controller.bannerUrl.isNotEmpty) {
          String shopLogo = controller.logoUrl;
          String shopBanner = controller.bannerUrl;
          String termsAndConditions =
              controller.isRemoveTerms.value ? "" : controller.termsUrl;
          if (arrParams.isNotEmpty && arrParams.contains("banner")) {
            shopBanner = model.fileList?[0].fileUrls?[0] ?? "";
          }
          if (arrParams.isNotEmpty && arrParams.contains("logo")) {
            int index = arrParams.indexOf("logo");
            shopLogo = model.fileList?[index].fileUrls?[0] ?? "";
          }
          if (arrParams.isNotEmpty && arrParams.contains("terms")) {
            int index = arrParams.indexOf("terms");
            controller.isRemoveTerms.value = false;
            termsAndConditions = model.fileList?[index].fileUrls?[0] ?? "";
          }
          controller.bannerImage = null;
          controller.logoImage = null;
          controller.termCondition = null;
          updateInfo(shopBanner, shopLogo, termsAndConditions);
        } else {
          String banner = model.fileList?[0].fileUrls?[0] ?? "";
          String logo = model.fileList?[1].fileUrls?[0] ?? "";
          String terms = "";
          if (arrParams.contains("terms")) {
            int index = arrParams.indexOf("terms");
            terms = model.fileList?[index].fileUrls?[0] ?? "";
          }
          updateInfo(banner, logo, terms);
        }
      }
    }
  }

  updateInfo(String banner, String logo, String terms) {
    Map<String, dynamic> info = {
      'banner': banner,
      'logo': logo,
      'terms': controller.isRemoveTerms.value ? "" : terms,
      "slogan": controller.slogan,
      "description": controller.description,
      "freeDelivery": controller.freeDeliveryTaget.value,
      "targetPrice": controller.targetPrice
    };
    String information = jsonEncode(info);
    prefs.setString("information", information);
    
    controller.updateshopInfo();
    tabPosition(1);
  }
}
