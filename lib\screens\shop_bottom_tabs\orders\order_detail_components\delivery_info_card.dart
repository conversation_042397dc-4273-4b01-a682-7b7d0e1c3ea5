import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';

class DeliveryInfoCard extends StatelessWidget {
  RxBool isExpanded = false.obs;
  final OrderItems orderItems;

  DeliveryInfoCard({Key? key, required this.orderItems}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.headerColorDark,
              AppColors.headerColorDark.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: () => isExpanded.value = !isExpanded.value,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: SvgPicture.string(
                          SvgStrings.iconDeliveryBlue,
                          height: 24,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          appLocal.deliveryDetails,
                          style: FontStyles.fontMedium(
                            fontSize: 18,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 300),
                        tween: Tween(
                          begin: 0,
                          end: isExpanded.value ? math.pi : 0,
                        ),
                        builder: (_, value, child) => Transform.rotate(
                          angle: value,
                          child: Icon(
                            Icons.expand_more_rounded,
                            color: Colors.white.withOpacity(0.9),
                            size: 30,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                height: isExpanded.value ? null : 0,
                child: ClipRect(
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 300),
                    opacity: isExpanded.value ? 1.0 : 0.0,
                    child: _buildExpandedContent(context),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExpandedContent(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;

    int pickupIndex = (orderItems.orderItemStatus ?? [])
        .indexWhere((element) => element.status == "On the way for pickup");
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        children: [
          const Divider(color: Colors.white24, thickness: 1),
          const SizedBox(height: 12),
          _buildInfoItem(
            SvgStrings.userProfileIcon,
            appLocal.driverName,
            orderItems.deliveryDetails?.deliveryDriverId?.name ?? "",
          ),
          _buildInfoItem(
            SvgStrings.iconDelivery,
            appLocal.deliveryCompany,
            orderItems.courrierId?.companyName ?? "",
          ),
          _buildInfoItem(
            SvgStrings.iconOrderLocationPin,
            appLocal.officeAddress,
            orderItems.courrierId?.officeAddress ?? "",
          ),
          _buildInfoItem(SvgStrings.iconCallBlue, appLocal.phone,
              orderItems.courrierId?.phoneNumber ?? ""),
          if (pickupIndex != -1)
            _buildInfoItem(
              SvgStrings.iconCallBlue,
              appLocal.phone,
              GlobalMethods.convertTimeFormate(
                  orderItems.orderItemStatus?[pickupIndex].createdAt ?? "",
                  format: "dd MMM, yyyy hh:mm a"),
            ),
          _buildInfoItem(
            SvgStrings.iconDelivery,
            orderItems.orderItemStatus?.last.status == "Delivered"
                ? appLocal.delivered
                : appLocal.estimatedDelivery,
            orderItems.orderItemStatus?.last.status == "Delivered"
                ? orderItems.orderItemStatus?.last.createdAt ?? ""
                : orderItems.estimatedDeliveryTime ?? "",
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String icon, String label, String text) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: SvgPicture.string(
              icon,
              height: 18,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.6),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  text,
                  style: FontStyles.fontMedium(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
