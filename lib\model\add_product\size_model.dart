class SizesModel {
  final String? typename;
  final List<Sizes>? sizes;
  final int? status;
  final String? message;

  SizesModel({
    this.typename,
    this.sizes,
    this.status,
    this.message,
  });

  SizesModel.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      sizes = (json['sizes'] as List?)?.map((dynamic e) => Sizes.fromJson(e as Map<String,dynamic>)).toList(),
      status = json['status'] as int?,
      message = json['message'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'sizes' : sizes?.map((e) => e.toJson()).toList(),
    'status' : status,
    'message' : message
  };
}

class Sizes {
  final String? typename;
  final String? sizeUnit;
  final List<String>? sizeValues;
  final Sizes? ar;

  Sizes({
    this.typename,
    this.sizeUnit,
    this.sizeValues,
    this.ar,
  });

  Sizes.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      sizeUnit = json['sizeUnit'] as String?,
      ar = json['ar'] != null ? Sizes.fromJson(json['ar'] as Map<String,dynamic>) : null,
      sizeValues = (json['sizeValues'] as List?)?.map((dynamic e) => e as String).toList();

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'sizeUnit' : sizeUnit,
    'sizeValues' : sizeValues,
    'ar' : ar?.toJson() 
  };
}