import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/shop/ViewShopModel.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';

class ShopAbout extends StatelessWidget {
  final Shop shop = Get.arguments[0];

  var titlePadding = const EdgeInsets.only(left: 6, top: 24);
  var boxDecoration = BoxDecoration(
    color: AppColors.feildColorDark,
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.06),
        blurRadius: 12,
        offset: const Offset(0, 6),
      )
    ],
    borderRadius: BorderRadius.circular(4),
  );

  ShopAbout({super.key});

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.aboutTheShop,
        leadingBack: true,
      ),
      body: Container(
        margin: const EdgeInsets.only(
            left: kLeftSpace, right: kRightSpace, top: 16).r,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (shop.shopDescription?.isNotEmpty ?? false)
                Padding(
                  padding: titlePadding,
                  child: Text(appLocal.description,
                      style: FontStyles.fontMedium()),
                ),
              if (shop.shopDescription?.isNotEmpty ?? false)
                Container(
                  width: MediaQuery.of(context).size.width,
                  margin: const EdgeInsets.only(top: 10).r,
                  decoration: boxDecoration,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0).r,
                    child: Text(
                      shop.shopDescription ?? "",
                      style: FontStyles.fontRegular(fontSize: 12),
                    ),
                  ),
                ),
              if (shop.shopTermsAndConditions?.isNotEmpty ?? false)
                Padding(
                  padding: titlePadding,
                  child: Text(
                    appLocal.termsAndCondition,
                    style: FontStyles.fontMedium(),
                  ),
                ),
              if (shop.shopTermsAndConditions?.isNotEmpty ?? false)
                 SizedBox(height: 10.h),
              if (shop.shopTermsAndConditions?.isNotEmpty ?? false)
                InkWell(
                  onTap: () => GlobalMethods.launchInWebView(
                      shop.shopTermsAndConditions ?? ""),
                  child: Container(
                    height: 60.h,
                    decoration: boxDecoration,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24).r,
                      child: Row(
                        children: <Widget>[
                          SvgPicture.string(SvgStrings.iconDocument),
                           SizedBox(width: 8.w),
                          Expanded(
                            child: Text(
                              (shop.shopTermsAndConditions ?? "")
                                  .split('Z')
                                  .last,
                              maxLines: 1,
                              style: FontStyles.fontRegular(
                                  decoration: TextDecoration.underline,
                                  color: AppColors.colorPrimary),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
               SizedBox(height: 20.h),
              Container(
                decoration: boxDecoration,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                          left: kLeftSpace, top: 16, right: kRightSpace).r,
                      child: Text(
                        appLocal.pickupAddress,
                        style: FontStyles.fontBold(fontSize: 12),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          left: kLeftSpace, top: 10, right: kRightSpace).r,
                      child: Row(
                        children: [
                          SvgPicture.string(
                            SvgStrings.iconAbout,
                            color: AppColors.colorPrimary,
                          ),
                           SizedBox(width: 10.w),
                          Expanded(
                            child: Text(
                              appLocal.pickupAddressUsageContant,
                              style: FontStyles.fontRegular(
                                fontSize: 12,
                                color: Colors.white.withOpacity(0.5),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                     SizedBox(height: 10.h),
                    ListView.builder(
                        itemCount: shop.shopPickupAddresses?.length ?? 0,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          ShopPickupAddresses model =
                              (shop.shopPickupAddresses ?? [])[index];
                          return ElbaabFeildContainerWidget(
                            borderWidth: 1,
                            edgeInsets: const EdgeInsets.only(
                                left: 10, right: 10, bottom: 10).r,
                            child: Column(
                              children: [
                                _infoListTile(appLocal.city,
                                    model.pickUpCity ?? "", true),
                                _infoListTile(
                                    appLocal.address, model.pickUpAddress ?? "", true),
                                if (model
                                        .pickUpMapLocation?.coordinates?.isNotEmpty ??
                                    false)
                                  _infoListTile(
                                      appLocal.location, appLocal.mapLocation, true,
                                      isLink: true,
                                      locationLat: model.pickUpMapLocation
                                              ?.coordinates?.first ??
                                          0.0,
                                      locationLong: model.pickUpMapLocation
                                              ?.coordinates?.last ??
                                          0.0),
                                _infoListTile(
                                    appLocal.number,
                                    model.pickUpContactMobileNumber?.number ??
                                        "",
                                    true),
                                _infoListTile(
                                    appLocal.landNumber,
                                    model.pickUpContactLandNumber?.number ?? "",
                                    true),
                              ],
                            ),
                          );
                        })
                  ],
                ),
              ),
              Container(
                decoration: boxDecoration,
                margin: const EdgeInsets.symmetric(vertical: 20),
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    Text(
                    appLocal.customerContact,
                      style: FontStyles.fontBold(fontSize: 12),
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        SvgPicture.string(
                          SvgStrings.iconAbout,
                          color: AppColors.colorPrimary,
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                          appLocal.contactDetailsInfo,
                            style: FontStyles.fontRegular(
                                fontSize: 12,
                                color: Colors.white.withOpacity(0.5)),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    _infoListTile(
                        appLocal.phoneNumber,
                        shop.shopContactDetails?.phoneNumber ?? "",
                        false),
                    if (shop.shopContactDetails?.whatsUpPhoneNumber
                            ?.isNotEmpty ??
                        false)
                      _infoListTile(
                          appLocal.whatsapp,
                          shop.shopContactDetails?.whatsUpPhoneNumber ?? "",
                          true),
                    if (shop.shopContactDetails?.email?.isNotEmpty ?? false)
                      _infoListTile(appLocal.emailAddress,
                          (shop.shopContactDetails?.email ?? ""), false),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _infoListTile(String title, String value, bool requiredTopSpace,
      {bool isLink = false, double locationLat = 0, double locationLong = 0}) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: 16, vertical: requiredTopSpace ? 5 : 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              title,
              style: FontStyles.fontRegular(fontSize: 12),
            ),
          ),
          Expanded(
            flex: 2,
            child: InkWell(
              onTap: () {
                if (isLink) {
                  if (locationLat != 0 && locationLong != 0) {
                    GlobalMethods.openMap(locationLat, locationLong);
                  } else {
                    GlobalMethods.launchInWebView(value);
                  }
                }
              },
              child: Text(
                isLink ? value.split('/').last : value,
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  decoration:
                      isLink ? TextDecoration.underline : TextDecoration.none,
                  color: isLink
                      ? AppColors.colorPrimary
                      : Colors.white.withOpacity(0.5),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
