import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/components/otp_dailog.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/provider/account_setting_info_provider.dart';
import 'package:overolasuppliers/provider/language_change.dart';
import 'package:overolasuppliers/provider/security_session_provider.dart';
import 'package:overolasuppliers/provider/shop_info_provider.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:overolasuppliers/screens/products/add_product/model/account_setting_model.dart';
import 'package:overolasuppliers/widgets/elbaab_homepage_header.dart';
import 'package:provider/provider.dart';

class Profile extends StatefulWidget {
  const Profile({Key? key}) : super(key: key);

  @override
  State<Profile> createState() => _ProfileState();
}

class _ProfileState extends State<Profile>
    with AutomaticKeepAliveClientMixin<Profile>
    implements ServerResponse {
  late GraphQlInitilize _request;

  String termsAndConditions = '', termsAndConditionAr = '';

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    Future.delayed(
        Duration.zero,
        () => {
              _request.runQueryWithCache(
                context: context,
                query: GraphQlQuries.getTermsAndConditions,
                isRequiredLoader: false,
                type: "TermsAndConditions",
              ),
            });
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final shopInfo = Provider.of<ShopInfoProvider>(context).getShopInfo;
    final sessionDetails = Provider.of<SecuritySessionProvider>(context);
    final appLocal = AppLocalizations.of(context)!;
    if (context.watch<UpdatedInfo>().getTabPosition() == 3) {
      _request.runQueryWithCache(
        context: context,
        query: GraphQlQuries.getAccountSetting,
        isRequiredLoader: false,
        type: "getAccountSetting",
      );
    }
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // Custom App Bar with Profile Header
          SliverAppBar(
            expandedHeight: 200.h,
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            toolbarHeight: kToolbarHeight + MediaQuery.of(context).padding.top,
            flexibleSpace: LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
                final top = constraints.biggest.height;
                final expandRatio =
                    ((top - kToolbarHeight) / (200.h - kToolbarHeight))
                        .clamp(0.0, 1.0);
                final isCollapsed = expandRatio < 0.5;

                return Stack(
                  fit: StackFit.expand,
                  children: [
                    // Expanded Profile Content
                    if (!isCollapsed)
                      FlexibleSpaceBar(
                        background: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                AppColors.colorPrimary.withOpacity(0.8),
                                AppColors.headerColorDark,
                              ],
                            ),
                          ),
                          child: SafeArea(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircleAvatar(
                                  radius: 40,
                                  backgroundColor:
                                      AppColors.colorSecondary.withOpacity(0.2),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(40),
                                    child: GlobalMethods.netWorkImage(
                                      shopInfo.shop?.shopLogo ?? "",
                                      BorderRadius.circular(40),
                                      BoxFit.cover,
                                    ),
                                  ),
                                ),
                                SizedBox(height: 12.h),
                                Text(
                                  Provider.of<ShopInfoProvider>(context)
                                      .userName,
                                  style: FontStyles.fontSemibold(fontSize: 20),
                                ),
                                SizedBox(height: 4.h),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.location_on,
                                      size: 16,
                                      color: AppColors.colorPrimary,
                                    ),
                                    SizedBox(width: 4.w),
                                    Text(
                                      appLocal.pickUpCity(shopInfo
                                              .shop
                                              ?.shopPickupAddresses
                                              ?.first
                                              .pickUpCity ??
                                          ""),
                                      style: FontStyles.fontRegular(
                                        color: AppColors.colorPrimary,
                                      ),
                                    ),
                                    SizedBox(width: 16.w),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 8.w, vertical: 4.h),
                                      decoration: BoxDecoration(
                                        color: AppColors.colorSecondaryYellow
                                            .withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.star,
                                            color:
                                                AppColors.colorSecondaryYellow,
                                            size: 16,
                                          ),
                                          SizedBox(width: 4.w),
                                          Text(
                                            '${shopInfo.shop?.shopRate ?? 0}',
                                            style: FontStyles.fontSemibold(
                                              fontSize: 14,
                                              color: AppColors
                                                  .colorSecondaryYellow,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                    // Collapsed Header using ElbaabHomePageHeader
                    if (isCollapsed)
                    const  Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child:  ElbaabHomePageHeader(
                            title: EnStrings.myProfile),
                      ),
                  ],
                );
              },
            ),
          ),

          // Profile Options
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    appLocal.myInformation,
                    style: FontStyles.fontSemibold(fontSize: 18),
                  ),
                  SizedBox(height: 16.h),

                  // Account Management Card
                  _buildProfileCard(
                    icon: Icons.manage_accounts,
                    title: appLocal.accountAndMangment,
                    subtitle:
                        sessionDetails.sessionExpire ? 'Locked' : 'Active',
                    iconColor: AppColors.colorPrimary,
                    onTap: () async {
                      
                      if (sessionDetails.sessionExpire) {
                        _request.runQuery(
                            context: context,
                            query: GraphQlQuries.sendSettingsOtp,
                            type: "sendSettingsOtp");
                      } else {
                        var result =
                            await Get.toNamed(RouteNames.accountMangmentScreen);
                        if (result != null) {
                          _request.runQueryWithCache(
                            context: Get.context!,
                            isRequiredLoader: false,
                            query: GraphQlQuries.getAccountSetting,
                            type: "getAccountSetting",
                          );
                        }
                      }
                    },
                    trailing: sessionDetails.sessionExpire
                        ? Icon(Icons.lock, color: AppColors.colorDanger)
                        : Icon(Icons.lock_open,
                            color: AppColors.colorSecondary),
                  ),

                  SizedBox(height: 12.h),

                  // Settings Card
                  _buildProfileCard(
                    icon: Icons.settings,
                    title: appLocal.appSettings,
                    iconColor: AppColors.colorSecondary,
                    onTap: () async {
                      var result =
                          await Get.toNamed(RouteNames.appSettingScreen);
                      if (result != null) {
                        _request.runQueryWithCache(
                          context: Get.context!,
                          isRequiredLoader: false,
                          query: GraphQlQuries.getAccountSetting,
                          type: "getAccountSetting",
                        );
                      }
                    },
                  ),

                  SizedBox(height: 24.h),

                  Text(
                    'Other Options',
                    style: FontStyles.fontSemibold(fontSize: 18),
                  ),
                  SizedBox(height: 24.h),

                  // Additional Options Grid
                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.zero,
                    crossAxisCount: 2,
                    mainAxisSpacing: 16.h,
                    crossAxisSpacing: 16.w,
                    childAspectRatio: 1.5,
                    children: [
                      _buildOptionCard(
                        icon: Icons.delete,
                        title: appLocal.deleteProduct,
                        color: AppColors.colorDanger.withOpacity(0.1),
                        iconColor: AppColors.colorDanger,
                        onTap: () => Get.toNamed(RouteNames.trashProductScreen),
                      ),
                      _buildOptionCard(
                        icon: Icons.help_outline,
                        title: appLocal.faq,
                        color: AppColors.colorSecondary.withOpacity(0.1),
                        iconColor: AppColors.colorSecondary,
                        onTap: () => Get.toNamed(RouteNames.faqsScreen),
                      ),
                      _buildOptionCard(
                        icon: Icons.support_agent,
                        title: appLocal.customerService,
                        color: AppColors.colorPrimary.withOpacity(0.1),
                        iconColor: AppColors.colorPrimary,
                        onTap: () =>
                            Get.toNamed(RouteNames.elbaabSupportContactScreen),
                      ),
                      _buildOptionCard(
                        icon: Icons.language,
                        title: appLocal.changeLanguage,
                        color: AppColors.colorSecondaryYellow.withOpacity(0.1),
                        iconColor: AppColors.colorSecondaryYellow,
                        onTap: () {
                          String lang = "";
                          if (Get.locale?.languageCode == "ar") {
                            Provider.of<LanguageChnangeProvider>(context,
                                    listen: false)
                                .changeLanguage(context, 'en');
                            lang = "en";
                          } else {
                            Provider.of<LanguageChnangeProvider>(context,
                                    listen: false)
                                .changeLanguage(context, 'ar');
                            lang = "ar";
                          }
                          _request.runMutation(
                              context: context,
                              query: GraphQlQuries.updateUserLang,
                              type: "updateUserLang",
                              variables: {"lang": lang});
                        },
                      ),
                      _buildOptionCard(
                        icon: Icons.privacy_tip_outlined,
                        title: appLocal.privacyPolicy,
                        color: AppColors.colorPrimary.withOpacity(0.1),
                        iconColor: AppColors.colorPrimary,
                        onTap: () =>
                            htmlViewer(context, appLocal.privacyPolicy),
                      ),
                    ],
                  ),

                  SizedBox(height: 24.h),

                  // Logout Button
                  ElevatedButton(
                    onPressed: () => Alerts.alertView(
                        context: context,
                        title: EnStrings.alert,
                        content: EnStrings.logoutMessage,
                        cancelActionText: EnStrings.no,
                        defaultActionText: EnStrings.yes,
                        cancelAction: () => Get.back(),
                        action: () {
                          Get.back();
                          _request.runMutation(
                            context: context,
                            query: GraphQlQuries.removeFireBaseToken,
                            type: "logout",
                            variables: {
                              "firebaseDeviceToken": firebaseDeviceToken
                            },
                          );
                        }),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.colorDanger.withOpacity(0.1),
                      foregroundColor: AppColors.colorDanger,
                      minimumSize: Size(double.infinity, 50.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.logout),
                        SizedBox(width: 8.w),
                        Text(
                          appLocal.logout,
                          style: FontStyles.fontSemibold(fontSize: 16),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 24.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  htmlViewer(context, privacyPolicy) {
    showModalBottomSheet(
      context: MyApp.navigatorKey.currentContext!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          margin: EdgeInsets.only(top: 60.h),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.headerColorDark.withOpacity(0.95),
                AppColors.backgroundColorDark.withOpacity(0.98),
              ],
            ),
            borderRadius: BorderRadius.vertical(top: Radius.circular(40.r)),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 1.5,
            ),
          ),
          child: Column(
            children: [
              // Simplified Trendy Header
              Container(
                padding: EdgeInsets.fromLTRB(24.w, 16.h, 24.w, 16.h),
                child: Column(
                  children: [
                    // Modern Drag Handle
                    Container(
                      width: 38.w,
                      height: 4.h,
                      margin: EdgeInsets.only(bottom: 20.h),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),

                    // Minimalist Header Content
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Title with Icon
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(8.r),
                              decoration: BoxDecoration(
                                color: AppColors.colorPrimary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Icon(
                                Icons.shield_outlined,
                                color: AppColors.colorPrimary,
                                size: 20.sp,
                              ),
                            ),
                            SizedBox(width: 12.w),
                            Text(
                              privacyPolicy,
                              style: FontStyles.fontSemibold(
                                fontSize: 18,
                                color: Colors.white.withOpacity(0.95),
                              ),
                            ),
                          ],
                        ),
                        // Modern Close Button
                        InkWell(
                          onTap: () => Get.back(),
                          borderRadius: BorderRadius.circular(12.r),
                          child: Container(
                            padding: EdgeInsets.all(8.r),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Icon(
                              Icons.close_rounded,
                              color: Colors.white.withOpacity(0.7),
                              size: 20.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Content Area
              Expanded(
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Stack(
                    children: [
                      // Trendy Background Elements
                      Positioned(
                        right: -100.w,
                        top: 50.h,
                        child: Container(
                          height: 200.h,
                          width: 200.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                AppColors.colorPrimary.withOpacity(0.1),
                                AppColors.colorPrimary.withOpacity(0.05),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        left: -80.w,
                        bottom: 100.h,
                        child: Container(
                          height: 160.h,
                          width: 160.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                AppColors.colorSecondary.withOpacity(0.1),
                                AppColors.colorSecondary.withOpacity(0.05),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                      ),

                      // Content with Modern Scrollbar
                      Scrollbar(
                        radius: Radius.circular(20.r),
                        thickness: 6,
                        child: SingleChildScrollView(
                          physics: const BouncingScrollPhysics(),
                          child: Padding(
                            padding: EdgeInsets.symmetric(vertical: 24.h),
                            child: Container(
                              padding: EdgeInsets.all(24.r),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.05),
                                borderRadius: BorderRadius.circular(24.r),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.1),
                                ),
                              ),
                              child: HtmlWidget(
                                Get.locale?.languageCode == "en"
                                    ? termsAndConditions
                                    : termsAndConditionAr,
                                onErrorBuilder: (context, element, error) =>
                                    Text('$element error: $error'),
                                onLoadingBuilder:
                                    (context, element, loadingProgress) =>
                                        Center(
                                  child: Container(
                                    padding: EdgeInsets.all(16.r),
                                    decoration: BoxDecoration(
                                      color: AppColors.colorPrimary
                                          .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(16.r),
                                    ),
                                    child: CircularProgressIndicator(
                                      color: AppColors.colorPrimary,
                                      strokeWidth: 3,
                                    ),
                                  ),
                                ),
                                renderMode: RenderMode.column,
                                textStyle: FontStyles.fontRegular(
                                  fontSize: 15,
                                  color: Colors.white.withOpacity(0.85),
                                  height: 1.6,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == "TermsAndConditions") {
      if (response["status"] == statusOK) {
        if (response['termsAndConditions'] != null) {
          termsAndConditions =
              response['termsAndConditions']["termsAndConditionsText"];
          if (response['termsAndConditions']["ar"] != null &&
              response['termsAndConditions']["ar"]["termsAndConditionsText"] !=
                  null) {
            termsAndConditionAr =
                response['termsAndConditions']["ar"]["termsAndConditionsText"];
          } else {
            termsAndConditionAr =
                response['termsAndConditions']["termsAndConditionsText"];
          }
        }
      }
    } else if (type == "logout") {
      BaseModel model = BaseModel.fromJson(response);
      if (model.status == statusOK) {
        prefs.remove(merchantID);
        prefs.remove(ownerName);
        prefs.remove(authToken);
        Provider.of<SecuritySessionProvider>(context, listen: false)
            .closeSession();
        userAuthToken = "";
        supplierID = "";
        prefs.setString("information", "");
        prefs.setString("customerContact", "");
        prefs.setString("pickupAddress", "");
        Get.offAllNamed(RouteNames.loginScreen);
      }
    } else if (type == "getAccountSetting") {
      AccountSettingModel model = AccountSettingModel.fromJson(response);
      if (model.status == statusOK) {
        Provider.of<AccountSettingProvider>(context, listen: false)
            .setAccountSetting(model);
      }
    } else if (type == "sendSettingsOtp") {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => OtpDialog(validOtp: () async {
          var result = await Get.toNamed(RouteNames.accountMangmentScreen);
          if (result != null) {
            _request.runQueryWithCache(
              context: Get.context!,
              isRequiredLoader: false,
              query: GraphQlQuries.getAccountSetting,
              type: "getAccountSetting",
            );
          }
        }),
      );
    }
  }
}

Widget _buildProfileCard({
  required IconData icon,
  required String title,
  String? subtitle,
  required Color iconColor,
  required VoidCallback onTap,
  Widget? trailing,
}) {
  return Card(
    elevation: 0,
    color: iconColor.withOpacity(0.1),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    child: ListTile(
      onTap: onTap,
      leading: Icon(icon, color: iconColor),
      title: Text(title, style: FontStyles.fontSemibold()),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
    ),
  );
}

Widget _buildOptionCard({
  required IconData icon,
  required String title,
  required Color color,
  required Color iconColor,
  required VoidCallback onTap,
}) {
  return InkWell(
    onTap: onTap,
    borderRadius: BorderRadius.circular(16.r),
    child: Container(
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 24.sp,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              title,
              style: FontStyles.fontSemibold(
                fontSize: 14,
                color: Colors.white.withOpacity(0.9),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    ),
  );
}
