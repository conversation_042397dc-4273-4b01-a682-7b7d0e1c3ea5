import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/add_product/product_variation_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';

class AddProductStepThree extends GetView<AddProductController>
    with InputValidationUtil {
  String returnFitsId = "";

  AddProductStepThree({super.key});
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    Future.delayed(Duration.zero, () => checkReturnId());
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Form(
        key: controller.shipmentFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16).r,
              child: Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    appLocal.shipmentInformation,
                    style: FontStyles.fontMedium(fontSize: 15),
                  ),
                  IconButton(
                    onPressed: () => BottomSheets.showAlertMessageBottomSheet(
                        appLocal.infoForDeliveryCompany,
                        appLocal.shipmentInformation,
                        context),
                    icon: Icon(
                      Icons.info,
                      color: AppColors.colorPrimary,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 17,horizontal: 16).r,
                  child: Text(appLocal.shipmentFits,
                      style: FontStyles.fontMedium(fontSize: 15)),
                ),
                SizedBox(
                  height: 45.h,
                  child: Obx(
                    () => ListView.builder(
                        itemCount: controller.sizeFits.length,
                        padding: const EdgeInsets.symmetric(horizontal: 16).r,
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 11).r,
                            child: InkWell(
                              onTap: () {
                                controller.selectedShipmentFits.value = index;
                                controller.selectedSize.value =
                                    controller.sizeFits[index];
                                controller.shipmentFitsId =
                                    controller.sizeFits[index].id ?? "";
                                if (returnFitsId.isNotEmpty &&
                                    returnFitsId !=
                                        controller.shipmentFitsId) {
                                  controller.isRequiredUpdate.value = "";
                                } else if (returnFitsId.isNotEmpty &&
                                    returnFitsId ==
                                        controller.shipmentFitsId) {
                                  controller.isRequiredUpdate.value = "true";
                                }
                              },
                              child: Obx(
                                () => Container(
                                  height: 45.h,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 15).r,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      width: 2,
                                      color: returnFitsId ==
                                              controller.sizeFits[index].id
                                          ? AppColors.colorDanger
                                          : controller.selectedShipmentFits
                                                      .value ==
                                                  index
                                              ? Colors.transparent
                                              : AppColors.colorGray,
                                    ),
                                    color: returnFitsId ==
                                            controller.sizeFits[index].id
                                        ? AppColors.colorDanger
                                            .withOpacity(0.5)
                                        : controller.selectedShipmentFits
                                                    .value ==
                                                index
                                            ? AppColors.colorPrimary
                                            : Colors.transparent,
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Center(
                                    child: Text(
                                      appLocal.localeName == "en"
                                          ? controller.sizeFits[index].name ??
                                              ""
                                          : controller.sizeFits[index]
                                                  .sizeCategoriesAr?.name ??
                                              "",
                                      style: FontStyles.fontRegular(
                                          fontSize: 16),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        }),
                  ),
                ),
                Container(
                  height: 150.h,
                  margin: const EdgeInsets.only(top: 31,left: kLeftSpace,right: kRightSpace).r,
                  decoration: BoxDecoration(
                    border: Border.all(
                      width: 2,
                      color: AppColors.colorGray,
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(width: 5),
                      Expanded(
                          flex: 2,
                          child: SvgPicture.string(SvgStrings.dimensionsBox)),
                      Expanded(
                        flex: 3,
                        child: Obx(
                          () => Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Text(
                                appLocal.maxBoxDimension,
                                textAlign: TextAlign.center,
                                style: FontStyles.fontRegular(
                                    fontSize: 12,
                                    color: AppColors.colorPrimary),
                              ),
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      "${appLocal.width} : ${controller.selectedSize.value.dimensions?.width ?? "0"}",
                                      textAlign: TextAlign.center,
                                      style: FontStyles.fontRegular(
                                          fontSize: 11),
                                    ),
                                  ),
                                  Expanded(
                                    child: Text(
                                      "${appLocal.height} : ${controller.selectedSize.value.dimensions?.height ?? "0"}",
                                      textAlign: TextAlign.center,
                                      style: FontStyles.fontRegular(
                                          fontSize: 11),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      "${appLocal.length} : ${controller.selectedSize.value.dimensions?.length ?? "0"}",
                                      textAlign: TextAlign.center,
                                      style: FontStyles.fontRegular(
                                          fontSize: 11),
                                    ),
                                  ),
                                  Expanded(
                                    child: Text(
                                      "${appLocal.weight} : ${controller.selectedSize.value.dimensions?.weight ?? "0"}",
                                      textAlign: TextAlign.center,
                                      style: FontStyles.fontRegular(
                                          fontSize: 11),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 30,left: kLeftSpace,right: kRightSpace).r,
              child: Text(
                appLocal.maxNumberItemsPerOrder,
                style: FontStyles.fontRegular(),
              ),
            ),
            ElbaaabInputTextField(
              margin: const EdgeInsets.only(top: 10,left: kLeftSpace,right: kRightSpace).r,
              onChanged: (value) => controller.itemsPerOrder = value,
              inputFormatter: "[0-9]",
              initialValue: controller.itemsPerOrder,
              textDirection: appLocal.localeName == "ar"
                  ? TextDirection.rtl
                  : TextDirection.ltr,
              validator: (v) {
                if (v!.isEmpty) {
                  return appLocal.fieldRequired;
                } else if (controller.arrVariations.isNotEmpty && v != "0") {
                  List<int> arrQty = [];
                  for (Variants variant in controller.arrVariations) {
                    for (Variations variation in variant.variations ?? []) {
                      arrQty.add(variation.variantQte ?? 0);
                    }
                  }
                  if (arrQty.reduce(max) < int.parse(v)) {
                    return appLocal.itemPerOrderQuantityLessVariantAvailable;
                  } else {
                    return null;
                  }
                } else if (controller.arrVariations.isEmpty &&
                    (int.parse(controller.qty) < int.parse(v))) {
                  return appLocal.itemPerOrderQuantityLessAvailable;
                } else if (v == "0") {
                  return appLocal.zeroNotAcceptable;
                } else {
                  return null;
                }
              },
              inputType: TextInputType.number,
              label: appLocal.itemPerOrderFeildLabel,
              hint: appLocal.itemPerOrderFeildHint,
            ),
            Obx(
              () => controller.isRequiredUpdate.value.isNotEmpty
                  ? Padding(
                      padding: const EdgeInsets.only(top: 16,left: kLeftSpace,right: kRightSpace).r,
                      child: Text(
                        appLocal.adminRejectShipmentFitErrorMessage,
                        textAlign: TextAlign.center,
                        style: FontStyles.fontMedium(
                            color: AppColors.colorDanger, height: 2),
                      ),
                    )
                  : Container(),
            ),
             SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  bool validateFeilds() {
    bool requiredUpdate = false;
    if (controller.shipmentFitsId !=
        (controller.product?.productSizeCategory?.id ?? "")) {
      requiredUpdate = true;
    }
    if (controller.itemsPerOrder !=
        ("${controller.product?.itemsPerOrder ?? 0}")) {
      requiredUpdate = true;
    }
    return requiredUpdate;
  }

  setcacheInfo({required String shipmentFitsID}) {
    String selectedSize = jsonEncode(controller.selectedSize);
    Map<String, dynamic> info = {
      'shipmentFitsID': shipmentFitsID,
      "itemsPerOrder": controller.itemsPerOrder,
      "selectedSize": selectedSize
    };
    String shipment = jsonEncode(info);
    prefs.setString(productShipment, shipment);
    Get.toNamed(RouteNames.addProductStepFourScreen);
  }

  checkReturnId() {
    if (controller.validationHistory?.returnValues?.contains("Size Category") ??
        false) {
      returnFitsId = controller.product?.productSizeCategory?.id ?? "";
    }
  }
}
