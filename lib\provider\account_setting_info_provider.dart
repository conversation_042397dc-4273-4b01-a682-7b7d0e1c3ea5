import 'package:flutter/material.dart';
import 'package:overolasuppliers/screens/products/add_product/model/account_setting_model.dart';

class AccountSettingProvider extends ChangeNotifier {
  AccountSettingModel _accountSettingModel = AccountSettingModel();
  AccountSettingModel get getAccountSetting => _accountSettingModel;

  void setAccountSetting(AccountSettingModel result) {
    _accountSettingModel = result;
    notifyListeners();
  }
}
