# Enable Developer Mode for Windows
# This script enables Developer Mode which is required for Flutter symlink support

Write-Host "🔧 Enabling Windows Developer Mode for Flutter Development" -ForegroundColor Cyan
Write-Host "=======================================================" -ForegroundColor Cyan

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "[WARNING] This script should be run as administrator for best results" -ForegroundColor Yellow
    Write-Host "[INFO] Attempting to enable Developer Mode with current privileges..." -ForegroundColor Blue
}

try {
    # Enable Developer Mode via Registry
    Write-Host "[INFO] Enabling Developer Mode via registry..." -ForegroundColor Blue
    
    $registryPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\AppModelUnlock"
    
    # Create the registry key if it doesn't exist
    if (!(Test-Path $registryPath)) {
        New-Item -Path $registryPath -Force | Out-Null
        Write-Host "[INFO] Created registry path: $registryPath" -ForegroundColor Blue
    }
    
    # Set the AllowDevelopmentWithoutDevLicense value to 1
    Set-ItemProperty -Path $registryPath -Name "AllowDevelopmentWithoutDevLicense" -Value 1 -Type DWord
    Write-Host "[SUCCESS] Developer Mode enabled via registry" -ForegroundColor Green
    
    # Also try to enable via Settings URI (this will open Settings app)
    Write-Host "[INFO] Opening Windows Settings to verify Developer Mode..." -ForegroundColor Blue
    Start-Process "ms-settings:developers"
    
    Write-Host ""
    Write-Host "[SUCCESS] Developer Mode configuration completed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Please verify in the Settings app that Developer Mode is enabled:" -ForegroundColor Cyan
    Write-Host "1. In the Settings window that opened, go to 'For developers'" -ForegroundColor White
    Write-Host "2. Turn ON 'Developer Mode'" -ForegroundColor White
    Write-Host "3. Accept any prompts that appear" -ForegroundColor White
    Write-Host ""
    Write-Host "After enabling Developer Mode, you can run Flutter applications." -ForegroundColor Cyan
    
} catch {
    Write-Host "[ERROR] Failed to enable Developer Mode: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual steps to enable Developer Mode:" -ForegroundColor Yellow
    Write-Host "1. Open Windows Settings (Win + I)" -ForegroundColor White
    Write-Host "2. Go to Update `& Security > For developers" -ForegroundColor White
    Write-Host "3. Turn ON 'Developer Mode'" -ForegroundColor White
    Write-Host "4. Accept any prompts that appear" -ForegroundColor White
    Write-Host "5. Restart your computer if prompted" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
