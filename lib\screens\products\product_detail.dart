import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/other/badge_decoration.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/add_product/product_variation_model.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/screens/products/add_product/views/components/image_slider_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_carousel_feild_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';

import '../../model/add_product/view_prduct/product_detail_model.dart';

class ProductDetail extends GetView<AddProductController>
    implements ServerResponse {
  Product product = Get.arguments[0];
  RxList<Variants> arrVariations = <Variants>[].obs;
  RxBool isHide = false.obs;
  late GraphQlInitilize _request;
  String qty = "";
  RxString strError = ''.obs;
  RxInt productAvailableQte = 0.obs;
  int totalOption = 0;

  ProductDetail({super.key});

  void getVariations() {
    arrVariations.clear();
    if (product.productVariants?.isNotEmpty ?? false) {
      _processVariationsByColor();
      _processVariationsBySize();
      _processVariationsByCustomOptions();
    }
  }

  void _processVariationsByColor() {
    if ((product.productOptions?.productColors ?? []).isNotEmpty) {
      for (ProductColors color in product.productOptions!.productColors!) {
        final List<Variations> variations = _getVariationsByColor(color);
        arrVariations.add(Variants(
          colorName: color.colorName,
          colorFamily: color.colorFamily,
          colorIcon: color.colorIcon,
          variations: variations,
        ));
      }
    }
  }

  List<Variations> _getVariationsByColor(ProductColors color) {
    final List<Variations> newOptions = [];

    for (ProductVariants variation in product.productVariants!) {
      if (_matchesColor(color, variation)) {
        if (!newOptions.any((element) => element.id == variation.id)) {
          log("create variation ${jsonEncode(_createVariation(variation))}");
          newOptions.add(_createVariation(variation));
        }
      }
    }
    return newOptions;
  }

  bool _matchesColor(ProductColors color, ProductVariants variation) {
    return (color.colorFamily ==
            variation.variantAttributes?.variantColor?.colorFamily &&
        color.colorName?.trimRight() ==
            variation.variantAttributes?.variantColor?.colorName?.trimRight());
  }

  void _processVariationsBySize() {
    if ((product.productOptions?.productColors?.isEmpty ?? false) &&
        (product.productOptions?.productSizes ?? []).isNotEmpty) {
      for (OptionValues sizeType
          in product.productOptions!.productSizes![0].sizeValues!) {
        final List<Variations> newOptions = _getVariationsBySize(sizeType);
        arrVariations.add(Variants(
          size: sizeType.value,
          unit: product.productOptions!.productSizes![0].sizeUnit ?? "",
          colorName: "",
          colorFamily: "",
          colorIcon: "",
          variations: newOptions,
        ));
      }
    }
  }

  List<Variations> _getVariationsBySize(OptionValues sizeType) {
    final List<Variations> newOptions = [];
    for (ProductVariants variation in product.productVariants!) {
      if (sizeType.value == variation.variantAttributes?.variantSize?.size) {
        newOptions.add(_createVariation(variation));
      }
    }
    return newOptions;
  }

  void _processVariationsByCustomOptions() {
    if ((product.productOptions?.productColors?.isEmpty ?? false) &&
        (product.productOptions?.productSizes?.isEmpty ?? false) &&
        (product.productOptions?.productCustomOptions ?? []).isNotEmpty) {
      final List<Variations> newOptions = _getVariationsByCustomOptions();
      arrVariations.add(Variants(
        colorName: "",
        colorFamily: "",
        colorIcon: "",
        variations: newOptions,
      ));
    }
  }

  List<Variations> _getVariationsByCustomOptions() {
    final List<Variations> newOptions = [];
    for (ProductVariants variation in product.productVariants!) {
      newOptions.add(_createVariation(variation));
    }
    return newOptions;
  }

  Variations _createVariation(ProductVariants variation) {
    log("product variant ${jsonEncode(variation.ar)}");
    return Variations(
      variantName: variation.variantName,
      variantCode: variation.variantCode,
      variantPrice: variation.variantPrice.toString(),
      variantQte: variation.variantQte,
      variantEIN: variation.variantEIN,
      variantManufacturerId: variation.variantManufacturerId,
      variantImages: variation.variantImages,
      variantAttributes: variation.variantAttributes,
      isVariantModified: false,
      isVariantVisible: variation.isVariantVisible,
      ar: Variations(variantAttributes: variation.ar?.variantAttributes),
    );
  }

  @override
  Widget build(BuildContext context) {
    getTotalOptions();
    final appLocal = AppLocalizations.of(context)!;
    getVariations();
    _request = GraphQlInitilize(this);
    isHide.value = product.isHidden ?? false;
    productAvailableQte.value = product.productAvailableQte ?? 0;
    String keywords = "";
    for (int i = 0; i < (product.productKeyWords?.length ?? 0); i++) {
      if (i == (((product.productKeyWords?.length ?? 0)) - 1)) {
        keywords = "$keywords${product.productKeyWords?[i] ?? ""}";
      } else {
        keywords = "$keywords${product.productKeyWords?[i] ?? ""},";
      }
    }
    return Scaffold(
        appBar: ElbaabHeader(
          title: appLocal.productDetails,
          leadingBack: true,
        ),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Container(
                height: (MediaQuery.of(context).size.width +
                        ((MediaQuery.of(context).size.width / 3) +
                            (controller.colorList.length > 1 ? 120 : 0)))
                    .h,
                color: AppColors.headerColorDark,
                margin: EdgeInsets.only(top: 24.h),
                foregroundDecoration: product.productStatus == "Returned"
                    ? BadgeDecoration(
                        badgeColor: AppColors.colorDanger,
                        badgeSize: 120,
                        textSpan: TextSpan(
                          text: appLocal.productReturned,
                          style: FontStyles.fontBold(fontSize: 12),
                        ),
                      )
                    : null,
                child: ImageSliderWidget(
                    controller: controller, isDetailPage: true),
              ),
              SizedBox(height: 15.h),
              if (product.productStatus == "Returned")
                Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Text(
                    appLocal.productReturnMessage(
                        product.validationHistory?.last.returnMessage ?? ""),
                    style: FontStyles.fontMedium(
                        fontSize: 12, color: AppColors.colorDanger, height: 2),
                  ),
                ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.r),
                child: Row(
                  children: [
                    Text(
                      appLocal.hideThisItem,
                      style: FontStyles.fontSemibold(
                          color: Colors.white.withOpacity(0.86)),
                    ),
                    SizedBox(width: 5.w),
                    InkWell(
                      onTap: () => BottomSheets.showAlertMessageBottomSheet(
                          appLocal.alertMessageForhideItem,
                          appLocal.hideThisItem,
                          context),
                      child: Icon(
                        Icons.info,
                        color: AppColors.colorPrimary,
                      ),
                    ),
                    const Spacer(),
                    Obx(
                      () => Switch(
                        value: isHide.value,
                        onChanged: (v) {
                          isHide.value = v;
                          _request.runMutation(
                              context: context,
                              query: GraphQlQuries.setProductHiddenOrUnhidden,
                              variables:
                                  GraphQlVariables.setProductHiddenOrUnhidden(
                                      productId: product.id ?? "",
                                      isHidden: isHide.value),
                              type: "ProductVisibility");
                        },
                        activeColor: AppColors.colorPrimary,
                      ),
                    ),
                  ],
                ),
              ),
              if ((product.productStatus == "Pending" ||
                      product.productStatus == "Returned") &&
                  ((product.validationHistory?.length ?? 0) > 1) &&
                  (product.validationHistory
                          ?.any((element) => element.status == "Accepted") ??
                      false))
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  child: InkWell(
                    onTap: () {
                      Alerts.alertView(
                          context: context,
                          content: appLocal.restoreLastSubmission,
                          action: () {
                            Get.back();
                            _request.runMutation(
                                context: context,
                                query: GraphQlQuries.restoreProductState,
                                type: "restoreProductState",
                                variables: GraphQlVariables.deleteProductById(
                                    productId: product.id ?? ""));
                          },
                          cancelAction: () => Get.back(),
                          cancelActionText: appLocal.no,
                          defaultActionText: appLocal.yes);
                    },
                    child: Row(
                      children: [
                        Text(
                          appLocal.restoreLastChanges,
                          style: FontStyles.fontSemibold(
                              color: Colors.white.withOpacity(0.86)),
                        ),
                        SizedBox(width: 10.w),
                        InkWell(
                          onTap: () => BottomSheets.showAlertMessageBottomSheet(
                              appLocal.restoreLastChangesAlertMessage,
                              appLocal.restoreProduct,
                              context),
                          child: Icon(
                            Icons.info,
                            color: AppColors.colorPrimary,
                          ),
                        ),
                        const Spacer(),
                        Icon(
                          Icons.restore_page_outlined,
                          color: AppColors.colorPrimary,
                        ),
                      ],
                    ),
                  ),
                ),
              ElbaabNetworkEroor(strError: strError),
              ElbaabFeildContainerWidget(
                borderWidth: 0,
                edgeInsets: const EdgeInsets.only(
                    top: 10, left: kLeftSpace, right: kRightSpace),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 11, vertical: 24),
                  child: Column(
                    children: [
                      if (product.productVariants?.isEmpty ?? false)
                        Row(
                          children: [
                            Expanded(
                              child: Obx(
                                () => ElbaaabInputTextField(
                                  onChanged: (v) => qty = v,
                                  initialValue: "${productAvailableQte.value}",
                                  hint: appLocal.itemPerOrderFeildHint,
                                  textDirection: appLocal.localeName == "en"
                                      ? TextDirection.ltr
                                      : TextDirection.rtl,
                                  label: appLocal.qty,
                                  formatter: [
                                    FilteringTextInputFormatter.deny(
                                        RegExp(r'^0+')),
                                    FilteringTextInputFormatter.digitsOnly
                                  ],
                                  inputType: TextInputType.number,
                                ),
                              ),
                            ),
                            SizedBox(width: 6.w),
                            ElbaabButtonWidget(
                              onPress: () {
                                if (qty.isNotEmpty) {
                                  _request.runMutation(
                                      context: context,
                                      query: GraphQlQuries.changeQuantity,
                                      variables:
                                          GraphQlVariables.changeQuantity(
                                              productId: product.id ?? "",
                                              quantity: int.parse(qty)),
                                      type: "UpdateQty");
                                }
                              },
                              colors: AppColors.colorPrimary,
                              text: appLocal.change,
                              height: 42.h,
                            ),
                          ],
                        ),
                      SizedBox(height: 24.h),
                      Row(
                        children: [
                          Expanded(
                            flex: 1,
                            child: ElbaabFeildContainerWidget(
                              borderWidth: 1,
                              child: ElbaabButtonWidget(
                                onPress: () {
                                  Alerts.alertView(
                                      context: context,
                                      title: appLocal.alert,
                                      content: appLocal.deleteItemSaveAlert,
                                      defaultActionText: appLocal.yes,
                                      action: () {
                                        Get.back();
                                        _request.runMutation(
                                            context: context,
                                            query:
                                                GraphQlQuries.deleteProductById,
                                            variables: GraphQlVariables
                                                .deleteProductById(
                                                    productId:
                                                        product.id ?? ""),
                                            type: "RemoveProduct");
                                      },
                                      cancelActionText: appLocal.no,
                                      cancelAction: () => Get.back());
                                },
                                colors: AppColors.colorSecondary_Red_40,
                                text: appLocal.delete,
                                height: 42.h,
                              ),
                            ),
                          ),
                          if (product.productStatus != "Pending")
                            SizedBox(width: 10.w),
                          if (product.productStatus != "Pending")
                            Expanded(
                              flex: 1,
                              child: ElbaabFeildContainerWidget(
                                borderWidth: 1,
                                child: ElbaabButtonWidget(
                                  onPress: () {
                                    prefs.remove(productInformation);
                                    prefs.remove(productDetails);
                                    prefs.remove(productShipment);
                                    prefs.remove(productPolicies);
                                    Get.toNamed(
                                        RouteNames.addProductTabViewScreen,
                                        arguments: [product]);
                                  },
                                  colors: AppColors.colorPrimary,
                                  text: appLocal.editProduct,
                                  height: 42.h,
                                ),
                              ),
                            ),
                        ],
                      ),
                      if (product.productStatus == "Pending")
                        SizedBox(height: 10.h),
                      if (product.productStatus == "Pending")
                        Text(
                          appLocal.editPendingProductNotAllowed,
                          style: FontStyles.fontMedium(color: Colors.redAccent),
                        )
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 24.h),
                child: ElbaabCarouselFeildWidget(
                  aspectRatio: 5 / 4.8,
                  children: [
                    ElbaabFeildContainerWidget(
                      borderWidth: 0,
                      borderColor: Colors.transparent,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: kLeftSpace.r, right: kRightSpace.r),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _cardHeading(appLocal.informationTitleEnglish),
                            _detailRow(
                                appLocal.productname, product.productName ?? "",
                                isReturned: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Name") ??
                                    false),
                                maxLine: 2),
                            _detailRow(appLocal.description,
                                product.productDescription ?? "",
                                maxLine: 2,
                                isReturned: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Description") ??
                                    false)),
                            if (product.productEIN != null)
                              _detailRow(appLocal.productEin,
                                  "${product.productEIN ?? ""}"),
                            if (product.productManufacturerId?.isNotEmpty ??
                                false)
                              _detailRow(appLocal.productId,
                                  "#${product.productManufacturerId ?? ""}",
                                  isReturned: (controller
                                          .validationHistory?.returnValues
                                          ?.contains("Manufacturer ID") ??
                                      false)),
                            _detailRow(appLocal.categories,
                                "${product.productCategory?.categoryName ?? ""} -> ${product.productSubCategory?.subCategoryName ?? ""}",
                                maxLine: 2,
                                isReturned: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Category") ??
                                    false)),
                            _detailRow(appLocal.brand,
                                product.productBrand?.brandName ?? "",
                                isReturned: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Brand") ??
                                    false)),
                            if (keywords.isNotEmpty)
                              _detailRow(appLocal.keyword, keywords,
                                  isReturned: (controller
                                          .validationHistory?.returnValues
                                          ?.contains("Keywords") ??
                                      false)),
                          ],
                        ),
                      ),
                    ),
                    ElbaabFeildContainerWidget(
                      borderWidth: 0,
                      borderColor: Colors.transparent,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: kLeftSpace.r, right: kRightSpace.r),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _cardHeading(appLocal.informationTitleArabic),
                            _detailRow(appLocal.productname,
                                product.ar?.productName ?? "",
                                isReturned: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Arabic Name") ??
                                    false),
                                maxLine: 6),
                            _detailRow(appLocal.description,
                                product.ar?.productDescription ?? "",
                                maxLine: 4,
                                isReturned: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Arabic Description") ??
                                    false)),
                            if (product.productEIN != null)
                              _detailRow(appLocal.productEin,
                                  "${product.productEIN ?? ""}"),
                            if (product.productManufacturerId?.isNotEmpty ??
                                false)
                              _detailRow(appLocal.productId,
                                  "#${product.productManufacturerId ?? ""}",
                                  isReturned: (controller
                                          .validationHistory?.returnValues
                                          ?.contains("Manufacturer ID") ??
                                      false)),
                            _detailRow(appLocal.categories,
                                "${product.productCategory?.ar?.categoryName ?? ""} -> ${product.productSubCategory?.ar?.subCategoryName ?? ""}",
                                maxLine: 2,
                                isReturned: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Category") ??
                                    false)),
                            _detailRow(appLocal.brand,
                                product.productBrand?.ar?.brandName ?? "",
                                isReturned: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Brand") ??
                                    false)),
                            if (keywords.isNotEmpty)
                              _detailRow(appLocal.keyword, keywords,
                                  isReturned: (controller
                                          .validationHistory?.returnValues
                                          ?.contains("Keywords") ??
                                      false)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 24.h),
                child: ElbaabCarouselFeildWidget(
                    aspectRatio: ((product.productOptions?.productColors ?? [])
                                .isEmpty &&
                            (product.productOptions?.productSizes ?? [])
                                .isEmpty &&
                            (product.productOptions?.productCustomOptions ?? [])
                                .isEmpty)
                        ? 6 / 3.2
                        : totalOption == 1
                            ? 5.3 / 5.2
                            : 5 / 5,
                    children: [
                      ElbaabFeildContainerWidget(
                        borderWidth: 0,
                        borderColor: Colors.transparent,
                        child: Padding(
                          padding: EdgeInsets.only(
                              left: kLeftSpace.r, right: kRightSpace.r),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _cardHeading(appLocal.detailsTitleEnglish),
                              _detailRow(
                                  appLocal.price,
                                  appLocal
                                      .dynamicPrice(product.productPrice ?? ""),
                                  isReturned: (controller
                                          .validationHistory?.returnValues
                                          ?.contains("Price") ??
                                      false)),
                              Obx(
                                () => _detailRow(
                                    appLocal.availableQuantity,
                                    appLocal.dynamicPieceCount(
                                        "${productAvailableQte.value}"),
                                    isReturned: (controller
                                            .validationHistory?.returnValues
                                            ?.contains("Quantity") ??
                                        false)),
                              ),
                              _detailRow(
                                appLocal.miniQTY,
                                appLocal.dynamicPieceCount(
                                    "${product.productMinQte ?? 0}"),
                              ),
                              if (product.productVariants?.isNotEmpty ?? false)
                                _cardHeading(appLocal.productOptions),
                              if ((product.productOptions?.productColors ?? [])
                                  .isNotEmpty)
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        appLocal.color,
                                        style: FontStyles.fontRegular(
                                            fontSize: 12),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 2,
                                      child: SizedBox(
                                        height: 18,
                                        child: ListView.builder(
                                            itemCount: product.productOptions
                                                    ?.productColors?.length ??
                                                0,
                                            scrollDirection: Axis.horizontal,
                                            itemBuilder: (context, index) {
                                              return Container(
                                                height: 18,
                                                width: 18,
                                                margin: const EdgeInsets.only(
                                                    right: 8),
                                                child:
                                                    GlobalMethods.netWorkImage(
                                                        (product
                                                                .productOptions
                                                                ?.productColors?[
                                                                    index]
                                                                .colorIcon ??
                                                            ""),
                                                        BorderRadius.circular(
                                                            9),
                                                        BoxFit.cover),
                                              );
                                            }),
                                      ),
                                    )
                                  ],
                                ),
                              if ((product.productOptions?.productSizes ?? [])
                                  .isNotEmpty)
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        appLocal.size,
                                        style: FontStyles.fontRegular(
                                            fontSize: 12),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            product.productOptions?.productSizes
                                                    ?.first.sizeUnit ??
                                                "",
                                            style: FontStyles.fontMedium(
                                                height: 3,
                                                fontSize: 10,
                                                color: (controller
                                                            .validationHistory
                                                            ?.returnValues
                                                            ?.contains(
                                                                "Size unit 1") ??
                                                        false)
                                                    ? AppColors.colorDanger
                                                        .withOpacity(0.7)
                                                    : Colors.white),
                                          ),
                                          SizedBox(
                                            height: 24,
                                            child: ListView.builder(
                                                itemCount: product
                                                        .productOptions
                                                        ?.productSizes?[0]
                                                        .sizeValues
                                                        ?.length ??
                                                    0,
                                                scrollDirection:
                                                    Axis.horizontal,
                                                itemBuilder: (context, index) {
                                                  return Container(
                                                    height: 24,
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 14),
                                                    margin:
                                                        const EdgeInsets.only(
                                                            right: 8),
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              5),
                                                      border: Border.all(
                                                          color: AppColors
                                                              .colorGray,
                                                          width: 1),
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        product
                                                                .productOptions
                                                                ?.productSizes?[
                                                                    0]
                                                                .sizeValues?[
                                                                    index]
                                                                .value ??
                                                            "",
                                                        style: FontStyles
                                                            .fontRegular(
                                                          fontSize: 10,
                                                          color: AppColors
                                                              .colorGray,
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }),
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              if ((product.productOptions
                                          ?.productCustomOptions ??
                                      [])
                                  .isNotEmpty)
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        appLocal.customOption,
                                        style: FontStyles.fontRegular(
                                            fontSize: 12),
                                      ),
                                    ),
                                   Expanded(
                                        flex: 2,
                                        child: Column(
                                          children: [
                                            ...List.generate(
                                              product
                                                      .productOptions
                                                      ?.productCustomOptions
                                                      ?.length ??
                                                  0,
                                              (superIndex) => SizedBox(
                                                height: 55,
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      product
                                                              .productOptions
                                                              ?.productCustomOptions?[
                                                                  superIndex]
                                                              .optionTitle ??
                                                          "",
                                                      style: FontStyles.fontMedium(
                                                          fontSize: 10,
                                                          color: (controller
                                                                      .validationHistory
                                                                      ?.returnValues
                                                                      ?.contains(
                                                                          "${superIndex + 1}${GlobalMethods.ordinal(superIndex + 1)} custom option title") ??
                                                                  false)
                                                              ? AppColors
                                                                  .colorDanger
                                                                  .withOpacity(
                                                                      0.7)
                                                              : Colors.white),
                                                    ),
                                                    const SizedBox(height: 8),
                                                    SizedBox(
                                                      height: 24,
                                                      child: ListView.builder(
                                                          itemCount: product
                                                                  .productOptions
                                                                  ?.productCustomOptions?[
                                                                      superIndex]
                                                                  .optionValues
                                                                  ?.length ??
                                                              0,
                                                          scrollDirection:
                                                              Axis.horizontal,
                                                          itemBuilder:
                                                              (context, index) {
                                                            return Container(
                                                              height: 24,
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          14),
                                                              margin:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right: 8),
                                                              decoration:
                                                                  BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            5),
                                                                border: Border.all(
                                                                    color: AppColors
                                                                        .colorGray,
                                                                    width: 1),
                                                              ),
                                                              child: Center(
                                                                child: Text(
                                                                  product
                                                                          .productOptions
                                                                          ?.productCustomOptions?[
                                                                              superIndex]
                                                                          .optionValues?[
                                                                              index]
                                                                          .value ??
                                                                      "",
                                                                  style: FontStyles
                                                                      .fontMedium(
                                                                    fontSize:
                                                                        10,
                                                                    color: AppColors
                                                                        .colorGray,
                                                                  ),
                                                                ),
                                                              ),
                                                            );
                                                          }),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            )
                                          ],
                                        )),
                                  ],
                                ),
                              Obx(
                                () => arrVariations.isNotEmpty
                                    ? ListTile(
                                        onTap: () => Get.toNamed(
                                            RouteNames.viewVariationsScreen,
                                            arguments: [
                                              arrVariations,
                                              (product
                                                          .productOptions
                                                          ?.productColors
                                                          ?.isEmpty ??
                                                      false)
                                                  ? product.productImages?.first
                                                  : ""
                                            ]),
                                        contentPadding: EdgeInsets.zero,
                                        dense: true,
                                        leading: Text(
                                          appLocal.variation,
                                          style: FontStyles.fontBold(
                                            fontSize: 12,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                        ),
                                        trailing: const Icon(
                                          Icons.chevron_right,
                                          color: Colors.white60,
                                          size: 24,
                                        ),
                                      )
                                    : Container(),
                              ),
                            ],
                          ),
                        ),
                      ),
                      ElbaabFeildContainerWidget(
                        borderWidth: 0,
                        borderColor: Colors.transparent,
                        child: Padding(
                          padding: EdgeInsets.only(
                              left: kLeftSpace.r, right: kRightSpace.r),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _cardHeading(appLocal.detailsTitleArabic),
                              _detailRow(
                                  appLocal.price,
                                  appLocal
                                      .dynamicPrice(product.productPrice ?? ""),
                                  isReturned: (controller
                                          .validationHistory?.returnValues
                                          ?.contains("Price") ??
                                      false)),
                              Obx(
                                () => _detailRow(
                                    appLocal.availableQuantity,
                                    appLocal.dynamicPieceCount(
                                        "${productAvailableQte.value}"),
                                    isReturned: (controller
                                            .validationHistory?.returnValues
                                            ?.contains("Quantity") ??
                                        false)),
                              ),
                              _detailRow(
                                appLocal.miniQTY,
                                appLocal.dynamicPieceCount(
                                    "${product.productMinQte ?? 0}"),
                              ),
                              if (product.productVariants?.isNotEmpty ?? false)
                                _cardHeading(appLocal.productOptions),
                              if ((product.productOptions?.productColors ?? [])
                                  .isNotEmpty)
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        appLocal.color,
                                        style: FontStyles.fontRegular(
                                            fontSize: 12),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 2,
                                      child: SizedBox(
                                        height: 18.h,
                                        child: ListView.builder(
                                            itemCount: product.productOptions
                                                    ?.productColors?.length ??
                                                0,
                                            scrollDirection: Axis.horizontal,
                                            itemBuilder: (context, index) {
                                              return Container(
                                                height: 18.h,
                                                width: 18.w,
                                                margin: const EdgeInsets.only(
                                                        right: 8)
                                                    .r,
                                                child:
                                                    GlobalMethods.netWorkImage(
                                                        (product
                                                                .productOptions
                                                                ?.productColors?[
                                                                    index]
                                                                .colorIcon ??
                                                            ""),
                                                        BorderRadius.circular(
                                                            10.w),
                                                        BoxFit.cover),
                                              );
                                            }),
                                      ),
                                    )
                                  ],
                                ),
                              if ((product.productOptions?.productSizes ?? [])
                                  .isNotEmpty)
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        appLocal.size,
                                        style: FontStyles.fontRegular(
                                            fontSize: 12),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            product
                                                    .ar
                                                    ?.productOptions
                                                    ?.productSizes
                                                    ?.first
                                                    .sizeUnit ??
                                                "",
                                            style: FontStyles.fontMedium(
                                                height: 3,
                                                fontSize: 10,
                                                color: (controller
                                                            .validationHistory
                                                            ?.returnValues
                                                            ?.contains(
                                                                "Size unit 1") ??
                                                        false)
                                                    ? AppColors.colorDanger
                                                        .withOpacity(0.7)
                                                    : Colors.white),
                                          ),
                                          SizedBox(
                                            height: 24,
                                            child: ListView.builder(
                                                itemCount: product
                                                        .productOptions
                                                        ?.productSizes?[0]
                                                        .sizeValues
                                                        ?.length ??
                                                    0,
                                                scrollDirection:
                                                    Axis.horizontal,
                                                itemBuilder: (context, index) {
                                                  return Container(
                                                    height: 24,
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 14),
                                                    margin:
                                                        const EdgeInsets.only(
                                                            right: 8),
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              5),
                                                      border: Border.all(
                                                          color: AppColors
                                                              .colorGray,
                                                          width: 1),
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        product
                                                                .productOptions
                                                                ?.productSizes?[
                                                                    0]
                                                                .sizeValues?[
                                                                    index]
                                                                .value ??
                                                            "",
                                                        style: FontStyles
                                                            .fontRegular(
                                                          fontSize: 10,
                                                          color: AppColors
                                                              .colorGray,
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }),
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              if ((product.productOptions
                                          ?.productCustomOptions ??
                                      [])
                                  .isNotEmpty)
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        appLocal.customOption,
                                        style: FontStyles.fontRegular(
                                            fontSize: 12),
                                      ),
                                    ),
                                    // Expanded(
                                    //   flex: 2,
                                    //   child: SizedBox(
                                    //     height: 110,
                                    //     child: ListView.builder(
                                    // itemCount: product
                                    //         .productOptions
                                    //         ?.productCustomOptions
                                    //         ?.length ??
                                    //     0,
                                    //         shrinkWrap: true,
                                    //         physics:
                                    //             const NeverScrollableScrollPhysics(),
                                    //         itemBuilder: (context, superIndex) {
                                    // return SizedBox(
                                    //   height: 55,
                                    //   child: Column(
                                    //     crossAxisAlignment:
                                    //         CrossAxisAlignment.start,
                                    //     children: [
                                    //       Text(
                                    //         product
                                    //                 .productOptions
                                    //                 ?.productCustomOptions?[
                                    //                     superIndex]
                                    //                 .optionTitle ??
                                    //             "",
                                    //         style: FontStyles.fontMedium(
                                    //             fontSize: 10,
                                    //             color: (controller
                                    //                         .validationHistory
                                    //                         ?.returnValues
                                    //                         ?.contains(
                                    //                             "${superIndex + 1}${GlobalMethods.ordinal(superIndex + 1)} custom option title") ??
                                    //                     false)
                                    //                 ? AppColors
                                    //                     .colorDanger
                                    //                     .withOpacity(
                                    //                         0.7)
                                    //                 : Colors.white),
                                    //       ),
                                    //       const SizedBox(height: 8),
                                    //       SizedBox(
                                    //         height: 24,
                                    //         child: ListView.builder(
                                    //             itemCount: product
                                    //                     .productOptions
                                    //                     ?.productCustomOptions?[
                                    //                         superIndex]
                                    //                     .optionValues
                                    //                     ?.length ??
                                    //                 0,
                                    //             scrollDirection:
                                    //                 Axis.horizontal,
                                    //             itemBuilder:
                                    //                 (context, index) {
                                    //               return Container(
                                    //                 height: 24,
                                    //                 padding:
                                    //                     const EdgeInsets
                                    //                         .symmetric(
                                    //                         horizontal:
                                    //                             14),
                                    //                 margin:
                                    //                     const EdgeInsets
                                    //                         .only(
                                    //                         right: 8),
                                    //                 decoration:
                                    //                     BoxDecoration(
                                    //                   borderRadius:
                                    //                       BorderRadius
                                    //                           .circular(
                                    //                               5),
                                    //                   border: Border.all(
                                    //                       color: AppColors
                                    //                           .colorGray,
                                    //                       width: 1),
                                    //                 ),
                                    //                 child: Center(
                                    //                   child: Text(
                                    //                     product
                                    //                             .productOptions
                                    //                             ?.productCustomOptions?[
                                    //                                 superIndex]
                                    //                             .optionValues?[
                                    //                                 index]
                                    //                             .value ??
                                    //                         "",
                                    //                     style: FontStyles
                                    //                         .fontMedium(
                                    //                       fontSize:
                                    //                           10,
                                    //                       color: AppColors
                                    //                           .colorGray,
                                    //                     ),
                                    //                   ),
                                    //                 ),
                                    //               );
                                    //             }),
                                    //       ),
                                    //     ],
                                    //   ),
                                    // );
                                    //         }),
                                    //   ),
                                    // )
                                    Expanded(
                                        flex: 2,
                                        child: Column(
                                          children: [
                                            ...List.generate(
                                              product
                                                      .productOptions
                                                      ?.productCustomOptions
                                                      ?.length ??
                                                  0,
                                              (superIndex) => SizedBox(
                                                height: 55,
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      product.ar?.productOptions
                                                              ?.productCustomOptions?[
                                                                  superIndex]
                                                              .optionTitle ??
                                                          "",
                                                      style: FontStyles.fontMedium(
                                                          fontSize: 10,
                                                          color: (controller
                                                                      .validationHistory
                                                                      ?.returnValues
                                                                      ?.contains(
                                                                          "${superIndex + 1}${GlobalMethods.ordinal(superIndex + 1)} custom option title") ??
                                                                  false)
                                                              ? AppColors
                                                                  .colorDanger
                                                                  .withOpacity(
                                                                      0.7)
                                                              : Colors.white),
                                                    ),
                                                    const SizedBox(height: 8),
                                                    SizedBox(
                                                      height: 24,
                                                      child: ListView.builder(
                                                          itemCount: product
                                                                  .productOptions
                                                                  ?.productCustomOptions?[
                                                                      superIndex]
                                                                  .optionValues
                                                                  ?.length ??
                                                              0,
                                                          scrollDirection:
                                                              Axis.horizontal,
                                                          itemBuilder:
                                                              (context, index) {
                                                            return Container(
                                                              height: 24,
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          14),
                                                              margin:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right: 8),
                                                              decoration:
                                                                  BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            5),
                                                                border: Border.all(
                                                                    color: AppColors
                                                                        .colorGray,
                                                                    width: 1),
                                                              ),
                                                              child: Center(
                                                                child: Text(
                                                                  product.ar?.productOptions
                                                                          ?.productCustomOptions?[
                                                                              superIndex]
                                                                          .optionValues?[
                                                                              index]
                                                                          .value ??
                                                                      "",
                                                                  style: FontStyles
                                                                      .fontMedium(
                                                                    fontSize:
                                                                        10,
                                                                    color: AppColors
                                                                        .colorGray,
                                                                  ),
                                                                ),
                                                              ),
                                                            );
                                                          }),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            )
                                          ],
                                        )),
                                  ],
                                ),
                              Obx(
                                () => arrVariations.isNotEmpty
                                    ? ListTile(
                                        onTap: () => Get.toNamed(
                                            RouteNames.viewVariationsScreen,
                                            arguments: [
                                              arrVariations,
                                              (product
                                                          .productOptions
                                                          ?.productColors
                                                          ?.isEmpty ??
                                                      false)
                                                  ? product.productImages?.first
                                                  : ""
                                            ]),
                                        contentPadding: EdgeInsets.zero,
                                        dense: true,
                                        leading: Text(
                                          appLocal.variation,
                                          style: FontStyles.fontBold(
                                            fontSize: 12,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                        ),
                                        trailing: const Icon(
                                          Icons.chevron_right,
                                          color: Color.fromRGBO(
                                              252, 252, 252, 0.65),
                                          size: 24,
                                        ),
                                      )
                                    : Container(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ]),
              ),
              Padding(
                padding: EdgeInsets.only(top: 24.h),
                child:
                    ElbaabCarouselFeildWidget(aspectRatio: 5.6 / 5, children: [
                  ElbaabFeildContainerWidget(
                    borderWidth: 0,
                    borderColor: Colors.transparent,
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: kLeftSpace.r, right: kRightSpace.r),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _cardHeading(appLocal.shipmentTitleEnglish),
                          _detailRow(appLocal.itemPerOrder,
                              "${controller.product?.itemsPerOrder ?? 0}"),
                          _detailRow(
                              appLocal.shipmentFits,
                              controller.product?.productSizeCategory?.name ??
                                  "",
                              isReturned: (controller
                                      .validationHistory?.returnValues
                                      ?.contains("Size Category") ??
                                  false)),
                          Container(
                            height: 150,
                            decoration: BoxDecoration(
                              border: Border.all(
                                width: 2,
                                color: AppColors.colorGray,
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Row(
                              children: [
                                SvgPicture.string(SvgStrings.dimensionsBox),
                                Expanded(
                                  child: Obx(
                                    () => Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        _detailRow(
                                            appLocal.height,
                                            controller.selectedSize.value
                                                    .dimensions?.height ??
                                                "0"),
                                        _detailRow(
                                            appLocal.width,
                                            controller.selectedSize.value
                                                    .dimensions?.width ??
                                                "0"),
                                        _detailRow(
                                            appLocal.length,
                                            controller.selectedSize.value
                                                    .dimensions?.length ??
                                                "0"),
                                        _detailRow(
                                            appLocal.weight,
                                            controller.selectedSize.value
                                                    .dimensions?.weight ??
                                                "0"),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 5),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  ElbaabFeildContainerWidget(
                    borderWidth: 0,
                    borderColor: Colors.transparent,
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: kLeftSpace.r, right: kRightSpace.r),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _cardHeading(appLocal.shipmentTitleArabic),
                          _detailRow(appLocal.itemPerOrder,
                              "${controller.product?.itemsPerOrder ?? 0}"),
                          _detailRow(
                              appLocal.shipmentFits,
                              controller.product?.productSizeCategory
                                      ?.sizeCategoriesAr?.name ??
                                  "",
                              isReturned: (controller
                                      .validationHistory?.returnValues
                                      ?.contains("Size Category") ??
                                  false)),
                          Container(
                            height: 150,
                            decoration: BoxDecoration(
                              border: Border.all(
                                width: 2,
                                color: AppColors.colorGray,
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Row(
                              children: [
                                SvgPicture.string(SvgStrings.dimensionsBox),
                                Expanded(
                                  child: Obx(
                                    () => Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        _detailRow(
                                            appLocal.height,
                                            controller.selectedSize.value
                                                    .dimensions?.height ??
                                                "0"),
                                        _detailRow(
                                            appLocal.width,
                                            controller.selectedSize.value
                                                    .dimensions?.width ??
                                                "0"),
                                        _detailRow(
                                            appLocal.length,
                                            controller.selectedSize.value
                                                    .dimensions?.length ??
                                                "0"),
                                        _detailRow(
                                            appLocal.weight,
                                            controller.selectedSize.value
                                                    .dimensions?.weight ??
                                                "0"),
                                      ],
                                    ),
                                  ),
                                ),
                                SizedBox(width: 5.w),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ]),
              ),
              if (controller.product?.productSpecs?.isNotEmpty ?? false)
                ElbaabFeildContainerWidget(
                  borderWidth: 0,
                  edgeInsets: const EdgeInsets.only(
                      top: 24, left: kLeftSpace, right: kRightSpace),
                  borderColor: Colors.transparent,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 16),
                        child: _cardHeading(appLocal.specification),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 5, vertical: 8),
                        child: ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: controller.product?.productSpecs?.length,
                            itemBuilder: (context, index) {
                              ProductSpecs spec =
                                  (controller.product?.productSpecs ??
                                      [])[index];

                              ProductSpecs specAr =
                                  (controller.product?.ar?.productSpecs ??
                                      [])[index];

                              return Container(
                                decoration: BoxDecoration(
                                  color: index % 2 == 1
                                      ? AppColors.backgroundColorDark
                                      : AppColors.backgroundColorDark
                                          .withOpacity(0.4),
                                ),
                                child: Column(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.all(5.0),
                                      child: Row(
                                        children: <Widget>[
                                          Expanded(
                                            flex: 2,
                                            child: Align(
                                              alignment: Alignment.centerLeft,
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 10),
                                                child: Text(
                                                  spec.specsTitle ?? "",
                                                  style: FontStyles.fontRegular(
                                                      fontSize: 12,
                                                      color: (controller
                                                                  .validationHistory
                                                                  ?.returnValues
                                                                  ?.contains(
                                                                      "Specification ${index + 1}") ??
                                                              false)
                                                          ? AppColors
                                                              .colorDanger
                                                          : Colors.white),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 3,
                                            child: Align(
                                              alignment: Alignment.centerLeft,
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 10),
                                                child: Text(
                                                  spec.specsValue ?? "",
                                                  style: FontStyles.fontRegular(
                                                    fontSize: 12,
                                                    color: (controller
                                                                .validationHistory
                                                                ?.returnValues
                                                                ?.contains(
                                                                    "Specification ${index + 1} value ") ??
                                                            false)
                                                        ? AppColors.colorDanger
                                                            .withOpacity(0.7)
                                                        : Colors.white
                                                            .withOpacity(0.5),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Divider(
                                      color: AppColors.colorPrimary_40,
                                      height: 0.2,
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(5.0),
                                      child: Row(
                                        children: <Widget>[
                                          Expanded(
                                            flex: 2,
                                            child: Align(
                                              alignment: Alignment.centerLeft,
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 10),
                                                child: Text(
                                                  specAr.specsTitle ?? "",
                                                  style: FontStyles.fontRegular(
                                                      fontSize: 12,
                                                      color: (controller
                                                                  .validationHistory
                                                                  ?.returnValues
                                                                  ?.contains(
                                                                      "Specification ${index + 1}") ??
                                                              false)
                                                          ? AppColors
                                                              .colorDanger
                                                          : Colors.white),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 3,
                                            child: Align(
                                              alignment: Alignment.centerLeft,
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 10),
                                                child: Text(
                                                  specAr.specsValue ?? "",
                                                  style: FontStyles.fontRegular(
                                                    fontSize: 12,
                                                    color: (controller
                                                                .validationHistory
                                                                ?.returnValues
                                                                ?.contains(
                                                                    "Specification ${index + 1} value ") ??
                                                            false)
                                                        ? AppColors.colorDanger
                                                            .withOpacity(0.7)
                                                        : Colors.white
                                                            .withOpacity(0.5),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }),
                      ),
                    ],
                  ),
                ),
              Padding(
                padding: EdgeInsets.only(top: 24.h),
                child: ElbaabCarouselFeildWidget(
                  aspectRatio: 5.7 / 5,
                  children: [
                    ElbaabFeildContainerWidget(
                      borderWidth: 0,
                      borderColor: Colors.transparent,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: kLeftSpace.r, right: kRightSpace.r),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _cardHeading(appLocal.policesTitleEnglish),
                            Text(
                              appLocal.returnCondition,
                              style: FontStyles.fontRegular(fontSize: 12),
                            ),
                            _detailRow(
                                appLocal.acceptReturn,
                                (product.productPolicies?.productAcceptReturn ??
                                        false)
                                    ? appLocal.yes
                                    : appLocal.no),
                            _detailRow(
                                appLocal.productforFreeDelivery,
                                (product.isFreeDeliveryItem ?? false)
                                    ? appLocal.yes
                                    : appLocal.no),
                            if ((product.productPolicies?.productAcceptReturn ??
                                false))
                              (product.productPolicies?.productNotFreeReturn ??
                                      false)
                                  ? _detailRow(appLocal.notFreeForCustomer,
                                      appLocal.customerPayDeliveryCost)
                                  : _detailRow(appLocal.returnedCost,
                                      appLocal.sellerPayDeliveryCost),
                            if ((product.productPolicies?.productAcceptReturn ??
                                false))
                              _detailRow(
                                  appLocal.returnDuration,
                                  product.productPolicies
                                          ?.productReturnDuration ??
                                      ""),
                            _detailRow(
                                appLocal.warrantyDuration,
                                product.productPolicies
                                        ?.productWarrantyDuration ??
                                    "",
                                isReturned: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Warranty Duration") ??
                                    false)),
                            if (product.productPolicies?.productReturnPolicy
                                    ?.isNotEmpty ??
                                false)
                              _detailRow(
                                  appLocal.productPolicy,
                                  product.productPolicies
                                          ?.productReturnPolicy ??
                                      "",
                                  maxLine: 4,
                                  isReturned: (controller
                                          .validationHistory?.returnValues
                                          ?.contains("Policies") ??
                                      false)),
                          ],
                        ),
                      ),
                    ),
                    ElbaabFeildContainerWidget(
                      borderWidth: 0,
                      borderColor: Colors.transparent,
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: kLeftSpace.r, right: kRightSpace.r),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _cardHeading(appLocal.policesTitleArabic),
                            Text(
                              appLocal.returnCondition,
                              style: FontStyles.fontRegular(fontSize: 12),
                            ),
                            _detailRow(
                                appLocal.acceptReturn,
                                (product.productPolicies?.productAcceptReturn ??
                                        false)
                                    ? appLocal.yes
                                    : appLocal.no),
                            _detailRow(
                                appLocal.productforFreeDelivery,
                                (product.isFreeDeliveryItem ?? false)
                                    ? appLocal.yes
                                    : appLocal.no),
                            if ((product.productPolicies?.productAcceptReturn ??
                                false))
                              (product.productPolicies?.productNotFreeReturn ??
                                      false)
                                  ? _detailRow(appLocal.notFreeForCustomer,
                                      appLocal.customerPayDeliveryCost,
                                      maxLine: 3)
                                  : _detailRow(appLocal.returnedCost,
                                      appLocal.sellerPayDeliveryCost,
                                      maxLine: 3),
                            if ((product.productPolicies?.productAcceptReturn ??
                                false))
                              _detailRow(
                                  appLocal.returnDuration,
                                  product.ar?.productPolicies
                                          ?.productReturnDuration ??
                                      ""),
                            _detailRow(
                                appLocal.warrantyDuration,
                                product.ar?.productPolicies
                                        ?.productWarrantyDuration ??
                                    "",
                                isReturned: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Warranty Duration") ??
                                    false)),
                            if (product.productPolicies?.productReturnPolicy
                                    ?.isNotEmpty ??
                                false)
                              _detailRow(
                                  appLocal.productPolicy,
                                  product.ar?.productPolicies
                                          ?.productReturnPolicy ??
                                      "",
                                  maxLine: 4,
                                  isReturned: (controller
                                          .validationHistory?.returnValues
                                          ?.contains("Policies") ??
                                      false)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 50.h)
            ],
          ),
        ));
  }

  Widget _detailRow(String title, String value,
      {int maxLine = 1, bool isReturned = false}) {
    return Padding(
      padding: const EdgeInsets.only(top: 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              title,
              style: FontStyles.fontRegular(fontSize: 12),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value,
              maxLines: maxLine,
              style: FontStyles.fontRegular(
                fontSize: 12,
                color: isReturned
                    ? AppColors.colorDanger.withOpacity(0.7)
                    : Colors.white.withOpacity(0.5),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _cardHeading(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Text(title, style: FontStyles.fontBold(fontSize: 12)),
    );
  }

  @override
  onError(error, String type) {
    BaseModel model = BaseModel.fromJson(error);
    strError.value = model.message ?? "";
  }

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);

    if (model.status == statusOK) {
      switch (type) {
        case 'RemoveProduct':
        case 'restoreProductState':
        case 'ProductVisibility':
          Get.back(result: true);
          break;
        case 'UpdateQty':
          productAvailableQte.value = int.parse(qty);
          break;
      }
    } else {
      strError.value = model.message ?? "";
    }
  }

  getTotalOptions() {
    totalOption = 0;
    if (product.productOptions != null) {
      if (product.productOptions?.productColors?.isNotEmpty ?? false) {
        totalOption++;
      }
      if (product.productOptions?.productSizes?.isNotEmpty ?? false) {
        totalOption++;
      }
      if (product.productOptions?.productCustomOptions?.isNotEmpty ?? false) {
        totalOption++;
      }
    }
    print(" totalOption $totalOption");
  }
}
