{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98793ca17b1cae26777153cc0272803683", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986663a606b16a4069f78350af4fa98f21", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986663a606b16a4069f78350af4fa98f21", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980e2301c948ac081a27f0edc5a0b61a9a", "guid": "bfdfe7dc352907fc980b868725387e985a63d93e93828a5ffb1b1c0bfc20e3b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d141ee93d4c39ee8ed44c0eacb112404", "guid": "bfdfe7dc352907fc980b868725387e983578620150ec4cfaced272678fe44d7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98766941a832ae60dd4f08ce789286eb0d", "guid": "bfdfe7dc352907fc980b868725387e98c18de3ab5182d84c9c0175c3aba0fd45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a4f34c445dafcc5ab3ed281c482e18", "guid": "bfdfe7dc352907fc980b868725387e984a297d3f9b295183e3167ce8f3e637d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cd35bf08acad1d19bbeae3d87d001f8", "guid": "bfdfe7dc352907fc980b868725387e9809e32571848693982a1e49d490acd168", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813cbc22d357f5e4c1b30dbfdfdbdbbed", "guid": "bfdfe7dc352907fc980b868725387e983177711f1a68140586480fe8ab8e1148", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814e8473b70eccc22582d8614eba72a7a", "guid": "bfdfe7dc352907fc980b868725387e982b94efcba5bcf3d6f079d46b014c2ba9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878aa571d6978544b3dbf3b9eea1d636a", "guid": "bfdfe7dc352907fc980b868725387e983267a210d5ed3f2377367aac18fb611d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b283db03a9019a7905b793023db75d1b", "guid": "bfdfe7dc352907fc980b868725387e985fb5e30d85c549d0f46d6de6bd67c457", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a06cd71e0a1f6cf6cfb07e7ad8055219", "guid": "bfdfe7dc352907fc980b868725387e986f14500ccefea82da288481b5dddcdb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9863f906c754bb599f79d0599427f31", "guid": "bfdfe7dc352907fc980b868725387e98bf0abbb8ec3561691b61d3b58ceda1cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988121ef08ba9112fd7da3d79a0971d36a", "guid": "bfdfe7dc352907fc980b868725387e985b0d38c3b75a6fa0957c0dfece06f84e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1c05f410262d6bd1d8547535387508c", "guid": "bfdfe7dc352907fc980b868725387e98eaad785e8502dba1ac89989df49e5651", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f17a4abe381b5cf78dc64ad561188ab", "guid": "bfdfe7dc352907fc980b868725387e980ae95d60b4f1ad46f709cd42b28b9c54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855323b9c31c99988848d62d81aa31df6", "guid": "bfdfe7dc352907fc980b868725387e98001edf29f30f3a0d6968f1b9e63b7c00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826c86e3364a5f9c331e63f0a37e3a199", "guid": "bfdfe7dc352907fc980b868725387e98cdb7002555716bf47bd7586b4ac8236d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ff71cc0d7118c13766bf1dc87603747", "guid": "bfdfe7dc352907fc980b868725387e98afe46c3822400c8579fd6865c4ec06a8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dce5022f0a5165cef0a069e58da93a32", "guid": "bfdfe7dc352907fc980b868725387e982e652fc935434bcee769772a3371419c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e369c3a9a145669507b9127f888237af", "guid": "bfdfe7dc352907fc980b868725387e988747aaddd72cf806c7f759385ed906c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98354ec409dd005936f2624f059270ca74", "guid": "bfdfe7dc352907fc980b868725387e9863e009fb137902e81297ce1c64bc2c3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ce94238fcec1085b9307851b552e43d", "guid": "bfdfe7dc352907fc980b868725387e981738f2fe66b2f1157c6d5bcd6723dcff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864e4b0f87ee8adba03aaec914a288f92", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ccd48ceec181ee44c86e0435de1fe1a", "guid": "bfdfe7dc352907fc980b868725387e982c00cb8e7484561ce8a52e3103d40d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa29111d28dedd896b387641aa9b1c8c", "guid": "bfdfe7dc352907fc980b868725387e984a6c13a334bae14cd96094284d90a4ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ff670317e51d9d23f4f826c06a683c8", "guid": "bfdfe7dc352907fc980b868725387e9892d9842ee650c2ef18ab68a744a8c35c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ca52b397db204c49ff77779f451d773", "guid": "bfdfe7dc352907fc980b868725387e982e1ea7128555c3484de12aa0d21baa58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a28e921a2702af97dae0adb03cc665", "guid": "bfdfe7dc352907fc980b868725387e986c53e4a9cb33b77a82c428c58f00d6e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827d7aa5edd759c1cc526b56502c71aad", "guid": "bfdfe7dc352907fc980b868725387e98f12b35618e3c290a4a26cbd67c9c0700"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889de29c61c3e73f06f1e9d5ff20165c7", "guid": "bfdfe7dc352907fc980b868725387e98939bb9403a0fe28be3dcaf7b38e459ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a225324f5d8bc2431dfe630436260c0", "guid": "bfdfe7dc352907fc980b868725387e9837a212af2ef8d8299b0af7555e8de3dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fcf7ce9e302859b1f077045790144af", "guid": "bfdfe7dc352907fc980b868725387e98ba539d3d57137bdb21a3b8d3ebb5b664"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}