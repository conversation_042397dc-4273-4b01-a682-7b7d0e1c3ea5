<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="252.587" height="155.297" viewBox="0 0 252.587 155.297">
  <defs>
    <linearGradient id="linear-gradient" y1="1" x2="1.506" y2="-0.155" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0"/>
      <stop offset="0.253" stop-color="#eef" stop-opacity="0.404"/>
      <stop offset="0.629" stop-color="#d9d9ff"/>
    </linearGradient>
  </defs>
  <g id="Group_23638" data-name="Group 23638" transform="translate(-61 -220.707)">
    <g id="Group_21111" data-name="Group 21111" transform="translate(48.041 220.707)">
      <g id="Group_21108" data-name="Group 21108" transform="translate(0 0)">
        <g id="Managing_people" data-name="Managing people" transform="translate(12.96)">
          <path id="Background" d="M506.918,279.794c-26.847-47.8-48.978-2.1-118.641-53.627-28.329-20.956-94.39-17.3-118.905,8.18-26.984,28.047,4.068,60.557,0,95.124-9.11,77.417,100.654,3.045,162.443,32.893C475.413,388.9,525.253,312.435,506.918,279.794Z" transform="translate(-258.233 -212.562)" opacity="0.41" fill="url(#linear-gradient)"/>
        </g>
      </g>
    </g>
    <g id="New_message-amico" data-name="New message-amico" transform="translate(97.46 194.468)">
      <g id="freepik--Floor--inject-23" transform="translate(27.54 93.83)">
        <path id="freepik--floor--inject-23-2" data-name="freepik--floor--inject-23" d="M62.206,0c34.355,0,62.206,16.08,62.206,35.915S96.561,71.831,62.206,71.831,0,55.751,0,35.915,27.851,0,62.206,0Z" fill="#323741" opacity="0.88"/>
      </g>
      <g id="freepik--Shadow--inject-23" transform="translate(40.212 106.857)">
        <path id="freepik--shadow--inject-23-2" data-name="freepik--shadow--inject-23" d="M133.56,306.021,164.078,288.4a.738.738,0,0,0,0-1.4L106.3,253.644a2.676,2.676,0,0,0-2.422,0L73.359,271.261a.738.738,0,0,0,0,1.4l57.779,33.362A2.676,2.676,0,0,0,133.56,306.021Z" transform="translate(-72.857 -253.354)" fill="#5a5d67"/>
      </g>
      <g id="freepik--Clouds--inject-23" transform="translate(117.476 49.99)">
        <path id="freepik--Cloud--inject-23" d="M384.949,51.31l2.41,1.4a2.509,2.509,0,0,1-.022-.319v-.719c0-1.242.873-1.745,1.957-1.119a3.784,3.784,0,0,1,1.16,1.119c.176-1.56,1.4-2.142,2.889-1.283a6.438,6.438,0,0,1,2.914,5.033v1.043a2.734,2.734,0,0,1-.249,1.211l2,1.155A2.385,2.385,0,0,1,399.09,60.7c0,.691-.484.968-1.079.624L384.949,53.8a2.385,2.385,0,0,1-1.079-1.868C383.87,51.243,384.354,50.963,384.949,51.31Z" transform="translate(-374.167 -49.99)" fill="#ebebeb"/>
        <path id="freepik--cloud--inject-23-2" data-name="freepik--cloud--inject-23" d="M349.961,74.368l1.764,1.018a2.189,2.189,0,0,1,0-.232v-.512c0-.912.638-1.281,1.426-.839a2.777,2.777,0,0,1,.839.819c.129-1.146,1.026-1.572,2.117-.942a4.723,4.723,0,0,1,2.136,3.7v.766a2.03,2.03,0,0,1-.182.886l1.465.839a1.748,1.748,0,0,1,.794,1.37c0,.506-.355.71-.794.456l-9.566-5.5a1.75,1.75,0,0,1-.791-1.373C349.158,74.32,349.51,74.119,349.961,74.368Z" transform="translate(-349.169 -66.846)" fill="#ebebeb"/>
      </g>
      <g id="freepik--Device--inject-23" transform="translate(42.444 103.787)">
        <g id="freepik--device--inject-23-2" data-name="freepik--device--inject-23" transform="translate(0 0)">
          <g id="freepik--device--inject-23-3" data-name="freepik--device--inject-23">
            <path id="Path_21970" data-name="Path 21970" d="M167.978,318.226c.436.336.389.814-.137,1.118l-28.793,16.629a2.321,2.321,0,0,1-2.094,0L81.889,304.189c-.534-.308-.576-.791-.123-1.119a2.338,2.338,0,0,0-.926,1.734v1.384a2.324,2.324,0,0,0,1.049,1.818l55.045,31.774a2.31,2.31,0,0,0,2.1,0l28.8-16.627a2.315,2.315,0,0,0,1.049-1.815v-1.4A2.346,2.346,0,0,0,167.978,318.226Z" transform="translate(-80.84 -286.099)" fill="#455a64"/>
            <path id="Path_21971" data-name="Path 21971" d="M169.439,274.412l-55.061-31.785a2.321,2.321,0,0,0-2.094,0l-28.8,16.632a.637.637,0,0,0,0,1.208l55.048,31.785a2.321,2.321,0,0,0,2.094,0l28.8-16.632A.642.642,0,0,0,169.439,274.412Zm-7.469-2.57a.546.546,0,0,1,0,1.035L135.78,288a2,2,0,0,1-1.8,0L91.11,263.244a.548.548,0,0,1,0-1.037L117.3,247.084a1.47,1.47,0,0,1,1.507-.168Z" transform="translate(-82.43 -242.377)" fill="#37474f"/>
            <path id="Path_21972" data-name="Path 21972" d="M81.766,303a2.338,2.338,0,0,0-.926,1.734v1.384a2.326,2.326,0,0,0,1.049,1.818l55.045,31.794a2.181,2.181,0,0,0,1.051.252v-3.809a2.162,2.162,0,0,1-1.049-.249L81.889,304.138C81.355,303.839,81.313,303.347,81.766,303Z" transform="translate(-80.84 -286.048)" fill="#263238"/>
            <path id="Path_21973" data-name="Path 21973" d="M141.647,276.332a.428.428,0,0,0,0-.811,1.552,1.552,0,0,0-1.4,0,.428.428,0,0,0,0,.811A1.552,1.552,0,0,0,141.647,276.332Z" transform="translate(-123.427 -266.133)"/>
            <path id="Path_21974" data-name="Path 21974" d="M326.351,381.681a1.006,1.006,0,0,0,0-1.907,3.635,3.635,0,0,0-3.308,0,1.006,1.006,0,0,0,0,1.907A3.635,3.635,0,0,0,326.351,381.681Z" transform="translate(-254.823 -341.068)" fill="none" stroke="#e6e6e6" stroke-miterlimit="10" stroke-width="1"/>
            <path id="Path_21975" data-name="Path 21975" d="M140.037,278.4l5.425-3.129c.322-.187.338-.481.036-.657a1.261,1.261,0,0,0-1.124.02l-5.425,3.129c-.322.187-.338.481-.036.657A1.261,1.261,0,0,0,140.037,278.4Z" transform="translate(-122.519 -265.511)"/>
            <path id="Path_21976" data-name="Path 21976" d="M138.624,258.274a1.463,1.463,0,0,0-1.507.168l-26.193,15.122a.548.548,0,0,0,0,1.037l42.861,24.744a2,2,0,0,0,1.8,0l26.19-15.125a.545.545,0,0,0,0-1.035Z" transform="translate(-102.244 -253.724)" fill="#ba68c8"/>
            <path id="Path_21977" data-name="Path 21977" d="M138.624,258.274a1.463,1.463,0,0,0-1.507.168l-26.193,15.122a.548.548,0,0,0,0,1.037l42.861,24.744a2,2,0,0,0,1.8,0l26.19-15.125a.545.545,0,0,0,0-1.035Z" transform="translate(-102.244 -253.724)" fill="#fff" opacity="0.85"/>
          </g>
          <path id="Path_21978" data-name="Path 21978" d="M121.837,317.335l-.28-.162a.067.067,0,0,1,0-.126l.031-.017a.232.232,0,0,1,.218,0l.28.159c.059.036.059.092,0,.126l-.031.02A.249.249,0,0,1,121.837,317.335Z" transform="translate(-110.14 -296.135)" fill="#455a64"/>
          <path id="Path_21979" data-name="Path 21979" d="M123.09,316.007l-.484-.28c-.059-.034-.059-.089,0-.126l.031-.017a.249.249,0,0,1,.218,0l.484.28c.059.034.059.089,0,.126l-.031.017A.249.249,0,0,1,123.09,316.007Z" transform="translate(-110.896 -295.096)" fill="#455a64"/>
          <path id="Path_21980" data-name="Path 21980" d="M124.346,314.675l-.685-.394c-.062-.036-.062-.092,0-.126l.031-.02a.249.249,0,0,1,.218,0l.685.4a.067.067,0,0,1,0,.126l-.031.017A.232.232,0,0,1,124.346,314.675Z" transform="translate(-111.654 -294.051)" fill="#455a64"/>
          <path id="Path_21981" data-name="Path 21981" d="M124.834,312.91l-1.183-.685c-.062-.034-.062-.09,0-.126l.031-.017a.241.241,0,0,1,.218,0l1.18.682c.062.036.062.092,0,.126l-.028.02A.249.249,0,0,1,124.834,312.91Z" transform="translate(-111.647 -292.572)" fill="#455a64"/>
          <path id="Path_21982" data-name="Path 21982" d="M130.564,310.682a.075.075,0,0,1-.053-.014l-.173-.1-.559.322a.072.072,0,0,1-.081,0l-.064-.036-.028-.02V310.8l-.028-.7a.085.085,0,0,1,0-.031.1.1,0,0,1,.034-.025l.076-.045a.177.177,0,0,1,.064-.022.118.118,0,0,1,.059.017l.607.35.123-.07a.137.137,0,0,1,.062-.022.1.1,0,0,1,.056.017l.042.025a.026.026,0,0,1,.025.031c0,.011,0,.02-.036.034l-.123.073.173.1a.026.026,0,0,1,.025.031c0,.011-.014.022-.039.036l-.1.053A.117.117,0,0,1,130.564,310.682Zm-.338-.221-.433-.249.028.484Z" transform="translate(-115.947 -291.076)" fill="#455a64"/>
          <path id="Path_21983" data-name="Path 21983" d="M133.043,308.344a1.012,1.012,0,0,1-.559-.137c-.173-.1-.252-.21-.238-.327s.123-.232.322-.347a1.517,1.517,0,0,1,.246-.117,1.216,1.216,0,0,1,.232-.064.05.05,0,0,1,.036,0,.028.028,0,0,1,0,.014l.025.073a.017.017,0,0,1,0,.022h-.031a1.066,1.066,0,0,0-.341.131c-.134.076-.207.157-.218.238s.05.162.185.238a.744.744,0,0,0,.408.109.839.839,0,0,0,.4-.12,1.237,1.237,0,0,0,.126-.084.39.39,0,0,0,.084-.081l-.28-.162-.232.134a.176.176,0,0,1-.064.022.115.115,0,0,1-.056-.017l-.039-.022q-.028-.017-.025-.034a.142.142,0,0,1,.036-.034l.372-.215a.156.156,0,0,1,.073-.022.12.12,0,0,1,.064.02l.4.232a.061.061,0,0,1,.028.022v.025a.879.879,0,0,1-.352.3A1.311,1.311,0,0,1,133.043,308.344Z" transform="translate(-117.87 -289.18)" fill="#455a64"/>
          <path id="Path_21984" data-name="Path 21984" d="M162.683,290.174l.657.38a.2.2,0,0,0,.179,0l.663-.38c.048-.031.048-.076,0-.1l-.66-.38a.185.185,0,0,0-.179,0l-.66.38C162.632,290.1,162.632,290.143,162.683,290.174Z" transform="translate(-139.77 -276.444)" fill="#455a64"/>
          <path id="Path_21985" data-name="Path 21985" d="M162.721,289.471a.612.612,0,0,0-.28-.061.6.6,0,0,0-.28.061c-.129.076-.129.19,0,.263l.227.131.165-.034-.226-.129a.106.106,0,0,1,0-.2.254.254,0,0,1,.117-.031.207.207,0,0,1,.115.031l.226.131.168-.034Z" transform="translate(-139.352 -276.258)" fill="#455a64"/>
          <path id="Path_21986" data-name="Path 21986" d="M201.9,264.819a.64.64,0,0,0-.319.073l-1.678.965c-.145.081-.145.212,0,.3l.61.35a.641.641,0,0,0,.319.073.925.925,0,0,0,.154-.014.464.464,0,0,0,.162-.059l1.678-.962c.143-.084.143-.215,0-.3l-.61-.352A.629.629,0,0,0,201.9,264.819Zm-1.063,1.678a.294.294,0,0,1-.137-.036l-.607-.352a.12.12,0,0,1,0-.227l1.678-.962a.254.254,0,0,1,.087-.034h.05a.28.28,0,0,1,.134.039l.607.35a.12.12,0,0,1,0,.227l-1.678.965a.28.28,0,0,1-.134.039Z" transform="translate(-166.528 -258.543)" fill="#455a64"/>
          <path id="Path_21987" data-name="Path 21987" d="M202.041,266.879l-.022-.014-.559-.316a.089.089,0,0,1,0-.168l1.5-.867a.1.1,0,0,1,.031-.014l.022.014.559.316a.089.089,0,0,1,0,.165l-1.5.87Z" transform="translate(-167.689 -259.034)" fill="#455a64"/>
          <path id="Path_21988" data-name="Path 21988" d="M208.508,264.891a.422.422,0,0,1,.338.064l.112.067c.126.07.179.159.12.2l-.109.067-.559-.333Z" transform="translate(-172.738 -258.587)" fill="#455a64"/>
          <path id="Path_21989" data-name="Path 21989" d="M166.595,315.793a2.7,2.7,0,0,0,1.174-.324c.682-.394.858-.931-.224-1.605a1.336,1.336,0,0,1-.71.956A2.834,2.834,0,0,1,164,315c-.853-.489-.559-1.216.375-1.753a3.954,3.954,0,0,1,3.943.014c1.58.912,1.225,1.876.106,2.517a4.449,4.449,0,0,1-1.907.559Zm.394-2.22a2.213,2.213,0,0,0-2.044,0c-.445.254-.534.626-.042.909a1.538,1.538,0,0,0,1.588-.075A.864.864,0,0,0,166.989,313.573Z" transform="translate(-140.38 -293.059)" fill="#455a64"/>
          <path id="Path_21990" data-name="Path 21990" d="M182.155,308.992a1.225,1.225,0,0,1,1.118-.078c.28.168.227.447-.131.652a1.241,1.241,0,0,1-1.138.081C181.716,309.482,181.789,309.2,182.155,308.992Zm2.184,1.261a1.222,1.222,0,0,1,1.118-.076c.28.165.226.445-.131.652a1.25,1.25,0,0,1-1.138.081c-.285-.165-.215-.45.143-.646Z" transform="translate(-153.588 -290.237)" fill="#455a64"/>
          <path id="Path_21991" data-name="Path 21991" d="M189.391,301.45c-1.373-.794-1.373-1.619-.249-2.271a3.86,3.86,0,0,1,3.932.145c1.37.791,1.4,1.641.28,2.293A3.915,3.915,0,0,1,189.391,301.45Zm2.743-1.586c-1.149-.663-1.887-.657-2.416-.352s-.54.733.61,1.4,1.938.677,2.458.378S193.286,300.53,192.134,299.865Z" transform="translate(-158.271 -282.958)" fill="#455a64"/>
          <path id="Path_21992" data-name="Path 21992" d="M205.025,292.429c-1.376-.794-1.376-1.619-.252-2.268a3.872,3.872,0,0,1,3.932.143c1.367.789,1.415,1.644.28,2.293A3.915,3.915,0,0,1,205.025,292.429Zm2.743-1.583c-1.152-.665-1.89-.66-2.419-.355s-.54.733.612,1.4,1.938.674,2.455.375S208.917,291.509,207.768,290.847Z" transform="translate(-169.532 -276.462)" fill="#455a64"/>
          <path id="Path_21993" data-name="Path 21993" d="M181.663,312.4l22-12.7a1.86,1.86,0,0,1,1.678,0l5.623,3.246c.464.28.464.7,0,.97l-21.993,12.7a1.86,1.86,0,0,1-1.678,0l-5.632-3.246A.512.512,0,0,1,181.663,312.4Z" transform="translate(-153.219 -283.523)" fill="#95a2fe"/>
          <path id="Path_21994" data-name="Path 21994" d="M190.6,337.216l3.937,2.271a.788.788,0,0,0,.724,0l5.8-3.356c.2-.117.2-.3,0-.419l-3.937-2.273a.806.806,0,0,0-.724,0l-5.8,3.356a.221.221,0,0,0,0,.422Z" transform="translate(-159.795 -307.913)" fill="#fafafa"/>
          <path id="Path_21995" data-name="Path 21995" d="M196.543,336.143l4.667-.185-4.65-.07.187-2.508-.763,3.1-5.534.548,4.519-.213-.112,2.782.347-2.793,1.295-.061Z" transform="translate(-159.8 -307.933)" fill="#ba68c8"/>
          <path id="Path_21996" data-name="Path 21996" d="M220.491,320.718l5.685-3.269a1.012,1.012,0,0,1,.914,0c.249.143.24.378-.02.529l-5.685,3.269a1.013,1.013,0,0,1-.914,0C220.225,321.1,220.236,320.88,220.491,320.718Z" transform="translate(-181.298 -296.378)" fill="#fafafa"/>
          <path id="Path_21997" data-name="Path 21997" d="M226.752,315.567l11.521-6.658a1.012,1.012,0,0,1,.914,0c.249.143.24.38-.02.528L227.647,316.1a1.021,1.021,0,0,1-.917,0C226.483,315.961,226.492,315.726,226.752,315.567Z" transform="translate(-185.806 -290.226)" fill="#fafafa"/>
          <path id="Path_21998" data-name="Path 21998" d="M233.005,319.207l11.523-6.658a1.012,1.012,0,0,1,.914,0c.246.143.238.38-.02.529L233.9,319.735a1.012,1.012,0,0,1-.914,0C232.737,319.587,232.748,319.358,233.005,319.207Z" transform="translate(-190.312 -292.848)" fill="#fafafa"/>
        </g>
      </g>
      <g id="freepik--Message--inject-23" transform="translate(68.363 55.169)">
        <g id="freepik--Envelope--inject-23">
          <path id="Path_21999" data-name="Path 21999" d="M173.55,126.071l37.3-21.531,1.493.861a1.558,1.558,0,0,1,.7,1.216V134.13a1.555,1.555,0,0,1-.7,1.216l-35.9,20.72a1.569,1.569,0,0,1-1.4,0l-.789-.456a1.563,1.563,0,0,1-.7-1.216Z" transform="translate(-173.544 -94.465)" fill="#ba68c8"/>
          <path id="Path_22000" data-name="Path 22000" d="M173.55,126.071l37.3-21.531,1.493.861a1.558,1.558,0,0,1,.7,1.216V134.13a1.555,1.555,0,0,1-.7,1.216l-35.9,20.72a1.569,1.569,0,0,1-1.4,0l-.789-.456a1.563,1.563,0,0,1-.7-1.216Z" transform="translate(-173.544 -94.465)" fill="#fff"/>
          <path id="Path_22001" data-name="Path 22001" d="M210.64,78.794l.154-.084-.579-.336h0a1.12,1.12,0,0,0-.157-.073L193.644,72.22a1.119,1.119,0,0,0-1.258.4L174.023,99.434a3.2,3.2,0,0,0-.473,1.532v1.781l.559.33,36.729-21.864V79.432A1.214,1.214,0,0,0,210.64,78.794Z" transform="translate(-173.544 -71.141)" fill="#ba68c8"/>
          <path id="Path_22002" data-name="Path 22002" d="M193.5,73.42a.93.93,0,0,0-.19.2L174.95,100.435a2.853,2.853,0,0,0-.341.766l-.559-.33a3.665,3.665,0,0,1,.148-.405h0c.025-.056.05-.109.078-.162v-.025a1.461,1.461,0,0,1,.1-.162l18.369-26.825a.9.9,0,0,1,.092-.112l.1-.089Z" transform="translate(-173.905 -71.809)" fill="#fff"/>
          <path id="Path_22003" data-name="Path 22003" d="M259.821,78.717a.9.9,0,0,0-.157-.075l-16.417-6.085a1.119,1.119,0,0,0-1.068.2l-.559-.33a1.138,1.138,0,0,1,1.071-.2l16.414,6.082a.87.87,0,0,1,.154.076Z" transform="translate(-222.58 -71.148)" fill="#fff" opacity="0.35"/>
          <path id="Path_22004" data-name="Path 22004" d="M174.89,169.852a1.777,1.777,0,0,1,.1-.162A1.777,1.777,0,0,0,174.89,169.852Z" transform="translate(-174.51 -141.397)" fill="#ba68c8"/>
          <path id="Path_22005" data-name="Path 22005" d="M241.24,73.179a.744.744,0,0,1,.1-.089A.6.6,0,0,0,241.24,73.179Z" transform="translate(-222.306 -71.809)" fill="#ba68c8"/>
          <path id="Path_22006" data-name="Path 22006" d="M174,171.388a3.714,3.714,0,0,1,.148-.408A3.714,3.714,0,0,0,174,171.388Z" transform="translate(-173.869 -142.326)" fill="#ba68c8"/>
          <path id="Path_22007" data-name="Path 22007" d="M174.56,170.529c.025-.056.053-.109.078-.159C174.613,170.42,174.585,170.473,174.56,170.529Z" transform="translate(-174.272 -141.887)" fill="#ba68c8"/>
          <path id="Path_22008" data-name="Path 22008" d="M174.54,171Z" transform="translate(-174.258 -142.341)" opacity="0.2"/>
          <path id="Path_22009" data-name="Path 22009" d="M174.01,172.44Z" transform="translate(-173.876 -143.378)" opacity="0.2"/>
          <path id="Path_22010" data-name="Path 22010" d="M241.59,73.09Z" transform="translate(-222.559 -71.809)" opacity="0.2"/>
          <path id="Path_22011" data-name="Path 22011" d="M174.84,170.294v-.028C174.84,170.258,174.846,170.286,174.84,170.294Z" transform="translate(-174.474 -141.811)" opacity="0.2"/>
          <path id="Path_22012" data-name="Path 22012" d="M240.91,73.519A.682.682,0,0,1,241,73.41a.559.559,0,0,0-.092.109Z" transform="translate(-222.069 -72.04)" opacity="0.2"/>
          <path id="Path_22013" data-name="Path 22013" d="M194.416,73.836l-18.36,26.816a3.151,3.151,0,0,0-.475,1.53v1.781l37.3-21.531v-1.79a1.3,1.3,0,0,0-.786-1.119l-16.414-6.082A1.133,1.133,0,0,0,194.416,73.836Z" transform="translate(-175.007 -72.021)" fill="#95a2fe"/>
          <path id="Path_22014" data-name="Path 22014" d="M186.286,173.139l-1.2-.691h0a.721.721,0,0,1-.28-.652V134.505a2.142,2.142,0,0,1,.336-1.065l1.216.7a2.125,2.125,0,0,0-.336,1.071v37.277A.73.73,0,0,0,186.286,173.139Z" transform="translate(-181.652 -115.284)" fill="#e0e0e0"/>
          <path id="Path_22015" data-name="Path 22015" d="M190.126,88.487l29.959-17.292c.559-.33,1.026-.064,1.026.593v37.28a2.268,2.268,0,0,1-1.026,1.781l-29.959,17.289c-.559.327-1.026.062-1.026-.6V90.268A2.276,2.276,0,0,1,190.126,88.487Z" transform="translate(-184.746 -70.342)" fill="#f0f0f0"/>
          <path id="Path_22016" data-name="Path 22016" d="M217.32,68.588l1.174.682a.727.727,0,0,0-.688.1L187.85,86.657a1.98,1.98,0,0,0-.693.707l-1.216-.7a2.063,2.063,0,0,1,.691-.713L216.59,68.647a.7.7,0,0,1,.73-.064Z" transform="translate(-182.47 -68.509)" fill="#fafafa"/>
          <path id="Path_22017" data-name="Path 22017" d="M199.567,103.1l24.912-14.384c.336-.193.607-.056.607.308a1.3,1.3,0,0,1-.607,1L199.567,104.41c-.336.193-.607.056-.607-.3A1.3,1.3,0,0,1,199.567,103.1Z" transform="translate(-191.849 -83.002)" fill="#e0e0e0"/>
          <path id="Path_22018" data-name="Path 22018" d="M199.567,112.449l24.912-14.384c.336-.193.607-.056.607.308a1.3,1.3,0,0,1-.607,1L199.567,113.76c-.336.193-.607.056-.607-.3a1.3,1.3,0,0,1,.607-1.007Z" transform="translate(-191.849 -89.737)" fill="#e0e0e0"/>
          <path id="Path_22019" data-name="Path 22019" d="M199.567,121.8l24.912-14.384c.336-.193.607-.056.607.3a1.3,1.3,0,0,1-.607,1.007L199.567,123.11c-.336.193-.607.056-.607-.3A1.3,1.3,0,0,1,199.567,121.8Z" transform="translate(-191.849 -96.473)" fill="#e0e0e0"/>
          <path id="Path_22020" data-name="Path 22020" d="M199.567,131.156l24.912-14.381c.336-.193.607-.056.607.3a1.3,1.3,0,0,1-.607,1.007L199.567,132.47c-.336.193-.607.056-.607-.308A1.3,1.3,0,0,1,199.567,131.156Z" transform="translate(-191.849 -103.215)" fill="#e0e0e0"/>
          <path id="Path_22021" data-name="Path 22021" d="M199.567,140.5l24.912-14.384c.336-.193.607-.056.607.3a1.3,1.3,0,0,1-.607,1.007L199.567,141.81c-.336.193-.607.056-.607-.308A1.3,1.3,0,0,1,199.567,140.5Z" transform="translate(-191.849 -109.944)" fill="#e0e0e0"/>
          <path id="Path_22022" data-name="Path 22022" d="M199.567,149.849l24.912-14.384c.336-.193.607-.056.607.3a1.3,1.3,0,0,1-.607,1.007L199.567,151.16c-.336.193-.607.056-.607-.308A1.3,1.3,0,0,1,199.567,149.849Z" transform="translate(-191.849 -116.679)" fill="#e0e0e0"/>
          <path id="Path_22023" data-name="Path 22023" d="M199.567,159.17l24.912-14.4c.336-.193.607-.056.607.3a1.3,1.3,0,0,1-.607,1.007L199.567,160.47c-.336.193-.607.056-.607-.308a1.3,1.3,0,0,1,.607-.993Z" transform="translate(-191.849 -123.386)" fill="#e0e0e0"/>
          <path id="Path_22024" data-name="Path 22024" d="M199.567,168.545l24.912-14.384c.336-.2.607-.056.607.3a1.3,1.3,0,0,1-.607,1.007l-24.912,14.384c-.336.193-.607.056-.607-.308A1.3,1.3,0,0,1,199.567,168.545Z" transform="translate(-191.849 -130.147)" fill="#e0e0e0"/>
          <path id="Path_22025" data-name="Path 22025" d="M216.992,192.549a.34.34,0,0,1-.039.056l-15.542-6.893a2.676,2.676,0,0,0-3.356,1.035l-16.615,26.66-.615.355h0a.5.5,0,0,1-.475.053l17.088-27.42a2.684,2.684,0,0,1,3.356-1.037Z" transform="translate(-178.443 -152.52)" fill="#ba68c8"/>
          <path id="Path_22026" data-name="Path 22026" d="M196.506,198.658l.039-.034L181.421,222.9l-.615.355h0a.509.509,0,0,1-.475.053l15.567-25Z" transform="translate(-178.429 -162.007)" fill="#fff" opacity="0.4"/>
          <path id="Path_22027" data-name="Path 22027" d="M261.014,192.554a.338.338,0,0,1-.039.056l-15.544-6.9a2.676,2.676,0,0,0-3.356,1.035l-.615-.358a2.684,2.684,0,0,1,3.356-1.029Z" transform="translate(-222.465 -152.523)" fill="#fff"/>
          <path id="Path_22028" data-name="Path 22028" d="M219.634,107.282h0c-.034-.2-.14-.257-.28-.162a.808.808,0,0,0-.179.185l-.078.109L203.77,128.765l-2.29,3.191a1.306,1.306,0,0,1-1.261.473l-1.977-.47-15.1-3.59a.769.769,0,0,0-.308,0,.558.558,0,0,0-.115.036l.417-.24,14.541,3.459,1.983.453a1.3,1.3,0,0,0,1.261-.473l2.29-3.188,15.413-21.461a.615.615,0,0,1,.173-.159l-1.325-.763.559-.322.923.531a1.6,1.6,0,0,1,.679,1.04Z" transform="translate(-180.15 -95.308)" fill="#ba68c8"/>
          <path id="Path_22029" data-name="Path 22029" d="M243.758,187.36l-2.29,3.191a1.306,1.306,0,0,1-1.261.473l-1.977-.47.039-.034.1-.151,1.295.308a1.3,1.3,0,0,0,1.261-.473l2.268-3.154Z" transform="translate(-220.138 -153.903)" fill="#fff" opacity="0.4"/>
          <path id="Path_22030" data-name="Path 22030" d="M271.674,106.241l-.923-.531-.559.322,1.334.769h0a.627.627,0,0,0-.173.159l-15.41,21.461-.022.031h0l.559.31,15.346-21.35.078-.109a.807.807,0,0,1,.179-.185c.137-.1.243-.039.28.162h0a1.6,1.6,0,0,0-.688-1.04Z" transform="translate(-232.881 -95.308)" fill="#fff"/>
          <path id="Path_22031" data-name="Path 22031" d="M176.838,183.441l-3.288-1.9v28.323a1.563,1.563,0,0,0,.7,1.216l.789.456a1.569,1.569,0,0,0,1.4,0l.394-.229Z" transform="translate(-173.544 -149.933)" fill="#ba68c8"/>
          <path id="Path_22032" data-name="Path 22032" d="M176.838,183.441l-3.288-1.9v28.323a1.563,1.563,0,0,0,.7,1.216l.789.456a1.569,1.569,0,0,0,1.4,0l.394-.229Z" transform="translate(-173.544 -149.933)" fill="#95a2fe"/>
          <path id="Path_22033" data-name="Path 22033" d="M218.176,111.8v27.213a1.4,1.4,0,0,1-.218.71,1.454,1.454,0,0,1-.411.459l-.025.017-.048.031-35.893,20.72h0a.667.667,0,0,1-.137.064.892.892,0,0,1,0-.126v-27.5a.61.61,0,0,1,.789-.621l15.1,3.59,1.977.47a1.306,1.306,0,0,0,1.261-.473l2.279-3.2Z" transform="translate(-179.23 -99.695)" fill="#455a64"/>
          <path id="Path_22034" data-name="Path 22034" d="M218.727,110.98v27.513a1.429,1.429,0,0,1-.218.71,1.533,1.533,0,0,1-.411.461l-.028.014a.429.429,0,0,1-.045.031l-35.893,20.709h0a.5.5,0,0,1-.478.056.425.425,0,0,1-.21-.336.9.9,0,0,1,0-.126v-27.5a.61.61,0,0,1,.789-.621l15.1,3.59,1.977.47a1.306,1.306,0,0,0,1.261-.473l2.279-3.2,15.338-21.352.078-.109C218.509,110.454,218.727,110.522,218.727,110.98Z" transform="translate(-179.23 -98.819)" fill="#95a2fe"/>
          <path id="Path_22035" data-name="Path 22035" d="M273.175,195.581l.045-.031.028-.014a1.533,1.533,0,0,0,.411-.461,1.435,1.435,0,0,0,.215-.7L258,188.16l10.626,10.047Z" transform="translate(-234.38 -154.702)" fill="#ba68c8"/>
          <path id="Path_22036" data-name="Path 22036" d="M273.175,195.581l.045-.031.028-.014a1.533,1.533,0,0,0,.411-.461,1.435,1.435,0,0,0,.215-.7L258,188.16l10.626,10.047Z" transform="translate(-234.38 -154.702)" fill="#fff"/>
          <path id="Path_22037" data-name="Path 22037" d="M181.392,224.133a.484.484,0,0,0,.218.461.5.5,0,0,0,.478-.056h0l12.357-7.133,2.821-17.815-15.874,24.084Z" transform="translate(-179.191 -162.936)" fill="#ba68c8"/>
          <path id="Path_22038" data-name="Path 22038" d="M198.786,190.028,181.6,217.46a.484.484,0,0,1-.218-.461v-.456l15.164-24.249.117-.187,1.507-2.416h0Z" transform="translate(-179.184 -155.805)" fill="#95a2fe"/>
          <path id="Path_22039" data-name="Path 22039" d="M219.055,193.761a1.533,1.533,0,0,1-.411.461l-.028.014-35.938,20.751h0a.5.5,0,0,1-.478.056l17.186-27.44a2.7,2.7,0,0,1,3.381-1.037Z" transform="translate(-179.776 -153.388)" fill="#95a2fe"/>
          <path id="Path_22040" data-name="Path 22040" d="M219.048,212.136a1.454,1.454,0,0,1-.411.459l-.025.017-35.943,20.751h0a.506.506,0,0,1-.478.053l.478-.761,35.32-20.4.028-.017a1.43,1.43,0,0,0,.372-.4Z" transform="translate(-179.768 -171.761)" fill="#95a2fe"/>
          <path id="Path_22041" data-name="Path 22041" d="M173.661,172.44l.559.33a2.8,2.8,0,0,0-.131.766v1.778l-.559-.33V173.2A2.8,2.8,0,0,1,173.661,172.44Z" transform="translate(-173.53 -143.378)" fill="#fff"/>
          <path id="Path_22042" data-name="Path 22042" d="M178.516,182.912l-.526-.126-.417.24-.576-.333-1.418-.816.551-.327Z" transform="translate(-175.007 -149.941)" fill="#ba68c8"/>
          <path id="Path_22043" data-name="Path 22043" d="M178.5,182.919h0l-2.382-1.359-.559.327,1.418.816.576.333h0a.361.361,0,0,1,.106-.034.769.769,0,0,1,.308,0l1.16.28,13.226,3.154.117-.187Z" transform="translate(-174.992 -149.948)" fill="#fff"/>
        </g>
        <g id="freepik--Notification--inject-23" transform="translate(32.526 32.1)">
          <path id="Path_22044" data-name="Path 22044" d="M303.476,185.273l-2.654-1.538h0l-.022-.014h0c-1.158-.654-2.749-.559-4.508.461a14.173,14.173,0,0,0-6.412,11.1c0,2.041.719,3.476,1.874,4.147h0l2.419,1.4.559-.948a6.476,6.476,0,0,0,1.84-.716,14.173,14.173,0,0,0,6.412-11.1,6.824,6.824,0,0,0-.2-1.639Z" transform="translate(-289.872 -183.306)" fill="#ffdd7e"/>
          <path id="Path_22045" data-name="Path 22045" d="M290.093,218.46a10.313,10.313,0,0,0-.243,2.153c0,2.041.719,3.476,1.874,4.147h0l2.419,1.4.559-.948a6.477,6.477,0,0,0,1.84-.716,10.26,10.26,0,0,0,1.748-1.289Z" transform="translate(-289.85 -208.63)" opacity="0.15"/>
          <path id="Path_22046" data-name="Path 22046" d="M305.292,189.4a14.173,14.173,0,0,0-6.412,11.1c0,4.088,2.872,5.746,6.412,3.7a14.173,14.173,0,0,0,6.412-11.1C311.7,189.009,308.835,187.351,305.292,189.4Z" transform="translate(-296.355 -187.062)" fill="#ffdd7e"/>
          <path id="Path_22047" data-name="Path 22047" d="M315.706,202.458v-1.572c0-1.792-1.091-2.617-2.433-1.84h0a5.829,5.829,0,0,0-2.436,4.65v1.4a5.57,5.57,0,0,1-.819,2.743h0a1.519,1.519,0,0,0-.2.719h0c0,.45.28.66.612.464l5.685-3.283a1.471,1.471,0,0,0,.612-1.172h0a.573.573,0,0,0-.179-.475h0A1.636,1.636,0,0,1,315.706,202.458Z" transform="translate(-304.236 -194.434)" fill="#fff"/>
          <path id="Path_22048" data-name="Path 22048" d="M320.325,229a2.817,2.817,0,0,1-1.166,2.237c-.643.377-1.169-.022-1.169-.881Z" transform="translate(-310.121 -216.223)" fill="#fff"/>
        </g>
      </g>
    </g>
  </g>
</svg>
