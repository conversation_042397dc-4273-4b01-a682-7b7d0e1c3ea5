# Flutter Desktop Development Setup Guide

This guide will help you set up Flutter desktop development for Windows, macOS, and Linux platforms.

## Prerequisites

### 1. Flutter Installation

#### Windows
1. Download Flutter SDK from https://flutter.dev/docs/get-started/install/windows
2. Extract to `C:\flutter` (or your preferred location)
3. Add `C:\flutter\bin` to your PATH environment variable
4. Install Visual Studio 2022 with "Desktop development with C++" workload
5. Install Git for Windows

#### macOS
1. Download Flutter SDK from https://flutter.dev/docs/get-started/install/macos
2. Extract to `/Users/<USER>/flutter` (or your preferred location)
3. Add `/Users/<USER>/flutter/bin` to your PATH in `.zshrc` or `.bash_profile`
4. Install Xcode from the App Store
5. Install CocoaPods: `sudo gem install cocoapods`

#### Linux (Ubuntu/Debian)
1. Download Flutter SDK from https://flutter.dev/docs/get-started/install/linux
2. Extract to `/home/<USER>/flutter` (or your preferred location)
3. Add `/home/<USER>/flutter/bin` to your PATH in `.bashrc`
4. Install required dependencies:
   ```bash
   sudo apt-get update
   sudo apt-get install curl git unzip xz-utils zip libglu1-mesa
   sudo apt-get install clang cmake ninja-build pkg-config libgtk-3-dev
   ```

### 2. Enable Desktop Support

Run these commands in your project directory:

```bash
# Enable desktop support globally
flutter config --enable-windows-desktop
flutter config --enable-macos-desktop
flutter config --enable-linux-desktop

# Create platform-specific folders
flutter create --platforms=windows,macos,linux .
```

### 3. Verify Installation

```bash
flutter doctor
flutter devices
```

## Platform-Specific Configuration

### Windows Configuration
- Requires Visual Studio 2022 with C++ tools
- Windows 10 version 1903 or higher
- Supports both x64 and ARM64 architectures

### macOS Configuration
- Requires macOS 10.14 or higher
- Xcode 12.0 or higher
- Supports both Intel and Apple Silicon Macs

### Linux Configuration
- Requires Ubuntu 18.04 or higher (or equivalent)
- GTK 3.0 development libraries
- Supports x64 architecture

## Next Steps

After running the setup commands above, this project will be configured with:
1. Platform-specific folders (windows/, macos/, linux/)
2. Updated pubspec.yaml with desktop dependencies
3. Desktop-optimized UI components
4. Build and distribution configurations
5. Testing setup for desktop platforms

## Development Workflow

1. **Development**: Use `flutter run -d windows/macos/linux` to run on desktop
2. **Testing**: Use `flutter test` for unit tests, `flutter integration_test` for UI tests
3. **Building**: Use `flutter build windows/macos/linux` to create release builds
4. **Distribution**: Platform-specific packaging and distribution methods

## Project Structure

After setup, your project will have the following desktop-specific structure:

```
├── windows/                    # Windows desktop configuration
│   ├── runner/
│   │   ├── main.cpp
│   │   ├── flutter_window.h
│   │   └── flutter_window.cpp
│   └── CMakeLists.txt
├── macos/                      # macOS desktop configuration
│   └── Runner/
│       ├── AppDelegate.swift
│       ├── MainFlutterWindow.swift
│       └── Info.plist
├── linux/                     # Linux desktop configuration
│   ├── main.cc
│   ├── my_application.h
│   ├── my_application.cc
│   └── CMakeLists.txt
├── lib/
│   ├── helper/
│   │   ├── platform/
│   │   │   └── platform_helper.dart    # Platform detection utilities
│   │   └── desktop/
│   │       └── window_config.dart      # Desktop window management
│   └── widgets/
│       └── responsive/
│           └── responsive_layout.dart  # Responsive UI components
├── scripts/
│   ├── build_desktop.sh              # Unix build script
│   └── build_desktop.bat             # Windows build script
├── installer/
│   ├── windows/
│   │   └── installer.nsi              # NSIS installer script
│   ├── macos/
│   │   └── create_dmg.sh              # DMG creation script
│   └── linux/
│       └── create_appimage.sh         # AppImage creation script
└── test/
    ├── desktop_test_helper.dart       # Desktop testing utilities
    └── platform_test.dart             # Platform-specific tests
```

## Development Commands

### Running on Desktop
```bash
# Run on Windows
flutter run -d windows

# Run on macOS
flutter run -d macos

# Run on Linux
flutter run -d linux

# List available devices
flutter devices
```

### Building for Desktop
```bash
# Build for current platform
./scripts/build_desktop.sh        # Unix systems
./scripts/build_desktop.bat       # Windows

# Build for specific platforms
flutter build windows --release
flutter build macos --release
flutter build linux --release
```

### Testing
```bash
# Run all tests
flutter test

# Run platform-specific tests
flutter test test/platform_test.dart

# Run tests with coverage
flutter test --coverage
```

## Platform-Specific Features

### Conditional Feature Usage
```dart
import 'package:overolasuppliers/helper/platform/platform_helper.dart';

// Check platform capabilities
if (PlatformHelper.hasCameraSupport) {
  // Use camera on mobile
  await openCamera();
} else {
  // Show file picker on desktop
  await pickImageFile();
}

// Responsive UI
Widget buildUI() {
  return ResponsiveLayout(
    mobile: MobileLayout(),
    desktop: DesktopLayout(),
  );
}
```

### Desktop Window Management
```dart
import 'package:overolasuppliers/helper/desktop/window_config.dart';

// Initialize window configuration
await WindowConfig.initialize();

// Control window state
await WindowConfig.maximizeWindow();
await WindowConfig.minimizeWindow();
await WindowConfig.setWindowSize(Size(1200, 800));
```

## Distribution

### Windows
1. Build: `flutter build windows --release`
2. Create installer: Use NSIS script in `installer/windows/`
3. Sign: Use Windows SDK signing tools
4. Distribute: Microsoft Store, website, or other channels

### macOS
1. Build: `flutter build macos --release`
2. Create DMG: `./installer/macos/create_dmg.sh`
3. Sign & Notarize: Use Xcode or command line tools
4. Distribute: Mac App Store, website, or other channels

### Linux
1. Build: `flutter build linux --release`
2. Create AppImage: `./installer/linux/create_appimage.sh`
3. Create packages: .deb, .rpm, Flatpak, Snap
4. Distribute: Package repositories, website, or other channels

## Troubleshooting

### Common Issues

#### Flutter not found
- **Solution**: Add Flutter to your PATH environment variable
- **Windows**: Add `C:\flutter\bin` to PATH
- **macOS/Linux**: Add `/path/to/flutter/bin` to PATH in shell profile

#### Desktop support not enabled
```bash
flutter config --enable-windows-desktop
flutter config --enable-macos-desktop
flutter config --enable-linux-desktop
```

#### Missing dependencies
- **Windows**: Install Visual Studio 2022 with C++ tools
- **macOS**: Install Xcode and command line tools
- **Linux**: Install build essentials and GTK development libraries

#### Plugin compatibility issues
- Check `PLATFORM_DEPENDENCIES.md` for mobile-specific plugins
- Use conditional imports for platform-specific features
- Test on all target platforms

#### Build failures
1. Clean the project: `flutter clean && flutter pub get`
2. Check Flutter doctor: `flutter doctor`
3. Verify platform-specific dependencies
4. Check error logs for specific issues

### Performance Optimization

#### Desktop-specific optimizations
- Use `flutter build --release` for production builds
- Enable tree shaking: `--tree-shake-icons`
- Use `--obfuscate` for code protection
- Optimize assets for desktop screen sizes

#### Memory management
- Use `flutter build --split-debug-info` for better debugging
- Monitor memory usage with Flutter DevTools
- Optimize image assets for desktop resolutions

### Testing on Different Platforms

#### Windows Testing
- Test on Windows 10 and 11
- Verify with different screen DPI settings
- Test with Windows Defender and antivirus software

#### macOS Testing
- Test on Intel and Apple Silicon Macs
- Verify with different macOS versions (10.14+)
- Test with Gatekeeper and notarization

#### Linux Testing
- Test on Ubuntu, Fedora, and other distributions
- Verify with different desktop environments (GNOME, KDE, XFCE)
- Test with different GTK themes

