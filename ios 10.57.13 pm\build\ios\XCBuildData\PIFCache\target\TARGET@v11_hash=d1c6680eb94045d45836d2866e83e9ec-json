{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983491c2e5e3e56fae83980b5caf217565", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b2f4b25a8d90636f47ae32244238e77c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9855cd2fbb4972edc6073aa8f05798be2c", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e4d86d70890c21ed16cf87923a2cf2b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9855cd2fbb4972edc6073aa8f05798be2c", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b7fb43e5cb40c6c38d1a3e45c7031a6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982349704364a7a9b76b65572a2209554a", "guid": "bfdfe7dc352907fc980b868725387e98193cbc37df47ca6bd1f7b2987d581868", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980397c93d593f6a3d9a3aa32fdda20cbc", "guid": "bfdfe7dc352907fc980b868725387e981c77104c961383e47ab5bc0ee51362be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c20593296132c12d043e19ceb9e388ad", "guid": "bfdfe7dc352907fc980b868725387e983b9e66575394fd880d61297cab95ad0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9462fe19c99d4abed75335e3b6f138f", "guid": "bfdfe7dc352907fc980b868725387e98bcf4c9beab448fb2d7149cd3b63963a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981215b09cb0f7ef5314e3d3dcbdee10e7", "guid": "bfdfe7dc352907fc980b868725387e987c0a191d08bda3e5833bf846878d0b72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98336b36e18efee61868fa838fc395df1f", "guid": "bfdfe7dc352907fc980b868725387e986c2a7ffc1545121fc8cd04a3d7b29d5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a520fc7f992038b03dd3cecf51b6b82b", "guid": "bfdfe7dc352907fc980b868725387e98ebc431f31b4ba40239ec86964236fe84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f7f634f8adcd11ebb6faa8489f3f9c2", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b6c2377c791335b81f5182bd7b8a59c", "guid": "bfdfe7dc352907fc980b868725387e98df3885b7b8c513219172a52557fe731e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e36750fb6cd3df6fe639c1ffeffda4", "guid": "bfdfe7dc352907fc980b868725387e9854e6ca851f63ed3cc2e8e08edc65479a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c74fa87b8ae9f3fa2445b5c6c3cf1734", "guid": "bfdfe7dc352907fc980b868725387e98a45a633473bf72558a786ad97ba60a42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc42bd6c97cf75704ab65c80aafc0aa", "guid": "bfdfe7dc352907fc980b868725387e986e7fdef0912afe567091baba0ec28fcc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983fb1e1091addc45bdda18e41d318ff19", "guid": "bfdfe7dc352907fc980b868725387e98865d81ee9ba06a26516e000cf4f66bc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98028f3ee6038ef9d46fd4da5492211da1", "guid": "bfdfe7dc352907fc980b868725387e9865cd37c44f872c70257508efae941891"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891d74e962ec97e8c5c88478b04248d2c", "guid": "bfdfe7dc352907fc980b868725387e98b62591ad28ed7a89009145d6d76a21c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98911c010495fa554111678256e5dadb72", "guid": "bfdfe7dc352907fc980b868725387e98f0dfb7ca0ca36190fab8c20d10f6e2f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd049c858eb992b6ee2170787ef1269d", "guid": "bfdfe7dc352907fc980b868725387e981cee213e907a8cfbe7a4bca267a59968"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888a4b59ba9f89ccc88dd174cc85ba0bd", "guid": "bfdfe7dc352907fc980b868725387e98443745df8a468cbf6c91df114c6b4892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894836a8a09bfb2e1e07b0ec753b53b76", "guid": "bfdfe7dc352907fc980b868725387e98381aacafdc460bf824e5a270b95c9bde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98588941875538eb8564866a3aa13cc093", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98965601c12501e0ebb6cf0ea01b56631a", "guid": "bfdfe7dc352907fc980b868725387e982af67cb7b567ab7fa318a91c32ade8c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fedb9e79a3574297bf476752c1553e9", "guid": "bfdfe7dc352907fc980b868725387e988e3ec702f10fd46b525974c712062460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bb18d9921fe5a874c97530fd9b019f8", "guid": "bfdfe7dc352907fc980b868725387e98f812a7af6d7843cd1aafbed23a7cc2b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d008d588325773b0c5ce641f074653c7", "guid": "bfdfe7dc352907fc980b868725387e98b05d9a098385ea3e71527283300ee4b9"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}