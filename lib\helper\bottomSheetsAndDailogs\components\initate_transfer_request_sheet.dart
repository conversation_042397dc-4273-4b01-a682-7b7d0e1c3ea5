import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:slider_button/slider_button.dart';

class InitateTransferRequestSheet extends StatelessWidget
    implements ServerResponse {
  final double transferAmount;
  RxBool amountTransfer = false.obs;

  InitateTransferRequestSheet({Key? key, required this.transferAmount})
      : super(key: key);

  late GraphQlInitilize _request;

  @override
  Widget build(BuildContext context) {
    _request = GraphQlInitilize(this);
    return SafeArea(
      top: false,
      child: Obx(
        () => Container(
            height: amountTransfer.value ? 192 : 240,
            width: context.width,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(20),
                topLeft: Radius.circular(20),
              ),
              color: AppColors.feildColorDark,
            ),
            child: Column(
              children: <Widget>[
                Container(
                  margin: const EdgeInsets.all(10),
                  width: 90,
                  height: 5,
                  decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(5)),
                ),
                const Spacer(),
                Text(
                  EnStrings.transferRequest,
                  style: FontStyles.fontSemibold(),
                ),
                const Spacer(),
                if (!amountTransfer.value)
                  Text(
                    EnStrings.youAreGoingToSend,
                    style: FontStyles.fontRegular(),
                  ),
                if (!amountTransfer.value) const Spacer(),
                if (!amountTransfer.value)
                  Wrap(
                    children: <Widget>[
                      Text(
                        "$transferAmount",
                        style: FontStyles.fontBold(fontSize: 24),
                      ),
                      Text('AED', style: FontStyles.fontMedium(fontSize: 12)),
                    ],
                  ),
                if (amountTransfer.value)
                  RichText(
                    text: TextSpan(children: <TextSpan>[
                      TextSpan(
                          text: "$transferAmount",
                          style: FontStyles.fontBold(fontSize: 16)),
                      TextSpan(
                          text: " AED has been sent to you successfully",
                          style: FontStyles.fontRegular()),
                    ]),
                  ),
                const Spacer(),
                if (!amountTransfer.value)
                  SliderButton(
                    height: 60,
                    width: context.width - 30,
                    action: () async {
                      _request.runMutation(
                          context: context,
                          query: GraphQlQuries.addWithdrawalrequest,
                          variables: GraphQlVariables.addWithdrawalrequest(
                              requestedAmount: transferAmount));
                      return Future.value(true);
                    },
                    label: Text("     >  >  >   ",
                        style: FontStyles.fontBold(fontSize: 20)),
                    radius: 10,
                    shimmer: true,
                    buttonColor: AppColors.colorPrimary,
                    backgroundColor: AppColors.colorPrimary.withOpacity(0.5),
                    highlightedColor: AppColors.colorSecondary_Red,
                    baseColor: Colors.white,
                    child: Container(
                      width: 100,
                      decoration: BoxDecoration(
                        color: AppColors.colorPrimary,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Center(
                        child: Text(
                          EnStrings.send,
                          style: FontStyles.fontSemibold(),
                        ),
                      ),
                    ),
                  ),
                if (amountTransfer.value)
                  ElbaabButtonWidget(
                    height: 42,
                    onPress: () => Get.back(result: true),
                    margin: const EdgeInsets.symmetric(horizontal: 30),
                    colors: AppColors.colorPrimary,
                    text: EnStrings.done,
                  ),
                const Spacer(),
              ],
            )),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    BaseModel baseModel = BaseModel.fromJson(response);
    if (baseModel.status == statusOK) {
      amountTransfer.value = true;
    }
  }
}
