import 'package:get/get.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/screens/autentications/add_business_info.dart';
import 'package:overolasuppliers/screens/autentications/email_verification.dart';
import 'package:overolasuppliers/screens/autentications/forget_password/reset_password.dart';
import 'package:overolasuppliers/screens/autentications/login.dart';
import 'package:overolasuppliers/screens/autentications/number_verification.dart';
import 'package:overolasuppliers/screens/autentications/signup.dart';
import 'package:overolasuppliers/screens/autentications/signup_success.dart';
import 'package:overolasuppliers/screens/elbaab_support_contact.dart';
import 'package:overolasuppliers/screens/google_map/open_google_map.dart';
import 'package:overolasuppliers/screens/notifications.dart';
import 'package:overolasuppliers/screens/products/add_product/binding/add_product_binding.dart';
import 'package:overolasuppliers/screens/products/add_product/similar_products.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_colors.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_custom_options.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_step_five.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_step_four.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_step_one.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_step_three.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_step_two.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_tabview.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_size.dart';
import 'package:overolasuppliers/screens/products/add_product/views/product_specification.dart';
import 'package:overolasuppliers/screens/products/add_product/views/product_uploaded.dart';
import 'package:overolasuppliers/screens/products/add_product/views/select_category.dart';
import 'package:overolasuppliers/screens/products/add_product/views/select_manufactures.dart';
import 'package:overolasuppliers/screens/products/add_product/views/trash_products/trash_products.dart';
import 'package:overolasuppliers/screens/products/add_product/views/view_colors.dart';
import 'package:overolasuppliers/screens/products/match_product.dart';
import 'package:overolasuppliers/screens/products/product_detail.dart';
import 'package:overolasuppliers/screens/products/view_variations.dart';
import 'package:overolasuppliers/screens/select_language.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/bottom_navigation/shop_home.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/all_transferd_amount.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/all_sale.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/delivery_charges_fees.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/initiate_transfer_request.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/top_selling_items.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/total_vat.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/view_income_expenses.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/product_free_delivery.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/reviews.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/order_details.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/bank_info/update_bank_info.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/bank_info/view_bank_info.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/connected_pages/accounts_managment.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/connected_pages/change_password.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/connected_pages/change_your_email.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/connected_pages/change_your_number.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/connected_pages/returned_requests.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/connected_pages/app_settings.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/connected_pages/update_your_name.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/deactive_delete/delete_deactive_account.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/faqs.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/validation_history.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/controller/shop_binding.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/create_shop.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/create_shop_first_step.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/shop_about.dart';
import 'package:overolasuppliers/screens/splash_screen.dart';

allPages() {
  return [
    GetPage(name: RouteNames.splashScreen, page: () => const SplashScreen()),
    GetPage(name: RouteNames.selecLangugage, page: () =>  const SelectLanguage()),
    GetPage(name: RouteNames.signupScreen, page: () => const SignUp()),
    GetPage(
        name: RouteNames.selectCategoryScreen, page: () => SelectCategory()),
    GetPage(
        name: RouteNames.selectManufactureScreen,
        page: () => SelectManufacturers()),
    GetPage(
        name: RouteNames.productSpecificationScreen,
        page: () => ProductSpecification()),
    GetPage(name: RouteNames.addColorsScreen, page: () => const AddColors()),
    GetPage(
        name: RouteNames.addCustomOptionsScreen,
        page: () => const AddCustomOptions()),
    GetPage(name: RouteNames.addSizeScreen, page: () => const AddSize()),
    GetPage(
        name: RouteNames.addProductStepThreeScreen,
        page: () => AddProductStepThree()),
    GetPage(
        name: RouteNames.addProductStepFourScreen,
        page: () => AddProductStepFour()),
    GetPage(
        name: RouteNames.addProductStepFiveScreen,
        page: () => AddProductStepFive(),
        binding: AddProductBinding()),
    GetPage(
        name: RouteNames.productDetailScreen,
        page: () => ProductDetail(),
        binding: AddProductBinding()),
    GetPage(name: RouteNames.trashProductScreen, page: () => const TrashProducts()),
    GetPage(
        name: RouteNames.chanegPasswordScreen, page: () => ChangePassword()),
    GetPage(
        name: RouteNames.changeYourEmailScreen, page: () => ChangeYourEmail()),
    GetPage(name: RouteNames.orderDetailScreen, page: () => OrderDetail()),
    GetPage(name: RouteNames.allSalesScreen, page: () => const AllSales()),
    GetPage(
        name: RouteNames.allTransferdAmountScreen,
        page: () => const AllTransferdAmount()),
    GetPage(
        name: RouteNames.initiateTransferRequestScreen,
        page: () => InitiateTransferRequest()),
    GetPage(
        name: RouteNames.deliveryChargesFeesScreen,
        page: () => DeliveryChargesFees()),
    GetPage(
        name: RouteNames.viewIncomeExpensesScreen,
        page: () => const ViewIncomeExpenses()),
    GetPage(
        name: RouteNames.topSellingItemScreen, page: () => TopSellingItem()),
    GetPage(name: RouteNames.totalVatScreen, page: () => TotalVat()),
    GetPage(name: RouteNames.faqsScreen, page: () => const Faqs()),
    GetPage(name: RouteNames.reviewsScreen, page: () =>  const Reviews()),
    GetPage(name: RouteNames.matchProductScreen, page: () => MatchProduct()),
    GetPage(
        name: RouteNames.productFreeDeliveryScreen,
        page: () => const ProductFreeDelivery()),
    GetPage(name: RouteNames.notificationsScreen, page: () => const Notifications()),
    GetPage(
        name: RouteNames.updateBankInfoScreen, page: () => UpdateBankInfo()),
    GetPage(name: RouteNames.viewBankInfoScreen, page: () => ViewBankInfo()),
    GetPage(
        name: RouteNames.updateUserNameScreen,
        page: () => const UpdateUserName()),
    GetPage(name: RouteNames.appSettingScreen, page: () => const AppSetting()),
    GetPage(name: RouteNames.accountMangmentScreen, page: () => const AccountManagement()),
    GetPage(
        name: RouteNames.viewVariationsScreen, page: () => ViewVariations()),
    GetPage(
        name: RouteNames.productUploadScreen,
        page: () => const ProductUploaded()),
    GetPage(
        name: RouteNames.addProductStepTwoScreen,
        page: () => AddProductStepTwo()),
    GetPage(
        name: RouteNames.elbaabSupportContactScreen,
        page: () => ElbaabSupportContact()),
    GetPage(name: RouteNames.signupSuccessScreen, page: () => SignupSuccess()),
    GetPage(name: RouteNames.shopAboutScreen, page: () => ShopAbout()),
    GetPage(name: RouteNames.viewColorScreen, page: () => ViewColors()),
    GetPage(
        name: RouteNames.changeYourNumberScreen,
        page: () => ChangeYourNumber()),
    GetPage(
        name: RouteNames.validationHistoryScreen,
        page: () => ValidationHistory()),
    GetPage(
        name: RouteNames.createShopFirstStepScreen,
        page: () => const CreateShopFirstStep()),
    GetPage(
        name: RouteNames.addBusinessInfoScreen,
        page: () => const AddBusinessInformation()),
    GetPage(
        name: RouteNames.createShopScreen,
        page: () => CreateShop(),
        binding: ShopBinding()),
    GetPage(
        name: RouteNames.openGoogleMapScreen,
        page: () => const OpenGoogleMap()),
    GetPage(name: RouteNames.shopHomeScreen, page: () => const ShopHome()),
    GetPage(
        name: RouteNames.addProductTabViewScreen,
        page: () => const AddProductTabView(),
        binding: AddProductBinding()),
    GetPage(
        name: RouteNames.returendRequestsScreen,
        page: () => const ReturendRequests()),
    GetPage(
        name: RouteNames.similarProductsScreen, page: () => SimilarProducts()),
    GetPage(
        name: RouteNames.addProductFirstStepScreen,
        page: () => AddProductStepOne(),
        binding: AddProductBinding()),
    GetPage(name: RouteNames.resetPasswordScreen, page: () => ResetPassword()),
    GetPage(
        name: RouteNames.deleteAndDeactiveScreen,
        page: () => DeleteAndDeactiveAccount()),
    GetPage(name: RouteNames.loginScreen, page: () => const Login()),
    GetPage(
        name: RouteNames.numberVerificationScreen,
        page: () => NumberVerification()),
    GetPage(
        name: RouteNames.emailVerificationScreen,
        page: () => const EmailVerification()),
  ];
}
