import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/screens/products/add_product/model/product_detail_model.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ViewColors extends GetView<AddProductController> {
  ViewColors({Key? key}) : super(key: key);
  RxString errorColor = ''.obs;
  RxList<String> arrReturnImageUrls = <String>[].obs;
  late AppLocalizations appLocal;

  String colorCheck(String element, {required String type}) {
    int i = int.parse(element.replaceAll(RegExp(r'[^0-9]'), ''));
    var productColor =
        (controller.product?.productOptions?.productColors ?? [])[i - 1];
    int indexOf = controller.colorList
        .indexWhere((element) => element.id == productColor.id);
    if (indexOf != -1) {
      ColorModel colorModel = controller.colorList[indexOf];
      if (type == "icon") {
        if (colorModel.thumnailUrl == productColor.colorIcon) {
          return "*) ${appLocal.adminReturn("${appLocal.localeName == "ar" ? colorModel.colorFamilyAr : colorModel.colorFamily}${colorModel.colorName.isNotEmpty ? ", ${appLocal.localeName == "ar" ? colorModel.colorNameAr : colorModel.colorName}" : ""}")}";
        } else {
          return "";
        }
      } else if (type == "name") {
        if (colorModel.colorName == productColor.colorName) {
          return "*) ${appLocal.adminReturn("${appLocal.localeName == "ar" ? colorModel.colorFamilyAr : colorModel.colorFamily}${colorModel.colorName.isNotEmpty ? ", ${appLocal.localeName == "ar" ? colorModel.colorNameAr : colorModel.colorName}" : ""}")}";
        } else {
          return "";
        }
      } else {
        if (colorModel.colorFamily == productColor.colorFamily) {
          return "*) ${appLocal.adminReturn("${appLocal.localeName == "ar" ? colorModel.colorFamilyAr : colorModel.colorFamily}${colorModel.colorName.isNotEmpty ? ", ${appLocal.localeName == "ar" ? colorModel.colorNameAr : colorModel.colorName}" : ""}")}";
        } else {
          return "";
        }
      }
    } else {
      return "";
    }
  }

  String checkReturnedColors() {
    String strReturnedMessage = "";
    if (controller.product?.productOptions?.productColors?.isNotEmpty ??
        false) {
      for (var element in controller.validationHistory?.returnValues ?? []) {
        if (element.contains("Color Name")) {
          String returnMessage = colorCheck(element, type: "name");
          if (returnMessage.isNotEmpty) {
            strReturnedMessage += "$returnMessage ${appLocal.colorName}\n";
          }
        }
        if (element.contains("Color family")) {
          String returnMessage = colorCheck(element, type: "colorfamily");
          if (returnMessage.isNotEmpty) {
            strReturnedMessage += "$returnMessage ${appLocal.colorFamily}\n";
          }
        }
        if (element.contains("Color Icon")) {
          String returnMessage = colorCheck(element, type: "icon");
          if (returnMessage.isNotEmpty) {
            strReturnedMessage += "$returnMessage ${appLocal.colorIcon}\n";
          }
        }
        if (element.contains("image")) {
          int i = int.parse(element.replaceAll(RegExp(r'[^0-9]'), ''));
          String image = element.replaceAll(RegExp(r"[0-9]+"), "");
          int indexWhere =
              (controller.product?.productOptions?.productColors ?? [])
                  .indexWhere((element) =>
                      element.colorName?.trim() ==
                          image.replaceAll("image", "").trim() ||
                      element.colorFamily ==
                          image.replaceAll("image", "").removeAllWhitespace);
          if (indexWhere != -1) {
            String colorId =
                (controller.product?.productOptions?.productColors ??
                            [])[indexWhere]
                        .id ??
                    "";
            int colorIndex = controller.colorList
                .indexWhere((element) => element.id == colorId);
            if (colorIndex != -1) {
              ColorModel color = controller.colorList[colorIndex];
              if (color.imagesUrl.contains(controller.product?.productOptions
                      ?.productColors?[indexWhere].colorImages?[i - 1] ??
                  "")) {
                strReturnedMessage +=
                    "*) Admin returned ( ${color.colorFamily}${color.colorName.isNotEmpty ? ", ${color.colorName}" : ""} ) $i${GlobalMethods.ordinal(i)} image\n";
                arrReturnImageUrls.add(controller.product?.productOptions
                        ?.productColors?[indexWhere].colorImages?[i - 1] ??
                    "");
              }
            }
          }
        }
      }
    }
    return strReturnedMessage;
  }

  @override
  Widget build(BuildContext context) {
    appLocal = AppLocalizations.of(context)!;
    Future.delayed(
        Duration.zero, () => errorColor.value = checkReturnedColors());
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.viewColors,
        leadingBack: true,
      ),
      body: Obx(
        () => Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (errorColor.value.isNotEmpty)
                  Container(
                    padding: EdgeInsets.all(12.r),
                    margin: EdgeInsets.only(bottom: 16.h),
                    decoration: BoxDecoration(
                      color: AppColors.colorDanger.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      errorColor.value,
                      style: FontStyles.fontRegular(
                        fontSize: 13,
                        color: AppColors.colorDanger,
                      ),
                    ),
                  ),
                ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: controller.colorList.length,
                    separatorBuilder: (context, index) => SizedBox(height: 16.h),
                    itemBuilder: (context, section) {
                      int previousSizeIndex = -1;
                      RxBool isHidden =
                          controller.colorList[section].isHidden.obs;
                      if (controller.product != null &&
                          (controller.product!.productOptions?.productColors
                                  ?.isNotEmpty ??
                              false)) {
                        if (section <
                            (controller.product!.productOptions?.productColors
                                    ?.length ??
                                0)) {
                          previousSizeIndex = controller.colorList.indexWhere(
                              (element) =>
                                  element.colorFamily ==
                                      (controller
                                              .product!
                                              .productOptions
                                              ?.productColors?[section]
                                              .colorFamily ??
                                          "") &&
                                  element.colorName ==
                                      (controller
                                              .product!
                                              .productOptions
                                              ?.productColors?[section]
                                              .colorName ??
                                          ""));
                        }
                      }
                      return Container(
                        height: 110.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.r),
                          border: Border.all(
                            color: Colors.grey.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        padding: EdgeInsets.all(8.r),
                        child: Stack(
                          children: [
                            Positioned.fill(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Wrap(
                                    crossAxisAlignment:
                                        WrapCrossAlignment.center,
                                    children: <Widget>[
                                      SizedBox(
                                        width: 20.w,
                                        height: 20.h,
                                        child: GlobalMethods.netWorkImage(
                                          controller
                                              .colorList[section].thumnailUrl,
                                          BorderRadius.circular(10),
                                          BoxFit.cover,
                                        ),
                                      ),
                                      SizedBox(width: 10.w),
                                      Text(
                                        '${appLocal.localeName == "en" ? controller.colorList[section].colorFamily : controller.colorList[section].colorFamilyAr}${(controller.colorList[section].colorName.isEmpty) ? "" : " , ${appLocal.localeName == "en" ? controller.colorList[section].colorName : controller.colorList[section].colorNameAr}"}',
                                        style: FontStyles.fontRegular(
                                            fontSize: 16),
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          ColorModel colorModel =
                                              controller.colorList[section];
                                          Get.toNamed(
                                              RouteNames.addColorsScreen,
                                              arguments: [
                                                colorModel,
                                                section
                                              ])?.then((value) => errorColor
                                              .value = checkReturnedColors());
                                        },
                                        icon: SvgPicture.string(
                                            SvgStrings.iconEditGray),
                                      ),
                                      if (previousSizeIndex != -1 &&
                                          (controller.isApprovedProduct ||
                                              controller.isMatchProduct))
                                        IconButton(
                                          onPressed: () {
                                            if (controller.isApprovedProduct ||
                                                (controller.isMatchProduct)) {
                                              if (previousSizeIndex ==
                                                  section) {
                                                int isLastItem = 0;
                                                for (ColorModel color
                                                    in controller.colorList) {
                                                  if (color.isHidden) {
                                                    isLastItem++;
                                                  }
                                                }
                                                if (isLastItem ==
                                                    (controller
                                                            .colorList.length -
                                                        1)) {
                                                  BottomSheets
                                                      .showAlertMessageBottomSheet(
                                                          "You can't hide all colors",
                                                          appLocal.alert,
                                                          context);
                                                } else {
                                                  BottomSheets
                                                      .showAlertMessageBottomSheet(
                                                          "Hide color will not show this color to the customers",
                                                          appLocal.alert,
                                                          context,
                                                          onActionClick: () {
                                                    controller
                                                        .colorList[section]
                                                        .isHidden = true;
                                                    isHidden.value = true;
                                                  });
                                                }
                                              }
                                            }
                                          },
                                          icon: Icon(
                                            Icons.remove_red_eye_sharp,
                                            color:
                                                Colors.white.withOpacity(0.42),
                                          ),
                                        ),
                                    ],
                                  ),
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: SizedBox(
                                      height: 50.h,
                                      child: ListView.builder(
                                          scrollDirection: Axis.horizontal,
                                          itemCount: controller
                                              .colorList[section]
                                              .imagesUrl
                                              .length,
                                          itemBuilder: (contex, index) {
                                            return Container(
                                              margin: const EdgeInsets.only(
                                                      right: 12)
                                                  .r,
                                              height: 50.h,
                                              width: 50.w,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                border: Border.all(
                                                  color: arrReturnImageUrls
                                                          .contains(controller
                                                              .colorList[
                                                                  section]
                                                              .imagesUrl[index])
                                                      ? AppColors.colorDanger
                                                      : Colors.transparent,
                                                  width: 2,
                                                ),
                                              ),
                                              child: GlobalMethods.netWorkImage(
                                                  controller.colorList[section]
                                                      .imagesUrl[index],
                                                  BorderRadius.circular(5),
                                                  BoxFit.cover),
                                            );
                                          }),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (previousSizeIndex != -1)
                              InkWell(
                                onTap: () => {
                                  controller.colorList[section].isHidden =
                                      false,
                                  isHidden.value = false
                                },
                                child: Obx(
                                  () => AnimatedContainer(
                                    curve: Curves.slowMiddle,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: !isHidden.value
                                          ? Colors.transparent
                                          : Colors.black.withOpacity(0.6),
                                    ),
                                    width: isHidden.value ? Get.width : 0,
                                    height: 100.h,
                                    duration: const Duration(seconds: 1),
                                    child: !isHidden.value
                                        ? Container()
                                        : const Icon(
                                            Icons.visibility_off,
                                            color: Colors.white,
                                            size: 40,
                                          ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      );
                    }),
                SizedBox(height: 30.h),
                if (controller.colorList.length < 30)
                  ElbaabButtonWidget(
                    onPress: () => Get.toNamed(RouteNames.addColorsScreen),
                    height: 48.h,
                    colors: AppColors.colorSecondaryYellow,
                    textColor: Colors.black,
                    text: appLocal.addNewColor,
                    borderRadius: 10.r,
                  ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: AppColors.headerColorDark,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: ElbaabButtonWidget(
          onPress: () => Get.back(),
          height: 48.h,
          colors: AppColors.colorPrimary,
          text: appLocal.done,
          borderRadius: 10.r,
        ),
      ),
    );
  }
}
