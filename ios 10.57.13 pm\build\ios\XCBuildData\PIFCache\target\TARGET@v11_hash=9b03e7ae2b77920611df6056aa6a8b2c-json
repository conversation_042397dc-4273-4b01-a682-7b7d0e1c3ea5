{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9843cd30290ed74ae825e8d2d840883a81", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987395a9585d0ac06757f168d9519bdb22", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981633dd420f71be32a2100d0c9f509efb", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98ddb57623034971eb788acd58415d50ba", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981633dd420f71be32a2100d0c9f509efb", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e980d3c4f5b871cbfb10f7ea9c0cfa5848e", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98eb10b9a2177b02af7154c29e66f1484f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988eef72faaf37024eeb1fa2d459069dd3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9862869ab606ce2b67e83372df960f3227", "guid": "bfdfe7dc352907fc980b868725387e98919712dbb502b10677933f273987f0d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7219a409d4894349f761e032b5aeaec", "guid": "bfdfe7dc352907fc980b868725387e98af968dc2c95cb7e613b7b533f58805be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac472bdf48bf21661733e10451fef58e", "guid": "bfdfe7dc352907fc980b868725387e98f16b67d5d8f9df0416f84a7d3ae98b7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835af710edb63901c5d7c4afd0edba3c3", "guid": "bfdfe7dc352907fc980b868725387e98b5372ed8a742f1f225d82d45760b581d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de978834a7801b7ccdb519fda8d81a7", "guid": "bfdfe7dc352907fc980b868725387e984bf7932b727a3b4241c2329cbd011ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f18715139f4bd179f76f0425de789a5c", "guid": "bfdfe7dc352907fc980b868725387e988700d43d85ec7d81d4eaaf1f9ff231dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bca76333e2e440afe67e5df9396be3f", "guid": "bfdfe7dc352907fc980b868725387e988edee458a83ae6be5aca58be674a9f59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce08031fd0b76a548f6a11ceb529fc1d", "guid": "bfdfe7dc352907fc980b868725387e98c1f5d55290096c7fe028c9506edcbca5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cbc3f0476c37d291382d1aa7333c02d", "guid": "bfdfe7dc352907fc980b868725387e98c3bf643d633e40906ab1b7f4e2434ac8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bfb4a96267019a95b7e8a93007dfff2", "guid": "bfdfe7dc352907fc980b868725387e98f61e9a73be46f8f173fdcd7ddbfcf347"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b149084f73c3457637fcd802b12b5763", "guid": "bfdfe7dc352907fc980b868725387e98a030cdd3866a41423161105886a89efb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829aeff768e529b5a6a2e478ed2396ff4", "guid": "bfdfe7dc352907fc980b868725387e984119bcde0b30bc292f483f8ae4ccccd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3e2a8866cb0d367d4496287c9d963bb", "guid": "bfdfe7dc352907fc980b868725387e98b2d886384add3e5cb8be0b3246b8f2fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e32598a0caf1c1b625a99d369af54643", "guid": "bfdfe7dc352907fc980b868725387e98dd9407ed1d77b6637c8435803e62eb14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984577436f2df650e8e14a545c5b8b0c1c", "guid": "bfdfe7dc352907fc980b868725387e98e315f2d3376eb784a9adeb48da8fd146"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc1516aca942611e3b4f655cf346e813", "guid": "bfdfe7dc352907fc980b868725387e98a70bb89382a3efef2c2c53d63433ac49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7176104a604c359d66d4a3646b08339", "guid": "bfdfe7dc352907fc980b868725387e9840d786128a4101ebbd60c4e3f669b81d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860a7549823136d7e3c890b0711f4e429", "guid": "bfdfe7dc352907fc980b868725387e984f7446cdf63f68f063e39dfaa11a3b50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e1443748e22ff973dcd4ca9201ab780", "guid": "bfdfe7dc352907fc980b868725387e98fd116f04175d76e3ee2db0e0d2349418"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e1e8f7329da7745cca14e83ded97c01", "guid": "bfdfe7dc352907fc980b868725387e988189d8e82f81dd698781df27243aa382"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd8d81187be137aa296b961c48604cdc", "guid": "bfdfe7dc352907fc980b868725387e9828a7146cc313c04a9d8402ca0425e6c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e49eb9f8e5351f663d73f68e954e17d8", "guid": "bfdfe7dc352907fc980b868725387e98a1bb064ad3c8b95f2d9629021e03a9ad"}], "guid": "bfdfe7dc352907fc980b868725387e9897561472148b08d380fcda3e4adb7abc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}