{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98afbda319b1574a2a6edc5b0010a518ea", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982faf399afc379e21f977747c30699208", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98220df45bedc8de859ca63e3c6f5190fd", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989160c91531347ed12e95ccdf05ccd4b1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98220df45bedc8de859ca63e3c6f5190fd", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fe875d31da65e373e14ca6f427cc167", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986f40bcf0fae856b6bfd657ff7097790a", "guid": "bfdfe7dc352907fc980b868725387e98ee1fed5d364027e3dc871c6c73dce1b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b6105dd07ab98277de7676bb044851d", "guid": "bfdfe7dc352907fc980b868725387e9872f0d6191eabafe89d9893cdffa44c38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bcf3f635b6135e961dfcdc77ce2500d", "guid": "bfdfe7dc352907fc980b868725387e98159dc36ad8974fdd266c86230ca50678"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e64386303b8398f77ae6f12d3e421be", "guid": "bfdfe7dc352907fc980b868725387e98a5a5c3f3911d7fd0183410fc72591884", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98290422045ba9695b87c56b9654681063", "guid": "bfdfe7dc352907fc980b868725387e985ff67a0a08d2f2d50de48e15a9d12146", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c5ec49362faf2dc71ed60da98f07b9c", "guid": "bfdfe7dc352907fc980b868725387e986bcdde779cc4e0e604800c817e5cd3dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cea71f3dc0a06d0c530772d65c28654", "guid": "bfdfe7dc352907fc980b868725387e98f7a8c832a2135ab33dfae3b6043fd9da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a49ef884db9b62d74d97cdc6bd667c6", "guid": "bfdfe7dc352907fc980b868725387e98da61d7b0ba0196a22f3e5b0ce196d8c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dc6efaa852d4a5ed61dd17920c7e4aa", "guid": "bfdfe7dc352907fc980b868725387e98a75ed79e12a1edb5e8df9b0a7c1d4da4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98648ef8e99fdb60aef9d3ea70ef6ab822", "guid": "bfdfe7dc352907fc980b868725387e98afbbcc72fdbe5a4c129e38a3e4e72d19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b62a264930e05047dcabfbff112dd467", "guid": "bfdfe7dc352907fc980b868725387e985bfceb0c105014d7a1d025be6045d602", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887f9cec0ab07b11d49e74374b2d344c4", "guid": "bfdfe7dc352907fc980b868725387e980675c51bb6fcd79dd958c18b276506ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c1357a293abd46b6911f768e9f2764b", "guid": "bfdfe7dc352907fc980b868725387e98fe1b3cecdc1a4b44592ebbf6db918943", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc0fa428c9869ac93727b03f1cf2441a", "guid": "bfdfe7dc352907fc980b868725387e98c1fb561e1e1f687f115a74a2091cac40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd1eae28bdae961ed7974943be615cef", "guid": "bfdfe7dc352907fc980b868725387e98cf234de07a230a1965f408dcbfc3c8a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5df74151f53a97e3eced2838575950f", "guid": "bfdfe7dc352907fc980b868725387e9847b3fe5aefdf4044d5c58aacf531f58c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5ebd9837fcbde570712abf177ce7efe", "guid": "bfdfe7dc352907fc980b868725387e98530ff99a7b1e88b03647894611405c21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ce0da17776458a81bb837c263690675", "guid": "bfdfe7dc352907fc980b868725387e981ef02c2e287e54c269200f46ccfde3d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a03748556a555d7668e81dc3883711a3", "guid": "bfdfe7dc352907fc980b868725387e98e028dd62ad5c3cdf2056df534ec16dde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823322137326b8775b6096146a5b5a3e8", "guid": "bfdfe7dc352907fc980b868725387e98d35c03b3097761b81a27568e126c6af2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1f208277e7d6698587f4031793b3b4f", "guid": "bfdfe7dc352907fc980b868725387e98eff1cf8c80b98f1b5a5646603e1671b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e471ebcdf911581bae351374f5a5994c", "guid": "bfdfe7dc352907fc980b868725387e9862653356354d51192ad3e19a8626d3a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809fc7e29ec30e2a0f566b187a89ebbdb", "guid": "bfdfe7dc352907fc980b868725387e9840a9d6c1b974739e72d010e9a91be0cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840309cabc6025d9d9f1f56b456916e17", "guid": "bfdfe7dc352907fc980b868725387e98e77dbb02be5c0df1151aa9a441662c05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b38d74b4a04c1e7372c35d935076a7a5", "guid": "bfdfe7dc352907fc980b868725387e98256f1f40e4e00e5d4682d3b546c58e68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98894e45e13eb19e51eb200476c35d16db", "guid": "bfdfe7dc352907fc980b868725387e98c201f7fb571014f678f4ab816d48e14d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98588b06d388a2f5f24006ffb505beed8d", "guid": "bfdfe7dc352907fc980b868725387e9837c2aa14daa4cbf90f234b0c01c29762", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a62b87325c94995a5ae03475572622b1", "guid": "bfdfe7dc352907fc980b868725387e98782488c24f163e2b4a8902206dcd8ca1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805fa5a3f4b88e99c6e0523638bfcd24a", "guid": "bfdfe7dc352907fc980b868725387e985b4617f46e6f28898192757eac12dfb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e617ad8089ba2e05f1613f6ed66c898d", "guid": "bfdfe7dc352907fc980b868725387e988fedb334bf41670aa60c3b3c35079bd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c619f21383d7af83413e8e06c766f75a", "guid": "bfdfe7dc352907fc980b868725387e98b0917b7ee5499423884749f101a67893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcffb09082438a9ad59c206f31a61b90", "guid": "bfdfe7dc352907fc980b868725387e9808a3c6a225f43daf2424e8d9fe93d0e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e0c8d3d0159eb683423592522d21c97", "guid": "bfdfe7dc352907fc980b868725387e9845733412288baa3558d5ced626d4c32d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cd6b325815b54d4f06dd84f80404162", "guid": "bfdfe7dc352907fc980b868725387e98e04a1cb49bab36b96569a52e27d22ed6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e74fb9043f363e049e99fc4ef341437", "guid": "bfdfe7dc352907fc980b868725387e98ad14412663282fc6db2ee336b8e81ede", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988391907f676e41b3db4e380d0f809985", "guid": "bfdfe7dc352907fc980b868725387e98a65bceff0ca6512f0f115eee09c843c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ada3d0f4465a3783583ea3f34959cf1", "guid": "bfdfe7dc352907fc980b868725387e98985bfcf7741453abcbd7097ccef31e28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982604b71d37583d057e6f1e8f2e26027d", "guid": "bfdfe7dc352907fc980b868725387e98bbb40c086877424dc520bd01cb298013", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b131174e425dfa632dcc17f95137abb", "guid": "bfdfe7dc352907fc980b868725387e98827edd4b86673f96e7dd17c6423afa2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a34fc7edc2f4f3ae946eae955b7e8de", "guid": "bfdfe7dc352907fc980b868725387e984e3b34e7f72980e2e7209cbc0554af00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea9f1ad852bdf5cc0710ade8a149c8c2", "guid": "bfdfe7dc352907fc980b868725387e98ec946513bf509e8e474d4d99a3ed167f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bda5d0c158e9500b0483e53aeaba177e", "guid": "bfdfe7dc352907fc980b868725387e9884169b7fc46700d5720569aa39806cc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873916042492f28c1c7f7a5ed893348b5", "guid": "bfdfe7dc352907fc980b868725387e9806f2d18863b5a2da7e1dd3d1d1b3095d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e8fc052ae1355b06003f1d7184d9a53", "guid": "bfdfe7dc352907fc980b868725387e98a038ae0f4dfcbf359be5175e670ebf4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a009f51e3f711e6ad5ab85246b6cbdfb", "guid": "bfdfe7dc352907fc980b868725387e98e76588758bd62b995b408fded2f87a56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986738d231e1c9c5fc180caadef4efb802", "guid": "bfdfe7dc352907fc980b868725387e98c65e4da1cbc026652503a968267e9e2a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9852615fc97422b6b7c2ad8bc8d52c7e3e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f9bafc9f5768ee98ec96854a54182c0", "guid": "bfdfe7dc352907fc980b868725387e984585829eb2e7ed88bf3fa5a9d62036db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852e89bed81b3b37aed76a154adcaa2a6", "guid": "bfdfe7dc352907fc980b868725387e9865fa20e4507433d045f8dc599a74d00e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eba1cff9956c67bc1b683a6f8a667ede", "guid": "bfdfe7dc352907fc980b868725387e98675fcc8bd0ea036c58b156b5996034bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847828ccd0b237953ce375492757dc553", "guid": "bfdfe7dc352907fc980b868725387e9846d388892123079ae30fc8132a46625b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efe65681955f4f77914e8de40c1aaf4a", "guid": "bfdfe7dc352907fc980b868725387e988ae6905bdb4382570dc68affe5efed1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5f7f8a4abd178a906994942a23ce034", "guid": "bfdfe7dc352907fc980b868725387e9848d59ffeb1f88acd31f1e4286e437185"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff6826e108b50911b3a3425629582d39", "guid": "bfdfe7dc352907fc980b868725387e98c0b9af07bf2820000e0cff4d99f15611"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bc906aa9f45850d7da3db0a02353db3", "guid": "bfdfe7dc352907fc980b868725387e9823ed0d266f079a2ea1a87bdd555de702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ea25769918541f99b4394d70a461556", "guid": "bfdfe7dc352907fc980b868725387e9829763e818d578a1ba91c62025f9384c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e953f11e794482c1f96ef7f38f9145d", "guid": "bfdfe7dc352907fc980b868725387e987c0ab695b21562e9b4d294f83fe7cfb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984917940b455065984cb563189d363504", "guid": "bfdfe7dc352907fc980b868725387e982b4d9ffe4d514ba160cf43180bc10c32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836ea7f2491af8b0f8484a4f33a30a759", "guid": "bfdfe7dc352907fc980b868725387e98b6a72672e49e21527824dac9832646ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988caa2b339e2c47eb6eddc2c5f7dbf81f", "guid": "bfdfe7dc352907fc980b868725387e9887ab300c5a885ddd6c63505770d956fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811c82521caff1e8ce8d5038e31dded95", "guid": "bfdfe7dc352907fc980b868725387e98be64c95501c88f9bab3077223c66f8e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98272907db0d7236da4b84d75e5fd2dd17", "guid": "bfdfe7dc352907fc980b868725387e984d637e3755f3e35d60341a6291af1365"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aecbe25cf3b61a6db26ade3cc6b476d", "guid": "bfdfe7dc352907fc980b868725387e981fb2fbd40916e1d3a8bae7b709d01946"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ed06c204e537ec2c6ad045a45ee43b", "guid": "bfdfe7dc352907fc980b868725387e9872cf70a56443964c82363cede0914a2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d886b02fdec67e66c9d2698960b6f1a0", "guid": "bfdfe7dc352907fc980b868725387e98ee39552013408909148ba0c31f51601b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b95728abbcde8df316d58aa8e8af42cc", "guid": "bfdfe7dc352907fc980b868725387e985ce255223cfccb2f0a713241f04c2ca8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcfbb9829ffa1aab6f0d3331e7d9ae8e", "guid": "bfdfe7dc352907fc980b868725387e98a5134edc4a54eb987c8ba180a65a618c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98787397161d596cffdc4c887cb9587e8f", "guid": "bfdfe7dc352907fc980b868725387e98f1e6f05a5ef2de7a8f8b7f09f2b0628e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fc13cc4f9973e943c9b80eebcb70d64", "guid": "bfdfe7dc352907fc980b868725387e98a91905b10b2f9f883e4bbb3d61d21270"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ce8835064b473052316c8822114ee1", "guid": "bfdfe7dc352907fc980b868725387e98193bf3279d971c773ced60d3d82ba2cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fa8939b90ef55327464d516a98d819f", "guid": "bfdfe7dc352907fc980b868725387e984074885d10ec3eb454293f67c9df843a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cd0597ca9418796112daeac78825aa9", "guid": "bfdfe7dc352907fc980b868725387e9883e9810f3a3d4cbd8849ce7c0a75c755"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a06ee104a5b81c766c5b70a60fdd6838", "guid": "bfdfe7dc352907fc980b868725387e981252e06930e7348adccd5856d9f92a9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983878bf3c4d526d23802ca7685e3d4f21", "guid": "bfdfe7dc352907fc980b868725387e98989c71a1f3fb0057ba68d3466049d39c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e24b0e33dac6af0712347a7b007af69", "guid": "bfdfe7dc352907fc980b868725387e98ba0d27a5d928e08b76d90a906d871b47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7a8ed8957aed4b778662fc3a09e9795", "guid": "bfdfe7dc352907fc980b868725387e986ff55e865b8c31371c6e2ee3c358bcd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ce838ff69df4f44d5bb4239649923b0", "guid": "bfdfe7dc352907fc980b868725387e98b2aa4defcc7d29ac029a2e2e77b74ff4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b030e5a72643af4656085d2ec9e6ae", "guid": "bfdfe7dc352907fc980b868725387e98cf9974d12ba0411909a2b2a4c221fd61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864fefa715154d65392a9de6c1fc9e4c9", "guid": "bfdfe7dc352907fc980b868725387e98464843304a1dbce7eda1d4e77c260fd3"}], "guid": "bfdfe7dc352907fc980b868725387e98ab27711318147f8622abc84eca98caf6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e9855464ae3101564e351bcd02748a59f08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3a06ca66f4cff56a5c2a0fbbee9848a", "guid": "bfdfe7dc352907fc980b868725387e9880ec4fc5e06e48e1ea386e366a6a71e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e43bfb287db1c7fa6a592bcadf53b8fe", "guid": "bfdfe7dc352907fc980b868725387e982aee9ac05c85ab6920e4a156b9028f2c"}], "guid": "bfdfe7dc352907fc980b868725387e98a45a8f62ecc52a870d6fc8979a897c98", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9852b72b8d77b129e905144a3d1c7c496b", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e981dd80c2dfc981fcae1cb48341829d9ff", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}