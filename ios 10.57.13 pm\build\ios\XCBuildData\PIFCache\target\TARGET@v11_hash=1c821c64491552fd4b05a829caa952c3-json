{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9843cd30290ed74ae825e8d2d840883a81", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987c1a49d2eb4928f38153fbb752b43f0d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981633dd420f71be32a2100d0c9f509efb", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a061109697a6fecb5c4052027830873", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981633dd420f71be32a2100d0c9f509efb", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a2bfc0afebe00a97dd280fbd3b086c7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983eb63be37619b63a1c2cc547a5d50bba", "guid": "bfdfe7dc352907fc980b868725387e98305321bb0f6c61d76ab6f8f834f9e1bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989c7318c71e29c5c2fa728fab937f84e4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9847522e00840b9c684922faa85f043d0f", "guid": "bfdfe7dc352907fc980b868725387e986b9c07eb90d1c19fb7ac9f978eac2cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869dcaa66a1bd6ae7975448a5cd6e2754", "guid": "bfdfe7dc352907fc980b868725387e9899bf2eef86ec86d3071d0464ba311bc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98339f289dc7a0b72f3726cf6f784c3d6d", "guid": "bfdfe7dc352907fc980b868725387e989923225760583f9044f3477d68370fb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ef0cd372245ee45f5bbf7fc9011c9f7", "guid": "bfdfe7dc352907fc980b868725387e980b8fe6bd9ce7b7f7f9b672f675ac40ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fafc5f81bfa4e4ced5a608edd84ae6a3", "guid": "bfdfe7dc352907fc980b868725387e98c7914d6db7a604491a406b7ffd04ffdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc3666d89640a8aa2b2567546677ac70", "guid": "bfdfe7dc352907fc980b868725387e98eb2913c855fd2bba90610341d1005093"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801be4848da2bad8e929f361ce0a69202", "guid": "bfdfe7dc352907fc980b868725387e9818bf3e3f4e2e4ff6409fd3de93a3e67b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98167dedc6462bf8ca9713a6a0439f44da", "guid": "bfdfe7dc352907fc980b868725387e9862f3f56aa2a92efeb6d501703a17fb78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838b0a136a93b7ac7e25f4ccc99c564c6", "guid": "bfdfe7dc352907fc980b868725387e986f0a5a4d4d049699b38b834e5127e27c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba30ed2a8db57505f43044c98ce62e0f", "guid": "bfdfe7dc352907fc980b868725387e98b11d52cb23817759d43df3901c9dfdcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acf9c151433c58f2d3cbd4877b8b3c45", "guid": "bfdfe7dc352907fc980b868725387e982e2cfaddbe60c31e36e71f4cf7b78cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8fdcf6b0cff0786b12a9ce215f40ab7", "guid": "bfdfe7dc352907fc980b868725387e9880b17e68f279e7d7783a51e9bb5287b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c09e682405eeb3234b2c2809867519ea", "guid": "bfdfe7dc352907fc980b868725387e98edb05713e1e170990070d795fbfd9ad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ce40b3531092156eef2ad1b14604f60", "guid": "bfdfe7dc352907fc980b868725387e98571373774a6c562d2a57eeef6625aad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98716c649537d26eb66d97068d5360b064", "guid": "bfdfe7dc352907fc980b868725387e982b8ddf46d054df8e6dd54a4248a7bf7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866d7cefeea66c45808b3f0146085d721", "guid": "bfdfe7dc352907fc980b868725387e98a0be07ca2e426e3bc60d30898d361288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee79f9b2927bb353080d45004d55477d", "guid": "bfdfe7dc352907fc980b868725387e98b3f4cf0af8ed4ae02ddeeb10e435cd75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d8cff17f996f539f951e69cbcc7922", "guid": "bfdfe7dc352907fc980b868725387e9812c712ccbecb48d4ce26f86166ff880e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d228f7b206e320eac8e12e7d72141e2", "guid": "bfdfe7dc352907fc980b868725387e982a440638f7cb9965d090ab0c181da156"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983873b6f8d02f75abc68d8dbe88eeb30f", "guid": "bfdfe7dc352907fc980b868725387e9803b6b2b356410bc7bcfcc1b875c69472"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a92bec8cb1101e31004e9f7d25453a", "guid": "bfdfe7dc352907fc980b868725387e98ec9c4546923a074af807de40344c27c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c43e1955fe97dc200be4879d7120686", "guid": "bfdfe7dc352907fc980b868725387e985cfeda817f2be498febfcb296540639a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867dbf2ebf43eac9370998d5e85faf749", "guid": "bfdfe7dc352907fc980b868725387e982082b1aebfa678d630e6c739c18a5003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982582c029787777039783b44c3f39a907", "guid": "bfdfe7dc352907fc980b868725387e987c4d107baf407af054f8312cf0ad4178"}], "guid": "bfdfe7dc352907fc980b868725387e98239213265a3abaa90df058a8c1bb91cf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e9840e570f7d28f64055b1dab9dc749246d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c123d96ea39fb63b38c28685f730bf52", "guid": "bfdfe7dc352907fc980b868725387e98192e2db6ac44b70d83d52bc9a34503d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df66cd2fbbc62e80c8a30c56780678c6", "guid": "bfdfe7dc352907fc980b868725387e98f0a88717dff9f0a44bc37f82f0d7d1f4"}], "guid": "bfdfe7dc352907fc980b868725387e98819fd04ebca59115ef049e04b7b72408", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988c2f740ef6174a43ddde75c8d424f6f2", "targetReference": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3"}], "guid": "bfdfe7dc352907fc980b868725387e984c4b1b52a05e595bcc1230a93d57ff06", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f752ba05adf197c3696519e901961310", "name": "DKImagePickerController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}