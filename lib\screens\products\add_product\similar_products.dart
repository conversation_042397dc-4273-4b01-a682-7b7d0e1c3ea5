import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/other/scan_barcode.dart';
import 'package:overolasuppliers/helper/other/shimmer.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/add_product/similar_product_model.dart';
import 'package:overolasuppliers/model/add_product/view_prduct/product_detail_model.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';

class SimilarProducts extends StatelessWidget with ServerResponse {
  late GraphQlInitilize _request;
  late SimilarProductModel similarProductModel;
  final _searchQueryController = TextEditingController();
  Timer? _debounce;
  String query = "";
  RxString strError = "".obs;
  final int _debouncetime = 1000;
  RxBool haveText = false.obs, haveProducts = true.obs;
  final RxList<ProductCatelog> _similarProducts = <ProductCatelog>[].obs;

  SimilarProducts({super.key});

  _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(Duration(milliseconds: _debouncetime), () {
      if (_searchQueryController.text.isNotEmpty) {
        if (query == _searchQueryController.text) return;
        query = _searchQueryController.text;
        haveText.value = true;
        _similarProducts.clear();
        strError.value = "";
        _request.runQuery(
            context: MyApp.navigatorKey.currentContext!,
            query: GraphQlQuries.getProductsFromCatalog,
            isRequiredLoader: true,
            variables: {
              "searchFilter": _searchQueryController.text,
              "page": 1,
              "itemsNumber": 20
            });
      } else {
        _searchQueryController.clear();
        _similarProducts.clear();
        strError.value = "";
        haveText.value = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    _searchQueryController.addListener(_onSearchChanged);
    _request = GraphQlInitilize(this);
    var size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: const Color(0xFF1A1B1E),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        title: Text(
          appLocal.similarProduct,
          style: FontStyles.fontBold(fontSize: 24),
        ),
        actions: [
          IconButton(
            onPressed: () => Get.back(),
            icon: Container(
              padding: EdgeInsets.all(8.r),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: SvgPicture.string(SvgStrings.iconCancel),
            ),
          ),
          SizedBox(width: 16.w),
        ],
      ),
      body: Column(
        children: [
          // Search Container
          Container(
            margin: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2D36),
              borderRadius: BorderRadius.circular(16),
            ),
            child: TextFormField(
              controller: _searchQueryController,
              inputFormatters: [LengthLimitingTextInputFormatter(200)],
              style: FontStyles.fontMedium(fontSize: 16),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: appLocal.searchProductFeildHint,
                hintStyle: FontStyles.fontRegular(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.5),
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                prefixIcon: Icon(
                  Icons.search_rounded,
                  size: 24,
                  color: Colors.white.withOpacity(0.7),
                ),
                suffixIcon: Obx(
                  () => haveText.value
                      ? IconButton(
                          onPressed: () {
                            _searchQueryController.clear();
                            _similarProducts.clear();
                          },
                          icon: Container(
                            padding: EdgeInsets.all(4.r),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(Icons.close, color: Colors.white, size: 18),
                          ),
                        )
                      : IconButton(
                          onPressed: () async {
                            final barcode = await Get.to(() => ScanBarCode(), fullscreenDialog: true);
                            if (barcode != null) {
                              _searchQueryController.text = barcode;
                              haveText.value = true;
                            }
                          },
                          icon: Container(
                            padding: EdgeInsets.all(8.r),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: SvgPicture.asset(
                              'assets/images/scanner.svg',
                              color: Colors.white,
                              height: 20.h,
                            ),
                          ),
                        ),
                ),
              ),
            ),
          ),

          // Main Content
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Obx(
                () => _similarProducts.isEmpty
                    ? _buildEmptyState(context, appLocal)
                    : _buildProductGrid(context, size),
              ),
            ),
          ),

          // Error Display
          SafeArea(
            child: ElbaabNetworkEroor(
              strError: strError,
              alignment: Alignment.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, AppLocalizations appLocal) {
    if (_searchQueryController.text.isNotEmpty) {
      return Column(
        children: [
          const Spacer(),
          if (haveProducts.value)
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.search_off_rounded,
                    size: 64,
                    color: Colors.white.withOpacity(0.3),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    "No Items Found",
                    style: FontStyles.fontSemibold(
                      fontSize: 18,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          const Spacer(),
        ],
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(20.r),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2D36),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text.rich(
                  TextSpan(
                    text: appLocal.matchExsistingProduct,
                    style: FontStyles.fontBold(fontSize: 14),
                    children: <InlineSpan>[
                      TextSpan(
                        text: appLocal.searchType,
                        style: FontStyles.fontRegular(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.7),
                        ),
                      )
                    ],
                  ),
                ),
                SizedBox(height: 24.h),
                Center(
                  child: Text(
                    appLocal.or,
                    style: FontStyles.fontBold(fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 24.h),
          ElbaabGradientButtonWidget(
            onPress: () {
              String id = prefs.getString(lastProductEditId) ?? "";
              if (id.isNotEmpty) {
                prefs.remove(lastProductEditId);
                prefs.remove(productInformation);
                prefs.remove(productDetails);
                prefs.remove(productShipment);
                prefs.remove(productPolicies);
              }
              Get.toNamed(RouteNames.addProductTabViewScreen);
            },
            text: appLocal.skipAddNewProduct,
          ),
        ],
      ),
    );
  }

  Widget _buildProductGrid(BuildContext context, Size size) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification sn) {
        if (!(_similarProducts.any((element) => element.id == null)) &&
            sn is ScrollUpdateNotification &&
            sn.metrics.pixels <= sn.metrics.maxScrollExtent - 100) {
          if (similarProductModel.pagination?.hasNextPage ?? false) {
            _loadMoreProducts();
          }
        }
        return true;
      },
      child: GridView.builder(
        itemCount: _similarProducts.length,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
          childAspectRatio: 0.7,
        ),
        itemBuilder: (context, index) => _buildProductCard(context, index),
      ),
    );
  }

  Widget _buildProductCard(BuildContext context, int index) {
    ProductCatelog product = _similarProducts[index];
    
    if (product.id == null) {
      return Shimmer.fromColors(
        baseColor: const Color(0xFF2A2D36),
        highlightColor: AppColors.colorPrimary.withOpacity(0.5),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey,
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: () => _handleProductTap(context, index),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF2A2D36),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image section
            Expanded(
              flex: 5,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                  image: _getProductImage(product),
                ),
                child: Center(
                  child: Container(
                    padding: EdgeInsets.all(8.r),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: SvgPicture.string(
                      SvgStrings.iconUpload2,
                      height: 24.h,
                    ),
                  ),
                ),
              ),
            ),
            // Details section
            Expanded(
              flex: 4,
              child: Padding(
                padding: EdgeInsets.all(8.r),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.productBrand?.brandName ?? "",
                      style: FontStyles.fontBold(fontSize: 11),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      product.productName ?? "",
                      style: FontStyles.fontMedium(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.7),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => _handleProductTap(context, index),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.colorPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: EdgeInsets.symmetric(vertical: 6.h),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.matchThisProduct,
                          style: FontStyles.fontMedium(fontSize: 11),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  DecorationImage _getProductImage(ProductCatelog product) {
    if ((product.productImages?.isEmpty ?? false) &&
        (product.productOptions?.productColors?.isEmpty ?? false)) {
      return const DecorationImage(
        image: AssetImage('assets/images/no_image.png'),
        fit: BoxFit.cover,
      );
    }
    
    return DecorationImage(
      image: NetworkImage(
        (product.productImages?.isEmpty ?? false)
            ? (product.productOptions?.productColors?.first.colorImages?.first ?? "")
            : product.productImages?.first ?? "",
      ),
      fit: BoxFit.cover,
    );
  }

  void _loadMoreProducts() {
    _request.runQuery(
      context: MyApp.navigatorKey.currentContext!,
      query: GraphQlQuries.getProductsFromCatalog,
      isRequiredLoader: false,
      logVariables: true,
      variables: {
        "searchFilter": _searchQueryController.text,
        "page": ((similarProductModel.pagination?.page ?? 0) + 1),
        "itemsNumber": ((similarProductModel.pagination?.totalItems ?? 0) - _similarProducts.length) <= 10
            ? ((similarProductModel.pagination?.totalItems ?? 0) - _similarProducts.length)
            : 10
      },
    );
    _similarProducts.addAll(List.generate(6, (index) => ProductCatelog()));
  }

  void _handleProductTap(BuildContext context, int index) {
    _request.runQuery(
      context: context,
      query: GraphQlQuries.getProductCatalogById,
      variables: {
        "productCatalogId": (_similarProducts[index].id ?? "")
      },
      type: "Product_Details",
    );
  }

  @override
  onError(error, String type) {
    strError.value = BaseModel.fromJson(error).message ?? "";
    haveProducts.value = false;
    if (type != "Product_Details") {
      _similarProducts.clear();
    }
  }

  @override
  onSucess(response, String type) {
    log(" ${jsonEncode(response)}");

    if (type == "Product_Details") {
      ProductDetailModel model = ProductDetailModel.fromJson(response);
      if (model.status == statusOK) {
        Get.toNamed(RouteNames.matchProductScreen, arguments: [model.product]);
      }
    } else {
      similarProductModel = SimilarProductModel.fromJson(response);
      _similarProducts.removeWhere((element) => element.id == null);
      strError.value = "";
      if (similarProductModel.status == statusOK) {
        _similarProducts.addAll(similarProductModel.pagination?.items ?? []);
      }
    }
  }
}
