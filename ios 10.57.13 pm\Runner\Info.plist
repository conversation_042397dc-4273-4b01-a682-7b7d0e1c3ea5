<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Elbaab Suppliers</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>elbaab_suppliers</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>elbaabsupplier</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FLTEnableImpeller</key>
	<true/>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>sms</string>
		<string>tel</string>
		<string>https</string>
		<string>whatsapp</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>To help you scan QR codes, products, and shop images, we need your permission to access the camera. Just head to your iPhone&apos;s Settings, tap Privacy, then Camera, and enable access for our app. Enjoy seamless scanning and shopping with us!</string>
	<key>NSFaceIDUsageDescription</key>
	<string>To provide you with secure and convenient mobile authentication, we need your permission to access your device&apos;s authentication features.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Dear supplier! To make it easier for you to add and manage your branches, we kindly request permission to access your location. This will help us accurately locate and label your branches on the map</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Dear supplier! To make it easier for you to add and manage your branches, we kindly request permission to access your location. This will help us accurately locate and label your branches on the map</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Dear supplier! To make it easier for you to add and manage your branches, we kindly request permission to access your location. This will help us accurately locate and label your branches on the map</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>To provide the best experience while using our camera, we need access to your microphone to capture audio along with video</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>To enhance your experience and enable you to upload photos, we need your permission to access your photos. </string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>branch_key</key>
	<string>key_live_nc1MLbjZvDan9IttJsgt8ofmqqirpZ9g</string>
	<key>branch_universal_link_domains</key>
	<array>
		<string>elbaabsupplier.app.link</string>
		<string>elbaabsupplier-alternate.app.link</string>
		<string>elbaabsupplier.test-app.link</string>
		<string>elbaabsupplier-alternate.test-app.link</string>
	</array>
</dict>
</plist>
