import 'dart:async';

import 'package:flutter/material.dart';

class SecuritySessionProvider extends ChangeNotifier {
  int _remainingTime = 900;
  late Timer _timer;

  bool _isSessionExpire = true;

  bool get sessionExpire => _isSessionExpire;

  void startSession() {
    _remainingTime = 900;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime > 0) {
        if (_isSessionExpire) {
          _isSessionExpire = false;
          notifyListeners();
        }
        _remainingTime--;
      } else {
        _isSessionExpire = true;
        notifyListeners();
        _timer.cancel();
      }
    });
  }

  void closeSession() {
    if (!_isSessionExpire) {
      _isSessionExpire = true;
      notifyListeners();
      _timer.cancel();
    }
  }
}
