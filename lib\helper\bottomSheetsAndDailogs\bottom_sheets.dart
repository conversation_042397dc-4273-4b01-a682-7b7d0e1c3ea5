import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/components/all_pickup_addrees.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/components/branch_number_verification.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/components/date_picker.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/components/date_range_picker.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/components/initate_transfer_request_sheet.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/components/user_unauthrized.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/enums/enums.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class BottomSheets {
  static Future<int> showListPicker(
    BuildContext context,
    List<String> list,
  ) async {
    List<Text> pickerItems = [];
    for (var list in list) {
      pickerItems.add((Text(
        list,
        style: FontStyles.fontRegular(height: 2),
        textAlign: TextAlign.center,
      )));
    }
    int selectedIndex = 0;
    int index = await showCupertinoModalPopup(
      context: context,
      builder: (_) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
                topRight: Radius.circular(20), topLeft: Radius.circular(20)),
            color: AppColors.backgroundColorDark,
          ),
          height: 320,
          child: Column(
            children: [
              Padding(
                padding:
                    const EdgeInsets.only(left: kLeftSpace, right: kRightSpace),
                child: SizedBox(
                  height: 50,
                  child: Stack(
                    children: <Widget>[
                      Align(
                        alignment: Alignment.centerLeft,
                        child: TextButton(
                          onPressed: () => Get.back(),
                          child: Text(EnStrings.cancel,
                              style: FontStyles.fontMedium()),
                        ),
                      ),
                      Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () => Get.back(result: selectedIndex),
                          child: Text(
                            EnStrings.done,
                            style: FontStyles.fontMedium(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 250,
                child: CupertinoPicker(
                  itemExtent: 40,
                  useMagnifier: true,
                  scrollController: FixedExtentScrollController(initialItem: 0),
                  children: pickerItems,
                  onSelectedItemChanged: (index) => selectedIndex = index,
                ),
              ),
            ],
          ),
        );
      },
    );

    return index;
  }

  static Future<String> dateRangeSelector(context, String type) async {
    String dateRange = await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return DateRangePicker(type: type);
        });
    return dateRange;
  }

  static Future<bool> userUnauthrized(context) async {
    bool auth = await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isDismissible: false,
        isScrollControlled: true,
        enableDrag: false,
        builder: (context) {
          return const UserUnauthrized();
        });
    return auth;
  }

  static Future<String> datePicker(context) async {
    String date = await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return DatePicker();
        });
    return date;
  }

  static Future<bool?> allPickupAddress(context, String id, bool isUpdate,
      {String pickupAddressId = "", String ros = ""}) async {
    return await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isDismissible: true,
        isScrollControlled: true,
        enableDrag: false,
        builder: (context) {
          return AllPickupAddress(
            orderId: id,
            isUpdate: isUpdate,
            pickupAddressId: pickupAddressId,
            ros: ros,
          );
        });
  }

  static Future<String> branchNumberVerification(
      BuildContext context, String phoneNumber, VoidCallback onVerificationSuccess) async {
    return await showDialog(
      context: context,
      builder: (BuildContext cxt) {
        return BranchNumberVerification(
          phoneNumber: phoneNumber,
          onVerificationSuccess: onVerificationSuccess,
        );
      },
    );
  }

  static Future<bool> initateTransferRequesBottomtSheet(
      context, double requestedAmount) async {
    bool status = await showModalBottomSheet(
        context: context,
        enableDrag: false,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return InitateTransferRequestSheet(transferAmount: requestedAmount);
        });
    return status;
  }

  static Future<bool> showAlertMessageBottomSheet(
      String message, String? title, context,
      {String? buttonText,
      String? image,
      bool isSVG = false,
      Function? onActionClick}) async {
    final appLocal = AppLocalizations.of(context)!;
    bool hideStatus = await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext bc) {
        return Wrap(
          children: [
            Container(
              padding: const EdgeInsets.only(
                  left: 11, right: 11, bottom: 30, top: 16),
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(20),
                      topLeft: Radius.circular(20)),
                  color: AppColors.backgroundColorDark),
              child: Column(
                children: <Widget>[
                  Container(
                    width: 65,
                    height: 2,
                    decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.circular(5)),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    title ?? appLocal.alert,
                    style: FontStyles.fontSemibold(),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    message,
                    style: FontStyles.fontRegular(height: 2),
                    textAlign: TextAlign.center,
                  ),
                  if (isSVG) const SizedBox(height: 24),
                  if (isSVG) SvgPicture.string(image ?? ''),
                  const SizedBox(
                    height: 32,
                  ),
                  ElbaabGradientButtonWidget(
                    onPress: () => {
                      Get.back(result: true),
                      if (onActionClick != null) {onActionClick()},
                    },
                    text: buttonText ?? appLocal.continu,
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            )
          ],
        );
      },
    );
    return hideStatus;
  }

  static Future<UpdateMedia> editPhoto(context) async {
    UpdateMedia updateMedia = await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext bc) {
        return Wrap(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.only(
                  left: 19, right: 19, bottom: 30, top: 40),
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(20),
                    topLeft: Radius.circular(20)),
                color: AppColors.backgroundColorDark,
              ),
              child: Column(
                children: <Widget>[
                  ElbaabButtonWidget(
                    onPress: () => Get.back(result: UpdateMedia.replace),
                    colors: AppColors.colorPrimary,
                    text: 'Replace Photo',
                  ),
                  const SizedBox(height: 20),
                  ElbaabButtonWidget(
                    onPress: () => Get.back(result: UpdateMedia.delete),
                    colors: AppColors.colorDanger,
                    text: 'Delete',
                  ),
                ],
              ),
            )
          ],
        );
      },
    );
    return updateMedia;
  }
}
