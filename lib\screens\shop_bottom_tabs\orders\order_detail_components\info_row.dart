import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class InfoRow extends StatelessWidget {
  final String image;
  final String title;
  final String info;
  final double? padding;

  const InfoRow(
      {Key? key,
      required this.image,
      required this.title,
      required this.info,
      this.padding})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: padding ?? 0),
      child: Row(
        children: [
          SvgPicture.string(image),
           SizedBox(width: 8.w),
          Expanded(
            flex: 2,
            child: Text(
              '$title ',
              style: FontStyles.fontRegular(fontSize: 12),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              info,
              style: FontStyles.fontRegular(
                fontSize: 11,
                color: Colors.white.withOpacity(0.5),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
