import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/model/login_model.dart';
import 'package:overolasuppliers/screens/autentications/login_widgets/enable_local_auth_sheet.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_password_textfield.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'fade_slide_transition.dart';

class LoginForm extends StatefulWidget {
  final Function onSignUpClick;
  const LoginForm({
    Key? key,
    required this.animation,
    required this.onSignUpClick,
  }) : super(key: key);

  final Animation<double> animation;

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm>
    with InputValidationUtil
    implements ServerResponse {
  late GraphQlInitilize _request;

  String strEmail = '', strPassword = '';

  RxBool password = true.obs;

  RxString strError = ''.obs;

  final RxBool _savePassword = false.obs, _rememberMe = false.obs;

  var localAuth = LocalAuthentication();

  final GlobalKey<FormState> _loginFormKey = GlobalKey<FormState>();

  Future<void> _readFromStorage(BuildContext context) async {
    String isLocalAuthEnabled = prefs.getString(localAuthEnable) ?? "false";

    if ("true" == isLocalAuthEnabled) {
      bool didAuthenticate = await localAuth.authenticate(
          localizedReason:
              AppLocalizations.of(context)?.authenticateToSignIn ?? "");

      if (didAuthenticate) {
        strEmail = prefs.getString(localAuthEmail) ?? "";
        strPassword = prefs.getString(localAuthPassword) ?? "";
        userAuthToken = '';
        _request.runMutation(
          context: Get.context!,
          query: GraphQlQuries.login,
          variables:
              GraphQlVariables.login(email: strEmail, password: strPassword,lang: Get.locale?.languageCode ?? "en"),
        );
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    String isRemember = prefs.getString(rememberMe) ?? "false";
    if (isRemember == "true") {
      strEmail = prefs.getString(localAuthEmail) ?? "";
      strPassword = prefs.getString(localAuthPassword) ?? "";
      Future.delayed(Duration.zero, () {
        _rememberMe.value = true;
        if (Get.arguments != null) {
          strError.value = Get.arguments[0];
        }
      });
    }

    _readFromStorage(context);
  }

  _onFormSubmit() async {
    if (_savePassword.value) {
      prefs.setString(localAuthEnable, "false");
      prefs.setString(localAuthEmail, strEmail);
      prefs.setString(localAuthPassword, strPassword);
      if (await localAuth.canCheckBiometrics) {
        showModalBottomSheet<void>(
          context: MyApp.navigatorKey.currentContext!,
          builder: (BuildContext context) {
            return EnableLocalAuthModalBottomSheet(action: () {
              prefs.setString(localAuthEnable, "true");
            });
          },
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    final height =
        MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top;
    final space = height > 650 ? kSpaceM : kSpaceS;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: kPaddingL),
      child: Form(
        key: _loginFormKey,
        child: SingleChildScrollView(
          child: Column(
            children: <Widget>[
              FadeSlideTransition(
                animation: widget.animation,
                additionalOffset: 0.0,
                child: ElbaaabInputTextField(
                  initialValue: strEmail,
                  onChanged: (v) => strEmail = v,
                  label: appLocal.emailfeildLabel,
                  charaterlimit: 50,
                  isAllowSpace: false,
                  autoTextDirection: true,
                  hint: appLocal.emailHint,
                  validator: (v) => validateEmail(v, isOptionalFeild: false),
                  inputType: TextInputType.emailAddress,
                  inputAction: TextInputAction.next,
                ),
              ),
              SizedBox(height: space),
              FadeSlideTransition(
                  animation: widget.animation,
                  additionalOffset: space,
                  child: Obx(
                    () => ElbaabPasswordTextField(
                        onChanged: (v) => strPassword = v,
                        initialValue: strPassword,
                        validator: validatePassword,
                        hint: appLocal.passwordHint,
                        label: appLocal.passwordFeildLabel,
                        obscure: password.value,
                        obscureState: () {
                          password.value
                              ? password.value = false
                              : password.value = true;
                        }),
                  )),
              FadeSlideTransition(
                animation: widget.animation,
                additionalOffset: space,
                child: Obx(
                  () => Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: strError.value.isEmpty
                          ? EdgeInsets.zero
                          : const EdgeInsets.all(8.0),
                      child: Text(
                        strError.value,
                        style: FontStyles.fontRegular(
                          fontSize: 12,
                          color: AppColors.colorDanger,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              FadeSlideTransition(
                animation: widget.animation,
                additionalOffset: space,
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              Obx(
                                () => Checkbox(
                                  value: _rememberMe.value,
                                  onChanged: (v) {
                                    if (v == false) {
                                      _rememberMe.value = v!;
                                      prefs.setString(rememberMe, "$v");
                                      prefs.setString(localAuthEmail, "");
                                      prefs.setString(localAuthPassword, "");
                                    } else {
                                      if (strEmail.isNotEmpty &&
                                          strPassword.isNotEmpty) {
                                        strError.value = "";
                                        _rememberMe.value = v!;
                                      } else {
                                        strError.value =
                                            appLocal.aleartRememberBe;
                                      }
                                    }
                                  },
                                  activeColor: AppColors.colorPrimary,
                                ),
                              ),
                              Text(
                                appLocal.rememberMe,
                                style: FontStyles.fontRegular(
                                  fontSize: 12,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          child: Text(
                            appLocal.forgetPaswoord,
                            style: FontStyles.fontLight(
                              fontSize: 12,
                              color: AppColors.colorPrimary,
                            ),
                          ),
                          onTap: () => Get.toNamed(
                              RouteNames.resetPasswordScreen,
                              arguments: [strEmail]),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Obx(
                          () => Checkbox(
                            value: _savePassword.value,
                            onChanged: (v) => _savePassword.value = v!,
                            activeColor: AppColors.colorPrimary,
                          ),
                        ),
                        Text(
                          appLocal.useBiometricAcess,
                          style: FontStyles.fontRegular(
                            fontSize: 12,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(height: space),
              FadeSlideTransition(
                animation: widget.animation,
                additionalOffset: 2 * space,
                child: ElbaabButtonWidget(
                  colors: AppColors.colorPrimary,
                  height: 42,
                  onPress: () {
                    if (_loginFormKey.currentState!.validate()) {
                      userAuthToken = '';
                      _request.runMutation(
                        context: context,
                        query: GraphQlQuries.login,
                        variables: GraphQlVariables.login(
                            email: strEmail, password: strPassword,lang: appLocal.localeName),
                      );
                    } else {
                      strError.value = "";
                    }
                  },
                  text: appLocal.login,
                  margin: const EdgeInsets.only(top: 60, bottom: 20),
                ),
              ),
              SizedBox(height: 2 * space),
              FadeSlideTransition(
                animation: widget.animation,
                additionalOffset: 3 * space,
                child: Center(
                  child: Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      Text(
                        '${appLocal.youDontHaveAccess} ',
                        style: FontStyles.fontRegular(),
                      ),
                      InkWell(
                        onTap: () => widget.onSignUpClick(),
                        child: Text(
                          appLocal.signup,
                          style: FontStyles.fontRegular(
                              color: AppColors.colorPrimary),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 3 * space),
            ],
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {
    strError.value = BaseModel.fromJson(error).message ?? "";
  }

  @override
  onSucess(response, String type) {
    LoginModel loginModel = LoginModel.fromJson(response);
    if (loginModel.user?.isEmailVerified == false ||
        loginModel.user?.isPhoneVerified == false ||
        loginModel.user?.supplier == null ||
        loginModel.user?.supplier != null) {
      GlobalMethods.enableFutureLoginCheck(
          email: strEmail, password: strPassword);
    }
    if (loginModel.user?.supplier?.shopId != null &&
        (loginModel.user?.supplier?.shopId?.isCompleted ?? false)) {
      GlobalMethods.enableFutureLoginCheck(
          email: strEmail, password: strPassword);
    }
    if (loginModel.status == statusOK) {
      _onFormSubmit();
      String isLocalAuthEnabled = prefs.getString(localAuthEnable) ?? "false";
      if (isLocalAuthEnabled == "true") {
        String email = prefs.getString(localAuthEmail) ?? "";
        if (email != strEmail) {
          prefs.setString(localAuthEnable, "false");
        }
      }
      if (_rememberMe.value) {
        prefs.setString(rememberMe, "true");
        prefs.setString(localAuthEmail, strEmail);
        prefs.setString(localAuthPassword, strPassword);
      }
      GlobalMethods.checkLoginStatus(loginModel,
          email: strEmail, password: strPassword);
    } else {
      strError.value = loginModel.message!;
    }
  }
}
