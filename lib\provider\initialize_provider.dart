import 'package:overolasuppliers/provider/ConnectivityChangeNotifier.dart';
import 'package:overolasuppliers/provider/account_setting_info_provider.dart';
import 'package:overolasuppliers/provider/language_change.dart';
import 'package:overolasuppliers/provider/order_status_provider.dart';
import 'package:overolasuppliers/provider/security_session_provider.dart';
import 'package:overolasuppliers/provider/shop_info_provider.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:provider/provider.dart';

providerList(context) {
  return [
    ChangeNotifierProvider(
      create: (context) {
        ConnectivityChangeNotifier changeNotifier =
            ConnectivityChangeNotifier();
        changeNotifier.initialLoad();
        return changeNotifier;
      },
    ),
    ChangeNotifierProvider<UpdatedInfo>(
      create: (context) => UpdatedInfo(),
    ),
    ChangeNotifierProvider<LanguageChnangeProvider>(
      create: (context) => LanguageChnangeProvider(),
    ),
    ChangeNotifierProvider<OrderStatusProvider>(create: (context)=> OrderStatusProvider()),
    ChangeNotifierProvider<SecuritySessionProvider>(
      create: (context) => SecuritySessionProvider(),
    ),
    ChangeNotifierProvider<AccountSettingProvider>(
      create: (context) => AccountSettingProvider(),
    ),
    ChangeNotifierProvider<ShopInfoProvider>(
      create: (context) => ShopInfoProvider(),
    ),
  ];
}
