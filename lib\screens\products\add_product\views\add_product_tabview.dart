import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/view_prduct/product_detail_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_step_five.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_step_four.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_step_one.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_step_three.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_product_step_two.dart';
import 'package:overolasuppliers/screens/products/add_product/views/components/header_close_wisget.dart';
import 'package:overolasuppliers/screens/products/add_product/views/product_price_variations.dart';
import 'package:overolasuppliers/screens/products/add_product/views/product_specification.dart';
import 'package:overolasuppliers/widgets/elbaab_border_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

class AddProductTabView extends StatefulWidget {
  const AddProductTabView({super.key});

  @override
  State<AddProductTabView> createState() => _AddProductTabViewState();
}

class _AddProductTabViewState extends State<AddProductTabView>
    implements ServerResponse {
  final controller = Get.find<AddProductController>();
  late GraphQlInitilize _request;

  List<Widget> arrTab = [
    AddProductStepOne(),
    AddProductStepTwo(),
    const ProductPriceAndVariations(),
    ProductSpecification(),
    AddProductStepThree(),
    AddProductStepFour(),
    AddProductStepFive(),
  ];

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);

    Future.delayed(Duration.zero, () {
      if (controller.validationHistory?.returnValues != null) {
        controller.formKey.currentState!.validate();
      }
      if (controller.isDraftProduct) {
        controller.changeTab(isNext: false, isJumpPage: 6);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        appBar: ElbaabHeader(
          title: appLocal.addProduct,
          trailingWidget: const HeaderCloseWidget(),
        ),
        body: Column(
          children: [
            Obx(
              () => AnimatedContainer(
                height: controller.isHideStepper.value ? 0 : 80,
                curve: Curves.linear,
                duration: const Duration(milliseconds: 100),
                margin: const EdgeInsets.only(top: 10),
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Center(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    physics: const NeverScrollableScrollPhysics(),
                    controller: controller.autoScrollController,
                    child: Row(
                      children: [
                        stepper(
                          isDone: controller.tabPosition.value > 0,
                          inProgress: controller.tabPosition.value == 0,
                          title: appLocal.information,
                          scrolTag: 0,
                          isFirst: true,
                        ),
                        stepper(
                          isDone: controller.tabPosition.value > 1,
                          inProgress: controller.tabPosition.value == 1,
                          scrolTag: 1,
                          title: appLocal.options,
                        ),
                        stepper(
                          isDone: controller.tabPosition.value > 2,
                          inProgress: controller.tabPosition.value == 2,
                          scrolTag: 2,
                          title: appLocal.priceAndVariations,
                        ),
                        stepper(
                          isDone: controller.tabPosition.value > 3,
                          inProgress: controller.tabPosition.value == 3,
                          scrolTag: 3,
                          title: appLocal.specification,
                        ),
                        stepper(
                          isDone: controller.tabPosition.value > 4,
                          inProgress: controller.tabPosition.value == 4,
                          scrolTag: 4,
                          title: appLocal.shipment,
                        ),
                        stepper(
                          isDone: controller.tabPosition.value > 5,
                          inProgress: controller.tabPosition.value == 5,
                          scrolTag: 5,
                          title: appLocal.polices,
                        ),
                        stepper(
                          isDone: controller.tabPosition.value > 6,
                          inProgress: controller.tabPosition.value == 6,
                          scrolTag: 6,
                          title: appLocal.review,
                          isLast: true,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: PageView.builder(
                    itemCount: arrTab.length,
                    physics: const NeverScrollableScrollPhysics(),
                    controller: controller.pageController,
                    itemBuilder: (context, index) {
                      return arrTab[index];
                    }),
              ),
            ),
          ],
        ),
        bottomNavigationBar: SafeArea(
          child: Obx(
            () => Container(
              height: controller.tabPosition.value == 6 ? 0 : 65,
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: Center(
                child: Row(
                  children: [
                    AnimatedContainer(
                      curve: Curves.easeIn,
                      height: controller.tabPosition.value > 0 ? 42 : 0,
                      width: controller.tabPosition.value > 0
                          ? ((context.width / 2) - 40)
                          : 0,
                      duration: const Duration(milliseconds: 100),
                      child: ElbaabBorderButtonWidget(
                        text: controller.tabPosition.value > 0
                            ? appLocal.back
                            : "",
                        onPress: () => controller.changeTab(isNext: false),
                      ),
                    ),
                    if (controller.tabPosition.value > 0)
                      const SizedBox(width: 10),
                    Expanded(
                      flex: 1,
                      child: ElbaabGradientButtonWidget(
                        onPress: () => {
                          controller.isRequiredRematch = false,
                          controller.changeTab(isNext: true)
                        },
                        text: appLocal.next,
                      ),
                    ),
                    if (controller.isRequiredRematch) const SizedBox(width: 10),
                    if (controller.isRequiredRematch)
                      Expanded(
                        flex: 1,
                        child: ElbaabBorderButtonWidget(
                          onPress: () {
                            Alerts.alertView(
                                context: context,
                                defaultActionText: appLocal.yes,
                                content:
                                    appLocal.resetProductWillDiscardChanges,
                                action: () {
                                  _request.runQuery(
                                      context: context,
                                      query:
                                          GraphQlQuries.getProductCatalogById,
                                      variables: {
                                        "productCatalogId": (controller
                                                .product?.productCatalog ??
                                            "")
                                      },
                                      type: "Product_Details");
                                },
                                cancelActionText: appLocal.no,
                                cancelAction: () => Get.back());
                          },
                          text: appLocal.reset,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == "Product_Details") {
      ProductDetailModel model = ProductDetailModel.fromJson(response);
      controller.isRequiredRematch = false;
      Get.offAllNamed(RouteNames.addProductTabViewScreen, arguments: [
        model.product,
        true,
        false,
        true,
        controller.product?.id ?? ""
      ]);
    }
  }

  Widget stepper(
      {required bool isDone,
      required bool inProgress,
      required String title,
      required int scrolTag,
      bool isFirst = false,
      bool isLast = false}) {
    return AutoScrollTag(
      key: ValueKey(scrolTag),
      controller: controller.autoScrollController,
      index: scrolTag,
      child: Container(
        constraints: const BoxConstraints(minHeight: 70, minWidth: 90),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned(
              left: 0,
              bottom: 25,
              child: AnimatedContainer(
                height: isFirst ? 0 : 3,
                width: isFirst ? 0 : 45,
                color: inProgress || isDone
                    ? AppColors.colorPrimary
                    : AppColors.colotMidGray,
                duration: const Duration(milliseconds: 600),
              ),
            ),
            Positioned(
              right: 0,
              bottom: 25,
              child: AnimatedContainer(
                height: isLast ? 0 : 3,
                width: isLast ? 0 : 45,
                duration: const Duration(milliseconds: 600),
                color: isDone ? AppColors.colorPrimary : AppColors.colotMidGray,
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 20),
              child: isDone
                  ? SvgPicture.string(
                      SvgStrings.iconTick,
                      width: 24,
                      height: 24,
                    )
                  : Container(
                      height: 24,
                      width: 24,
                      decoration: BoxDecoration(
                        color: AppColors.colorPrimary,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          width: 7,
                          color: AppColors.headerColorDark,
                        ),
                      ),
                    ),
            ),
            (!inProgress)
                ? Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: FontStyles.fontRegular(
                        fontSize: 9,
                        color: Colors.white60,
                      ),
                    ),
                  )
                : Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      constraints:
                          const BoxConstraints(minHeight: 28, minWidth: 90),
                      decoration: BoxDecoration(
                        color: AppColors.colorPrimary,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Text(
                            title,
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            style: FontStyles.fontRegular(
                              fontSize: 9,
                              color: Colors.white60,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
