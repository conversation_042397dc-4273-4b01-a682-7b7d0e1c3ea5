import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/dio/dio_client_network.dart';
import 'package:overolasuppliers/main.dart';

class ConnectivityChangeNotifier extends ChangeNotifier {
  bool isConected = false;

  ConnectivityChangeNotifier() {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      resultHandler(result);
    });
  }
  ConnectivityResult _connectivityResult = ConnectivityResult.none;

  ConnectivityResult get connectivity => _connectivityResult;

  void resultHandler(ConnectivityResult result) async {
    if (result == ConnectivityResult.none) {
      try {
        final response = await serviceLocatorInstance<DioClientNetwork>()
            .dio
            .get('https://www.google.com');
        if (response.statusCode == 200) {
          isConected = true;
        } else {
          isConected = false;
        }
      } catch(e) {
        isConected = false;
      }
    } else {
      isConected = true;
    }
    _connectivityResult = result;
    notifyListeners();
  }

  initialLoad() async {
    ConnectivityResult connectivityResult =
        await (Connectivity().checkConnectivity());
    resultHandler(connectivityResult);
  }
}
