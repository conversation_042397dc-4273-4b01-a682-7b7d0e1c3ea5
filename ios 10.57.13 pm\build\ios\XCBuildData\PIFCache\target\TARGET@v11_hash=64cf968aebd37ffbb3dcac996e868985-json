{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f894e56351ce65c50760f71423feb59", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9805cdb552ed2490dd3d4ab3ec4ce5880a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9842222da0015abc37a9a29e54568dc61c", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98861a6ed3e85feffbfbe277bddde9e616", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9842222da0015abc37a9a29e54568dc61c", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98195c21900d865680a5922b504da15aa6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b0cd08412cac0c4ec26b23630a0b497", "guid": "bfdfe7dc352907fc980b868725387e987b2b9283e820418d3304525f5fa22a7e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9823f9f64334bbf224b215b2f516f39014", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e6475799d1324d2220972f4d5b89d777", "guid": "bfdfe7dc352907fc980b868725387e980ca4406ce61bd01eef5416ca59235c11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa72ef8a309612ca84ad5ed86903acca", "guid": "bfdfe7dc352907fc980b868725387e98d4fe9404674541f58e6937cd7de5fb4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f102e0c6a0c02811f6d5dd5fa6e726a7", "guid": "bfdfe7dc352907fc980b868725387e98343cb4beb36dfbb586c138aec2b374f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bc6198f6c1640b435d00d4737ec3edf", "guid": "bfdfe7dc352907fc980b868725387e9803e971685db85aec0ad8fe5ca1052d41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eea70bc78eeabaf8b6563a60b9ebe6a", "guid": "bfdfe7dc352907fc980b868725387e9879f05f91d7eda97b3a5c942c2adaed0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829cc3bd9643cfab2396968820d045480", "guid": "bfdfe7dc352907fc980b868725387e989acda093c189bcbc4adf9892b4b0e917"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824883d040b2fe4c244f85cca10f234bf", "guid": "bfdfe7dc352907fc980b868725387e98eecfdabcce63d8cea0261a98380b58ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98673d0b547cdbba53cf0698e417e55e3a", "guid": "bfdfe7dc352907fc980b868725387e980ff65c6139e39c6a7c45d65cac50b8dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986321e6fb7e41aebb5e21ec7d2d9050ea", "guid": "bfdfe7dc352907fc980b868725387e98b9de7cd3e4f63b22dc0fe93fc07b9a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dccf1c8dad090a6a03c714e9a2fb1df4", "guid": "bfdfe7dc352907fc980b868725387e985d3f080606ada0b4508cdd1870792480"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a596b7e75ee96ac71a284292da3372e", "guid": "bfdfe7dc352907fc980b868725387e98a9834530c8c56c8c4a3988d787e3147b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802a02a2f6fa4c3d793de36625b8955d4", "guid": "bfdfe7dc352907fc980b868725387e983fa9b81e1b8f64fb3d60058843b1fc00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869e08385a5e965684ba6a18ffc29c4de", "guid": "bfdfe7dc352907fc980b868725387e9875f465fba7a851003fd6e0fd8d4c7dd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8b630a240fea5d2b4f71c75e53ab3a6", "guid": "bfdfe7dc352907fc980b868725387e98e5431d3efd7eee2636a477a293adffe1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810733532d88c290aeffbb6e7285a669c", "guid": "bfdfe7dc352907fc980b868725387e9862e7f3eeae924b5d5bf9c3ab76fbe9c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac8f83b0b607962315c558d710330b02", "guid": "bfdfe7dc352907fc980b868725387e9836b497dd472e5d2996d4011246fb4fe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899dc3f90d361f849da91aa4b6d6f1a60", "guid": "bfdfe7dc352907fc980b868725387e98be0a8253c6768b9bd1d663b1a112b4ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf2018d2ca819e7566d965a30ea039d2", "guid": "bfdfe7dc352907fc980b868725387e98c0dee3c13ccb6a6e4a2d4d791e0ce44e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd548a3f04ad06d7bd4da1134240f87", "guid": "bfdfe7dc352907fc980b868725387e987e9b2af4a6f84244f2007d38669e5150"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984924728618998da74cddec90d4aa9fe4", "guid": "bfdfe7dc352907fc980b868725387e98736cb4a10f4faf0d59e73c336a67addb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98566ecb167ed0bcc6ead56010cee42f7b", "guid": "bfdfe7dc352907fc980b868725387e981854584e7a455ada7f43afe4df4804c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dbac958bff84a903cad07c040533385", "guid": "bfdfe7dc352907fc980b868725387e98df98db4e87a65aceab73cb05ba63f415"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a6daddb87725f6fdce06ce7b2af1692", "guid": "bfdfe7dc352907fc980b868725387e985ae88b25dfa39daf3902de2c326106a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aea70251b44526584963fea7200f1ec6", "guid": "bfdfe7dc352907fc980b868725387e980f46bb2b1555d7963d500036407f9256"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c41d3c642c01140b616f15ef03243a5e", "guid": "bfdfe7dc352907fc980b868725387e9823752c6876b44128178367d9e4c7eaf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847a6685c71e193076029ec87021c1eb9", "guid": "bfdfe7dc352907fc980b868725387e9856fa3c1819ab1571048c3765f3725822"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824b1f978cac3c5a4d17029d733586613", "guid": "bfdfe7dc352907fc980b868725387e985c70e265802d3264829fb20d830dd678"}], "guid": "bfdfe7dc352907fc980b868725387e98a139fb28ca3ac25514741a0a80c123af", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98df2ed2fea1693476460d9eded1a603c8", "guid": "bfdfe7dc352907fc980b868725387e985cde73bd39e38d599858554925534c22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6a92f9de5d49fc8cf3db6f39f75245e", "guid": "bfdfe7dc352907fc980b868725387e98f414e54445e118184d6daa94e62d850c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e986800f423d8a15dcf9eef951910202ab7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c123d96ea39fb63b38c28685f730bf52", "guid": "bfdfe7dc352907fc980b868725387e98b153e67cd0e2c1fdb9dd7c0b0b52d8c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df66cd2fbbc62e80c8a30c56780678c6", "guid": "bfdfe7dc352907fc980b868725387e98467697980c65f1ad516ab32ad97d2682"}], "guid": "bfdfe7dc352907fc980b868725387e9828bab0f26112a3d80782af7daa04e59c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9844930cf427b0870bd41ed33469939d4f", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e981e89a2835b83a46fe4d1fc8edd07c25b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}