import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ChangeYourEmail extends StatelessWidget
    with InputValidationUtil
    implements ServerResponse {
  RxInt status = 0.obs;
  RxString strError = "".obs;
  String strEmail = "",
      strCode = "",
      strDefaultEmail = prefs.getString(localAuthEmail) ?? "";
  late GraphQlInitilize _request;
  final ValueNotifier<int> _time = ValueNotifier(90);
  late AppLocalizations appLocal;

  ChangeYourEmail({super.key});
  void startTimer() {
    const oneSec = Duration(seconds: 1);
    Timer.periodic(
      oneSec,
      (Timer timer) {
        if (_time.value == 0) {
          timer.cancel();
        } else {
          _time.value--;
        }
      },
    );
  }

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    appLocal = AppLocalizations.of(context)!;
    _request = GraphQlInitilize(this);
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.changeYourEmail,
        leadingBack: true,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Obx(
              () => status.value == 0
                  ? emailWidget()
                  : status.value == 1
                      ? enterOtp(context)
                      : emailChanged(),
            ),
            ElbaabNetworkEroor(strError: strError)
          ],
        ),
      ),
      bottomNavigationBar: Obx(
        () => SizedBox(
          height: 100.h,
          child: Center(
            child: ElbaabGradientButtonWidget(
              edgeInsets: const EdgeInsets.symmetric(horizontal: 30).r,
              onPress: () {
                if (_formKey.currentState!.validate()) {
                  switch (status.value) {
                    case 0:
                      status.value++;
                      _request.runMutation(
                          context: context,
                          query: GraphQlQuries.changeEmail,
                          type: "SendEmail",
                          variables:
                              GraphQlVariables.changeEmail(email: strEmail));
                      break;
                    case 1:
                      if (strCode.length < 6) {
                        strError.value = appLocal.pleaseEnterCompeleteCode;
                      } else {
                        strError.value = "";
                        _request.runMutation(
                          context: context,
                          query: GraphQlQuries.emailVerify,
                          variables: GraphQlVariables.verifyEmail(
                              userId: supplierID, otpNumber: strCode),
                        );
                      }

                      break;
                    case 2:
                      Get.back();
                      break;
                    default:
                  }
                }
              },
              text: status.value == 0 ? appLocal.next : appLocal.done,
            ),
          ),
        ),
      ),
    );
  }

  Widget emailWidget() {
    return ElbaaabInputTextField(
      margin:
          const EdgeInsets.only(left: kLeftSpace, right: kRightSpace, top: 24)
              .r,
      onChanged: (v) => strEmail = v,
      hint: appLocal.emailHint,
      label: appLocal.newEmail,
      autoTextDirection: true,
      validator: (v) => validateEmail(
        v,
        serverValue: strDefaultEmail,
        errorMessage: appLocal.emailClaimed,
      ),
      inputFormatter: '[a-zA-Z0-9@.]',
      initialValue: strDefaultEmail,
      inputType: TextInputType.emailAddress,
    );
  }

  Widget enterOtp(BuildContext context) {
    return Container(
      margin:
          const EdgeInsets.only(left: kLeftSpace, right: kRightSpace, top: 100)
              .r,
      child: Column(
        children: [
          Text(
            appLocal.pleaseEnterDigitalCode,
            style: FontStyles.fontSemibold(
              fontSize: 20,
            ),
          ),
          SizedBox(height: 29.h),
          PinCodeTextField(
            appContext: context,
            length: 6,
            hintCharacter: "0",
            hintStyle: FontStyles.fontBold(
                fontSize: 16, color: Colors.white.withOpacity(0.5)),
            animationType: AnimationType.slide,
            pinTheme: PinTheme(
              shape: PinCodeFieldShape.underline,
              inactiveColor: const Color.fromRGBO(203, 208, 220, 1),
              inactiveFillColor: AppColors.backgroundColorDark,
              selectedFillColor: AppColors.backgroundColorDark,
              activeColor: AppColors.colorPrimary,
              disabledColor: Colors.white,
              selectedColor: AppColors.colorPrimary,
              activeFillColor: AppColors.backgroundColorDark,
            ),
            onCompleted: (v) {},
            cursorColor: AppColors.colorYellow,
            animationDuration: const Duration(milliseconds: 300),
            keyboardType: TextInputType.number,
            onChanged: (value) => strCode = value,
          ),
          ValueListenableBuilder(
            valueListenable: _time,
            builder: (context, value, child) {
              return Padding(
                padding: EdgeInsets.only(top: 24.h),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: value == 0
                      ? InkWell(
                          onTap: () {
                            _request.runMutation(
                              context: context,
                              query: GraphQlQuries.changeEmail,
                              type: "SendEmail",
                              variables:
                                  GraphQlVariables.changeEmail(email: strEmail),
                            );
                          },
                          child: RichText(
                            text: TextSpan(
                                style: FontStyles.fontRegular(fontSize: 10),
                                children: <TextSpan>[
                                  const TextSpan(
                                    text: EnStrings.notReceived,
                                    style: TextStyle(
                                      color: Colors.white,
                                    ),
                                  ),
                                  TextSpan(
                                    text: EnStrings.resendOtp,
                                    style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      color: AppColors.colorPrimary,
                                    ),
                                  ),
                                ]),
                          ),
                        )
                      : Wrap(
                          children: [
                            SizedBox(
                              width: 28.w,
                              child: Text(
                                '$value',
                                style: FontStyles.fontRegular(fontSize: 17),
                              ),
                            ),
                            Text(
                              appLocal.secondsRemainToResendOtp,
                              style: FontStyles.fontRegular(fontSize: 17),
                            )
                          ],
                        ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget emailChanged() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SvgPicture.asset("assets/images/emailSend.svg"),
        SizedBox(height: 32.h),
        Text(
          appLocal.emailChangeSuccessfully(strEmail),
          textAlign: TextAlign.center,
          style: FontStyles.fontRegular(height: 2),
        ),
      ],
    );
  }

  @override
  onError(error, String type) {
    strError.value = appLocal.localeName == "ar"
        ? BaseModel.fromJson(error).arMessage ?? ""
        : BaseModel.fromJson(error).message ?? "";
  }

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (type == "SendEmail") {
      if (model.status == statusOK) {
        _time.value = 90;
        startTimer();
      } else {
        strError.value = appLocal.localeName == "ar"
            ? model.arMessage ?? ""
            : model.message ?? "";
      }
    } else {
      if (model.status == statusOK) {
        status.value++;
      } else {
        strError.value = appLocal.localeName == "ar"
            ? model.arMessage ?? ""
            : model.message ?? "";
      }
    }
  }
}
