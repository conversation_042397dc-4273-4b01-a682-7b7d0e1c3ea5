name: overola_suppliers
description: A new Flutter application.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.1+1

environment:
  sdk: ">=2.17.5 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Core UI & State Management (Cross-platform)
  flutter_svg:
  google_fonts: 4.0.4
  provider: ^6.0.2
  get: ^4.6.5
  get_it: ^7.2.0
  flutter_screenutil: ^5.9.3
  cupertino_icons: ^1.0.5

  # UI Components (Cross-platform)
  pin_code_fields: ^8.0.1
  flutter_spinkit: ^5.1.0
  scroll_to_index: ^3.0.1
  visibility_detector: ^0.4.0+2
  sticky_headers: ^0.3.0+2
  slider_button: ^2.0.0
  carousel_slider: ^5.0.0
  percent_indicator: ^4.0.1
  pie_chart: ^5.4.0
  shimmer: ^3.0.0
  flutter_staggered_animations: ^1.0.0
  flutter_animate: ^4.5.0
  calendar_date_picker2: ^0.5.5
  flutter_widget_from_html: ^0.10.0
  lottie: ^2.3.2
  transparent_image: ^2.0.1
  preload_page_view: ^0.2.0

  # Networking & Data (Cross-platform)
  dio: ^5.1.2
  graphql: ^5.1.1
  http_parser: ^4.0.1
  connectivity_plus: ^4.0.0

  # Storage & Caching (Cross-platform)
  shared_preferences: ^2.0.15
  path_provider: ^2.0.11
  flutter_cache_manager: ^3.3.0
  file_picker: ^5.2.10

  # Utilities (Cross-platform)
  intl: ^0.19.0
  translator: ^0.1.7
  mime: ^1.0.2
  objectid: ^2.1.0
  timeago: ^3.6.1
  url_launcher: ^6.1.4
  flutter_local_notifications: ^14.0.0+2

  # Firebase (Cross-platform with desktop support) - Temporarily commented out
  # firebase_core: ^2.12.0
  # firebase_messaging: ^14.6.0
  # firebase_crashlytics: ^3.4.18
  # firebase_analytics: ^10.8.9

  # Image Processing (Cross-platform)
  image: ^4.0.17

  # Mobile-specific dependencies (conditional usage required)
  google_maps_flutter: ^2.0.5
  geolocator: ^9.0.2
  geocoding: ^2.0.0
  sms_autofill: ^2.2.0
  permission_handler: ^10.2.0
  # flutter_branch_sdk: ^6.6.0  # Temporarily disabled for emulator compatibility
  local_auth: ^2.1.2
  app_settings: ^5.0.0
  crop_your_image: ^0.7.5
  camera: ^0.10.5+4
  photo_gallery: ^2.1.1
  flutter_image_compress: ^2.1.0
  scan: ^1.6.0
  flutter_share: ^2.0.0

  # Desktop-specific dependencies (commented out to resolve conflicts)
  # window_manager: ^0.3.7
  # desktop_drop: ^0.4.4
  # file_selector: ^1.0.3
  # flutter_map: ^6.1.0
  # latlong2: ^0.9.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.

flutter:
  generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/images/
  
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages