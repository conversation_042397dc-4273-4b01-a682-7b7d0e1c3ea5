import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/other/expandable_text.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/product_variation_model.dart';
import 'package:overolasuppliers/screens/products/add_product/views/components/image_slider_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';

import '../../model/add_product/view_prduct/product_detail_model.dart';

class MatchProduct extends StatelessWidget {
  Product product = Get.arguments[0];
  RxList<Variants> arrVariations = <Variants>[].obs;
  RxBool isHide = false.obs;

  MatchProduct({super.key});

  @override
  Widget build(BuildContext context) {
    String keywords = "";
    for (int i = 0; i < (product.productKeyWords?.length ?? 0); i++) {
      keywords = "$keywords${product.productKeyWords?[i] ?? ""}, ";
    }
    return Scaffold(
        appBar: const ElbaabHeader(
          title: 'Product Detail',
          leadingBack: true,
        ),
        body: SingleChildScrollView(
          child: Column(
            children: <Widget>[
              Container(
                height: MediaQuery.of(context).size.width +
                    ((MediaQuery.of(context).size.width / 3) +
                        ((product.productOptions?.productColors?.isNotEmpty ??
                                false)
                            ? 120
                            : 0)),
                color: AppColors.headerColorDark,
                margin: const EdgeInsets.only(top: 24),
                child: ImageSliderWidget(
                  isReview: true,
                  matchedProduct: product,
                ),
              ),
              ElbaabFeildContainerWidget(
                borderWidth: 0,
                edgeInsets: const EdgeInsets.only(
                    top: 24, left: kLeftSpace, right: kRightSpace),
                borderColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: kLeftSpace,
                      right: kRightSpace,
                      top: 10,
                      bottom: 16),
                  child: Column(
                    children: [
                      _cardHeading(EnStrings.information),
                      _detailRow(
                          EnStrings.productname, product.productName ?? "",
                          maxLine: 3),
                      _detailRow(EnStrings.description,
                          product.productDescription ?? "",
                          maxLine: 4),
                      if ((product.productManufacturerId ?? "").isNotEmpty)
                        _detailRow(EnStrings.productId,
                            "#${product.productManufacturerId ?? ""}"),
                      _detailRow(EnStrings.categories,
                          "${product.productCategory?.categoryName ?? ""} -> ${product.productSubCategory?.subCategoryName ?? ""}",
                          maxLine: 2),
                      _detailRow(EnStrings.brand,
                          product.productBrand?.brandName ?? ""),
                      if (keywords.isNotEmpty)
                        _detailRow(EnStrings.keyword, keywords),
                    ],
                  ),
                ),
              ),
              ElbaabFeildContainerWidget(
                borderWidth: 0,
                edgeInsets: const EdgeInsets.only(
                    top: 24, left: kLeftSpace, right: kRightSpace),
                borderColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: kLeftSpace,
                      right: kRightSpace,
                      top: 10,
                      bottom: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _cardHeading(EnStrings.details),
                      const SizedBox(height: 6),
                      if ((product.productOptions != null))
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text("Product options",
                              style: FontStyles.fontBold(fontSize: 12)),
                        ),
                      if ((product.productOptions?.productColors ?? [])
                          .isNotEmpty)
                        const SizedBox(height: 24),
                      if ((product.productOptions?.productColors ?? [])
                          .isNotEmpty)
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                EnStrings.color,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: SizedBox(
                                height: 18,
                                child: ListView.builder(
                                    itemCount: product.productOptions
                                            ?.productColors?.length ??
                                        0,
                                    scrollDirection: Axis.horizontal,
                                    itemBuilder: (context, index) {
                                      return Container(
                                        height: 18,
                                        width: 18,
                                        margin: const EdgeInsets.only(right: 8),
                                        child: GlobalMethods.netWorkImage(
                                          (product
                                                  .productOptions
                                                  ?.productColors?[index]
                                                  .colorIcon ??
                                              ""),
                                          BorderRadius.circular(9),
                                          BoxFit.none,
                                        ),
                                      );
                                    }),
                              ),
                            )
                          ],
                        ),
                      if ((product.productOptions?.productSizes ?? [])
                          .isNotEmpty)
                        const SizedBox(height: 16),
                      if ((product.productOptions?.productSizes ?? [])
                          .isNotEmpty)
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                EnStrings.size,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: SizedBox(
                                height: 24,
                                child: ListView.builder(
                                    itemCount: product
                                            .productOptions
                                            ?.productSizes?[0]
                                            .sizeValues
                                            ?.length ??
                                        0,
                                    scrollDirection: Axis.horizontal,
                                    itemBuilder: (context, index) {
                                      return Container(
                                        height: 24,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 14),
                                        margin: const EdgeInsets.only(right: 8),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          border: Border.all(
                                              color: AppColors.colorGray,
                                              width: 1),
                                        ),
                                        child: Center(
                                          child: Text(
                                            product
                                                    .productOptions
                                                    ?.productSizes?[0]
                                                    .sizeValues?[index].value ??
                                                "",
                                            style: FontStyles.fontRegular(
                                              fontSize: 10,
                                              color: AppColors.colorGray,
                                            ),
                                          ),
                                        ),
                                      );
                                    }),
                              ),
                            )
                          ],
                        ),
                      if ((product.productOptions?.productCustomOptions ?? [])
                          .isNotEmpty)
                        const SizedBox(height: 16),
                      if ((product.productOptions?.productCustomOptions ?? [])
                          .isNotEmpty)
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                EnStrings.customOption,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: SizedBox(
                                height: 110,
                                child: ListView.builder(
                                    itemCount: product.productOptions
                                            ?.productCustomOptions?.length ??
                                        0,
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemBuilder: (context, superIndex) {
                                      return SizedBox(
                                        height: 55,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              product
                                                      .productOptions
                                                      ?.productCustomOptions?[
                                                          superIndex]
                                                      .optionTitle ??
                                                  "",
                                              style: FontStyles.fontMedium(
                                                  fontSize: 10),
                                            ),
                                            const SizedBox(height: 8),
                                            SizedBox(
                                              height: 24,
                                              child: ListView.builder(
                                                  itemCount: product
                                                          .productOptions
                                                          ?.productCustomOptions?[
                                                              superIndex]
                                                          .optionValues
                                                          ?.length ??
                                                      0,
                                                  scrollDirection:
                                                      Axis.horizontal,
                                                  itemBuilder:
                                                      (context, index) {
                                                    return Container(
                                                      height: 24,
                                                      padding: const EdgeInsets
                                                              .symmetric(
                                                          horizontal: 14),
                                                      margin:
                                                          const EdgeInsets.only(
                                                              right: 8),
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(5),
                                                        border: Border.all(
                                                            color: AppColors
                                                                .colorGray,
                                                            width: 1),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          product
                                                                  .productOptions
                                                                  ?.productCustomOptions?[
                                                                      superIndex]
                                                                  .optionValues?[index].value ??
                                                              "",
                                                          style: FontStyles
                                                              .fontMedium(
                                                            fontSize: 10,
                                                            color: AppColors
                                                                .colorGray,
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  }),
                                            ),
                                          ],
                                        ),
                                      );
                                    }),
                              ),
                            )
                          ],
                        ),
                      Obx(
                        () => arrVariations.isNotEmpty
                            ? ListTile(
                                onTap: () => Get.toNamed(
                                    RouteNames.viewVariationsScreen,
                                    arguments: [arrVariations]),
                                contentPadding: const EdgeInsets.symmetric(
                                    vertical: 0.0, horizontal: 16.0),
                                dense: true,
                                leading: Transform.translate(
                                  offset: const Offset(-16, 0),
                                  child: Text(
                                    EnStrings.variation,
                                    style: FontStyles.fontBold(
                                      fontSize: 12,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ),
                                trailing: Transform.translate(
                                  offset: const Offset(16, 0),
                                  child: const Icon(
                                    Icons.chevron_right,
                                    color: Color.fromRGBO(252, 252, 252, 0.65),
                                    size: 24,
                                  ),
                                ),
                              )
                            : Container(),
                      ),
                      if ((product.productSpecs ?? []).isNotEmpty)
                        ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 0.0, horizontal: 16.0),
                          dense: true,
                          leading: Transform.translate(
                            offset: const Offset(-16, 0),
                            child: Text(
                              EnStrings.specification,
                              style: FontStyles.fontBold(
                                fontSize: 12,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      if ((product.productSpecs ?? []).isNotEmpty)
                        ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: product.productSpecs?.length,
                            itemBuilder: (context, index) {
                              ProductSpecs spec =
                                  (product.productSpecs ?? [])[index];
                              return Container(
                                height: 36,
                                decoration: BoxDecoration(
                                  color: index % 2 == 1
                                      ? AppColors.backgroundColorDark
                                      : AppColors.backgroundColorDark
                                          .withOpacity(0.4),
                                ),
                                child: Row(
                                  children: <Widget>[
                                    Expanded(
                                      flex: 2,
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(left: 10),
                                          child: Text(
                                            spec.specsTitle ?? "",
                                            style: FontStyles.fontRegular(
                                              fontSize: 12,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 3,
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(left: 10),
                                          child: Text(
                                            spec.specsValue ?? "",
                                            style: FontStyles.fontRegular(
                                              fontSize: 12,
                                              color:
                                                  Colors.white.withOpacity(0.5),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }),
                    ],
                  ),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 30, vertical: 40),
                child: ElbaabGradientButtonWidget(
                  onPress: () => Get.toNamed(
                      RouteNames.addProductTabViewScreen,
                      arguments: [product, true, false]),
                  text: "Match Product",
                ),
              ),
            ],
          ),
        ));
  }

  Widget _detailRow(String title, String value, {int maxLine = 1}) {
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              title,
              style: FontStyles.fontRegular(fontSize: 12),
            ),
          ),
          Expanded(
            flex: 2,
            child: ExpandableText(
              value,
              trimLines: maxLine,
            ),
          )
        ],
      ),
    );
  }

  Widget _cardHeading(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(title, style: FontStyles.fontBold(fontSize: 12)),
      ),
    );
  }
}
