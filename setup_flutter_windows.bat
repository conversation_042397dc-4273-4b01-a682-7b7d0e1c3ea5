@echo off
REM Flutter Setup Script for Windows
REM This script downloads and installs Flutter SDK and sets up the development environment

setlocal enabledelayedexpansion

echo 🚀 Flutter Desktop Development Setup for Windows
echo ================================================

REM Colors for output (Windows doesn't support ANSI colors in batch by default)
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

echo %INFO% Starting Flutter installation process...

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo %WARNING% This script should be run as administrator for best results
    echo %WARNING% Some features may not work properly without admin privileges
    pause
)

REM Set installation directory
set "FLUTTER_DIR=C:\flutter"
set "FLUTTER_BIN=%FLUTTER_DIR%\bin"

REM Check if Flutter is already installed
if exist "%FLUTTER_BIN%\flutter.bat" (
    echo %INFO% Flutter is already installed at %FLUTTER_DIR%
    goto :configure_flutter
)

echo %INFO% Creating Flutter installation directory...
if not exist "%FLUTTER_DIR%" mkdir "%FLUTTER_DIR%"

REM Download Flutter SDK
echo %INFO% Downloading Flutter SDK...
echo %INFO% This may take several minutes depending on your internet connection...

REM Use PowerShell to download Flutter
powershell -Command "& {
    $ProgressPreference = 'SilentlyContinue'
    $url = 'https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.24.3-stable.zip'
    $output = 'C:\flutter_sdk.zip'
    Write-Host 'Downloading Flutter SDK...'
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
        Write-Host 'Download completed successfully'
    } catch {
        Write-Host 'Download failed: ' $_.Exception.Message
        exit 1
    }
}"

if %errorlevel% neq 0 (
    echo %ERROR% Failed to download Flutter SDK
    echo %ERROR% Please check your internet connection and try again
    pause
    exit /b 1
)

echo %SUCCESS% Flutter SDK downloaded successfully

REM Extract Flutter SDK
echo %INFO% Extracting Flutter SDK...
powershell -Command "& {
    try {
        Expand-Archive -Path 'C:\flutter_sdk.zip' -DestinationPath 'C:\' -Force
        Write-Host 'Extraction completed successfully'
    } catch {
        Write-Host 'Extraction failed: ' $_.Exception.Message
        exit 1
    }
}"

if %errorlevel% neq 0 (
    echo %ERROR% Failed to extract Flutter SDK
    pause
    exit /b 1
)

REM Clean up downloaded zip file
del "C:\flutter_sdk.zip" >nul 2>&1

echo %SUCCESS% Flutter SDK extracted to %FLUTTER_DIR%

:configure_flutter
REM Add Flutter to PATH
echo %INFO% Configuring Flutter PATH...

REM Check if Flutter is already in PATH
echo %PATH% | findstr /C:"%FLUTTER_BIN%" >nul
if %errorlevel% equ 0 (
    echo %INFO% Flutter is already in PATH
) else (
    echo %INFO% Adding Flutter to system PATH...
    
    REM Add to system PATH (requires admin privileges)
    setx PATH "%PATH%;%FLUTTER_BIN%" /M >nul 2>&1
    if %errorlevel% equ 0 (
        echo %SUCCESS% Flutter added to system PATH
    ) else (
        echo %WARNING% Could not add to system PATH (admin required)
        echo %INFO% Adding to user PATH instead...
        setx PATH "%PATH%;%FLUTTER_BIN%" >nul 2>&1
        if %errorlevel% equ 0 (
            echo %SUCCESS% Flutter added to user PATH
        ) else (
            echo %ERROR% Failed to add Flutter to PATH
        )
    )
)

REM Update current session PATH
set "PATH=%PATH%;%FLUTTER_BIN%"

REM Enable desktop support
echo %INFO% Enabling Flutter desktop support...
"%FLUTTER_BIN%\flutter.bat" config --enable-windows-desktop
"%FLUTTER_BIN%\flutter.bat" config --enable-macos-desktop
"%FLUTTER_BIN%\flutter.bat" config --enable-linux-desktop

REM Disable analytics (optional)
"%FLUTTER_BIN%\flutter.bat" config --no-analytics

echo %INFO% Running Flutter doctor to check installation...
"%FLUTTER_BIN%\flutter.bat" doctor

echo %INFO% Checking for Visual Studio installation...
if exist "C:\Program Files\Microsoft Visual Studio\2022" (
    echo %SUCCESS% Visual Studio 2022 found
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019" (
    echo %SUCCESS% Visual Studio 2019 found
) else (
    echo %WARNING% Visual Studio not found
    echo %WARNING% You need Visual Studio 2019 or 2022 with C++ tools for Windows development
    echo %INFO% Download from: https://visualstudio.microsoft.com/downloads/
)

REM Navigate to project directory and get dependencies
echo %INFO% Setting up project dependencies...
cd /d "%~dp0"
"%FLUTTER_BIN%\flutter.bat" pub get

if %errorlevel% equ 0 (
    echo %SUCCESS% Project dependencies installed successfully
) else (
    echo %ERROR% Failed to install project dependencies
    echo %ERROR% Please run 'flutter pub get' manually after fixing any issues
)

echo %SUCCESS% Flutter setup completed!
echo.
echo Next steps:
echo 1. Restart your command prompt or IDE to use Flutter
echo 2. Run 'flutter doctor' to verify installation
echo 3. Run 'flutter devices' to see available devices
echo 4. Run 'flutter run -d windows' to run the app on Windows
echo.
echo For development, you can use:
echo - Visual Studio Code with Flutter extension
echo - Android Studio with Flutter plugin
echo - IntelliJ IDEA with Flutter plugin
echo.

pause
