import 'package:overolasuppliers/model/shop/ViewShopModel.dart';

class OrdersModel {
  final String? typename;
  final int? status;
  final String? message;
  final OrdersPaggination? ordersPaggination;

  OrdersModel({
    this.typename,
    this.status,
    this.message,
    this.ordersPaggination,
  });

  OrdersModel.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        status = json['status'] as int?,
        message = json['message'] as String?,
        ordersPaggination =
            (json['orderItems'] as Map<String, dynamic>?) != null
                ? OrdersPaggination.fromJson(
                    json['orderItems'] as Map<String, dynamic>)
                : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'status': status,
        'message': message,
        'orderItems': ordersPaggination?.toJson()
      };
}

class OrdersPaggination {
  final String? typename;
  final List<OrderItems>? items;
  final int? page;
  final bool? hasNextPage;
  final int? totalPages;
  final int? totalItems;

  OrdersPaggination({
    this.typename,
    this.items,
    this.page,
    this.hasNextPage,
    this.totalPages,
    this.totalItems,
  });

  OrdersPaggination.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        items = (json['items'] as List?)
            ?.map((dynamic e) => OrderItems.fromJson(e as Map<String, dynamic>))
            .toList(),
        page = json['page'] as int?,
        hasNextPage = json['hasNextPage'] as bool?,
        totalPages = json['totalPages'] as int?,
        totalItems = json['totalItems'] as int?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'items': items?.map((e) => e.toJson()).toList(),
        'page': page,
        'hasNextPage': hasNextPage,
        'totalItems': totalItems,
        'totalPages': totalPages
      };
}

class OrderItems {
  final String? notes;
  final String? readinessPeriode;
  final String? invoice;
  final String? shipmentQrCode;
  final List<OrderItemStatus>? orderItemStatus;
  final BillingAddress? billingAddress;
  final String? estimatedDeliveryTime;
  final CourrierId? courrierId;
  final PickupDetails? pickupDetails;
  final DeliveryDetails? deliveryDetails;
  final String? createdAt;
  final String? id;
  final String? orderItemCode;
  final ClientId? clientId;
  final ShopPickupAddresses? pickupAddress;
  final List<Items>? items;
  final dynamic totalCost;
  final dynamic finalCost;
  final dynamic shipmentCost;
  final dynamic supplierFeeCharges;
  OrderItems({
    this.notes,
    this.readinessPeriode,
      this.deliveryDetails,
    this.invoice,
    this.shipmentQrCode,
    this.orderItemStatus,
    this.pickupDetails,
    this.estimatedDeliveryTime,
    this.courrierId,
    this.createdAt,
    this.finalCost,
    this.id,
    this.orderItemCode,
    this.billingAddress,
    this.clientId,
    this.pickupAddress,
    this.items,
    this.totalCost,
    this.shipmentCost,
    this.supplierFeeCharges,
  });

  OrderItems.fromJson(Map<String, dynamic> json)
      : notes = json['notes'] as String?,
        orderItemStatus = (json['orderItemStatus'] as List?)
            ?.map((dynamic e) =>
                OrderItemStatus.fromJson(e as Map<String, dynamic>))
            .toList(),
        createdAt = json['createdAt'] as String?,
        readinessPeriode = json['readinessPeriode'] as String?,
        id = json['_id'] as String?,
        estimatedDeliveryTime = json['estimatedDeliveryTime'] as String?,
        courrierId = (json['courrierId'] as Map<String, dynamic>?) != null
            ? CourrierId.fromJson(json['courrierId'] as Map<String, dynamic>)
            : null,
        pickupDetails = (json['pickupDetails'] as Map<String, dynamic>?) != null
            ? PickupDetails.fromJson(
                json['pickupDetails'] as Map<String, dynamic>)
            : null,
        deliveryDetails = (json['deliveryDetails'] as Map<String, dynamic>?) != null
            ? DeliveryDetails.fromJson(
                json['deliveryDetails'] as Map<String, dynamic>)
            : null, 
        orderItemCode = json['orderItemCode'] as String?,
        invoice = json['invoice'] as String?,
        shipmentQrCode = json['shipmentQrCode'] as String?,
        billingAddress =
            (json['billingAddress'] as Map<String, dynamic>?) != null
                ? BillingAddress.fromJson(
                    json['billingAddress'] as Map<String, dynamic>)
                : null,
        clientId = (json['clientId'] as Map<String, dynamic>?) != null
            ? ClientId.fromJson(json['clientId'] as Map<String, dynamic>)
            : null,
        pickupAddress = (json['pickupAddress'] as Map<String, dynamic>?) != null
            ? ShopPickupAddresses.fromJson(
                json['pickupAddress'] as Map<String, dynamic>)
            : null,
        items = (json['items'] as List?)
            ?.map((dynamic e) => Items.fromJson(e as Map<String, dynamic>))
            .toList(),
        totalCost = json['totalCost'],
        finalCost = json['finalCost'],
        shipmentCost = json['shipmentCost'],
        supplierFeeCharges = json['supplierFeeCharges'];

  get deliveredAt => null;

  Map<String, dynamic> toJson() => {
        'notes': notes,
        'createdAt': createdAt,
        '_id': id,
        'readinessPeriode': readinessPeriode,
        'estimatedDeliveryTime': estimatedDeliveryTime,
        'courrierId': courrierId?.toJson(),
        'deliveryDetails': deliveryDetails?.toJson(),
        'invoice': invoice,
        'orderItemCode': orderItemCode,
        'finalCost': finalCost,
        'shipmentQrCode': shipmentQrCode,
        'supplierFeeCharges': supplierFeeCharges,
        'shipmentCost': shipmentCost,
        'clientId': clientId?.toJson(),
        'pickupDetails': pickupDetails?.toJson(),
        'orderItemStatus': orderItemStatus?.map((e) => e.toJson()).toList(),
        'billingAddress': billingAddress?.toJson(),
        'pickupAddress': pickupAddress?.toJson(),
        'items': items?.map((e) => e.toJson()).toList(),
        'totalCost': totalCost,
      };
}

class OrderItemStatus {
  final String? typename;
  final String? status;
  final String? createdAt;

  OrderItemStatus({
    this.typename,
    this.createdAt,
    this.status,
  });

  OrderItemStatus.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        createdAt = json['createdAt'] as String?,
        status = json['status'] as String?;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'createdAt': createdAt, 'status': status};
}

class BillingAddress {
  final String? typename;
  final String? address;

  BillingAddress({
    this.typename,
    this.address,
  });

  BillingAddress.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        address = json['address'] as String?;

  Map<String, dynamic> toJson() => {'__typename': typename, 'address': address};
}

class ClientId {
  final String? typename;
  final UserId? userId;

  ClientId({
    this.typename,
    this.userId,
  });

  ClientId.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        userId = (json['userId'] as Map<String, dynamic>?) != null
            ? UserId.fromJson(json['userId'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'userId': userId?.toJson()};
}

class UserId {
  final String? typename;
  final String? userName;
  final dynamic userPhoneNumber;
  final String? userEmail;

  UserId({
    this.typename,
    this.userName,
    this.userPhoneNumber,
    this.userEmail,
  });

  UserId.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        userName = json['userName'] as String?,
        userPhoneNumber = json['userPhoneNumber'],
        userEmail = json['userEmail'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'userName': userName,
        'userPhoneNumber': userPhoneNumber,
        'userEmail': userEmail
      };
}

class Items {
  final String? typename;
  final Product? product;
  final String? id;
  final Variant? variant;
  final int? quantity;

  final List<OrderItemStatus>? orderItemProductStatus;

  Items(
      {this.typename,
      this.product,
      this.id,
      this.variant,
      this.quantity,
      this.orderItemProductStatus});

  Items.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        id = json['_id'] as String?,
        orderItemProductStatus = (json['orderItemProductStatus'] as List?)
            ?.map((dynamic e) =>
                OrderItemStatus.fromJson(e as Map<String, dynamic>))
            .toList(),
        product = (json['product'] as Map<String, dynamic>?) != null
            ? Product.fromJson(json['product'] as Map<String, dynamic>)
            : null,
        variant = (json['variant'] as Map<String, dynamic>?) != null
            ? Variant.fromJson(json['variant'] as Map<String, dynamic>)
            : null,
        quantity = json['quantity'] as int?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'product': product?.toJson(),
        'variant': variant?.toJson(),
        '_id': id,
        'orderItemProductStatus':
            orderItemProductStatus?.map((e) => e.toJson()).toList(),
        'quantity': quantity
      };
}

class Product {
  final String? productEIN;
  final dynamic productName;
  final bool? isFreeDeliveryItem;
  final dynamic id;
  final dynamic productPrice;
  final dynamic productImages;
  final ProductCategory? productCategory;
  final ProductSubCategory? productSubCategory;
  final ProductBrand? productBrand;

  Product({
    this.productEIN,
    this.productName,
    this.id,
    this.productPrice,
    this.productImages,
    this.isFreeDeliveryItem,
    this.productCategory,
    this.productSubCategory,
    this.productBrand,
  });

  Product.fromJson(Map<String, dynamic> json)
      : productEIN = json['productEIN'] as String?,
        productName = json['productName'],
        id = json['_id'],
        isFreeDeliveryItem = json['isFreeDeliveryItem'],
        productPrice = json['productPrice'],
        productImages = json['productImages'],
        productCategory =
            (json['productCategory'] as Map<String, dynamic>?) != null
                ? ProductCategory.fromJson(
                    json['productCategory'] as Map<String, dynamic>)
                : null,
        productSubCategory =
            (json['productSubCategory'] as Map<String, dynamic>?) != null
                ? ProductSubCategory.fromJson(
                    json['productSubCategory'] as Map<String, dynamic>)
                : null,
        productBrand = (json['productBrand'] as Map<String, dynamic>?) != null
            ? ProductBrand.fromJson(
                json['productBrand'] as Map<String, dynamic>)
            : null;
  Map<String, dynamic> toJson() => {
        'productEIN': productEIN,
        'productName': productName,
        'isFreeDeliveryItem': isFreeDeliveryItem,
        '_id': id,
        'productPrice': productPrice,
        'productImages': productImages,
        'productCategory': productCategory?.toJson(),
        'productSubCategory': productSubCategory?.toJson(),
        'productBrand': productBrand?.toJson()
      };
}

class ProductCategory {
  final String? typename;
  final dynamic categoryName;

  ProductCategory({
    this.typename,
    this.categoryName,
  });

  ProductCategory.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        categoryName = json['categoryName'];

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'categoryName': categoryName};
}

class Variant {
  final String? variantEIN;
  final String? id;
  final bool? isFreeDeliveryItem;
  final ProductId? productId;
  final String? variantName;
  final VariantAttributes? variantAttributes;
  final List<String>? variantImages;
  final dynamic variantPrice;

  Variant({
    this.variantEIN,
    this.id,
    this.productId,
    this.isFreeDeliveryItem,
    this.variantName,
    this.variantAttributes,
    this.variantImages,
    this.variantPrice,
  });

  Variant.fromJson(Map<String, dynamic> json)
      : variantEIN = json['variantEIN'] as String?,
        id = json['_id'] as String?,
        productId = (json['productId'] as Map<String, dynamic>?) != null
            ? ProductId.fromJson(json['productId'] as Map<String, dynamic>)
            : null,
        variantName = json['variantName'] as String?,
        isFreeDeliveryItem = json['isFreeDeliveryItem'] as bool?,
        variantAttributes =
            (json['variantAttributes'] as Map<String, dynamic>?) != null
                ? VariantAttributes.fromJson(
                    json['variantAttributes'] as Map<String, dynamic>)
                : null,
        variantImages = (json['variantImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        variantPrice = json['variantPrice'];

  Map<String, dynamic> toJson() => {
        'variantEIN': variantEIN,
        '_id': id,
        'isFreeDeliveryItem': isFreeDeliveryItem,
        'productId': productId?.toJson(),
        'variantName': variantName,
        'variantAttributes': variantAttributes?.toJson(),
        'variantImages': variantImages,
        'variantPrice': variantPrice
      };
}

class ProductId {
  final String? typename;
  final bool? isFreeDeliveryItem;
  final ProductCategory? productCategory;
  final ProductSubCategory? productSubCategory;
  final ProductBrand? productBrand;

  ProductId({
    this.typename,
    this.productCategory,
    this.isFreeDeliveryItem,
    this.productSubCategory,
    this.productBrand,
  });

  ProductId.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        isFreeDeliveryItem = json['isFreeDeliveryItem'] as bool?,
        productCategory =
            (json['productCategory'] as Map<String, dynamic>?) != null
                ? ProductCategory.fromJson(
                    json['productCategory'] as Map<String, dynamic>)
                : null,
        productSubCategory =
            (json['productSubCategory'] as Map<String, dynamic>?) != null
                ? ProductSubCategory.fromJson(
                    json['productSubCategory'] as Map<String, dynamic>)
                : null,
        productBrand = (json['productBrand'] as Map<String, dynamic>?) != null
            ? ProductBrand.fromJson(
                json['productBrand'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'productCategory': productCategory?.toJson(),
        'productSubCategory': productSubCategory?.toJson(),
        'productBrand': productBrand?.toJson()
      };
}

class ProductSubCategory {
  final String? typename;
  final String? subCategoryName;

  ProductSubCategory({
    this.typename,
    this.subCategoryName,
  });

  ProductSubCategory.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        subCategoryName = json['subCategoryName'] as String?;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'subCategoryName': subCategoryName};
}

class ProductBrand {
  final String? typename;
  final String? brandName;

  ProductBrand({
    this.typename,
    this.brandName,
  });

  ProductBrand.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        brandName = json['brandName'] as String?;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'brandName': brandName};
}

class VariantAttributes {
  final String? typename;
  final VariantSize? variantSize;
  final VariantColor? variantColor;
  final List<VariantCustomOptions>? variantCustomOptions;

  VariantAttributes({
    this.typename,
    this.variantSize,
    this.variantColor,
    this.variantCustomOptions,
  });

  VariantAttributes.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        variantSize = (json['variantSize'] as Map<String, dynamic>?) != null
            ? VariantSize.fromJson(json['variantSize'] as Map<String, dynamic>)
            : null,
        variantColor = (json['variantColor'] as Map<String, dynamic>?) != null
            ? VariantColor.fromJson(
                json['variantColor'] as Map<String, dynamic>)
            : null,
        variantCustomOptions = (json['variantCustomOptions'] as List?)
            ?.map((dynamic e) =>
                VariantCustomOptions.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'variantSize': variantSize?.toJson(),
        'variantColor': variantColor?.toJson(),
        'variantCustomOptions':
            variantCustomOptions?.map((e) => e.toJson()).toList(),
      };
}

class VariantSize {
  final String? typename;
  final dynamic size;

  VariantSize({
    this.typename,
    this.size,
  });

  VariantSize.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        size = json['size'];

  Map<String, dynamic> toJson() => {'__typename': typename, 'size': size};
}

class VariantColor {
  final String? typename;
  final String? colorFamily;
  final dynamic colorImages;
  final String? colorName;
  final String? colorIcon;

  VariantColor({
    this.typename,
    this.colorFamily,
    this.colorImages,
    this.colorName,
    this.colorIcon,
  });

  VariantColor.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        colorFamily = json['colorFamily'] as String?,
        colorIcon = json['colorIcon'] as String?,
        colorImages = json['colorImages'],
        colorName = json['colorName'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'colorFamily': colorFamily,
        'colorImages': colorImages,
        'colorIcon': colorIcon,
        'colorName': colorName
      };
}


class VariantCustomOptions {
  final String? attributeTitle;
  final String? attributeValue;

  VariantCustomOptions({
    this.attributeTitle,
    this.attributeValue,
  });

  VariantCustomOptions.fromJson(Map<String, dynamic> json)
      : attributeTitle = json['attributeTitle'] as String?,
        attributeValue = json['attributeValue'] as String?;

  Map<String, dynamic> toJson() =>
      {'attributeTitle': attributeTitle, 'attributeValue': attributeValue};
}

class CourrierId {
  final String? typename;
  final String? companyName;
  final String? officeAddress;
  final String? phoneNumber;

  CourrierId({
    this.typename,
    this.companyName,
    this.officeAddress,
    this.phoneNumber,
  });

  CourrierId.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        companyName = json['companyName'] as String?,
        officeAddress = json['officeAddress'] as String?,
        phoneNumber = json['phoneNumber'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'companyName': companyName,
        'officeAddress': officeAddress,
        'phoneNumber': phoneNumber
      };
}

class PickupDetails {
  final PickupDriver? pickupDriverId;
  PickupDetails({this.pickupDriverId});

  PickupDetails.fromJson(Map<String, dynamic> json)
      : pickupDriverId =
            (json['pickupDriverId'] as Map<String, dynamic>?) != null
                ? PickupDriver.fromJson(
                    json['pickupDriverId'] as Map<String, dynamic>)
                : null;

  Map<String, dynamic> toJson() => {'pickupDriverId': pickupDriverId?.toJson()};
}

class DeliveryDetails {
  final DeliveryDriver? deliveryDriverId;
  DeliveryDetails({this.deliveryDriverId});

  DeliveryDetails.fromJson(Map<String, dynamic> json)
      : deliveryDriverId =
            (json['deliveryDriverId'] as Map<String, dynamic>?) != null 
            ? DeliveryDriver.fromJson(
                json['deliveryDriverId'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {'deliveryDriverId': deliveryDriverId?.toJson()};
}

class DeliveryDriver {
  final String? name;

  DeliveryDriver({this.name});

  DeliveryDriver.fromJson(Map<String, dynamic> json)
      : name = json['name'] as String?;

  Map<String, dynamic> toJson() => {'name': name};
}




class PickupDriver {
  final String? name;

  PickupDriver({this.name});

  PickupDriver.fromJson(Map<String, dynamic> json)
      : name = json['name'] as String?;

  Map<String, dynamic> toJson() => {'name': name};
}
