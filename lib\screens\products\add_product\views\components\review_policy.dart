import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_carousel_feild_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';

class ReviewPolicy extends StatelessWidget {
   ReviewPolicy({Key? key, required this.controller}) : super(key: key);
  final AddProductController controller;

  late AppLocalizations appLocal;

  @override
  Widget build(BuildContext context) {
     appLocal = AppLocalizations.of(context)!;
    return Padding(
      padding: const EdgeInsets.only(top: 24).r,
      child: ElbaabCarouselFeildWidget(
        aspectRatio: 5.2/5,
        children: [
        ElbaabFeildContainerWidget(
        borderWidth: 0,
        borderColor: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.only(
              left: kLeftSpace, right: kRightSpace, top: 10, bottom: 16).r,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      appLocal.policesTitleEnglish,
                      style: FontStyles.fontBold(fontSize: 12),
                    ),
                  ),
                  IconButton(
                    onPressed: () =>
                        controller.changeTab(isNext: false, isJumpPage: 5),
                    icon: Container(
                      height: 30,
                      width: 30,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.06),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: SvgPicture.string(SvgStrings.iconEditGray),
                      ),
                    ),
                  ),
                ],
              ),
              Text(
                appLocal.returnCondition,
                style: FontStyles.fontBold(fontSize: 12),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      appLocal.productforFreeDelivery,
                      style: FontStyles.fontRegular(fontSize: 12),
                    ),
                  ),
                  Expanded(flex: 2, child: getFreeDeliveryStatus())
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      appLocal.acceptReturn,
                      style: FontStyles.fontRegular(fontSize: 12),
                    ),
                  ),
                  Expanded(flex: 2, child: getAcceptReturn())
                ],
              ),
              if (controller.acceptReturn.value)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        appLocal.returnCost,
                        style: FontStyles.fontRegular(fontSize: 12),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        controller.returnType.value == '0'
                            ? appLocal.sellerPayDeliveryCost
                            : appLocal.customerPayDeliveryCost,
                        style: FontStyles.fontRegular(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.5),
                        ),
                      ),
                    )
                  ],
                ),
            
              if (controller.acceptReturn.value ||
                  (controller.product?.productPolicies?.productAcceptReturn ??
                      false))
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        appLocal.returnDuration,
                        style: FontStyles.fontRegular(fontSize: 12),
                      ),
                    ),
                    Expanded(flex: 2, child: getReturnDuration())
                  ],
                ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      appLocal.warrantyDuration,
                      style: FontStyles.fontRegular(fontSize: 12),
                    ),
                  ),
                  Expanded(flex: 2, child: getWarantyDuration())
                ],
              ),
              if (controller.txtProductPolicy.text.isNotEmpty)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        appLocal.productPolicy,
                        style: FontStyles.fontRegular(fontSize: 12),
                      ),
                    ),
                    Expanded(flex: 2, child: getPolicy())
                  ],
                ),
            ],
          ),
        ),
      ),
      
      ElbaabFeildContainerWidget(
        borderWidth: 0,
        borderColor: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.only(
              left: kLeftSpace, right: kRightSpace, top: 10, bottom: 16).r,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      appLocal.policesTitleArabic,
                      style: FontStyles.fontBold(fontSize: 12),
                    ),
                  ),
                  IconButton(
                    onPressed: () =>
                        controller.changeTab(isNext: false, isJumpPage: 5),
                    icon: Container(
                      height: 30,
                      width: 30,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.06),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: SvgPicture.string(SvgStrings.iconEditGray),
                      ),
                    ),
                  ),
                ],
              ),
              Text(
                appLocal.returnCondition,
                style: FontStyles.fontBold(fontSize: 12),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      appLocal.productforFreeDelivery,
                      style: FontStyles.fontRegular(fontSize: 12),
                    ),
                  ),
                  Expanded( child: getFreeDeliveryStatus())
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                     appLocal.acceptReturn,
                      style: FontStyles.fontRegular(fontSize: 12),
                    ),
                  ),
                  Expanded( child: getAcceptReturn())
                ],
              ),
              if (controller.acceptReturn.value)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        appLocal.returnCost,
                        style: FontStyles.fontRegular(fontSize: 12),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        controller.returnType.value == '0'
                            ? appLocal.sellerPayDeliveryCost
                            : appLocal.customerPayDeliveryCost,
                        style: FontStyles.fontRegular(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.5),
                        ),
                      ),
                    )
                  ],
                ),
         
              if (controller.acceptReturn.value ||
                  (controller.product?.productPolicies?.productAcceptReturn ??
                      false))
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        appLocal.returnDuration,
                        style: FontStyles.fontRegular(fontSize: 12),
                      ),
                    ),
                    Expanded(flex: 2, child: getReturnDuration())
                  ],
                ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      appLocal.warrantyDuration,
                      style: FontStyles.fontRegular(fontSize: 12),
                    ),
                  ),
                  Expanded( child: getWarantyDuration(isArabic: true))
                ],
              ),
              if (controller.txtProductPolicy.text.isNotEmpty)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        appLocal.productPolicy,
                        style: FontStyles.fontRegular(fontSize: 12),
                      ),
                    ),
                    Expanded(flex: 2, child: getPolicy(isArabic: true))
                  ],
                ),
            ],
          ),
        ),
      )
       
      ]),
    );}

  Widget getAcceptReturn() {
    String policies = prefs.getString(productPolicies) ?? "";
    bool cacheValue = false;
    bool serverValue = false;
    if (policies.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(policies);
      cacheValue = info["accept_return"] ?? false;
      serverValue =
          controller.product?.productPolicies?.productAcceptReturn ?? false;
    }
    return controller.validateWidget(
        cacheValue: cacheValue ? appLocal.yes : appLocal.no,
        serverValue: serverValue ? appLocal.yes : appLocal.no,
        feildValue: controller.acceptReturn.value ? appLocal.yes : appLocal.no,
        errorCacheValue: appLocal.youRemovedProductAcceptReturnPolicies,
        errorServerValue: appLocal.productAcceptReturnNotProvided);
  }

  Widget getFreeDeliveryStatus() {
    String policies = prefs.getString(productPolicies) ?? "";
    bool cacheValue = false;
    bool serverValue = false;
    if (policies.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(policies);
      cacheValue = info["is_free_delivery"] ?? false;
      serverValue = controller.product?.isFreeDeliveryItem ?? false;
    }
    return controller.validateWidget(
        cacheValue: cacheValue ? appLocal.yes : appLocal.no,
        serverValue: serverValue ? appLocal.yes : appLocal.no,
        feildValue: controller.freeDelivery.value ? appLocal.yes : appLocal.no,
        errorCacheValue: appLocal.youRemovedProductFreeDeliveryPolicies,
        errorServerValue: appLocal.productForFreeDeliveryNotProvided);
  }

  Widget getReturnDuration({bool isArabic = false}) {
    String policies = prefs.getString(productPolicies) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (policies.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(policies);
      if(isArabic){

      cacheValue = info["return_duration"] ?? "";
      serverValue =
          controller.product?.ar?.productPolicies?.productReturnDuration ?? "";
      }else{

      cacheValue = info["return_duration"] ?? "";
      serverValue =
          controller.product?.productPolicies?.productReturnDuration ?? "";
      }
    }
    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: controller.returnPolicy.value == "Other"
            ? controller.customReturnPolicy
            : controller.returnPolicy.value,
        errorCacheValue: appLocal.errorCacheValueReturnDuration,
        errorServerValue: appLocal.errorServerValueReturnDuration);
  }

  Widget getWarantyDuration({ bool isArabic = false}) {
    String policies = prefs.getString(productPolicies) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (policies.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(policies);
      if(isArabic){

      cacheValue = info["warranty_duration_ar"] ?? "";
      serverValue =
          controller.product?.ar?.productPolicies?.productWarrantyDuration ?? "";
      }else{

      cacheValue = info["warranty_duration"] ?? "";
      serverValue =
          controller.product?.productPolicies?.productWarrantyDuration ?? "";
      }
    }
    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: isArabic?controller.warrantyPolicy.value == "Other"
            ? controller.customWarrantyPolicyAr
            : controller.warrantyPolicyAr.value: controller.warrantyPolicy.value == "Other"
            ? controller.customWarrantyPolicy
            : controller.warrantyPolicy.value,
        errorCacheValue: appLocal.errorCacheValueWarrantyDuration,
        errorServerValue: appLocal.errorServerValueWarrantyDuration);
  }

  Widget getPolicy({ bool isArabic = false}) {
    String policies = prefs.getString(productPolicies) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (policies.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(policies);
      if(isArabic){
cacheValue = info["policyAr"] ?? "";
      serverValue =
          controller.product?.ar?.productPolicies?.productReturnPolicy ?? "";
      }else{
cacheValue = info["policy"] ?? "";
      serverValue =
          controller.product?.productPolicies?.productReturnPolicy ?? "";
      }
      
    }
    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: isArabic?controller.txtProductPolicyAr.text: controller.txtProductPolicy.text,
        errorCacheValue: appLocal.errorCacheValuePolicies,
        errorServerValue: appLocal.errorServerValuePolicies);
  }
}
