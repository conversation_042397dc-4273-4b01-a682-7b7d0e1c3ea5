import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class RowDetails extends StatelessWidget {
  final String detail;
  final Color? textColor;

  const RowDetails({Key? key, required this.detail, this.textColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        detail,
        style: FontStyles.fontRegular(
          fontSize: 11,
          color: textColor ?? Colors.white.withOpacity(0.8),
        ),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
