import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/other/popup_loader_component.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_border_button_widget.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:photo_gallery/photo_gallery.dart';
import 'package:transparent_image/transparent_image.dart';

class ElbaabGalleryView extends StatefulWidget {
  final bool isForMultiImage;
  final int? selectionLimit;
  const ElbaabGalleryView(
      {super.key, required this.isForMultiImage, this.selectionLimit});

  @override
  State<ElbaabGalleryView> createState() => _ElbaabGalleryViewState();
}

class _ElbaabGalleryViewState extends State<ElbaabGalleryView> {
  List<Album>? _albums;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _loading = true;
    initAsync();
  }

  Future<void> initAsync() async {
    if (await _promptPermissionSetting()) {
      List<Album> albums =
          await PhotoGallery.listAlbums(mediumType: MediumType.image);
      setState(() {
        _albums = albums;
        _loading = false;
      });
    }
    setState(() {
      _loading = false;
    });
  }

  Future<bool> _promptPermissionSetting() async {
    if (Platform.isIOS) {
      if (await Permission.photos.request().isGranted ||
          await Permission.storage.request().isGranted) {
        return true;
      }
    }
    if (Platform.isAndroid) {
      if (await Permission.storage.request().isGranted ||
          await Permission.photos.request().isGranted &&
              await Permission.videos.request().isGranted) {
        return true;
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: Colors.black,
      body: _loading
          ? Center(
              child: SpinKitSpinningLines(color: AppColors.colorPrimary),
            )
          : SafeArea(
              child: CustomScrollView(
                physics: const BouncingScrollPhysics(),
                slivers: [
                  // Enhanced Trendy Header
                  SliverToBoxAdapter(
                    child: Container(
                      padding: const EdgeInsets.fromLTRB(24, 32, 24, 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      AppColors.colorPrimary.withOpacity(0.3),
                                      AppColors.colorPrimary.withOpacity(0.1),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: AppColors.colorPrimary.withOpacity(0.2),
                                    width: 1,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.colorPrimary.withOpacity(0.1),
                                      blurRadius: 20,
                                      spreadRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.photo_library_rounded,
                                  color: AppColors.colorPrimary,
                                  size: 28,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    appLocal.albums,
                                    style: FontStyles.fontSemibold(
                                      fontSize: 32,
                                      color: Colors.white,
                                    ),
                                  ),
                                  Text(
                                    "${_albums?.length ?? 0} ${appLocal.photos}",
                                    style: FontStyles.fontMedium(
                                      fontSize: 14,
                                      color: Colors.white.withOpacity(0.6),
                                    ),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              GestureDetector(
                                onTap: () => Get.back(),
                                child: Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.1),
                                      width: 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 10,
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.close_rounded,
                                    color: Colors.white.withOpacity(0.8),
                                    size: 24,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Enhanced Grid
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    sliver: SliverGrid(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        mainAxisSpacing: 10.0,
                        crossAxisSpacing: 10.0,
                        childAspectRatio: 0.75, // Adjusted for better proportions
                      ),
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final album = _albums![index];
                          return AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            child: GestureDetector(
                              onTap: () async {
                                final result = await Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => AlbumPage(
                                      album,
                                      widget.isForMultiImage,
                                      widget.selectionLimit,
                                    ),
                                  ),
                                );
                                if (result != null) {
                                  Get.back(result: result);
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Colors.white.withOpacity(0.12),
                                      Colors.white.withOpacity(0.05),
                                    ],
                                  ),
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.15),
                                    width: 1,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 15,
                                      offset: const Offset(0, 5),
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Stack(
                                    fit: StackFit.expand,
                                    children: [
                                      Hero(
                                        tag: 'album_${album.id}',
                                        child: FadeInImage(
                                          fit: BoxFit.cover,
                                          placeholder: MemoryImage(kTransparentImage),
                                          image: AlbumThumbnailProvider(
                                            album: album,
                                            highQuality: true,
                                          ),
                                        ),
                                      ),
                                      // Enhanced Gradient Overlay
                                      Container(
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                            colors: [
                                              Colors.transparent,
                                              Colors.black.withOpacity(0.5),
                                              Colors.black.withOpacity(0.8),
                                            ],
                                            stops: const [0.4, 0.75, 1.0],
                                          ),
                                        ),
                                      ),
                                      // Enhanced Album Info
                                      Positioned(
                                        bottom: 0,
                                        left: 0,
                                        right: 0,
                                        child: Container(
                                          padding: const EdgeInsets.all(16),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                album.name ?? "Elbaab",
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: FontStyles.fontBold(
                                                  fontSize: 18,
                                                  color: Colors.white,
                                                ),
                                              ),
                                              const SizedBox(height: 8),
                                              Container(
                                                padding: const EdgeInsets.symmetric(
                                                  horizontal: 10,
                                                  vertical: 6,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: AppColors.colorPrimary.withOpacity(0.25),
                                                  borderRadius: BorderRadius.circular(12),
                                                  border: Border.all(
                                                    color: AppColors.colorPrimary.withOpacity(0.3),
                                                    width: 1,
                                                  ),
                                                ),
                                                child: Row(
                                                  mainAxisSize: MainAxisSize.min,
                                                  children: [
                                                    Icon(
                                                      Icons.photo_library_outlined,
                                                      size: 14,
                                                      color: AppColors.colorPrimary,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      "${album.count} ${appLocal.photos}",
                                                      style: FontStyles.fontMedium(
                                                        fontSize: 12,
                                                        color: AppColors.colorPrimary,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                        childCount: _albums?.length ?? 0,
                      ),
                    ),
                  ),
                  const SliverPadding(padding: EdgeInsets.only(bottom: 24)),
                ],
              ),
            ),
    );
  }
}

class AlbumPage extends StatefulWidget {
  final Album album;
  final bool isForMultiImage;
  final int? selectionLimit;
  const AlbumPage(this.album, this.isForMultiImage, this.selectionLimit,
      {super.key});

  @override
  State<StatefulWidget> createState() => AlbumPageState();
}

class AlbumPageState extends State<AlbumPage> {
  List<Medium>? _media;
  String tempDirPath = "";
  RxList<Medium> selectedFiles = <Medium>[].obs;
  @override
  void initState() {
    super.initState();
    initAsync();
    Future.delayed(Duration.zero, () async {
      final tempDir = await getDownloadsDirectory();
      tempDirPath = tempDir?.path ?? "";
    });
  }

  void initAsync() async {
    MediaPage mediaPage = await widget.album.listMedia();
    setState(() {
      _media = mediaPage.items;
    });
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // Enhanced Header
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(24, 32, 24, 24),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.colorPrimary.withOpacity(0.3),
                                AppColors.colorPrimary.withOpacity(0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: AppColors.colorPrimary.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            Icons.photo_library_rounded,
                            color: AppColors.colorPrimary,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.album.name ?? "Elbaab",
                              style: FontStyles.fontSemibold(
                                fontSize: 32,
                                color: Colors.white,
                              ),
                            ),
                            Text(
                              "${_media?.length ?? 0} ${appLocal.photos}",
                              style: FontStyles.fontMedium(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap: () => Get.back(),
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.1),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.close_rounded,
                              color: Colors.white.withOpacity(0.8),
                              size: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Enhanced Grid
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  sliver: SliverGrid(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      mainAxisSpacing: 6.0,
                      crossAxisSpacing: 6.0,
                      childAspectRatio: 0.75,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final medium = _media![index];
                        return GestureDetector(
                          onTap: () async {
                            File file = await medium.getFile();
                            var arrPath = ["JPEG", "JPG", "PNG", "HEIC", "HEIF"];
                            if (arrPath.contains(file.path.split('.').last.toUpperCase())) {
                              if (widget.isForMultiImage) {
                                if (selectedFiles.length < (widget.selectionLimit ?? 0)) {
                                  if (selectedFiles.contains(medium)) {
                                    selectedFiles.remove(medium);
                                  } else {
                                    selectedFiles.add(medium);
                                  }
                                } else {
                                  if (selectedFiles.contains(medium)) {
                                    selectedFiles.remove(medium);
                                  }
                                }
                              } else {
                                Get.back(result: file);
                              }
                            } else {
                              BottomSheets.showAlertMessageBottomSheet(
                                appLocal.invalidImageFormate,
                                appLocal.alert,
                                Get.context!,
                              );
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white.withOpacity(0.12),
                                  Colors.white.withOpacity(0.05),
                                ],
                              ),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.15),
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 15,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(6),
                              child: Stack(
                                fit: StackFit.expand,
                                children: [
                                  FadeInImage(
                                    fit: BoxFit.cover,
                                    placeholder: MemoryImage(kTransparentImage),
                                    image: ThumbnailProvider(
                                      mediumId: medium.id,
                                      mediumType: medium.mediumType,
                                      highQuality: true,
                                    ),
                                  ),
                                  Obx(() => selectedFiles.contains(medium)
                                      ? Container(
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              begin: Alignment.topCenter,
                                              end: Alignment.bottomCenter,
                                              colors: [
                                                AppColors.colorPrimary.withOpacity(0.3),
                                                AppColors.colorPrimary.withOpacity(0.5),
                                              ],
                                            ),
                                          ),
                                          child: Center(
                                            child: Container(
                                              height: 32,
                                              width: 32,
                                              decoration: BoxDecoration(
                                                color: AppColors.colorPrimary,
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: Colors.white,
                                                  width: 2,
                                                ),
                                              ),
                                              alignment: Alignment.center,
                                              child: Text(
                                                (selectedFiles.indexOf(medium) + 1).toString(),
                                                style: FontStyles.fontBold(
                                                  fontSize: 16,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ),
                                          ),
                                        )
                                      : Container()),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                      childCount: _media?.length ?? 0,
                    ),
                  ),
                ),
                const SliverPadding(padding: EdgeInsets.only(bottom: 80)),
              ],
            ),
            // Add Selected Images Preview Overlay
            Positioned(
              bottom: 100,
              right: 24,
              child: Obx(() => selectedFiles.isEmpty
                  ? const SizedBox.shrink()
                  : Container(
                      height: 80,
                      constraints: const BoxConstraints(maxWidth: 200),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.black.withOpacity(0.5),
                            Colors.black.withOpacity(0.3),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.4),
                            blurRadius: 15,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Stack(
                        alignment: Alignment.centerRight,
                        children: [
                          // Render selected images from right to left with overlap
                          for (var i = min(selectedFiles.length - 1, 3); i >= 0; i--)
                            Positioned(
                              right: i * 30.0,
                              child: Container(
                                height: 50,
                                width: 50,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.5),
                                      blurRadius: 10,
                                      offset: const Offset(0, 3),
                                    ),
                                    BoxShadow(
                                      color: AppColors.colorPrimary.withOpacity(0.2),
                                      blurRadius: 8,
                                      spreadRadius: 1,
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(6),
                                  child: Stack(
                                    children: [
                                      FadeInImage(
                                        fit: BoxFit.cover,
                                        placeholder: MemoryImage(kTransparentImage),
                                        image: ThumbnailProvider(
                                          mediumId: selectedFiles[i].id,
                                          mediumType: selectedFiles[i].mediumType,
                                          highQuality: true,
                                        ),
                                      ),
                                      // Add subtle gradient overlay on each image
                                      Container(
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                            colors: [
                                              Colors.white.withOpacity(0.1),
                                              Colors.transparent,
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          // Show count if more than 4 images are selected
                          if (selectedFiles.length > 4)
                            Positioned(
                              right: 90,
                              child: Container(
                                height: 50,
                                width: 50,
                                decoration: BoxDecoration(
                                  color: AppColors.colorPrimary,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.colorPrimary.withOpacity(0.4),
                                      blurRadius: 12,
                                      spreadRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Center(
                                  child: Text(
                                    '+${selectedFiles.length - 4}',
                                    style: FontStyles.fontBold(
                                      fontSize: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    )),
            ),
          ],
        ),
      ),
      bottomSheet: Obx(
        () => selectedFiles.isEmpty
            ? Container(height: 0)
            : Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.black,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                child: ElbaabBorderButtonWidget(
                  onPress: () async {
                    List<File> arrFiles = [];
                    PopupLoader.showLoadingDialog(context);
                    if (selectedFiles.isNotEmpty) {
                      for (var element in selectedFiles) {
                        File file = await element.getFile();
                        bool isValidSize = (await checkFileSize(file.path, 1));
                        if (isValidSize) {
                          arrFiles.add(file);
                        } else {
                          File compressImage = await compressAndSaveImage(
                              file.readAsBytesSync());
                          arrFiles.add(compressImage);
                        }
                      }
                      PopupLoader.hideLoadingDialog();
                      Get.back(result: arrFiles);
                    } else {
                      PopupLoader.hideLoadingDialog();
                      Get.back();
                    }
                  },
                  text: "${appLocal.confirmSlection} (${selectedFiles.length})",
                ),
              ),
      ),
    );
  }

  Future<bool> checkFileSize(String filepath, int decimals) async {
    var file = File(filepath);
    int bytes = await file.length();
    if (bytes <= 0) return false;
    var i = (log(bytes) / log(1024)).floor();
    if (i <= 2) {
      if (i == 2) {
        if (double.parse(((bytes / pow(1024, i)).toStringAsFixed(decimals))) <=
            2) {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    } else {
      return false;
    }
  }

  Future<File> compressAndSaveImage(Uint8List originalImageFile) async {
    final timeStamp = DateTime.now().millisecondsSinceEpoch;
    String outputPath = '$tempDirPath/$timeStamp.png';
    var result = await FlutterImageCompress.compressWithList(
      originalImageFile,
      minHeight: 1920,
      minWidth: 1080,
      quality: 65,
    );

    var compressedFile = File(outputPath);
    compressedFile.writeAsBytesSync(result);
    return compressedFile;
  }
}
