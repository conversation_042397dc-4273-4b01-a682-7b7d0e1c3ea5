import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:provider/provider.dart';

class PopupLoader {
  static showLoadingDialog(context, {bool closeSnackBar = true, bool canPop = false}) {
    if (closeSnackBar) {
      Get.closeAllSnackbars();
    }
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return PopScope(
            canPop: canPop,
            child: Center(
              child: SpinKitFadingCircle (
                color: AppColors.colorPrimary,
                size: 50.0,
              ),
            ),
          );
        });
  }

  static showProgressLoadingDialog(context) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          final info = Provider.of<UpdatedInfo>(context);
          return Scaffold(
            backgroundColor: Colors.transparent,
            body: SafeArea(
              child: Padding(
                padding: const EdgeInsets.only(top: 40.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: LinearPercentIndicator(
                    width: MediaQuery.of(context).size.width,
                    animation: true,
                    lineHeight: 20.0,
                    animationDuration: 2500,
                    percent: info.getUploadingProgress(),
                    center: Text(
                      "Uploading Files",
                      style: FontStyles.fontRegular(fontSize: 10),
                    ),
                    backgroundColor: AppColors.feildColorDark,
                    progressColor: AppColors.colorPrimary,
                  ),
                ),
              ),
            ),
          );
        });
  }

  static hideLoadingDialog() {
    Get.back();
  }
}
