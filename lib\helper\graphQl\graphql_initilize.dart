import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:graphql/client.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/other/popup_loader_component.dart';
import 'package:overolasuppliers/helper/strings/http_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';

import '../routes/route_names.dart';

final policies = Policies(
    fetch: FetchPolicy.networkOnly, cacheReread: CacheRereadPolicy.ignoreAll);
GraphQLClient _client = GraphQLClient(
  cache: GraphQLCache(),
  link: HttpLink(HTTPConstants.graphlQlBaseUrl),
  defaultPolicies: DefaultPolicies(
    watchQuery: policies,
    query: policies,
    mutate: policies,
  ),
);

class GraphQlInitilize {
  final ServerResponse serverResponse;
  GraphQlInitilize(this.serverResponse);
  runMutation(
      {required BuildContext context,
      required String query,
      String type = "",
      bool isLoader = true,
      required Map<String, dynamic> variables}) async {
    if (isLoader) {
      PopupLoader.showLoadingDialog(context);
    }
    final options = MutationOptions(
      document: gql(query),
      variables: variables,
    );
    if (userAuthToken.isNotEmpty) {
      _client = GraphQLClient(
        cache: GraphQLCache(),
        link: HttpLink(HTTPConstants.graphlQlBaseUrl, defaultHeaders: {
          'authorization': 'Bearer $userAuthToken',
        }),
        defaultPolicies: DefaultPolicies(
          watchQuery: policies,
          query: policies,
          mutate: policies,
        ),
      );
    }

    final QueryResult result = await _client.mutate(options);
    if (result.hasException) {
      if (isLoader) {
        PopupLoader.hideLoadingDialog();
      }
      log("$type Error $result");
      serverResponse.onError(result.data, type);
    } else {
      if (isLoader) {
        PopupLoader.hideLoadingDialog();
      }
      dynamic response = result.data;
      BaseModel model = BaseModel.fromJson(response["action"]);
      if (type == "removeFireBaseToken") {
        Get.offNamed(RouteNames.loginScreen,
            arguments: [BaseModel.fromJson(response).message]);
      } else if (model.status == status400 &&
          (model.message ?? "").contains("unauthorized")) {
        String json = jsonEncode(response);
        log("response $json");
        BottomSheets.userUnauthrized(Get.context);
      } else if (model.status == status400) {
        log("$type Error ${response["action"]}");
        serverResponse.onError(response["action"], type);
      } else if (model.status == 402) {
        runMutation(
          context: Get.context!,
          query: GraphQlQuries.removeFireBaseToken,
          type: "removeFireBaseToken",
          variables: {"firebaseDeviceToken": firebaseDeviceToken},
        );
      } else {
        String json = jsonEncode(response);
        log("response $json");
        serverResponse.onSucess(response["action"], type);
      }
    }
  }

  runQuery(
      {required BuildContext context,
      required String query,
      String type = "",
      bool isRequiredLoader = true,
      bool logVariables = false,
      Map<String, dynamic>? variables}) async {
    if (logVariables) {
      log("variables ${jsonEncode(variables)}");
    }
    if (isRequiredLoader) {
      PopupLoader.showLoadingDialog(context);
    }
    final QueryOptions options = QueryOptions(
      document: gql(query),
      variables: variables ?? {},
    );
    final GraphQLClient client = GraphQLClient(
      cache: GraphQLCache(),
      link: HttpLink(
        HTTPConstants.graphlQlBaseUrl,
        defaultHeaders: {
          'authorization': 'Bearer $userAuthToken',
        },
      ),
      defaultPolicies: DefaultPolicies(
          watchQuery: policies, query: policies, mutate: policies),
    );
    HttpLink httpLink = client.link as HttpLink;
    log("HEADERS ${httpLink.defaultHeaders}");
    final QueryResult result = await client.query(options);
    if (result.hasException) {
      if (isRequiredLoader) {
        PopupLoader.hideLoadingDialog();
      }
      log("$type Error ${result.exception}");
      serverResponse.onError(result.data, type);
    } else {
      if (isRequiredLoader) {
        PopupLoader.hideLoadingDialog();
      }
      dynamic response = result.data;
      log("response ${jsonEncode(response)}");
      if (response["action"] != Map<String, dynamic>) {
        serverResponse.onSucess(response["action"], type);
      } else {
        BaseModel model = BaseModel.fromJson(response["action"]);
        if (model.status == status400 ||
            (model.message ?? "").contains("unauthorized")) {
          String json = jsonEncode(response);
          log("response $json");
          BottomSheets.userUnauthrized(Get.context);
        } else if (model.status == status400) {
          log("$type Error ${response["action"]}");
          serverResponse.onError(response["action"], type);
        } else if (model.status == 402) {
          runMutation(
            context: Get.context!,
            query: GraphQlQuries.removeFireBaseToken,
            type: "removeFireBaseToken",
            variables: {"firebaseDeviceToken": firebaseDeviceToken},
          );
        } else {
          String json = jsonEncode(response);
          log("response $json");
          serverResponse.onSucess(response["action"], type);
        }
      }
    }
  }

  runQueryWithCache(
      {required BuildContext context,
      required String query,
      String type = "",
      bool isRequiredLoader = true,
      bool isHideSnackBar = true,
      Map<String, dynamic>? variables}) async {
    final GraphQLClient client = GraphQLClient(
      cache: GraphQLCache(),
      link: HttpLink(
        HTTPConstants.graphlQlBaseUrl,
        defaultHeaders: {
          'authorization': 'Bearer $userAuthToken',
        },
      ),
    );
    if (isRequiredLoader) {
      PopupLoader.showLoadingDialog(context, closeSnackBar: isHideSnackBar);
    }
    final QueryOptions options = QueryOptions(
      document: gql(query),
      variables: variables ?? {},
    );

    final QueryResult result = await client.query(options);
    if (result.hasException) {
      if (isRequiredLoader) {
        PopupLoader.hideLoadingDialog();
      }
      log("$type Error ${result.exception}");
      serverResponse.onError(result.data, type);
    } else {
      if (isRequiredLoader) {
        PopupLoader.hideLoadingDialog();
      }
      dynamic response = result.data;
      String json = jsonEncode(response);
      log("response $json");
      BaseModel model = BaseModel.fromJson(response["action"]);
      if (model.status == status400 &&
          (model.message ?? "").contains("unauthorized")) {
        BottomSheets.userUnauthrized(Get.context);
      } else if (model.status == status400) {
        log("$type Error ${response["action"]}");
      } else if (model.status == 402) {
        runMutation(
          context: Get.context!,
          query: GraphQlQuries.removeFireBaseToken,
          type: "removeFireBaseToken",
          variables: {"firebaseDeviceToken": firebaseDeviceToken},
        );
      } else {
        serverResponse.onSucess(response["action"], type);
      }
    }
  }

  static Future<dynamic> streamQuery(
      {required String query, Map<String, dynamic>? variables}) async {
    final QueryOptions options = QueryOptions(
      document: gql(query),
      variables: variables ?? {},
    );
    if (userAuthToken.isNotEmpty) {
      _client = GraphQLClient(
        cache: GraphQLCache(),
        link: HttpLink(HTTPConstants.graphlQlBaseUrl, defaultHeaders: {
          'authorization': 'Bearer $userAuthToken',
        }),
        defaultPolicies: DefaultPolicies(
          watchQuery: policies,
          query: policies,
          mutate: policies,
        ),
      );
    }
    HttpLink httpLink = _client.link as HttpLink;
    log("HEADERS ${httpLink.defaultHeaders}");
    final QueryResult result = await _client.query(options);

    if (result.hasException) {
      log("Error ${result.exception}");
    } else {
      dynamic response = result.data;
      String json = jsonEncode(response);
      log("stream $json");
      return response["action"];
    }
  }
}

abstract class ServerResponse {
  dynamic onSucess(dynamic response, String type);
  dynamic onError(dynamic error, String type);
}
