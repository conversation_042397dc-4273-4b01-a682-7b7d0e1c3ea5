import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/decimal_textinput_formatter.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/no_leading_space_formatter.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/no_space_formatter.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

// ignore: must_be_immutable
class ElbaaabInputTextField extends StatelessWidget {
  final ValueChanged<String> onChanged;
  final Function(String value)? onFieldSubmitted;
  final String hint;
  final String? label;
  final String? initialValue;
  final String? inputFormatter;
  final TextInputType? inputType;
  final Widget? prefix;
  final Widget? suffix;
  final double? height;
  final Function? suffixClick;
  final String? errorText;
  final EdgeInsets? margin;
  final List<TextInputFormatter>? formatter;
  final FormFieldValidator<String>? validator;
  final bool error;
  final bool readOnly;
  final bool autofocus;
  final bool isAllowSpace;
  final bool isClearable;
  final bool requiredCounter;
  final int? decimalNumerLimit;
  final TextDirection textDirection;
  final int? charaterlimit;
  TextInputAction inputAction;
  final Function? onTap;
  final AutovalidateMode autovalidateMode;
  final FocusNode? focusNode;
  final TextEditingController? editingController;
  final bool autoTextDirection;

  ElbaaabInputTextField({
    Key? key,
    required this.onChanged,
    required this.hint,
    this.label,
    this.autoTextDirection = false,
    this.autofocus = false,
    this.textDirection = TextDirection.ltr,
    this.inputFormatter,
    this.isClearable = false,
    this.readOnly = false,
    this.prefix,
    this.height,
    this.suffix,
    this.focusNode,
    this.initialValue,
    this.editingController,
    this.suffixClick,
    this.errorText,
    this.onTap,
    this.validator,
    this.margin,
    this.formatter,
    this.error = true,
    this.requiredCounter = false,
    this.charaterlimit,
    this.autovalidateMode = AutovalidateMode.onUserInteraction,
    this.inputAction = TextInputAction.done,
    this.inputType,
    this.decimalNumerLimit,
    this.isAllowSpace = true,
    this.onFieldSubmitted,
  }) : super(key: key);

  var controller = TextEditingController();
  Timer? _debounce;

  void _onTextChanged(String text) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (onFieldSubmitted != null) {
        onFieldSubmitted!(text);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (formatter != null) {
      formatter!.add(LengthLimitingTextInputFormatter(charaterlimit));
    }
    if(editingController != null){
      Future.delayed(Duration.zero,()=> editingController?.text = initialValue ?? "");
    }else{
    controller.text = initialValue ?? "";
    }

    if (isClearable) {}

    return Container(
      margin: margin,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(10)),
      ),
      child: TextFormField(
        controller:editingController ??  controller,
        key: Key(initialValue ?? ''),
        autofocus: autofocus,
        focusNode: focusNode,
        textDirection: autoTextDirection ? Get.locale?.languageCode == 'ar' ? TextDirection.rtl:TextDirection.ltr : textDirection,
        readOnly: onTap == null ? readOnly : true,
        onTap: () => onTap == null ? () {} : onTap!(),
        autovalidateMode: autovalidateMode,
        maxLength: requiredCounter ? charaterlimit : null,
        inputFormatters: inputFormatter != null
            ? [
                FilteringTextInputFormatter.allow(RegExp(inputFormatter!)),
                LengthLimitingTextInputFormatter(charaterlimit),
                if (decimalNumerLimit != null)
                  DecimalTextInputFormatter(decimalNumerLimit ?? 0),
                if (isAllowSpace) NoLeadingSpaceFormatter(),
                if (!isAllowSpace) NoSpaceFormatter(),
              ]
            : formatter ??
                [
                  if (isAllowSpace) NoLeadingSpaceFormatter(),
                  if (!isAllowSpace) NoSpaceFormatter(),
                  LengthLimitingTextInputFormatter(charaterlimit),
                ],
        // initialValue: isClearable ? null : initialValue ?? '',
        validator: validator,
        textInputAction: inputAction,
        maxLines: height != null ? 6 : 1,
        onChanged: (v) {
          _onTextChanged(v);
          onChanged(v);
        },
        style: FontStyles.fontRegular(),
        decoration: InputDecoration(
          fillColor: AppColors.feildColorDark,
          filled: true,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(
              width: 1,
              color: AppColors.feildBorderColor,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              width: 1,
              color: Colors.white,
            ),
          ),
          hoverColor: AppColors.colorBlue,
          errorText: errorText,
          errorStyle: error
              ? FontStyles.fontRegular(
                  color: AppColors.colorDanger, fontSize: 12)
              : FontStyles.fontRegular(fontSize: 0),
          errorMaxLines: 2,
          hintStyle: FontStyles.fontRegular(
              fontStyle: FontStyle.italic,
              fontSize: 12,
              color: Colors.white.withOpacity(0.5)),
          hintText: hint,
          labelText: label ?? "",
          labelStyle: FontStyles.fontRegular(),
          prefixIcon: prefix != null
              ? Container(
                  margin: const EdgeInsets.only(left: 10, right: 5),
                  child: prefix,
                )
              : null,
          suffixIcon: isClearable
              ? IconButton(
                  icon: const Icon(
                    Icons.cancel,
                    size: 20,
                    color: Color.fromRGBO(161, 161, 168, 1),
                  ),
                  onPressed: () => controller.clear(),
                )
              : suffix != null
                  ? suffixClick == null
                      ? suffix
                      : IconButton(
                          icon: SizedBox(
                            height: 24,
                            width: 24,
                            child: suffix,
                          ),
                          onPressed: () => suffixClick!())
                  : null,
          prefixIconConstraints: prefix != null
              ? const BoxConstraints(minWidth: 23, maxHeight: 20)
              : const BoxConstraints(minHeight: 0, minWidth: 0),
        ),
        keyboardType: inputType ?? TextInputType.text,
      ),
    );
  }
}
