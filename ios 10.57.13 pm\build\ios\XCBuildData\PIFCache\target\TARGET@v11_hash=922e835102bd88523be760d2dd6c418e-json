{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985adbb77df7a422911070b297b45d1e8e", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f2f2de5dcf833fd3a13da3884ccb96b5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988c46b96c05e8bc92e2738ba939c0f951", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c2a5257139f17ec02c7d140b840e1a07", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988c46b96c05e8bc92e2738ba939c0f951", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989394e66715d779da308c0a605f339b32", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb58a2170d34dfb3d7afbf26b21caa4f", "guid": "bfdfe7dc352907fc980b868725387e985139f8f913443488bfedfbd824cc8e4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce3c286e7b8edbc81fb95012b36b4a9f", "guid": "bfdfe7dc352907fc980b868725387e985e265735dae592444a27bbd89bceff1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822cb8c219ecf84af3d817bee2ce6c612", "guid": "bfdfe7dc352907fc980b868725387e9814ed82ee68448a966e4f41f54c37caf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f796406a61133161f36b2e195d4da53d", "guid": "bfdfe7dc352907fc980b868725387e9811cde7f4ffcbecc47be67fc712d0a902"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871585d8e63b3137350d8ed3eee28dbe2", "guid": "bfdfe7dc352907fc980b868725387e983f007bb20f1c7704c834ecba17af57ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875aba85f6070c71d0ec43734ae3d3721", "guid": "bfdfe7dc352907fc980b868725387e98487758b5bd035c6bb0dbbcd3c0fc7911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575fd97d02064f442a50b888da082e55", "guid": "bfdfe7dc352907fc980b868725387e9844a8587e38ee864c818893b8cbcb815c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df0a707a834be2f42faae398576103cb", "guid": "bfdfe7dc352907fc980b868725387e9886edc3ca102d7ef515ca4376bba5beb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d354cb29f0ff931ea68565eb44ff1075", "guid": "bfdfe7dc352907fc980b868725387e9816fbdc029906ebd530233a72091ab462", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a95544fcb00b065877f1e2870e2c23bd", "guid": "bfdfe7dc352907fc980b868725387e9850da69a782c2c8a00cc76dec39d18285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b9f767e536365757de68cf26625d411", "guid": "bfdfe7dc352907fc980b868725387e98158d876d56f2b85e99638a3393f35e2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce1d585e8b698d2f1fe7b3e266c88a38", "guid": "bfdfe7dc352907fc980b868725387e9873f6b65b6096f116125a8bbdacd3faf0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817651c403241a8054a0cc48d3d4aad1f", "guid": "bfdfe7dc352907fc980b868725387e9878d0403d6791bb23e339060e4ee9045d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988644c77e6235197a963a4975ffeee608", "guid": "bfdfe7dc352907fc980b868725387e981026f60af275bfb6fc52a2824b14d5b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808dfa393bc72438d5cc8a3a74595c03b", "guid": "bfdfe7dc352907fc980b868725387e98d13bb595c4afcfe8f5937386fedcbf5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f4dc924f574358c8654b521d5b7829c", "guid": "bfdfe7dc352907fc980b868725387e986f1b10ba6e38e4582bfaa7703941ed55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836f912f0d2061d4383fb2c4f71c2cad7", "guid": "bfdfe7dc352907fc980b868725387e985647d3b625c42866c5724ec952c0f0dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897f137a2175e2929d656896e3c0a2686", "guid": "bfdfe7dc352907fc980b868725387e986c6ac8178abf9d2ccfebf7dc71c7777c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5410e6e3db4e690b5635ccf55344256", "guid": "bfdfe7dc352907fc980b868725387e98534ae24a341b460225e0316296983b5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865bfeaf09a0f5327e8f7e198f16d72ca", "guid": "bfdfe7dc352907fc980b868725387e989eb845aa9e89ec84f7cac00dad46b448", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7220b6da6f29b09f8ca843a4acca344", "guid": "bfdfe7dc352907fc980b868725387e9881e3ee93f7ebabc7821c68005089fc39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98105197c687bc6b2550eb3a8217a32037", "guid": "bfdfe7dc352907fc980b868725387e98acaee3c6f74f14cdce2540e660ce6077", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c09092386119c034a54271d836d80370", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7e187863e64123b2d7544c9af5c7fcf", "guid": "bfdfe7dc352907fc980b868725387e98235d8f97b510297fc5d7259eb1a36801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807ae6c164482514026cb878113eea4c3", "guid": "bfdfe7dc352907fc980b868725387e9827d1075cee495eb9b7f4192ee6f3615e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840bc1dd5d96d44def1c712743ae45d3b", "guid": "bfdfe7dc352907fc980b868725387e98df838057c38c902136589f48d7f9dd5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d626886b92a1899a98e06432ae02384d", "guid": "bfdfe7dc352907fc980b868725387e98cd4777ef75aef5f6bc3dd712fdf4fbd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c58170b15cc6545096d099f0e42beb1d", "guid": "bfdfe7dc352907fc980b868725387e9892d1137e8f2a9c923a116530aa54d6d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abb91ea43a20a2e118990a96fa84a5dd", "guid": "bfdfe7dc352907fc980b868725387e98bbf809b8a3a0c210f275cc9b70af14cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865baae6f921407526e71a0317cd854dc", "guid": "bfdfe7dc352907fc980b868725387e989c5664914eac2d326139caa84c499fd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b52b6a6a5049812f7724f4e6e1901fd5", "guid": "bfdfe7dc352907fc980b868725387e9891a44c62994a8ce9f47b324bcb8d457e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821a3a7ebad7d6947ffe4224e51e6bfc3", "guid": "bfdfe7dc352907fc980b868725387e98441e64b4b25f49917a67a0b4885442d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ae74387c9911f65ae99e6e6e9a98dc8", "guid": "bfdfe7dc352907fc980b868725387e982c47de470fd17063a54076ed6d3e729b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d26f8997672272ee440d7b79f3911f66", "guid": "bfdfe7dc352907fc980b868725387e98246e164f85e6fe7bf57e466cfa47d3e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872fc3670bc3c4f292c51d3dafccf999a", "guid": "bfdfe7dc352907fc980b868725387e98d9396f5a02e429d427c5e6ae1a70106e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f394f90979230304371559d6c4fcff1b", "guid": "bfdfe7dc352907fc980b868725387e98cbadb8fbe85a930c1c4c33b12f2a2ba9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842f0f26150bc89673a0010a5caedb03f", "guid": "bfdfe7dc352907fc980b868725387e98fc9c7bc775b89cc951fbb825f9d69928"}], "guid": "bfdfe7dc352907fc980b868725387e98efb3217688b8a8dc6387bf1493e95015", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e98d040109eff413c21eb81203d108cf48a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df66cd2fbbc62e80c8a30c56780678c6", "guid": "bfdfe7dc352907fc980b868725387e984746e9975164e19cf2974e43d65e887e"}], "guid": "bfdfe7dc352907fc980b868725387e9897aea3f9ad8fca86ce5a1729f7eb4205", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c284b01f1cffd6d95d9e5b05998c62ab", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98e8adddfb79f8fca6d766b8c8b99e27eb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}