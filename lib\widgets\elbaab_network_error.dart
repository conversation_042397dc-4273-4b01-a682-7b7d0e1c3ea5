import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class ElbaabNetworkEroor extends StatelessWidget {
  final RxString strError;
  final EdgeInsets? padding;
  final Alignment alignment;
  const ElbaabNetworkEroor({Key? key, required this.strError, this.padding, this.alignment = Alignment.centerLeft})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          padding ?? const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      child: Obx(
        () => Text(
          strError.value,
          style: FontStyles.fontMedium(color: AppColors.colorDanger),
        ),
      ),
    );
  }
}
