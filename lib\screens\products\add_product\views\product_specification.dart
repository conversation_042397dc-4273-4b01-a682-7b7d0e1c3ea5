import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/screens/products/add_product/model/product_detail_model.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_carousel_feild_widget.dart';
import 'package:translator/translator.dart';

class ProductSpecification extends GetView<AddProductController>
    with InputValidationUtil {
  var titleController = TextEditingController();
  var titleArController = TextEditingController();
  var valueController = TextEditingController();
  var valueArController = TextEditingController();
  var focusNode = FocusNode();
  List<String> returnSpec = [];
  int indexWhere = -1;
  RxString errorTranslation = "".obs;
  late AppLocalizations appLocal;
  String specId = "";
  Timer? _typingTimer;

  List<ProductSpecificationModel> returnSpecList = [];

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  ProductSpecification({super.key});
  @override
  Widget build(BuildContext context) {
    appLocal = AppLocalizations.of(context)!;
    if (controller.product != null &&
        (controller.product?.productSpecs?.isNotEmpty ?? false)) {
      returnSpecList.clear();
      returnSpec.clear();
      for (var index = 0;
          index < (controller.product?.productSpecs?.length ?? 0);
          index++) {
        if ((controller.validationHistory?.returnValues
                ?.contains("Specification ${index + 1}") ??
            false)) {
          returnSpecList.add(ProductSpecificationModel(
            (controller.product?.productSpecs?[index].specsTitle ?? ""),
            (controller.product?.productSpecs?[index].specsValue ?? ""),
            (controller.product?.productSpecs?[index].specsId ?? ""),
            (controller.product?.ar?.productSpecs?[index].specsTitle ?? ""),
            (controller.product?.ar?.productSpecs?[index].specsValue ?? ""),
          ));
          returnSpec
              .add(controller.product?.productSpecs?[index].specsTitle ?? "");
        } else {
          returnSpec.add("");
        }
      }
    }
    if (returnSpec.every((element) => element.isEmpty)) {
      returnSpec.removeWhere((element) => element.isEmpty);
    }
    return Scaffold(
      body: SingleChildScrollView(
        reverse: true,
        child: Obx(
          () => Form(
            key: _formKey,
            child: Column(
              children: <Widget>[
                Padding(
                  padding: EdgeInsets.all(10.r),
                  child: Column(
                    children: [
                      if (returnSpec.isNotEmpty)
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 12.w),
                          decoration: BoxDecoration(
                            color: AppColors.colorDanger.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.info_outline, color: AppColors.colorDanger, size: 18),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Text(
                                  appLocal.returnSpecHighlighted,
                                  style: FontStyles.fontMedium(
                                    color: AppColors.colorDanger, 
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      // SizedBox(height: 16.h),
                      Container(
                        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                        decoration: BoxDecoration(
                          color: AppColors.headerColorDark.withOpacity(0.4),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.list_alt, size: 18, color: Colors.white70),
                            SizedBox(width: 8.w),
                            Text(
                              returnSpec.isEmpty ? appLocal.specification : appLocal.newSpecification,
                              style: FontStyles.fontBold(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 16.h),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: controller.productSpecificationList.length,
                        separatorBuilder: (context, index) => SizedBox(height: 4.h),
                        itemBuilder: (context, index) {
                          ProductSpecificationModel spec = controller.productSpecificationList[index];
                          if (index < returnSpec.length) {
                            indexWhere = returnSpec.indexOf(spec.title);
                          }
                          return Card(
                            elevation: 2,
                            margin: EdgeInsets.zero,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(
                                color: indexWhere == index ? AppColors.colorDanger : Colors.transparent,
                                width: 1,
                              ),
                            ),
                            color: index % 2 == 1 
                                ? AppColors.headerColorDark 
                                : AppColors.headerColorDark.withOpacity(0.4),
                            child: InkWell(
                              onTap: () {
                                if (titleController.text.isEmpty ||
                                    valueController.text.isEmpty) {
                                  controller.specEditIndex = index;
                                  titleController.text = spec.title;
                                  specId = spec.specsId;
                                  valueController.text = spec.value;
                                  titleArController.text = spec.titleAr;
                                  valueArController.text = spec.valueAr;
                                  controller.productSpecificationList
                                      .removeAt(index);
                                }
                              },
                              borderRadius: BorderRadius.circular(8),
                              child: Padding(
                                padding: EdgeInsets.all(8.r),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          _buildSpecificationRow(
                                            title: spec.title,
                                            value: spec.value,
                                          ),
                                          Divider(
                                            color: AppColors.colorPrimary_40,
                                            height: 12.h,
                                          ),
                                          _buildSpecificationRow(
                                            title: spec.titleAr,
                                            value: spec.valueAr,
                                            isRTL: appLocal.localeName == "ar",
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (!controller.isMatchProduct)
                                      IconButton(
                                        icon: const Icon(
                                          Icons.delete_outline,
                                          size: 20,
                                          color: Colors.redAccent,
                                        ),
                                        onPressed: () => Alerts.alertView(
                                          context: context,
                                          title: appLocal.alert,
                                          content: appLocal.aleartOnRemoveSpec,
                                          defaultActionText: appLocal.yes,
                                          action: () {
                                            Get.back();
                                            controller.productSpecificationList
                                                .removeAt(index);
                                            returnSpec.removeAt(index);
                                          },
                                          cancelActionText: appLocal.no,
                                          cancelAction: () => Get.back(),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                      Obx(
                        () => controller.isRequiredUpdate.value.isNotEmpty
                            ? Padding(
                                padding: const EdgeInsets.only(
                                    top: 16.0, bottom: 20),
                                child: Align(
                                  alignment: Alignment.center,
                                  child: Text(
                                    controller.isRequiredUpdate.value,
                                    textAlign: TextAlign.center,
                                    style: FontStyles.fontMedium(
                                        color: AppColors.colorDanger),
                                  ),
                                ),
                              )
                            : Container(),
                      ),
                    ],
                  ),
                ),
                if (controller.productSpecificationList.length < 25)
                  _buildSpecificationInputs(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSpecificationRow({
    required String title,
    required String value,
    bool isRTL = false,
  }) {
    return Row(
      textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            title,
            style: FontStyles.fontMedium(fontSize: 13),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: FontStyles.fontRegular(
              fontSize: 13,
              color: Colors.white70,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSpecificationInputs() {
    return Container(
      margin: EdgeInsets.only(top: 24.h),
      decoration: BoxDecoration(
        color: AppColors.backgroundColorDark,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.feildBorderColor.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          SizedBox(height: 10.h),
          ElbaabCarouselFeildWidget(
              aspectRatio: 7 / 4,
              children: [
                Column(
                  mainAxisAlignment:
                      MainAxisAlignment.spaceEvenly,
                  children: [
                    TextFormField(
                      validator: validateFieldEmpty,
                      focusNode: appLocal.localeName == "en"
                          ? focusNode
                          : null,
                      controller: titleController,
                      onChanged: (v) {
                        if (appLocal.localeName == 'en') {
                          if (v.isEmpty) {
                            titleArController.clear();
                            return;
                          }
                          if (_typingTimer != null) {
                            _typingTimer!.cancel();
                          }
                          titleArController.clear();
                          _typingTimer = Timer(
                              const Duration(seconds: 1), () {
                            GoogleTranslator()
                                .translate(v, to: 'ar')
                                .then((result) {
                              titleArController.text =
                                  result.text;
                              errorTranslation.value =
                                  appLocal.readySpecTranslation;
                              Future.delayed(
                                  Durations.extralong4,
                                  () => errorTranslation.value =
                                      "");
                            });
                          });
                        }
                      },
                      style: FontStyles.fontRegular(),
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(200)
                      ],
                      textDirection: TextDirection.ltr,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(
                            width: 1,
                            color: AppColors.feildBorderColor,
                          ),
                        ),
                        fillColor: AppColors.backgroundColorDark,
                        filled: true,
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: const BorderSide(
                            width: 1,
                            color: Colors.white,
                          ),
                        ),
                        hintStyle: FontStyles.fontRegular(
                          color: Colors.white.withOpacity(0.5),
                          fontStyle: FontStyle.italic,
                        ),
                        hintText: appLocal.specTitleFeildHint,
                        labelText: appLocal.specFeildLabelEnglish,
                        labelStyle: FontStyles.fontRegular(),
                      ),
                    ),
                    TextFormField(
                      validator: validateFieldEmpty,
                      controller: valueController,
                      onChanged: (v) {
                        if (appLocal.localeName == 'en') {
                          if (v.isEmpty) {
                            valueArController.clear();
                            return;
                          }
                          if (_typingTimer != null) {
                            _typingTimer!.cancel();
                          }
                          valueArController.clear();
                          _typingTimer = Timer(
                              const Duration(seconds: 1), () {
                            GoogleTranslator()
                                .translate(v, to: 'ar')
                                .then((result) {
                              valueArController.text =
                                  result.text;
                              errorTranslation.value =
                                  appLocal.readySpecTranslation;
                              Future.delayed(
                                  Durations.extralong4,
                                  () => errorTranslation.value =
                                      "");
                            });
                          });
                        }
                      },
                      style: FontStyles.fontRegular(),
                      textDirection: TextDirection.ltr,
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(200)
                      ],
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(
                            width: 1,
                            color: AppColors.feildBorderColor,
                          ),
                        ),
                        fillColor: AppColors.backgroundColorDark,
                        filled: true,
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: const BorderSide(
                            width: 1,
                            color: Colors.white,
                          ),
                        ),
                        hintStyle: FontStyles.fontRegular(
                          color: Colors.white.withOpacity(0.5),
                          fontStyle: FontStyle.italic,
                        ),
                        hintText: appLocal.specValueFeildHint,
                        labelText: appLocal.specValueEnglish,
                        labelStyle: FontStyles.fontRegular(),
                      ),
                      textInputAction: TextInputAction.next,
                      onFieldSubmitted: (v) => addOrUpdateData(),
                    ),
                  ],
                ),
                Column(
                  mainAxisAlignment:
                      MainAxisAlignment.spaceEvenly,
                  children: [
                    TextFormField(
                      validator: validateFieldEmpty,
                      focusNode: appLocal.localeName == "ar"
                          ? focusNode
                          : null,
                      controller: titleArController,
                      style: FontStyles.fontRegular(),
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(200)
                      ],
                      onChanged: (v) {
                        if (appLocal.localeName == 'ar') {
                          if (v.isEmpty) {
                            titleController.clear();
                            return;
                          }
                          if (_typingTimer != null) {
                            _typingTimer!.cancel();
                          }
                          titleController.clear();
                          _typingTimer = Timer(
                              const Duration(seconds: 1), () {
                            GoogleTranslator()
                                .translate(v, to: 'en')
                                .then((result) {
                              titleController.text = result.text;
                              errorTranslation.value =
                                  appLocal.readySpecTranslation;
                              Future.delayed(
                                  Durations.extralong4,
                                  () => errorTranslation.value =
                                      "");
                            });
                          });
                        }
                      },
                      textDirection: TextDirection.rtl,
                      decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(10),
                            borderSide: BorderSide(
                              width: 1,
                              color: AppColors.feildBorderColor,
                            ),
                          ),
                          fillColor:
                              AppColors.backgroundColorDark,
                          filled: true,
                          focusedBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(10),
                            borderSide: const BorderSide(
                              width: 1,
                              color: Colors.white,
                            ),
                          ),
                          hintStyle: FontStyles.fontRegular(
                            color: Colors.white.withOpacity(0.5),
                            fontStyle: FontStyle.italic,
                          ),
                          hintText: appLocal.specTitleFeildHint,
                          labelText:
                              appLocal.specFeildLabelArabic,
                          labelStyle: FontStyles.fontRegular()),
                    ),
                    TextFormField(
                      validator: validateFieldEmpty,
                      controller: valueArController,
                      onChanged: (v) {
                        if (appLocal.localeName == 'ar') {
                          if (v.isEmpty) {
                            valueController.clear();
                            return;
                          }
                          if (_typingTimer != null) {
                            _typingTimer!.cancel();
                          }
                          valueController.clear();
                          _typingTimer = Timer(
                              const Duration(seconds: 1), () {
                            GoogleTranslator()
                                .translate(v, to: 'en')
                                .then((result) {
                              valueController.text = result.text;
                              errorTranslation.value =
                                  appLocal.readySpecTranslation;
                              Future.delayed(
                                  Durations.extralong4,
                                  () => errorTranslation.value =
                                      "");
                            });
                          });
                        }
                      },
                      style: FontStyles.fontRegular(),
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(200)
                      ],
                      textDirection: TextDirection.rtl,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(
                            width: 1,
                            color: AppColors.feildBorderColor,
                          ),
                        ),
                        fillColor: AppColors.backgroundColorDark,
                        filled: true,
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: const BorderSide(
                            width: 1,
                            color: Colors.white,
                          ),
                        ),
                        hintStyle: FontStyles.fontRegular(
                          color: Colors.white.withOpacity(0.5),
                          fontStyle: FontStyle.italic,
                        ),
                        hintText: appLocal.specValueFeildHint,
                        labelText: appLocal.specValueArabic,
                        labelStyle: FontStyles.fontRegular(),
                      ),
                      textInputAction: TextInputAction.next,
                      onFieldSubmitted: (v) => addOrUpdateData(),
                    ),
                  ],
                )
              ]),
          Obx(
            () => errorTranslation.value.isNotEmpty
                ? Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    child: Text(
                      errorTranslation.value,
                      style: FontStyles.fontRegular(color: AppColors.colorDanger),
                    ),
                  )
                : const SizedBox.shrink(),
          ),
          ElbaabButtonWidget(
            margin: EdgeInsets.all(16.r),
            onPress: () => addOrUpdateData(),
            colors: AppColors.colorPrimary,
            text: appLocal.add,
            height: 46.h,
            borderRadius: 12,
          ),
        ],
      ),
    );
  }

  addOrUpdateData() {
    errorTranslation.value = "";
    if (appLocal.localeName == 'ar') {
      if (((valueArController.text.isNotEmpty &&
              valueArController.text.isNotEmpty)) &&
          (titleController.text.isEmpty || valueController.text.isEmpty)) {
        errorTranslation.value = appLocal.aleartFillSpecFeild;
        return;
      }
    } else {
      if (((titleController.text.isNotEmpty &&
              valueController.text.isNotEmpty)) &&
          (valueArController.text.isEmpty || valueArController.text.isEmpty)) {
        errorTranslation.value = appLocal.aleartFillSpecFeild;
        return;
      }
    }

    if (_formKey.currentState!.validate()) {
      String strTitle = titleController.text;
      String strTitleAr = titleArController.text;
      String strValue = valueController.text;
      String strValueAr = valueArController.text;

      titleController.clear();
      valueController.clear();
      titleArController.clear();
      valueArController.clear();
      focusNode.requestFocus();

      if (controller.specEditIndex != -1) {
        controller.productSpecificationList.insert(
            controller.specEditIndex,
            ProductSpecificationModel(
                strTitle, strValue, specId, strTitleAr, strValueAr));
        if (controller.isRequiredUpdate.value ==
            appLocal.aleartCompeleteEditSpec) {
          controller.isRequiredUpdate.value = "";
        }
        controller.specEditIndex = -1;
        specId = "";
      } else {
        controller.productSpecificationList.add(ProductSpecificationModel(
            strTitle, strValue, "", strTitleAr, strValueAr));
      }
    }
  }
}
