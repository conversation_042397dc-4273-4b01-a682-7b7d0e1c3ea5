class ValidationHistoryModel {
  final String? typename;
  final String? message;
  final int? status;
  final List<History>? registrationHistory;
  final List<History>? shopHistory;
  final ProductHistory? productHistory;

  ValidationHistoryModel({
    this.typename,
    this.message,
    this.status,
    this.registrationHistory,
    this.shopHistory,
    this.productHistory,
  });

  ValidationHistoryModel.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        message = json['message'] as String?,
        status = json['status'] as int?,
        registrationHistory = (json['registrationHistory'] as List?)
            ?.map((dynamic e) => History.fromJson(e as Map<String, dynamic>))
            .toList(),
        shopHistory = (json['shopHistory'] as List?)
            ?.map((dynamic e) => History.fromJson(e as Map<String, dynamic>))
            .toList(),
        productHistory =
            (json['productHistory'] as Map<String, dynamic>?) != null
                ? ProductHistory.fromJson(
                    json['productHistory'] as Map<String, dynamic>)
                : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'message': message,
        'status': status,
        'registrationHistory':
            registrationHistory?.map((e) => e.toJson()).toList(),
        'shopHistory': shopHistory?.map((e) => e.toJson()).toList(),
        'productHistory': productHistory?.toJson()
      };
}

class History {
  final String? typename;
  final String? id;
  final dynamic status;
  final String? requestType;
  final dynamic returnMessage;
  final List<dynamic>? returnValues;
  final String? createdAt;
  final String? updatedAt;

  History({
    this.typename,
    this.id,
    this.status,
    this.requestType,
    this.returnMessage,
    this.returnValues,
    this.createdAt,
    this.updatedAt,
  });

  History.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        id = json['_id'] as String?,
        status = json['status'],
        requestType = json['requestType'] as String?,
        returnMessage = json['returnMessage'],
        returnValues = json['returnValues'] as List?,
        createdAt = json['createdAt'] as String?,
        updatedAt = json['updatedAt'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        '_id': id,
        'status': status,
        'requestType': requestType,
        'returnMessage': returnMessage,
        'returnValues': returnValues,
        'createdAt': createdAt,
        'updatedAt': updatedAt
      };
}

class ProductHistory {
  final String? typename;
  final List<Product>? items;
  final int? page;
  final bool? hasNextPage;
  final int? totalItems;
  final int? totalPages;

  ProductHistory({
    this.typename,
    this.items,
    this.page,
    this.hasNextPage,
    this.totalItems,
    this.totalPages,
  });

  ProductHistory.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        items = (json['items'] as List?)
            ?.map((dynamic e) => Product.fromJson(e as Map<String, dynamic>))
            .toList(),
        page = json['page'] as int?,
        hasNextPage = json['hasNextPage'] as bool?,
        totalItems = json['totalItems'] as int?,
        totalPages = json['totalPages'] as int?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'items': items?.map((e) => e.toJson()).toList(),
        'page': page,
        'hasNextPage': hasNextPage,
        'totalItems': totalItems,
        'totalPages': totalPages
      };
}

class Product {
  final String? typename;
  final String? id;
  final String? productName;
  final List<History>? validationHistory;

  Product({
    this.typename,
    this.productName,
    this.id,
    this.validationHistory,
  });

  Product.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        productName = json['productName'] as String?,
        id = json['_id'] as String?,
        validationHistory = (json['validationHistory'] as List?)
            ?.map((dynamic e) => History.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        '_id': id,
        'productName': productName,
        'validationHistory': validationHistory?.map((e) => e.toJson()).toList()
      };
}
