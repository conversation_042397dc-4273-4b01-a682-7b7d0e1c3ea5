import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/components/product_free_delivery_listtile.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:sticky_headers/sticky_headers.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../../model/add_product/product_by_shop_model.dart';

class ProductFreeDelivery extends StatefulWidget {
  const ProductFreeDelivery({super.key});

  @override
  State<ProductFreeDelivery> createState() => _ProductFreeDeliveryState();
}

class _ProductFreeDeliveryState extends State<ProductFreeDelivery>
    implements ServerResponse {
  RxInt categoryIndex = 0.obs;

  late AutoScrollController controller;

  late GraphQlInitilize _request;

  RxList<String> arrCategories = <String>[].obs;

  RxString productCount = "0".obs, strError = "".obs;

  RxList<String> arrFreeDeliveryIds = <String>[].obs;

  List<String> arrRemoeItemIds = [];

  late ProductByShopModel productByShopModel;

  RxList<ShopProductList> arrProducts = <ShopProductList>[].obs;

  @override
  void initState() {
    super.initState();

    controller = AutoScrollController(
        viewportBoundaryGetter: () =>
            Rect.fromLTRB(0, 80, 0, MediaQuery.of(context).padding.bottom),
        axis: Axis.vertical);
    controller.addListener(_onScroll);

    _request = GraphQlInitilize(this);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _request.runQuery(
        context: context,
        query: GraphQlQuries.getProducts,
        variables: GraphQlVariables.getPaginated(),
      );
    });
  }

  _onScroll() {
    if (controller.position.pixels == controller.position.maxScrollExtent) {
      if (productByShopModel.shopProductPaggination?.hasNextPage ?? false) {
        _request.runQuery(
            context: context,
            query: GraphQlQuries.getProducts,
            variables: GraphQlVariables.getPaginated(
                page: (productByShopModel.shopProductPaggination?.page ?? 0) +
                    1));
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
    controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const ElbaabHeader(
        title: EnStrings.productforFreeDelivery,
        leadingBack: true,
      ),
      body: Obx(
        () => strError.value.isNotEmpty
            ? Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline_sharp,
                    color: AppColors.colorDanger,
                  ),
                  ElbaabNetworkEroor(strError: strError),
                ],
              )
            : SingleChildScrollView(
                controller: controller,
                child: StickyHeader(
                  header: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      ColoredBox(
                        color: AppColors.backgroundColorDark,
                        child: Container(
                          height: 60,
                          margin: const EdgeInsets.only(left: kLeftSpace),
                          child: Column(
                            children: [
                              SizedBox(
                                height: 59,
                                child: Obx(
                                  () => ListView.builder(
                                      itemCount: arrCategories.length,
                                      scrollDirection: Axis.horizontal,
                                      itemBuilder: (context, index) {
                                        return Padding(
                                          padding:
                                              const EdgeInsets.only(right: 10),
                                          child: InkWell(
                                            onTap: () {
                                              if (categoryIndex.value ==
                                                  index) {
                                                return;
                                              }
                                              categoryIndex.value = index;
                                              _scrollToIndex(index);
                                            },
                                            child: Obx(
                                              () => Stack(
                                                alignment: Alignment.center,
                                                children: [
                                                  Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 50),
                                                    child: Text(
                                                      arrCategories[index],
                                                      style: categoryIndex
                                                                  .value ==
                                                              index
                                                          ? FontStyles.fontSemibold(
                                                              color: AppColors
                                                                  .colorPrimary)
                                                          : FontStyles
                                                              .fontRegular(
                                                              color: Colors
                                                                  .white
                                                                  .withOpacity(
                                                                      0.6),
                                                            ),
                                                    ),
                                                  ),
                                                  Positioned(
                                                    left: 0,
                                                    right: 0,
                                                    bottom: 0,
                                                    child: Container(
                                                      height: 2,
                                                      color: categoryIndex
                                                                  .value ==
                                                              index
                                                          ? AppColors
                                                              .colorPrimary
                                                          : Colors.transparent,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      }),
                                ),
                              ),
                              Container(
                                height: 0.5,
                                color: AppColors.colotJumbo,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        color: AppColors.backgroundColorDark,
                        padding: const EdgeInsets.only(right: 16, top: 16),
                        alignment: Alignment.centerRight,
                        child: Obx(
                          () => Text(
                            productCount.value == "0"
                                ? "Loading..."
                                : "Result ($productCount)",
                            style: FontStyles.fontRegular(
                              fontSize: 12,
                              color: AppColors.colorPrimary,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  content: Obx(
                    () => Column(
                      children: [...getProducts(context)],
                    ),
                  ),
                ),
              ),
      ),
      bottomNavigationBar: Obx(
        () => arrFreeDeliveryIds.isNotEmpty || arrRemoeItemIds.isNotEmpty
            ? ElbaabButtonWidget(
                onPress: () {
                  if (arrRemoeItemIds.isNotEmpty) {
                    _request.runQuery(
                      context: context,
                      query: GraphQlQuries.removeProductFromFreeDelivery,
                      variables: GraphQlVariables.addProductToFreeDelivery(
                          productIds: arrRemoeItemIds),
                    );
                  }
                  _request.runMutation(
                    context: context,
                    type: "addFreeDelivery",
                    query: GraphQlQuries.addProductToFreeDelivery,
                    variables: GraphQlVariables.addProductToFreeDelivery(
                        productIds: arrFreeDeliveryIds),
                  );
                },
                height: 42,
                margin:
                    const EdgeInsets.symmetric(vertical: 24, horizontal: 30),
                colors: AppColors.colorPrimary,
                text: "Confirm ( ${arrFreeDeliveryIds.length} )",
              )
            : Container(
                height: 30,
              ),
      ),
    );
  }

  List<Widget> getProducts(BuildContext context) {
    List<Widget> sliders = [];
    for (int section = 0; section < arrCategories.length; section++) {
      sliders.add(
        Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: VisibilityDetector(
                key: Key("category ${section.toString()}"),
                onVisibilityChanged: (VisibilityInfo info) {
                  if (info.visibleFraction == 1) {
                    if (categoryIndex.value != section) {
                      categoryIndex.value = section;
                    }
                  }
                },
                child: AutoScrollTag(
                  key: ValueKey(section),
                  controller: controller,
                  index: section,
                  child: ListTile(
                    title: Transform.translate(
                      offset: const Offset(-16, 0),
                      child: Text(
                        arrCategories[section],
                        style: FontStyles.fontMedium(),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView.builder(
                  itemCount: arrProducts[section].products.length,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    return ProductFreeDeliveryListTile(
                      product: arrProducts[section].products[index],
                      onTap: (id) {
                        if (arrFreeDeliveryIds.contains(id)) {
                          arrRemoeItemIds.add(id);
                          arrFreeDeliveryIds.remove(id);
                        } else {
                          arrFreeDeliveryIds.add(id);
                        }
                      },
                    );
                  }),
            ),
          ],
        ),
      );
    }
    return sliders;
  }

  Future _scrollToIndex(int index) async {
    await controller.scrollToIndex(index,
        preferPosition: AutoScrollPosition.begin);
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == "addFreeDelivery") {
      BaseModel baseModel = BaseModel.fromJson(response);
      if (baseModel.status == statusOK) {
        Get.back();
      }
    } else {
      productByShopModel = ProductByShopModel.fromJson(response);
      if (productByShopModel.status == statusOK) {
        if (productCount.value == "0") {
          productCount.value =
              "${productByShopModel.shopProductPaggination?.totalItems ?? 0}";
        }

        for (ShopProducts obj
            in productByShopModel.shopProductPaggination?.items ?? []) {
          if (!arrCategories
              .contains(obj.productSubCategory?.subCategoryName)) {
            arrCategories.add(obj.productSubCategory?.subCategoryName ?? "");
            arrProducts.add(ShopProductList(
                obj.productSubCategory?.subCategoryName ?? "", [obj]));
          } else {
            List<ShopProducts> arr = arrProducts[arrCategories
                    .indexOf(obj.productSubCategory?.subCategoryName ?? "")]
                .products;
            arr.add(obj);
            arrProducts[arrCategories
                    .indexOf(obj.productSubCategory?.subCategoryName ?? "")] =
                ShopProductList(
                    obj.productSubCategory?.subCategoryName ?? "", arr);
          }
          if (obj.isFreeDeliveryItem ?? false) {
            arrFreeDeliveryIds.add(obj.id ?? "");
          }
        }
      } else {
        strError.value = productByShopModel.message ?? "";
        arrCategories.clear();
        arrProducts.clear();
      }
    }
  }
}
