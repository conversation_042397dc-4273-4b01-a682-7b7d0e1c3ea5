class BankInfoModel {
  final String? typename;
  final int? status;
  final String? message;
  final BankAccountDetails? bankAccountDetails;

  BankInfoModel({
    this.typename,
    this.status,
    this.message,
    this.bankAccountDetails,
  });

  BankInfoModel.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      status = json['status'] as int?,
      message = json['message'] as String?,
      bankAccountDetails = (json['bankAccountDetails'] as Map<String,dynamic>?) != null ? BankAccountDetails.fromJson(json['bankAccountDetails'] as Map<String,dynamic>) : null;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'status' : status,
    'message' : message,
    'bankAccountDetails' : bankAccountDetails?.toJson()
  };
}

class BankAccountDetails {
  final String? typename;
  final String? iban;
  final String? city;
  final String? accountNumber;
  final String? accountHolderName;
  final String? bankName;

  BankAccountDetails({
    this.typename,
    this.iban,
    this.city,
    this.accountNumber,
    this.accountHolderName,
    this.bankName,
  });

  BankAccountDetails.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      iban = json['iban'] as String?,
      city = json['city'] as String?,
      accountNumber = json['accountNumber'] as String?,
      accountHolderName = json['accountHolderName'] as String?,
      bankName = json['bankName'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'iban' : iban,
    'city' : city,
    'accountNumber' : accountNumber,
    'accountHolderName' : accountHolderName,
    'bankName' : bankName
  };
}