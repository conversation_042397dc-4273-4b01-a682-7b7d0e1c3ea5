import 'dart:async';
import 'dart:io';

import 'package:crop_your_image/crop_your_image.dart' show <PERSON><PERSON>, CropController, CropStatus;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/other/popup_loader_component.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:path_provider/path_provider.dart';
import 'package:preload_page_view/preload_page_view.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

class MultiImageCropService extends StatefulWidget {
  const MultiImageCropService({
    Key? key,
    required this.files,
    required this.aspectRatio,
    required this.requiredCirsleCroper,
    required this.isProductImages,
  }) : super(key: key);

  final List<File> files;
  final double aspectRatio;
  final bool requiredCirsleCroper, isProductImages;

  @override
  State<MultiImageCropService> createState() => _MultiImageCropServiceState();
}

class _MultiImageCropServiceState extends State<MultiImageCropService>
    with SingleTickerProviderStateMixin {
  final PreloadPageController _pageController = PreloadPageController();
  AutoScrollController? _autoScrollController;
  final scrollDirection = Axis.horizontal;
  List<CropController> cropController = [];
  List<File> cropFiles = [];
  RxBool isFirstTime = true.obs;

  int currentPage = 0;

  List<File> get arrfiles => widget.files;
  String tempDirPath = "";
  List<String> formates = ["JPEG", "JPG", "PNG", "HEIC", "HEIF"];
  final RxDouble _progress = 0.0.obs;
  final RxBool _isProcessing = false.obs;

  Future<bool> checkFileSize(String filepath, int decimals) async {
    try {
      var file = File(filepath);
      int bytes = await file.length();
      if (bytes <= 0) return false;
      
      double sizeMB = bytes / (1024 * 1024);
      return sizeMB <= 5.0;
    } catch (e) {
      return false;
    }
  }

  Future<File> compressAndSaveImage(Uint8List originalImageFile) async {
    try {
      final timeStamp = DateTime.now().millisecondsSinceEpoch;
      String outputPath = '$tempDirPath/$timeStamp.png';
      
      var result = await FlutterImageCompress.compressWithList(
        originalImageFile,
        minHeight: 1920,
        minWidth: 1080,
        quality: 85,
        rotate: 0,
        format: CompressFormat.png,
        keepExif: false,
      );

      if (result.length > 2 * 1024 * 1024) {
        result = await FlutterImageCompress.compressWithList(
          result,
          minHeight: 1280,
          minWidth: 720,
          quality: 70,
        );
      }

      var compressedFile = File(outputPath);
      await compressedFile.writeAsBytes(result);
      return compressedFile;
    } catch (e) {
      throw Exception('Image compression failed: $e');
    }
  }

  Future<List<File>> processBatchImages(List<File> images) async {
    final List<File> processedImages = [];
    const int batchSize = 5;
    
    for (var i = 0; i < images.length; i += batchSize) {
      final end = (i + batchSize < images.length) ? i + batchSize : images.length;
      final batch = images.sublist(i, end);
      
      await Future.wait(
        batch.map((file) async {
          try {
            final processed = await compressAndSaveImage(
              await file.readAsBytes()
            );
            processedImages.add(processed);
          } catch (e) {
            debugPrint('Error processing image: $e');
          }
        })
      );
      
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    return processedImages;
  }

  @override
  void initState() {
    super.initState();
    _autoScrollController = AutoScrollController(
        viewportBoundaryGetter: () =>
            Rect.fromLTRB(0, 0, MediaQuery.of(context).padding.bottom, 0),
        axis: scrollDirection);
    cropController =
        List.generate(arrfiles.length, (index) => CropController());

    Future.delayed(Duration.zero, () async {
      final tempDir = await getTemporaryDirectory();
      tempDirPath = tempDir.path;
    });
  }

  @override
  void dispose() {
    _clearTempFiles();
    // cropController.forEach((controller) => controller.dispose());
    _pageController.dispose();
    _autoScrollController!.dispose();
    super.dispose();
  }

  Future<void> _clearTempFiles() async {
    try {
      final dir = Directory(tempDirPath);
      if (await dir.exists()) {
        await for (var entity in dir.list()) {
          if (entity is File) {
            await entity.delete();
          }
        }
      }
    } catch (e) {
      debugPrint('Error clearing temp files: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: Text(
          appLocal.imageCropper,
          style: FontStyles.fontMedium(
            fontSize: 18,
            color: Colors.white,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Get.back(),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            croppingView(appLocal),
            if (widget.files.length > 1)
              modernThumbnailsControl(),
            Obx(() => (!isFirstTime.value)
                ? modernActionBar(appLocal)
                : const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }

  Widget croppingView(AppLocalizations appLocal) {
    return Expanded(
      child: Stack(
        children: [
          PreloadPageView.builder(
            controller: _pageController,
            itemCount: arrfiles.length,
            preloadPagesCount: arrfiles.length,
            physics: const NeverScrollableScrollPhysics(),
            onPageChanged: (page) async {
              await _autoScrollController!.scrollToIndex(page,
                  preferPosition: AutoScrollPosition.middle);
              setState(() {
                currentPage = page;
              });
            },
            itemBuilder: (context, index) {
              return Stack(
                children: [
                  // Main Crop Widget
                  Crop(
                    image: File(arrfiles[index].path).readAsBytesSync(),
                    controller: cropController[index],
                    onCropped: _handleCropped,
                    onStatusChanged: (status) {
                      if (status == CropStatus.ready) {
                        Future.delayed(const Duration(seconds: 1),
                            () => isFirstTime.value = false);
                      }
                    },
                    initialAreaBuilder: (!widget.isProductImages)
                        ? null
                        : (rect) => Rect.fromLTRB(rect.left + 10, rect.top + 18,
                            rect.right - 10, rect.bottom - 18),
                    withCircleUi: widget.requiredCirsleCroper,
                    aspectRatio: widget.aspectRatio,
                    baseColor: Colors.black,
                    maskColor: Colors.white.withAlpha(100),
                    radius: 8, // Increased corner radius
                    cornerDotBuilder: (size, edgeAlignment) =>  DecoratedBox(
                      decoration: BoxDecoration(
                        color: AppColors.colorPrimary,
                        shape: BoxShape.circle,
                        boxShadow: const [
                          BoxShadow(
                            color: Colors.black26,
                            blurRadius: 8,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                    ),
                    interactive: true,
                    fixArea: true,
                  ),
                  
                  // Overlay Instructions
                  Positioned(
                    top: 16,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.black87,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.touch_app,
                                  color: Colors.white70,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Drag to move',
                                  style: FontStyles.fontRegular(
                                    fontSize: 14,
                                    color: Colors.white70,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 12),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.black87,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.pinch,
                                  color: Colors.white70,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Pinch to zoom',
                                  style: FontStyles.fontRegular(
                                    fontSize: 14,
                                    color: Colors.white70,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Image Counter
                  if (arrfiles.length > 1)
                    Positioned(
                      top: 16,
                      right: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black87,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '${index + 1}/${arrfiles.length}',
                          style: FontStyles.fontMedium(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                ],
              );
            },
          ),

          // Loading Overlay
          Obx(() => _isProcessing.value
              ? Positioned.fill(
                  child: Container(
                    color: Colors.black87,
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 100,
                            height: 100,
                            child: CircularProgressIndicator(
                              value: _progress.value,
                              strokeWidth: 8,
                              backgroundColor: Colors.white24,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.colorPrimary,
                              ),
                            ),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Processing ${(_progress.value * 100).toInt()}%',
                            style: FontStyles.fontMedium(
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              : const SizedBox.shrink()),

          // First-time Tutorial Overlay
          Obx(
            () => isFirstTime.value
                ? Positioned.fill(
                    child: Container(
                      color: Colors.black.withOpacity(0.85),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Lottie.asset(
                              'assets/images/pinch_animation.json',
                              width: 200,
                              height: 200,
                            ),
                            const SizedBox(height: 24),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(30),
                              ),
                              child: Text(
                                appLocal.pinchZoomInout,
                                style: FontStyles.fontMedium(
                                  fontSize: 18,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            TextButton(
                              onPressed: () => isFirstTime.value = false,
                              child: Text(
                                'Got it!',
                                style: FontStyles.fontMedium(
                                  fontSize: 16,
                                  color: AppColors.colorPrimary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  void _handleCropped(Uint8List image) async {
    final directory = await getTemporaryDirectory();
    final timeStamp = DateTime.now().millisecondsSinceEpoch;
    File file = File('${directory.path}/$timeStamp.png');
    await file.writeAsBytes(image);
    
    bool isValidSize = await checkFileSize(file.path, 1);
    if (isValidSize) {
      cropFiles.add(file);
    } else {
      File compressImage = await compressAndSaveImage(image);
      bool isCompressedValidSize = await checkFileSize(compressImage.path, 1);
      
      if (widget.files.length == 1 && !isCompressedValidSize) {
        Alerts.alertView(
          context: Get.context!,
          content: "File should be less than 5 MB",
          action: () => Get.back(),
          defaultActionText: EnStrings.ok,
        );
      } else if (isCompressedValidSize) {
        cropFiles.add(compressImage);
      }
    }

    if (currentPage == arrfiles.length - 1) {
      PopupLoader.hideLoadingDialog();
      Get.back(result: cropFiles);
    } else {
      currentPage++;
      await _pageController.animateToPage(
        currentPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      Future.delayed(
        const Duration(milliseconds: 600),
        () => cropController[currentPage].crop(),
      );
    }
  }

  Widget modernThumbnailsControl() {
    return Container(
      height: 100,
      color: Colors.black,
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: ListView.builder(
        itemCount: arrfiles.length,
        scrollDirection: scrollDirection,
        controller: _autoScrollController,
        physics: const BouncingScrollPhysics(),
        itemBuilder: (context, index) {
          return AutoScrollTag(
            key: ValueKey(index),
            controller: _autoScrollController!,
            index: index,
            child: GestureDetector(
              onTap: () {
                _pageController.animateToPage(
                  index,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
              child: Container(
                width: 80,
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    width: 2,
                    color: currentPage == index
                        ? AppColors.colorPrimary
                        : Colors.transparent,
                  ),
                  boxShadow: currentPage == index
                      ? [
                          BoxShadow(
                            color: AppColors.colorPrimary.withOpacity(0.3),
                            blurRadius: 8,
                            spreadRadius: 2,
                          )
                        ]
                      : null,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: Image.file(
                    File(arrfiles[index].path),
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[800],
                        child: const Icon(
                          Icons.image_not_supported,
                          color: Colors.white54,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget modernActionBar(AppLocalizations appLocal) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.black,
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                arrfiles.removeAt(currentPage);
                cropController.removeAt(currentPage);
                if (arrfiles.isNotEmpty) {
                  _pageController.animateToPage(0,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                  currentPage = 0;
                }
                if (arrfiles.isEmpty) Get.back();
              },
              icon: const Icon(Icons.delete_outline, color: Colors.white),
              label: Text(appLocal.remove, style: FontStyles.fontMedium()),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.colorDanger,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () async {
                if (currentPage != (cropController.length - 1)) {
                  await _pageController.animateToPage(
                    currentPage + 1,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                } else {
                  await _pageController.animateToPage(
                    0,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                  Future.delayed(
                    Durations.medium3,
                    () => cropController[0].crop(),
                  );
                  PopupLoader.showLoadingDialog(Get.context, canPop: true);
                }
              },
              icon: Icon(
                currentPage != (cropController.length - 1)
                    ? Icons.arrow_forward
                    : Icons.check,
                color: Colors.white,
              ),
              label: Text(
                currentPage != (cropController.length - 1)
                    ? appLocal.next
                    : appLocal.done,
                style: FontStyles.fontMedium(),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.colorPrimary,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> processWithTimeout(Future<void> Function() process) async {
    try {
      await process().timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw TimeoutException('Operation timed out');
        },
      );
    } on TimeoutException catch (_) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text("operationTimeout"),
            backgroundColor: AppColors.colorDanger,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text("errorProcessingImages"),
            backgroundColor: AppColors.colorDanger,
          ),
        );
      }
    }
  }
}
