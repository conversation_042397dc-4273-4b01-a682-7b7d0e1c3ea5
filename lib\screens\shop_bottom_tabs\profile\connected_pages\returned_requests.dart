import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/other/shimmer.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/model/login_model.dart';
import 'package:overolasuppliers/model/shop/ViewShopModel.dart';
import 'package:overolasuppliers/model/validation_history_model.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ReturendRequests extends StatefulWidget {
  const ReturendRequests({super.key});

  @override
  State<ReturendRequests> createState() => _ReturendRequestsState();
}

class _ReturendRequestsState extends State<ReturendRequests>
    implements ServerResponse {
  late GraphQlInitilize _request;

  RxList<ReturendRequest> arrRequests = RxList<ReturendRequest>();

  List<History> arrHistory = [];

  List<Product> arrProductHistory = [];

  RxInt selectedTab = 0.obs;

  late ProductHistory productHistory;

  final ScrollController scrollController = ScrollController();
  late AppLocalizations appLocal;

  RxBool isLoading = true.obs;

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      scrollController.addListener(_onScroll);
      getHistory(context);
    });
  }

  @override
  void dispose() {
    super.dispose();
    scrollController.dispose();
  }

  void _onScroll() {
    if (scrollController.position.pixels ==
        (scrollController.position.maxScrollExtent)) {
      if (selectedTab.value == 2 && (productHistory.hasNextPage ?? false)) {
        _request.runQuery(
            context: context,
            query: GraphQlQuries.getProductValidationHistory,
            isRequiredLoader: false,
            variables: GraphQlVariables.getPaginated(
                page: ((productHistory.page ?? 0) + 1), itemsNumber: 10),
            type: "Product");
      }
    }
  }

  getHistory(BuildContext context) {
    Future.delayed(Duration.zero, () {
      isLoading.value = true;
      arrRequests.clear();
      arrRequests
          .add(ReturendRequest(requestType: appLocal.registration, history: []));
      arrRequests.add(ReturendRequest(requestType: appLocal.shop, history: []));
      arrRequests.add(ReturendRequest(
          requestType: appLocal.product, history: [], errorMessage: ""));
      if (Get.arguments != null) {
        if (Get.arguments == "shopRequest") {
          selectedTab.value = 1;
          _request.runQuery(
              context: context,
              isRequiredLoader: false,
              query: GraphQlQuries.getShopValidationHistory,
              type: "Shop");
        } else if (Get.arguments == "productRequest") {
          selectedTab.value = 2;
          _request.runQuery(
              context: context,
              isRequiredLoader: false,
              query: GraphQlQuries.getProductValidationHistory,
              variables:
                  GraphQlVariables.getPaginated(page: 1, itemsNumber: 10),
              type: "Product");
        }
      } else {
        _request.runQuery(
            context: context,
            isRequiredLoader: false,
            query: GraphQlQuries.getRegistrationValidationHistory,
            type: "Registration");
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    appLocal = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: ElbaabHeader(title: appLocal.returnRequest, leadingBack: true),
      body: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.headerColorDark,
              boxShadow: const [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 5,
                  spreadRadius: 1,
                ),
              ],
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            child: Obx(
              () => Row(
                children: List.generate(
                  arrRequests.length,
                  (index) => Expanded(
                    child: GestureDetector(
                      onTap: () => _handleTabChange(index),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          color: selectedTab.value == index
                              ? AppColors.colorPrimary
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: selectedTab.value == index
                                ? AppColors.colorPrimary
                                : Colors.white24,
                          ),
                        ),
                        child: Text(
                          arrRequests[index].requestType,
                          textAlign: TextAlign.center,
                          style: FontStyles.fontMedium(
                            fontSize: 13,
                            color: selectedTab.value == index
                                ? Colors.white
                                : Colors.white60,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Obx(
            () => Expanded(
              child: isLoading.value 
                  ? _buildSkeletonView()
                  : arrRequests.isEmpty || arrRequests[selectedTab.value].history.isEmpty
                      ? _buildEmptyState()
                      : _buildRequestsList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.history, size: 64, color: Colors.white24),
          const SizedBox(height: 16),
          Text(
            arrRequests[selectedTab.value].errorMessage,
            textAlign: TextAlign.center,
            style: FontStyles.fontMedium(color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestsList() {
    return ListView.builder(
      itemCount: arrRequests[selectedTab.value].history.length,
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        History history = arrRequests[selectedTab.value].history[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          color: AppColors.headerColorDark,
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: InkWell(
            onTap: () => _handleItemTap(history, index),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatusHeader(history),
                  if (selectedTab.value == 2) ...[
                    const Divider(color: Colors.white24, height: 24),
                    _buildProductInfo(index),
                  ],
                  const Divider(color: Colors.white24, height: 24),
                  _buildRequestDetails(history),
                  if (history.status == "Returned" && index == 0 && selectedTab < 2)
                    _buildUpdateButton(history, index),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusHeader(History history) {
    return Row(
      children: [
        Icon(
          history.status == "Returned" 
              ? Icons.error_outline
              : history.status == "Accepted"
                  ? Icons.check_circle_outline
                  : Icons.pending_outlined,
          color: _getStatusColor(history.status ?? ""),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            _getStatusText(history.status ?? ""),
            style: FontStyles.fontSemibold(
              fontSize: 14,
              color: _getStatusColor(history.status ?? ""),
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    if (status == "Returned") {
      return AppColors.colorDanger;
    } else if (status == "Accepted") {
      return AppColors.colorSecondary;
    } else {
      return AppColors.colorSecondaryYellow;
    }
  }

  String _getStatusText(String status) {
    if (status == "Returned") {
      return appLocal.returnedByAdmin;
    } else if (status == "Accepted") {
      return appLocal.acceptedByAdmin;
    } else {
      return appLocal.submittedByAdmin;
    }
  }

  Widget _buildProductInfo(int index) {
    if (selectedTab.value == 2) {
      return Row(
        children: [
          Expanded(
            flex: 1,
            child: Text(
              appLocal.productname,
              style: FontStyles.fontMedium(fontSize: 12),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              arrProductHistory[index].productName ?? "",
              maxLines: 2,
              style: FontStyles.fontMedium(height: 2),
            ),
          )
        ],
      );
    }
    return const SizedBox();
  }

  Widget _buildRequestDetails(History history) {
    return Column(
      children: [
        infoLisTile(
            history.status == "Returned"
                ? appLocal.date
                : appLocal.updatedDate,
            GlobalMethods.convertTimeFormate(
                history.updatedAt ?? "",
                format: "EEE dd,MMM yyyy hh:mm a")),
        if (history.status == "Returned")
          infoLisTile(appLocal.adminNotes, history.returnMessage ?? ""),
        if (history.status == "Returned")
          infoLisTile(appLocal.returnedFeilds,
              _getRejectionType((history.returnValues ?? []).cast<String>())),
      ],
    );
  }

  Widget _buildUpdateButton(History history, int index) {
    if (history.status == "Returned" && index == 0 && selectedTab < 2) {
      return Align(
        alignment: Alignment.centerRight,
        child: TextButton(
          onPressed: () {
            if (history.requestType == "Registration") {
              _request.runQuery(
                context: context,
                isRequiredLoader: false,
                query: GraphQlQuries.getBusinessInfo,
                type: "businessInfo",
              );
            } else if (history.requestType == "Shop") {
              _request.runQuery(
                  context: context,
                  isRequiredLoader: false,
                  query: GraphQlQuries.viewShop,
                  type: "SHOP");
            }
          },
          child: Text(
            "Update >",
            style: FontStyles.fontMedium(
              decoration: TextDecoration.underline,
              color: AppColors.colorPrimary,
            ),
          ),
        ),
      );
    }
    return const SizedBox();
  }

  String _getRejectionType(List<String> returnValues) {
    if (returnValues.isEmpty) return "";
    return returnValues.join("\n");
  }

  void _handleTabChange(int index) {
    selectedTab.value = index;
    if (arrRequests[index].history.isEmpty) {
      isLoading.value = true;
      if (index == 0) {
        _request.runQuery(
            context: context,
            query: GraphQlQuries.getRegistrationValidationHistory,
            isRequiredLoader: false,
            type: "Registration");
      } else if (index == 1) {
        _request.runQuery(
            context: context,
            query: GraphQlQuries.getShopValidationHistory,
            isRequiredLoader: false,
            type: "Shop");
      } else {
        _request.runQuery(
            context: context,
            query: GraphQlQuries.getProductValidationHistory,
            isRequiredLoader: false,
            variables: GraphQlVariables.getPaginated(page: 1, itemsNumber: 10),
            type: "Product");
      }
    }
  }

  void _handleItemTap(History history, int index) {
    if (selectedTab.value == 2) {
      for (Product element in arrProductHistory) {
        if ((history.updatedAt ?? 0) ==
            ((element.validationHistory ?? [])
                    .first
                    .updatedAt ??
                0)) {
          Get.toNamed(
              RouteNames.validationHistoryScreen,
              arguments: [
                element.validationHistory ?? [],
                element.id ?? ""
              ]);
          return;
        }
      }
    }
  }

  infoLisTile(String title, String info) {
    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: Text(title, style: FontStyles.fontMedium(fontSize: 12)),
          ),
          Expanded(
            flex: 1,
            child: Text(
              info,
              style: FontStyles.fontRegular(
                fontSize: 12,
                color: Colors.white.withOpacity(0.5),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  onError(error, String type) {
    isLoading.value = false;
    BaseModel model = BaseModel.fromJson(error);
    if (type == "Registration") {
      arrRequests[0] = ReturendRequest(
        requestType: arrRequests.first.requestType,
        history: [],
        errorMessage: model.message ?? "",
      );
    } else if (type == "Shop") {
      arrRequests[1] = ReturendRequest(
        requestType: arrRequests[1].requestType,
        history: [],
        errorMessage: model.message ?? "",
      );
    } else if (type == "Product") {
      arrRequests.last = ReturendRequest(
        requestType: arrRequests.last.requestType,
        history: [],
        errorMessage: model.message ?? "",
      );
    }
  }

  @override
  onSucess(response, String type) async {
    isLoading.value = false;
    if (type == "Registration") {
      ValidationHistoryModel registrationHistory =
          ValidationHistoryModel.fromJson(response);
      if (registrationHistory.status == statusOK) {
        arrRequests.first = ReturendRequest(
            requestType: arrRequests.first.requestType,
            history: registrationHistory.registrationHistory ?? []);
      } else {
        arrRequests.first = ReturendRequest(
          requestType: arrRequests.first.requestType,
          history: [],
          errorMessage: registrationHistory.message ?? "",
        );
      }
    } else if (type == "Shop") {
      ValidationHistoryModel shopHistory =
          ValidationHistoryModel.fromJson(response);
      if (shopHistory.status == statusOK) {
        arrRequests[1] = ReturendRequest(
            requestType: arrRequests[1].requestType,
            history: shopHistory.shopHistory ?? []);
      } else {
        arrRequests[1] = ReturendRequest(
          requestType: arrRequests[1].requestType,
          history: [],
          errorMessage: shopHistory.message ?? "",
        );
      }
    } else if (type == "businessInfo") {
      LoginModel model = LoginModel.fromJson(response);
      if (model.status == statusOK) {
        Get.toNamed(RouteNames.addBusinessInfoScreen,
            arguments: [supplierID, model.user?.supplier, true]);
      }
    } else if (type == "SHOP") {
      ViewShopModel model = ViewShopModel.fromJson(response);
      if (model.status == statusOK) {
        Get.toNamed(RouteNames.createShopScreen, arguments: [model.shop, true]);
      }
    } else if (type == "Product") {
      log(jsonEncode(response));
      ValidationHistoryModel productValidation =
          ValidationHistoryModel.fromJson(response);
      productHistory = productValidation.productHistory ?? ProductHistory();
      if (productValidation.status == statusOK) {
        List<History> arrProductValidationhistory = arrRequests.last.history;
        for (Product element in productValidation.productHistory?.items ?? []) {
          arrProductHistory.add(element);
          arrProductValidationhistory
              .add(element.validationHistory?.first ?? History());
        }
        arrRequests.last = ReturendRequest(
            requestType: arrRequests.last.requestType,
            history: arrProductValidationhistory);
      } else {
        arrRequests.last = ReturendRequest(
          requestType: arrRequests.last.requestType,
          history: [],
          errorMessage: productValidation.message ?? "",
        );
      }
    }
  }

  Widget _buildSkeletonView() {
    return ListView.builder(
      itemCount: 3,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          color: AppColors.headerColorDark,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.white24,
                      highlightColor: Colors.white38,
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: Colors.white24,
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Shimmer.fromColors(
                      baseColor: Colors.white24,
                      highlightColor: Colors.white38,
                      child: Container(
                        width: 120,
                        height: 16,
                        decoration: BoxDecoration(
                          color: Colors.white24,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ],
                ),
                const Divider(color: Colors.white24, height: 24),
                ...List.generate(2, (i) => Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Shimmer.fromColors(
                          baseColor: Colors.white24,
                          highlightColor: Colors.white38,
                          child: Container(
                            height: 14,
                            decoration: BoxDecoration(
                              color: Colors.white24,
                              borderRadius: BorderRadius.circular(7),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        flex: 2,
                        child: Shimmer.fromColors(
                          baseColor: Colors.white24,
                          highlightColor: Colors.white38,
                          child: Container(
                            height: 14,
                            decoration: BoxDecoration(
                              color: Colors.white24,
                              borderRadius: BorderRadius.circular(7),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
              ],
            ),
          ),
        );
      },
    );
  }
}

class ReturendRequest {
  final String requestType;
  final String errorMessage;
  final List<History> history;
  ReturendRequest(
      {required this.requestType,
      required this.history,
      this.errorMessage = ""});
}
