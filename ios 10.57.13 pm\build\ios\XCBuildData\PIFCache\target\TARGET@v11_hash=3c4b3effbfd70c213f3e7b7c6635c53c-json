{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cca60f9c966bde2af9b148f1f1bdf6d5", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f60ad41630d9e7ebc6257f2b7c9771a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9844a325ebfc345f2bb955bda9ff839c14", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808ebb61cc9b6bf2730a4627e98ee10ff", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9844a325ebfc345f2bb955bda9ff839c14", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984072357f32a9f8fc95b3c02424bde0a8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983eedea6cc3b9915b3abbbf80ceadcdb0", "guid": "bfdfe7dc352907fc980b868725387e98aa995f4fd8832b70d35a793e2c58b035", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfb12f804938de4be6d979a1a4111d5e", "guid": "bfdfe7dc352907fc980b868725387e98ee1ee74b5ae99302daf5646bfbbf13dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c82831ea1f1174e89e86f90f8c93d27", "guid": "bfdfe7dc352907fc980b868725387e98df5ec594fe9f0f2c64141a5d8f598177", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e36f7767aac3b32f4563d54da3946a4", "guid": "bfdfe7dc352907fc980b868725387e989b1e8a3aa0b6c16c265fb97a40f98f68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983de517ecc35d8517757ecfbb96465109", "guid": "bfdfe7dc352907fc980b868725387e98b1a1b60d4175bd6b7c3c3ea6a4bcd7c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837abe0ca4c656901b05375616685518d", "guid": "bfdfe7dc352907fc980b868725387e987b251cf83d7f700dc01b209e5ca4e0ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a7a81034649f528ac3b6e7b017a299e", "guid": "bfdfe7dc352907fc980b868725387e98eceba8b54ce3435e34a32ffdcea54e8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dcabcd2bd8003a31866051071028086", "guid": "bfdfe7dc352907fc980b868725387e9886d253775a080807baaf6cea7449caf9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5e28bff0f76432b0fca40175b57d24d", "guid": "bfdfe7dc352907fc980b868725387e98e2110e14936fb87644318315bebd22b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e473f807476821e6d4f472ddc031c11c", "guid": "bfdfe7dc352907fc980b868725387e98938409ebd6e8ad13541a0f88a19c8173", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9edb0e286af05d07760084b614ae023", "guid": "bfdfe7dc352907fc980b868725387e98b42e42472dfa234449e2b3d79e02ba09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec253135deff513bdd40246c64d2ca60", "guid": "bfdfe7dc352907fc980b868725387e98e9b2392395d6b4e86b2fd3c0e0c8bcde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea3f5797986d77cf9120cf5083636f3e", "guid": "bfdfe7dc352907fc980b868725387e98ec2d2843ca622efe666dc0ca842c297d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808aa0258521f99f5458882099d6f1e7a", "guid": "bfdfe7dc352907fc980b868725387e9828367f5c11f77572eaeab40f6dc9c0f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5320016f04fc023835d3bea14a0a62e", "guid": "bfdfe7dc352907fc980b868725387e98fe3cd4f136d50e46e284c4d651014fcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8b4469217bfccab243b2f76ab83660f", "guid": "bfdfe7dc352907fc980b868725387e982737b8e7bd5033241691d1bd812a6981", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e480b6b34f8d137990ec4971e494c19", "guid": "bfdfe7dc352907fc980b868725387e98653e4ee82198d0e4cfd42d1d837a324e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821b6ea4f716f0b00eae3c58a0698cf06", "guid": "bfdfe7dc352907fc980b868725387e9816c3e44d9b08aacfb6164ad4d181c8a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c8da4f46af8b521f34cc2e4d6b64967", "guid": "bfdfe7dc352907fc980b868725387e9898b5280df58d62a83dd146f3e8cf1ac2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98704626843a3187e48fb8ecc91683d035", "guid": "bfdfe7dc352907fc980b868725387e98d769b6663f9fcbddf8fccbd85b0b847a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bb89d4668ce3b26600e66105ca8e300", "guid": "bfdfe7dc352907fc980b868725387e9865a1a73c011fb2ac68471d69621fe98f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98893830191ea338f02e2c1dc91b910130", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98918a12d112524b7fda659f96cad2db92", "guid": "bfdfe7dc352907fc980b868725387e98ca2af142dd2f4c1cbd4bc96acee7cb57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987420a6cc34f293cc7c0b9ee78bd98bcd", "guid": "bfdfe7dc352907fc980b868725387e984336bdc47c8ca564386a1c8576fdee89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98367ac8a8b29b24e0ecf36062dad1537b", "guid": "bfdfe7dc352907fc980b868725387e9885fee017dfc51a0164954df2d2087e2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805c04264900c749c7963eac6f2f08e01", "guid": "bfdfe7dc352907fc980b868725387e98bb6789412ed4f8711ed5e92cdef71866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d245e1f3871d56cf9bf0e15e525959a9", "guid": "bfdfe7dc352907fc980b868725387e982a4147db67673ce801420a61bce475c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982998e37e8012c3949ad55786e9f214d7", "guid": "bfdfe7dc352907fc980b868725387e98e8c39d4ea7dc850bbb6960b6bb224394"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5110f23f49ce6c252e779144f2b3ca8", "guid": "bfdfe7dc352907fc980b868725387e984b664a5ceb878c93dab7de6024491322"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98494b1b9da36fb1ba422ab9c87634c54b", "guid": "bfdfe7dc352907fc980b868725387e98024c3ada7b69046640ed62e41899e267"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ae05b703b6945f7750b335088cc1aae", "guid": "bfdfe7dc352907fc980b868725387e985645a4a191527152741404cb24b7deb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983053baa66165445c67eee60a31ac88af", "guid": "bfdfe7dc352907fc980b868725387e98b60927a0bbb8e7efb8b16f05a412b2ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe56ecb768953c9b0f81e281135c1965", "guid": "bfdfe7dc352907fc980b868725387e98f7f3d8af76f1642f368a6513747712f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d1cfab0fd99f021fa49032e99759281", "guid": "bfdfe7dc352907fc980b868725387e981510b6b648bd6425e81f7ad46d4e86ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84ead1c090bd7b786d5b4323c84d9b0", "guid": "bfdfe7dc352907fc980b868725387e986ac94c446800990c7a0cf862178f8553"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984320a5eada5f508d57c2ed1fabf37636", "guid": "bfdfe7dc352907fc980b868725387e9892c478f28911b23c5a2dbd03f1e90651"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a81da869c3e0cbc3b20fb15df2cc31f9", "guid": "bfdfe7dc352907fc980b868725387e9815bf16158f513215373e12a4bd7f6448"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae6ed4dbc7d8890386cbe510413dea46", "guid": "bfdfe7dc352907fc980b868725387e98e3d0fb33cab988ea53ddbedb4fc8ea18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa9941a88f71bf389bcee84dc833242c", "guid": "bfdfe7dc352907fc980b868725387e98a9ec8eaffb68fa7a2a61ab748c486489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98371bdfecae35dfa9e07b7c84b8d651c2", "guid": "bfdfe7dc352907fc980b868725387e989224d114eb82cfec7cc86cdf970c8b42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba9da3d2ae003265bd4854717716f77e", "guid": "bfdfe7dc352907fc980b868725387e98d311d2d36b3697824b1aec4e21fa1a51"}], "guid": "bfdfe7dc352907fc980b868725387e988f2159a3fa518201e99f45201240c014", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e98c55b2fd41ec59641b36bba517fd96ffa"}], "guid": "bfdfe7dc352907fc980b868725387e98f59d14b41d6065eb13a4af8fcfae4a69", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983e9e224ef10dec5e1925539f36c732b7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}