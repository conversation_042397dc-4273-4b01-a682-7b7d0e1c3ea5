import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:provider/provider.dart';

class ElbaabShopBottomTab extends StatelessWidget {
  const ElbaabShopBottomTab({super.key});

  @override
  Widget build(BuildContext context) {
    final info = Provider.of<UpdatedInfo>(context, listen: false);
    int tabPosition = Provider.of<UpdatedInfo>(context).getTabPosition();
    final appLocalizations = AppLocalizations.of(context);

    return Container(
      height: 65.h,
      decoration: BoxDecoration(
        color: AppColors.headerColorDark,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.07),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildTabItem(
              title: appLocalizations?.dashboard ?? "",
              index: 0,
              selectedIcon: 'assets/images/dashboard_blue.svg',
              icon: 'assets/images/dashboard.svg',
              tabPosition: tabPosition,
              onTap: (v) => info.setTabPosition(v),
            ),
            _buildTabItem(
              title: appLocalizations?.order ?? "",
              index: 1,
              selectedIcon: 'assets/images/order_blue.svg',
              icon: 'assets/images/order.svg',
              tabPosition: tabPosition,
              onTap: (v) => info.setTabPosition(v),
            ),
            _buildShopTab(
              title: appLocalizations?.shop ?? "",
              tabPosition: tabPosition,
              info: info,
            ),
            _buildTabItem(
              title: appLocalizations?.profile ?? "",
              index: 3,
              selectedIcon: 'assets/images/profile_blue.svg',
              icon: 'assets/images/profile.svg',
              tabPosition: tabPosition,
              onTap: (v) => info.setTabPosition(v),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabItem({
    required String title,
    required int index,
    required String selectedIcon,
    required String icon,
    required int tabPosition,
    required Function(int) onTap,
  }) {
    final bool isSelected = tabPosition == index;
    
    return InkWell(
      onTap: () {
        if (!isSelected) {
          onTap(index);
          Get.offNamed(RouteNames.shopHomeScreen);
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: isSelected ? BoxDecoration(
          color: AppColors.colorPrimary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ) : null,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              isSelected ? selectedIcon : icon,
              height: 22.h,
              color: isSelected ? AppColors.colorPrimary : Colors.white.withOpacity(0.6),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: FontStyles.fontMedium(
                color: isSelected ? AppColors.colorPrimary : Colors.white.withOpacity(0.6),
                fontSize: 11,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShopTab({
    required String title,
    required int tabPosition,
    required dynamic info,
  }) {
    final bool isSelected = tabPosition == 2;
    
    const String shopIconUnselected = '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M3.5 21V7.5L2 4.5H22L20.5 7.5V21H3.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M2 4.5L8.5 2H15.5L22 4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M8.5 21V15.5C8.5 14.3954 9.39543 13.5 10.5 13.5H13.5C14.6046 13.5 15.5 14.3954 15.5 15.5V21" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';

    const String shopIconSelected = '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M3.5 21V7.5L2 4.5H22L20.5 7.5V21H3.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M2 4.5L8.5 2H15.5L22 4.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M8.5 21V15.5C8.5 14.3954 9.39543 13.5 10.5 13.5H13.5C14.6046 13.5 15.5 14.3954 15.5 15.5V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';

    return InkWell(
      onTap: () {
        if (!isSelected) {
          info.setTabPosition(2);
          Get.offNamed(RouteNames.shopHomeScreen);
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: isSelected ? BoxDecoration(
          color: AppColors.colorPrimary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ) : null,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.string(
              isSelected ? shopIconSelected : shopIconUnselected,
              height: 22.h,
              colorFilter: ColorFilter.mode(
                isSelected ? AppColors.colorPrimary : Colors.white.withOpacity(0.6),
                BlendMode.srcIn
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: FontStyles.fontMedium(
                color: isSelected ? AppColors.colorPrimary : Colors.white.withOpacity(0.6),
                fontSize: 11,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
// 203