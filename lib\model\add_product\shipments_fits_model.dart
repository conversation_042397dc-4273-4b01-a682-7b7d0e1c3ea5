class ShipmentFitsModel {
  final String? typename;
  final List<SizeCategories>? sizeCategories;
  final int? status;
  final String? message;

  ShipmentFitsModel({
    this.typename,
    this.sizeCategories,
    this.status,
    this.message,
  });

  ShipmentFitsModel.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      sizeCategories = (json['sizeCategories'] as List?)?.map((dynamic e) => SizeCategories.fromJson(e as Map<String,dynamic>)).toList(),
      status = json['status'] as int?,
      message = json['message'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'sizeCategories' : sizeCategories?.map((e) => e.toJson()).toList(),
    'status' : status,
    'message' : message
  };
}

class SizeCategories {
  final String? typename;
  final String? id;
  final String? name;
  final String? code;
  final Dimensions? dimensions;
  final SizeCategories? sizeCategoriesAr;

  SizeCategories({
    this.typename,
    this.id,
    this.name,
    this.code,
    this.dimensions,
    this.sizeCategoriesAr
  });

  SizeCategories.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      id = json['_id'] as String?,
      name = json['name'] as String?,
      code = json['code'] as String?,
      sizeCategoriesAr = (json['ar'] as Map<String,dynamic>?) != null ? SizeCategories.fromJson(json['ar'] as Map<String,dynamic>) : null,
      dimensions = (json['dimensions'] as Map<String,dynamic>?) != null ? Dimensions.fromJson(json['dimensions'] as Map<String,dynamic>) : null;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    '_id' : id,
    'name' : name,
    'ar' : sizeCategoriesAr?.toJson(),
    'code' : code,
    'dimensions' : dimensions?.toJson()
  };
}

class Dimensions {
  final String? typename;
  final String? width;
  final String? height;
  final String? weight;
  final String? length;

  Dimensions({
    this.typename,
    this.width,
    this.height,
    this.weight,
    this.length,
  });

  Dimensions.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      width = json['width'] as String?,
      height = json['height'] as String?,
      weight = json['weight'] as String?,
      length = json['length'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'width' : width,
    'height' : height,
    'weight' : weight,
    'length' : length
  };
}