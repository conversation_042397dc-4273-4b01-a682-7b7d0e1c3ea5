import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/other/password_strength_checker.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/model/login_model.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_password_textfield.dart';

class ChangePassword extends StatelessWidget
    with InputValidationUtil
    implements ServerResponse {
  String oldPass = '', newPass = '', confirmPass = '';
  ValueNotifier<bool> oldpass = ValueNotifier(true);
  ValueNotifier<bool> newpass = ValueNotifier(true);
  ValueNotifier<bool> confirmpass = ValueNotifier(true);
  bool isForget = Get.arguments[1] ?? false;
  String userId = Get.arguments[0];
  String userEmail = Get.arguments[2] ?? "";
  RxString error = ''.obs;
  bool _isStrong = false;
  final _passwordController = TextEditingController(text: "");

  final GlobalKey<FormState> _formkey = GlobalKey<FormState>();

  late GraphQlInitilize _request;

  ChangePassword({super.key});

  late AppLocalizations appLocal;

  @override
  Widget build(BuildContext context) {
    appLocal = AppLocalizations.of(context)!;
    _request = GraphQlInitilize(this);
    return Scaffold(
      appBar: ElbaabHeader(
        title: isForget ? appLocal.resetPassword : appLocal.changePassword,
        leadingBack: isForget ? false : true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
            child: Form(
              key: _formkey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(16.r),
                      child: Row(
                        children: [
                          Icon(Icons.info_outline, color: AppColors.colorPrimary),
                          SizedBox(width: 12.w),
                          Expanded(
                            child: Text(
                              appLocal.passwordCharaterLimit,
                              style: FontStyles.fontRegular(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 10.h),

                  if (!isForget) ...[
                    ValueListenableBuilder(
                      valueListenable: oldpass,
                      builder: (context, value, child) {
                        return ElbaabPasswordTextField(
                          edgeInsets: EdgeInsets.zero,
                          onChanged: (v) => oldPass = v,
                          hint: appLocal.passwordFeildHint,
                          label: appLocal.currentPassword,
                          validator: validatePassword,
                          obscure: value as bool,
                          obscureState: () => oldpass.value = !oldpass.value,
                        );
                      },
                    ),
                    Obx(
                      () => error.value.isEmpty
                          ? const SizedBox()
                          : Padding(
                              padding: EdgeInsets.only(top: 8.h, left: 8.w),
                              child: Text(
                                error.value,
                          style: FontStyles.fontRegular(
                            fontSize: 12,
                            color: AppColors.colorDanger,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 16.h),
                  ],

                  ValueListenableBuilder(
                    valueListenable: newpass,
                    builder: (context, value, child) {
                      return ElbaabPasswordTextField(
                        edgeInsets: EdgeInsets.zero,
                        onChanged: (v) => newPass = v,
                        hint: appLocal.passwordFeildHint,
                        passwordController: _passwordController,
                        label: appLocal.newPassword,
                        obscure: value as bool,
                        validator: validatePassword,
                        obscureState: () => newpass.value = !newpass.value,
                      );
                    },
                  ),
                  SizedBox(height: 16.h),

                  ValueListenableBuilder(
                    valueListenable: confirmpass,
                    builder: (context, value, child) {
                      return ElbaabPasswordTextField(
                        edgeInsets: EdgeInsets.zero,
                        onChanged: (v) => confirmPass = v,
                        hint: appLocal.passwordFeildHint,
                        label: appLocal.confirmPassword,
                        obscure: value as bool,
                        validator: (v) => validatePassword(v, previousPassword: newPass),
                        obscureState: () => confirmpass.value = !confirmpass.value,
                      );
                    },
                  ),
                  SizedBox(height: 16.h),

                  AnimatedBuilder(
                    animation: _passwordController,
                    builder: (context, child) {
                      return PasswordStrengthChecker(
                        onStrengthChanged: (value) => _isStrong = value,
                        password: _passwordController.text,
                      );
                    },
                  ),

                  if (isForget)
                    Obx(
                      () => Padding(
                        padding: EdgeInsets.only(top: 8.h, left: 8.w),
                        child: Text(
                          error.value,
                          style: FontStyles.fontRegular(
                            fontSize: 12,
                            color: AppColors.colorDanger,
                          ),
                        ),
                      ),
                    ),

                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 32.h),
                    child: ElbaabGradientButtonWidget(
                      onPress: () {
                        error.value = '';
                        if (_formkey.currentState!.validate()) {
                          if (isForget) {
                            _request.runMutation(
                              context: context,
                              query: GraphQlQuries.updatePassword,
                              variables: GraphQlVariables.resetPassword(
                                userId: userId,
                                newPassword: confirmPass,
                              ),
                            );
                          } else if (_isStrong) {
                            _request.runMutation(
                              context: context,
                              query: GraphQlQuries.changePassword,
                              variables: GraphQlVariables.changePassword(
                                oldPassword: oldPass,
                                newPassword: confirmPass,
                              ),
                            );
                          }
                        }
                      },
                      text: appLocal.save,
                      edgeInsets: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  onError(e, String type) {
    error.value = Get.locale?.languageCode == "ar"
        ? BaseModel.fromJson(e).arMessage ?? ""
        : BaseModel.fromJson(e).message ?? "";
  }

  @override
  onSucess(response, String type) {
    if (type == "login") {
      LoginModel loginModel = LoginModel.fromJson(response);
      if (loginModel.user?.isEmailVerified == false ||
          loginModel.user?.isPhoneVerified == false ||
          loginModel.user?.supplier == null ||
          loginModel.user?.supplier != null) {
        GlobalMethods.enableFutureLoginCheck(
            email: Get.arguments[2], password: confirmPass);
      }
      if (loginModel.user?.supplier?.shopId != null &&
          (loginModel.user?.supplier?.shopId?.isCompleted ?? false)) {
        GlobalMethods.enableFutureLoginCheck(
            email: Get.arguments[2], password: confirmPass);
      }
      if (loginModel.status == statusOK) {
        GlobalMethods.checkLoginStatus(loginModel,
            email: Get.arguments[2], password: confirmPass);
      } else {
        error.value = Get.locale?.languageCode == "ar"
            ? loginModel.arMessage!
            : loginModel.message!;
      }
    } else {
      BaseModel model = BaseModel.fromJson(response);
      if (model.status == statusOK) {
        if (isForget) {
          BottomSheets.showAlertMessageBottomSheet(
                  Get.locale?.languageCode == "ar"
                      ? model.arMessage ?? ""
                      : model.message ?? "",
                  appLocal.alert,
                  Get.context!)
              .then((value) {
            List arguments = Get.arguments;
            if (arguments.length > 2) {
              userAuthToken = '';
              _request.runMutation(
                context: Get.context!,
                query: GraphQlQuries.login,
                type: "login",
                variables: GraphQlVariables.login(
                    email: arguments[2] ?? "", password: confirmPass, lang: Get.locale?.languageCode ?? "en"),
              );
            } else {
              Get.back();
              Get.back();
            }
          });
        } else {
          BottomSheets.showAlertMessageBottomSheet(
                  Get.locale?.languageCode == "ar"
                      ? model.arMessage ?? ""
                      : model.message ?? "",
                  appLocal.alert,
                  Get.context!)
              .then((value) => Get.back());
        }
      } else {
        error.value = Get.locale?.languageCode == "ar"
            ? model.arMessage ?? ""
            : model.message ?? "";
      }
    }
  }
}
