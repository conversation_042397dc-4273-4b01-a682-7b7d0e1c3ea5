import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class NoBankInfo extends StatelessWidget {
  const NoBankInfo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/no_bank_info.png',
              height: 200,
              width: 200,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 32),
            Text(
              appLocal.kindlyAddBankAccount,
              textAlign: TextAlign.center,
              style: FontStyles.fontSemibold(
                color: Colors.white.withOpacity(0.8),
                fontSize: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
