import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/bank_info_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/bank_info/no_bank_info.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';

class ViewBankInfo extends StatelessWidget implements ServerResponse {
  RxBool haveBankInfo = false.obs;

  late GraphQlInitilize _request;
  Rx<BankAccountDetails> bankInfoModel = BankAccountDetails().obs;

  ViewBankInfo({super.key});
  getBankInfo(BuildContext context) {
    _request.runQuery(
        context: context,
        query: GraphQlQuries.getBankAccountDetails,
        isRequiredLoader: false);
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    _request = GraphQlInitilize(this);
    Future.delayed(Duration.zero, () => getBankInfo(context));
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.bankAccountInfo,
        leadingBack: true,
      ),
      body: Obx(
        () => haveBankInfo.value
            ? SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        appLocal.information,
                        style: FontStyles.fontBold(fontSize: 24),
                      ),
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          color: AppColors.headerColorDark,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    bankInfoModel.value.bankName ?? "",
                                    style: FontStyles.fontMedium(
                                        fontSize: 14,
                                        color: Colors.white.withOpacity(0.9)),
                                  ),
                                ),
                                IconButton(
                                  onPressed: () async {
                                    final result = await Get.toNamed(
                                        RouteNames.updateBankInfoScreen,
                                        arguments: [bankInfoModel.value]);
                                    if (result) {
                                      getBankInfo(context);
                                    }
                                  },
                                  icon: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.1),
                                        shape: BoxShape.circle),
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: SvgPicture.string(
                                          SvgStrings.iconEditGray),
                                    ),
                                  ),
                                )
                              ],
                            ),
                            const SizedBox(height: 24),
                            buildInfoCard(
                              appLocal.accountHolderName,
                              bankInfoModel.value.accountHolderName ?? "",
                              Icons.person_outline,
                            ),
                            buildInfoCard(
                              appLocal.accountNumber,
                              bankInfoModel.value.accountNumber ?? "",
                              Icons.account_balance_wallet_outlined,
                            ),
                            buildInfoCard(
                              appLocal.iban,
                              bankInfoModel.value.iban ?? "",
                              Icons.payment_outlined,
                            ),
                            buildInfoCard(
                              appLocal.city,
                              bankInfoModel.value.city ?? "",
                              Icons.location_city_outlined,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : const NoBankInfo(),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(20),
        child: Obx(
          () => ElbaabGradientButtonWidget(
            onPress: () async {
              final result = await Get.toNamed(RouteNames.updateBankInfoScreen,
                  arguments: [bankInfoModel.value]);
              if (result) {
                haveBankInfo.value = false;
                getBankInfo(context);
              }
            },
            text: haveBankInfo.value
                ? appLocal.updateBankAccount
                : appLocal.addNewBankAccount,
          ),
        ),
      ),
    );
  }

  Widget buildInfoCard(String title, String info, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: Colors.white.withOpacity(0.7), size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: FontStyles.fontRegular(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.5),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    info,
                    style: FontStyles.fontMedium(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    BankInfoModel info = BankInfoModel.fromJson(response);
    if (info.status == statusOK) {
      if (info.bankAccountDetails?.accountNumber != null &&
          info.bankAccountDetails?.iban != null) {
        haveBankInfo.value = true;
      }
      bankInfoModel.value = info.bankAccountDetails!;
    }
  }
}
