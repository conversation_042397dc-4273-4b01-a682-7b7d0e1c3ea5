import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/income_expense/income_expenses_model.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';

class TopSellingItem extends StatelessWidget {
  List<TopSellingItems> allItems = Get.arguments[0];

  TopSellingItem({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const ElbaabHeader(
        title: EnStrings.topSellingItems,
        leadingBack: true,
      ),
      body: GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisSpacing: 15,
              mainAxisSpacing: 15,
              childAspectRatio: (MediaQuery.of(context).size.width / 2 / 220),
              crossAxisCount: 2),
          itemCount: allItems.length,
          itemBuilder: (contex, index) {
            TopSellingItems item = allItems[index];
            return SizedBox(
              height: 182,
              child: Column(
                children: [
                  SizedBox(
                    height: 148,
                    width: MediaQuery.of(context).size.width / 2,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 15),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.11),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: GlobalMethods.netWorkImage(
                        item.product != null
                            ? item.product?.productImages?.first ?? ""
                            : item.variant?.variantImages?.first ?? "",
                        BorderRadius.circular(10),
                        BoxFit.cover,
                      ),
                    ),
                  ),
                  const Spacer(),
                  SizedBox(
                    height: 28,
                    width: 96,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.11),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Center(
                        child: Text(
                          '${item.sales ?? 0} Sales',
                          style: FontStyles.fontRegular(fontSize: 10),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
    );
  }
}
