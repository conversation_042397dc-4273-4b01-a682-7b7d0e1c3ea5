import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/login_model.dart';
import 'package:overolasuppliers/model/notifications_model.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;

class Notifications extends StatefulWidget {
  const Notifications({super.key});

  @override
  State<Notifications> createState() => _NotificationsState();
}

class _NotificationsState extends State<Notifications>
    implements ServerResponse {
  late GraphQlInitilize _request;

  final RxList<SortedNotification> _sortedNotifications =
      RxList<SortedNotification>();

  RxList<String> unReadNotificanIds = <String>[].obs;

  RxBool isLoading = false.obs;

  late NotificationsModel model;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);

    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _request.runQuery(
        context: context,
        query: GraphQlQuries.getNotifications,
        variables: GraphQlVariables.getPaginated(page: 1),
      );
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _loadMoreItems();
    }
  }

  _loadMoreItems() {
    if (isLoading.value ||
        (model.notifications?.hasNextPage ?? false) == false) {
      return;
    }
    isLoading.value = true;
    _request.runQuery(
      context: Get.context!,
      query: GraphQlQuries.getNotifications,
      variables: GraphQlVariables.getPaginated(
          page: ((model.notifications?.page ?? 0) + 1)),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppColors.backgroundColorDark,
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Modern Sliver App Bar
          SliverAppBar(
            expandedHeight: 140,
            floating: true,
            pinned: true,
            backgroundColor: AppColors.backgroundColorDark,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                appLocal.notification,
                style: FontStyles.fontBold(fontSize: 24, color: Colors.white),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.colorPrimary.withOpacity(0.2),
                      AppColors.backgroundColorDark,
                    ],
                  ),
                ),
              ),
            ),
            actions: [
              Obx(() => unReadNotificanIds.isEmpty
                  ? const SizedBox()
                  : TextButton.icon(
                      onPressed: () {
                        _request.runMutation(
                          context: context,
                          query: GraphQlQuries.viewAllNotifications,
                          type: "viewAllNotifications",
                          variables: {},
                        );
                      },
                      icon: const Icon(Icons.done_all_rounded, size: 20),
                      label: const Text('Mark all as read'),
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.colorPrimary,
                      ),
                    )),
            ],
          ),

          // Notifications List
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            sliver: Obx(
              () => SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, section) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Date Header
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Text(
                            _sortedNotifications[section].date,
                            style: FontStyles.fontSemibold(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                        ),
                        // Notifications for this date
                        ..._sortedNotifications[section]
                            .notifications
                            .asMap()
                            .entries
                            .map((entry) {
                          final items = entry.value;
                          final isUnread = !(items.isViewed ?? false);

                          return Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () => _handleNotificationTap(items),
                                borderRadius: BorderRadius.circular(16),
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: isUnread
                                          ? [
                                              AppColors.colorPrimary
                                                  .withOpacity(0.15),
                                              AppColors.colorPrimary
                                                  .withOpacity(0.05),
                                            ]
                                          : [
                                              Colors.white.withOpacity(0.05),
                                              Colors.white.withOpacity(0.02),
                                            ],
                                    ),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: isUnread
                                          ? AppColors.colorPrimary
                                              .withOpacity(0.3)
                                          : Colors.white.withOpacity(0.1),
                                    ),
                                  ),
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Notification Icon/Image
                                      Container(
                                        height: 50,
                                        width: 50,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          gradient: LinearGradient(
                                            colors: [
                                              AppColors.colorPrimary
                                                  .withOpacity(0.2),
                                              AppColors.colorPrimary
                                                  .withOpacity(0.1),
                                            ],
                                          ),
                                        ),
                                        padding: const EdgeInsets.all(12),
                                        child: Image.asset(
                                          "assets/images/app_icon.png",
                                          // color: AppColors.colorPrimary,
                                        ),
                                      ).animate(delay: 100.ms).fadeIn().scale(),
                                      const SizedBox(width: 16),

                                      // Notification Content
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    appLocal.localeName == "ar"
                                                        ? items.ar?.title ?? ""
                                                        : items.title ?? "",
                                                    style: FontStyles.fontBold(
                                                      fontSize: 16,
                                                      color: isUnread
                                                          ? AppColors
                                                              .colorPrimary
                                                          : Colors.white,
                                                    ),
                                                  ),
                                                ),
                                                if (isUnread)
                                                  Container(
                                                    height: 8,
                                                    width: 8,
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      color: AppColors
                                                          .colorPrimary,
                                                    ),
                                                  )
                                                      .animate(delay: 200.ms)
                                                      .scale()
                                                      .fadeIn(),
                                              ],
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              appLocal.localeName == "ar"
                                                  ? items.ar?.body ?? ""
                                                  : items.body ?? "",
                                              style: FontStyles.fontRegular(
                                                fontSize: 14,
                                                color: Colors.white
                                                    .withOpacity(0.7),
                                              ),
                                            ),
                                            const SizedBox(height: 12),
                                            Row(
                                              children: [
                                                const Icon(
                                                  Icons.access_time,
                                                  size: 14,
                                                  color: Colors.white38,
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  timeago.format(
                                                    DateTime.parse(
                                                        items.createdAt ?? ""),
                                                  ),
                                                  style: FontStyles.fontLight(
                                                    fontSize: 12,
                                                    color: Colors.white38,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          )
                              .animate(delay: (entry.key * 100).ms)
                              .fadeIn()
                              .slideX();
                        }).toList(),
                      ],
                    );
                  },
                  childCount: _sortedNotifications.length,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleNotificationTap(Items items) {
    if (!(items.isViewed ?? false)) {
      _request.runMutation(
        context: context,
        query: GraphQlQuries.viewNotifications,
        isLoader: false,
        type: "viewNotifications",
        variables: GraphQlVariables.viewNotifications(
          notificationIds: [items.id ?? ""],
        ),
      );
    }
    switch ((items.title ?? "").toLowerCase()) {
      case "registration returned":
        _request.runQuery(
          context: context,
          query: GraphQlQuries.getBusinessInfo,
          type: "businessInfo",
        );
        break;
      case "shop request returned":
        Get.toNamed(RouteNames.returendRequestsScreen,
            arguments: "shopRequest");
        break;
      case "product request returned":
        Get.toNamed(RouteNames.returendRequestsScreen,
            arguments: "productRequest");
        break;
      default:
    }
  }

  @override
  onError(error, String type) {
    isLoading.value = false;
  }

  @override
  onSucess(response, String type) {
    if (type == "businessInfo") {
      LoginModel model = LoginModel.fromJson(response);
      if (model.status == statusOK) {
        Get.toNamed(RouteNames.addBusinessInfoScreen,
            arguments: [supplierID, model.user?.supplier, true]);
      }
    } else if (type == "viewNotifications" || type == "viewAllNotifications") {
      _request.runQuery(
          context: Get.context!,
          query: GraphQlQuries.getTotalNotViewedNotifications,
          type: "getTotalNotViewedNotifications",
          isRequiredLoader: false);
    } else if (type == "getTotalNotViewedNotifications") {
      Provider.of<UpdatedInfo>(Get.context!, listen: false)
          .setNotificationCount(response ?? 0);
      _sortedNotifications.clear();
      unReadNotificanIds.clear();
      _request.runQuery(
        context: Get.context!,
        isRequiredLoader: false,
        query: GraphQlQuries.getNotifications,
        variables: GraphQlVariables.getPaginated(page: 1),
      );
    } else {
      model = NotificationsModel.fromJson(response);
      isLoading.value = false;

      if (model.status == statusOK) {
        (model.notifications?.items ?? [])
            .sort((a, b) => (b.createdAt ?? "").compareTo(a.createdAt ?? ""));
        for (Items element in model.notifications?.items ?? []) {
          if (!(element.isViewed ?? false)) {
            unReadNotificanIds.add(element.id ?? "");
          }
          var utc = DateTime.parse(element.createdAt ?? "");
          if (_sortedNotifications.isEmpty) {
            _sortedNotifications.add(SortedNotification(
                date: GlobalMethods.convertNotificationTimeStampToTimeAgo(
                    utc.toLocal().toString()),
                notifications: [element]));
          } else {
            bool isFound = false;
            for (SortedNotification sortedNotification
                in _sortedNotifications) {
              if (sortedNotification.date ==
                  GlobalMethods.convertNotificationTimeStampToTimeAgo(
                      element.createdAt ?? "")) {
                sortedNotification.notifications.add(element);
                isFound = true;
                break;
              }
            }
            if (!isFound) {
              _sortedNotifications.add(SortedNotification(
                  date: GlobalMethods.convertNotificationTimeStampToTimeAgo(
                      element.createdAt ?? ""),
                  notifications: [element]));
            }
          }
        }
      }
    }
  }
}
