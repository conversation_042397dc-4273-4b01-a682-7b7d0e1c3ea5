import 'dart:io';

import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/dio/dio_api_services.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/strings/http_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:provider/provider.dart';

class ApiRequest {
  final ServerResponse serverResponse;
  ApiRequest(this.serverResponse);

  Future<void> uploadImage(
      {required List<File> files,
      required List<String> parameters,
      String type = ""}) async {
    var formData = FormData();

    print("files $files");
    print("parameters $parameters");
    print("type $type");

    for (int i = 0; i < files.length; i++) {
      formData.files.add(
        MapEntry(
          parameters[i],
          await MultipartFile.fromFile(
            files[i].path,
            filename: files[i].path.split('/').last,
            contentType: MediaType(
                (lookupMimeType(files[i].path) ?? '').split('/').first,
                (lookupMimeType(files[i].path) ?? '').split('/').last),
          ),
        ),
      );
    }
    print("formData $formData");

    try {
      var res = await DioApiServices().postRequest(HTTPConstants.uploadFile,
          data: formData,
          requiredProgressLoader: true,
          authToken: userAuthToken, onSendProgress: (sent, total) {
        final progress = (sent / total);
        Provider.of<UpdatedInfo>(MyApp.navigatorKey.currentContext!,
                listen: false)
            .setUploadingProgress(progress);
      });
      serverResponse.onSucess(res, type);
    } catch (e) {
      serverResponse.onError(e, type);
    }
  }
}
