import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';

enum OrderStatus {
  newOrders,
  waitingPickupOrders,
  shippedOrders,
  canceledOrders,
  deliveredOrders,
  returnedOrders
}

class OrdersController extends GetxController implements ServerResponse {
  RxList<OrderItems> orders = RxList<OrderItems>([]);
  RxList<OrderItems> newOrders = RxList<OrderItems>([]);
  RxList<OrderItems> waitingPickupOrders = RxList<OrderItems>([]);
  RxList<OrderItems> shippedOrders = RxList<OrderItems>([]);
  OrdersModel? ordersModel;
  late GraphQlInitilize _graphQlInitilize;
  final ScrollController scrollController = ScrollController();

  List<OrderItems> dummyList = [
    OrderItems(),
    OrderItems(),
    OrderItems(),
    OrderItems()
  ];

  RxInt pendingOrderCount = 0.obs;
  RxInt returnedOrderCount = 0.obs;
  RxInt canceledOrderCount = 0.obs;
  RxInt shippedOrderCount = 0.obs;
  RxInt deliveredOrderCount = 0.obs;
  RxInt inProgressOrderCount = 0.obs;
  RxInt newReturnOrderCount = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _graphQlInitilize = GraphQlInitilize(this);
  }

  getOrdersCount() {
    _graphQlInitilize.runQuery(
        context: Get.context!,
        isRequiredLoader: false,
        query: GraphQlQuries.getOrderItemsLengths);
  }


  String getOrderStatus(OrderStatus orderStatus) {
    switch (orderStatus) {
      case OrderStatus.newOrders:
        return "Pending";
      case OrderStatus.waitingPickupOrders:
        return "Confirmed";
      case OrderStatus.shippedOrders:
        return "Picked up";
      case OrderStatus.canceledOrders:
        return "Canceled";
      case OrderStatus.deliveredOrders:
        return "Delivered";
      case OrderStatus.returnedOrders:
        return "Returned";
    }
  }

  String formatNumber(int number) {
    if (number < 1000) {
      return number.toString(); // Return as is if less than 1000
    } else if (number < 1000000) {
      // For thousands
      return '${(number / 1000).toStringAsFixed(0)} k';
    } else if (number < 1000000000) {
      // For millions
      return '${(number / 1000000).toStringAsFixed(0)} M';
    } else {
      // For billions
      return '${(number / 1000000000).toStringAsFixed(0)} B';
    }
  }

  getOrdersByStatus(
      {int page = 1,
      int itemsNumber = 15,
      bool isRequiredLoader = false,
      required String orderStatus,
      required GraphQlInitilize graphQlInitilize,
      required BuildContext context}) {
    graphQlInitilize.runQuery(
        context: context,
        isRequiredLoader: isRequiredLoader,
        query: GraphQlQuries.getOrderItems,
        variables: GraphQlVariables.getOrderPaginated(
            orderStatus: orderStatus, page: page, itemsNumber: itemsNumber));
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    OrdersCount ordersCount = OrdersCount.fromJson(response);
    pendingOrderCount.value = ordersCount.pending ?? 0;
    returnedOrderCount.value = ordersCount.returned ?? 0;
    canceledOrderCount.value = ordersCount.canceled ?? 0;
    shippedOrderCount.value = ordersCount.shipped ?? 0;
    deliveredOrderCount.value = ordersCount.delivered ?? 0;
    inProgressOrderCount.value = ordersCount.inProgress ?? 0;
    newReturnOrderCount.value = ordersCount.newReturn ?? 0;
  }
}

class OrdersCount {
  int? pending;
  int? returned;
  int? canceled;
  int? shipped;
  int? delivered;
  int? inProgress;
  int? newReturn;

  OrdersCount({
    this.pending,
    this.returned,
    this.canceled,
    this.shipped,
    this.delivered,
    this.inProgress,
    this.newReturn,
  });

  factory OrdersCount.fromJson(Map<String, dynamic> json) {
    return OrdersCount(
      pending: json['Pending'] != null ? json['Pending'] as int : null,
      returned: json['Returned'] != null ? json['Returned'] as int : null,
      canceled: json['Canceled'] != null ? json['Canceled'] as int : null,
      shipped: json['shipped'] != null ? json['shipped'] as int : null,
      delivered: json['Delivered'] != null ? json['Delivered'] as int : null,
      inProgress: json['Inprogress'] != null ? json['Inprogress'] as int : null,
      newReturn: json['NewReturn'] != null ? json['NewReturn'] as int : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Pending': pending,
      'Returned': returned,
      'Canceled': canceled,
      'shipped': shipped,
      'Delivered': delivered,
      'Inprogress': inProgress,
      'NewReturn': newReturn,
    };
  }
}
