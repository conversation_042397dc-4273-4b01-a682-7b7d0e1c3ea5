import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/controller/orders_controller.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/shipped_orders.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/waiting_pickup_orders.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class InProgressOrders extends StatefulWidget {
  const InProgressOrders({super.key});

  @override
  State<InProgressOrders> createState() => _InProgressOrdersState();
}

class _InProgressOrdersState extends State<InProgressOrders>
    with AutomaticKeepAliveClientMixin<InProgressOrders> {
  final controller = Get.find<OrdersController>();

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final appLocal = AppLocalizations.of(context)!;
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          toolbarHeight: 0,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(48),
            child: ColoredBox(
              color: AppColors.backgroundColorDark,
              child: Obx(
                () => TabBar(
                  tabAlignment: TabAlignment.fill,
                  labelColor: AppColors.colorPrimary,
                  unselectedLabelColor: Colors.white.withOpacity(0.6),
                  indicatorColor: AppColors.colorPrimary,
                  labelStyle: FontStyles.fontSemibold(),
                  unselectedLabelStyle: FontStyles.fontRegular(),
                  tabs: [
                    Tab(
                      text:appLocal.waitingOrderTab(controller.inProgressOrderCount.value == 0 ? '' : '( ${controller.formatNumber(controller.inProgressOrderCount.value)} )'),
                    ),
                     Tab(
                      text:appLocal.shippedOrderTab(controller.shippedOrderCount.value == 0 ? '' : '( ${controller.formatNumber(controller.shippedOrderCount.value)} )'),
                    ),
                   
                  ],
                ),
              ),
            ),
          ),
        ),
        body: const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          child: TabBarView(
            physics: NeverScrollableScrollPhysics(),
            children: [WaitingPickupOrders(), ShippedOrders()],
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
