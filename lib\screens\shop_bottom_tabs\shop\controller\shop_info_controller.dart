import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/media_picker.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/shop/ViewShopModel.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/branches.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ShopInfoController extends GetxController {
  File? bannerImage, logoImage, termCondition;
  RxString uploadTermContidition = 'Upload Terms & Conditions Doc'.obs;
  String storeName = '',
      targetPrice = '',
      bannerUrl = '',
      logoUrl = '',
      termsUrl = '',
      slogan = '',
      description = '',
      strContactNumber = '',
      strWhatsappNumber = '',
      strEmail = '';
  RxBool freeDeliveryTaget = false.obs,
      isLatLng = false.obs,
      isRequiredLocation = false.obs,
      isRemoveTerms = false.obs;
  List<String> cityList = [];
  List<String> cityListAr = [];
  ValidationHistory history = ValidationHistory();

  Shop? shop;
  RxList<PickupAddressModel> branches = <PickupAddressModel>[].obs;
  RxList<PickupAddressModel> filterBranch = <PickupAddressModel>[].obs;

  Future<dynamic> imagePicker(
      BuildContext context, double aspectRatioPreset) async {
    FocusManager.instance.primaryFocus?.unfocus();
    File? file = await showMediaPicker(context,
        requiredDocumentPicker: false,
        requiredEditor: true,
        aspectRatioPreset: aspectRatioPreset);

    if (file != null) {
      return file;
    }
    return null;
  }

  @override
  void onInit() {
    super.onInit();
    uploadTermContidition.value = AppLocalizations.of(Get.context!)!
        .uploadTermsAndCondiition;
    storeName = supplierBusinessName.isEmpty
        ? (shop?.shopName ?? "")
        : supplierBusinessName;
    List arrArguments = Get.arguments;
    String strRegisterationNumber = "";
    if (arrArguments.length > 2) {
      strRegisterationNumber = Get.arguments[2] ?? "";
    }

    branches.add(PickupAddressModel(
        "".obs,
        "",
        "",
        ContactNumber(
            number: strRegisterationNumber.obs,
            isVerified: strRegisterationNumber.isEmpty),
        ContactNumber(number: "".obs, isVerified: false),
        "",
        "",
        null,
        false,
        false,

                          false,
        false));
    if (Get.arguments[0] != null) {
      shop = Get.arguments[0];
      if (shop?.validationHistory != null) {
        history = (shop?.validationHistory ?? []).last;
      }
    }
    updateshopInfo(shop: shop);
    updatePickupAddress(shop: shop);
    updateContactInfo(shop: shop);
    update();
  }

  bool requiredPageAutoValidate() {
    if (history.returnValues?.contains("Shop Slogan") ?? false) {
      return true;
    }
    if (history.returnValues?.contains("Shop Description") ?? false) {
      return true;
    }
    if (history.returnValues?.contains("Free Delivery Target Price") ?? false) {
      return true;
    }
    if (history.returnValues?.contains("Shop Terms And Conditions") ?? false) {
      return true;
    }
    if (history.returnValues?.contains("Shop Banner") ?? false) {
      return true;
    }
    if (history.returnValues?.contains("Shop Logo") ?? false) {
      return true;
    } else if (history.returnValues?.contains("Phone number") ?? false) {
      return true;
    } else if (history.returnValues?.contains("Whatsup number") ?? false) {
      return true;
    } else if (history.returnValues?.contains("Email") ?? false) {
      return true;
    } else {
      return false;
    }
  }

  updateContactInfo({Shop? shop}) {
    String customerContact = prefs.getString(shopCustomerContact) ?? "";
    if (customerContact.isNotEmpty) {
      Map<String, dynamic> contactInfo = jsonDecode(customerContact);

      if (shop != null) {
        if (history.returnValues?.contains("Phone number") ?? false) {
          strContactNumber = contactInfo["phoneNumber"] ?? "";
        } else {
          strContactNumber = shop.shopContactDetails?.phoneNumber ?? "";
        }
        if (history.returnValues?.contains("Whatsup number") ?? false) {
          strWhatsappNumber = contactInfo["whatsUpPhoneNumber"] ?? "";
        } else {
          strWhatsappNumber = shop.shopContactDetails?.whatsUpPhoneNumber ?? "";
        }
        if (history.returnValues?.contains("Email") ?? false) {
          strEmail = contactInfo["email"] ?? "";
        } else {
          strEmail = shop.shopContactDetails?.email ?? "";
        }
      } else {
        strContactNumber = contactInfo["phoneNumber"] ?? "";
        strWhatsappNumber = contactInfo["whatsUpPhoneNumber"] ?? "";
        strEmail = contactInfo["email"] ?? "";
      }
    } else if (shop != null) {
      strContactNumber = shop.shopContactDetails?.phoneNumber ?? '';
      strEmail = shop.shopContactDetails?.email ?? '';
      strWhatsappNumber = shop.shopContactDetails?.whatsUpPhoneNumber ?? '';
    }
  }

  updatePickupAddress({Shop? shop}) {
    String address = prefs.getString(shopPickUpAddress) ?? "";
    if (address.isNotEmpty) {
      Map<String, dynamic> pickupAddress = jsonDecode(address);
      List<Map<String, dynamic>> addressList = [];
      for (var element in pickupAddress["pickupAdr"]) {
        addressList.add(element);
      }
      branches.clear();
      for (var element in addressList) {
        String city = element["pickUpCity"];
        String pickUpContactMobileNumber =
            element["pickUpContactMobileNumber"]["number"];
        String pickUpContactLandNumber =
            element["pickUpContactLandNumber"]["number"];
        if (city.isNotEmpty) {
          branches.add(PickupAddressModel(
              city.obs,
              element["pickUpAddress"],
              element["_id"],
              ContactNumber(
                  isVerified: element["pickUpContactMobileNumber"]
                      ["isVerified"],
                  number: pickUpContactMobileNumber.obs),
              ContactNumber(
                  isVerified: element["pickUpContactLandNumber"]["isVerified"],
                  number: pickUpContactLandNumber.obs),
              "",
              "",
              LatLng(element["coordinates"][0], element["coordinates"][1]),
              element["isUpdated"],
              element["isDeleted"],
              element["isUsedInOrder"],
              element["isAdded"]));
        }
      }
    } else if (shop?.shopPickupAddresses?.isNotEmpty ?? false) {
      branches.clear();
      shop?.shopPickupAddresses?.forEach((address) {
        branches.add(PickupAddressModel(
            (address.pickUpCity ?? "").obs,
            address.pickUpAddress ?? "",
            address.id ?? "",
            ContactNumber(
                number: (address.pickUpContactMobileNumber?.number ?? "").obs,
                isVerified:
                    address.pickUpContactMobileNumber?.isVerified ?? false),
            ContactNumber(
                number: (address.pickUpContactLandNumber?.number ?? "").obs,
                isVerified:
                    address.pickUpContactLandNumber?.isVerified ?? true),
            address.createdAt ?? "",
            address.updatedAt ?? "",
            (address.pickUpMapLocation?.coordinates?.isEmpty ?? false)
                ? const LatLng(0.0, 0.0)
                : LatLng(address.pickUpMapLocation?.coordinates?.first ?? 0,
                    address.pickUpMapLocation?.coordinates?.last ?? 0),
            false,
            false,
            address.isUsedInOrder ?? false,
            false));
      });
    }
  }

  updateshopInfo({Shop? shop}) {
    String information = prefs.getString(shopInfo) ?? "";
    if (information.isNotEmpty) {
      Map<String, dynamic> info = jsonDecode(information);
      if (shop != null) {
        if (history.returnValues?.contains("Shop Banner") ?? false) {
          bannerUrl = info["banner"] ?? "";
        } else {
          bannerUrl = shop.shopBanner ?? "";
        }
        if (history.returnValues?.contains("Shop Logo") ?? false) {
          logoUrl = info["logo"] ?? "";
        } else {
          logoUrl = shop.shopLogo ?? "";
        }
        if (history.returnValues?.contains("Shop Terms And Conditions") ??
            false) {
          termsUrl = info["terms"] ?? "";
          if (termsUrl.isNotEmpty) {
            uploadTermContidition.value = termsUrl.split("/").last;
          } else {
            isRemoveTerms.value = true;
            if (termCondition != null) {
              termCondition = null;
            }
            termsUrl = "";
            uploadTermContidition.value = "Upload Terms & Conditions Doc";
          }
        } else {
          termsUrl = shop.shopTermsAndConditions ?? "";
          if (termsUrl.isNotEmpty) {
            uploadTermContidition.value = termsUrl.split("/").last;
          } else {
            isRemoveTerms.value = true;
            if (termCondition != null) {
              termCondition = null;
            }
            termsUrl = "";
            uploadTermContidition.value = "Upload Terms & Conditions Doc";
          }
        }
        if (history.returnValues?.contains("Shop Description") ?? false) {
          description = info["description"] ?? "";
        } else {
          description = shop.shopDescription ?? "";
        }
        if (history.returnValues?.contains("Shop Slogan") ?? false) {
          slogan = info["slogan"];
        } else {
          slogan = shop.shopSlogan ?? "";
        }
        if (history.returnValues?.contains("Free Delivery Target Price") ??
            false) {
          targetPrice = info["targetPrice"];
          freeDeliveryTaget.value = info["freeDelivery"];
        } else {
          targetPrice = (shop.targetPriceForFdt ?? 0).toString();
          if (targetPrice == "0") {
            targetPrice = "";
          }
          freeDeliveryTaget.value = shop.freeDeliveryTarget ?? false;
        }
      } else {
        bannerUrl = info["banner"] ?? "";
        logoUrl = info["logo"] ?? "";
        termsUrl = info["terms"] ?? "";
        slogan = info["slogan"];
        description = info["description"];
        targetPrice = info["targetPrice"];
        freeDeliveryTaget.value = info["freeDelivery"];
        if (termsUrl.isNotEmpty) {
          uploadTermContidition.value = termsUrl.split("/").last;
        } else {
          isRemoveTerms.value = true;
          if (termCondition != null) {
            termCondition = null;
          }
          termsUrl = "";
          uploadTermContidition.value = "Upload Terms & Conditions Doc";
        }
      }
    } else if (shop != null) {
      bannerUrl = shop.shopBanner ?? "";
      logoUrl = shop.shopLogo ?? "";
      termsUrl = shop.shopTermsAndConditions ?? "";
      slogan = shop.shopSlogan ?? "";
      description = shop.shopDescription ?? "";
      targetPrice = (shop.targetPriceForFdt ?? 0).toString();
      if (targetPrice == "0") {
        targetPrice = "";
      }
      freeDeliveryTaget.value = shop.freeDeliveryTarget ?? false;
      if (termsUrl.isNotEmpty) {
        uploadTermContidition.value = termsUrl.split("/").last;
      } else {
        isRemoveTerms.value = true;
        if (termCondition != null) {
          termCondition = null;
        }
        termsUrl = "";
        uploadTermContidition.value = "Upload Terms & Conditions Doc";
      }
    }
  }
}
