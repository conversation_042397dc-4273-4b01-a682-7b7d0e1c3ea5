import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';

import '../../../../model/bank_info_model.dart';

class UpdateBankInfo extends StatelessWidget
    with InputValidationUtil
    implements ServerResponse {
  String strAccountNumber = '',
      strIban = '',
      strAccountHolder = '',
      strBankName = '',
      strCity = '';
  late GraphQlInitilize _request;
  Rx<BankAccountDetails> bankInfoModel = BankAccountDetails().obs;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  UpdateBankInfo({super.key});
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    _request = GraphQlInitilize(this);
    if (Get.arguments != null) {
      bankInfoModel.value = Get.arguments[0];
      strAccountHolder = bankInfoModel.value.accountHolderName ?? "";
      strAccountNumber = bankInfoModel.value.accountNumber ?? "";
      strBankName = bankInfoModel.value.bankName ?? "";
      strCity = bankInfoModel.value.city ?? "";
      strIban = bankInfoModel.value.iban ?? "";
    }
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.bankAccountInfo,
        leadingBack: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 32),
          child: Form(
            key: _formKey,
            child: Obx(
              () => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    ElbaaabInputTextField(
                      margin: const EdgeInsets.only(bottom: 24),
                      onChanged: (value) => strAccountNumber = value,
                      hint: '',
                      label: appLocal.accountNumber,
                      textDirection: appLocal.localeName == 'en'
                          ? TextDirection.ltr
                          : TextDirection.rtl,
                      validator: validateFieldEmpty,
                      initialValue: bankInfoModel.value.accountNumber ?? "",
                      inputType: TextInputType.number,
                      inputFormatter: "[0-9]",
                      charaterlimit: 12,
                    ),
                    ElbaaabInputTextField(
                      margin: const EdgeInsets.only(bottom: 24),
                      onChanged: (value) => strIban = value,
                      hint: '',
                      label: appLocal.iban,
                      textDirection: appLocal.localeName == 'en'
                          ? TextDirection.ltr
                          : TextDirection.rtl,
                      validator: validateFieldEmpty,
                      initialValue: bankInfoModel.value.iban ?? "",
                      inputType: TextInputType.text,
                      inputFormatter: "[0-9a-zA-Z]",
                      charaterlimit: 23,
                    ),
                    ElbaaabInputTextField(
                      margin: const EdgeInsets.only(bottom: 24),
                      onChanged: (value) => strAccountHolder = value,
                      hint: appLocal.nameHint,
                      charaterlimit: 60,
                      textDirection: appLocal.localeName == 'en'
                          ? TextDirection.ltr
                          : TextDirection.rtl,
                      validator: validateFieldEmpty,
                      label: appLocal.accountHolderName,
                      initialValue: bankInfoModel.value.accountHolderName ?? "",
                      inputType: TextInputType.text,
                      inputFormatter:
                          appLocal.localeName == 'ar' ? null : "[a-z A-Z]",
                    ),
                    ElbaaabInputTextField(
                      margin: const EdgeInsets.only(bottom: 24),
                      onChanged: (value) => strBankName = value,
                      hint: 'ex : alahli bank',
                      label: appLocal.bankName,
                      charaterlimit: 60,
                      textDirection: appLocal.localeName == 'en'
                          ? TextDirection.ltr
                          : TextDirection.rtl,
                      validator: validateFieldEmpty,
                      initialValue: bankInfoModel.value.bankName ?? "",
                      inputType: TextInputType.text,
                      inputFormatter:
                          appLocal.localeName == 'ar' ? null : "[a-z A-Z0-9]",
                    ),
                    ElbaaabInputTextField(
                      margin: const EdgeInsets.only(bottom: 30),
                      onChanged: (value) => strCity = value,
                      hint: 'ex : Dubai ',
                      validator: validateFieldEmpty,
                      label: appLocal.city,
                      textDirection: appLocal.localeName == 'en'
                          ? TextDirection.ltr
                          : TextDirection.rtl,
                      charaterlimit: 30,
                      initialValue: bankInfoModel.value.city ?? "",
                      inputType: TextInputType.text,
                      inputFormatter:
                          appLocal.localeName == 'ar' ? null : "[a-z A-Z]",
                    ),
                    GestureDetector(
                      onTap: () => BottomSheets.showAlertMessageBottomSheet(
                          appLocal.whyBankInfo,
                          appLocal.bankAccountInfo,
                          context),
                      child: Text(
                        appLocal.whyThisInformationRequired,
                        style: FontStyles.fontRegular(
                            color: AppColors.colorPrimary),
                      ),
                    ),
                    ElbaabGradientButtonWidget(
                        onPress: () {
                          if (_formKey.currentState!.validate()) {
                            _request.runMutation(
                                context: context,
                                query: bankInfoModel.value.accountHolderName !=
                                        null
                                    ? GraphQlQuries.updateBankAccountDetails
                                    : GraphQlQuries.addBankAccountDetails,
                                variables:
                                    GraphQlVariables.addBankAccountDetails(
                                        accountNumber: strAccountNumber,
                                        iban: strIban,
                                        accountHolderName: strAccountHolder,
                                        bankName: strBankName,
                                        city: strCity));
                          }
                        },
                        edgeInsets: const EdgeInsets.symmetric(vertical: 50),
                        text: bankInfoModel.value.accountHolderName != null
                            ? appLocal.update
                            : appLocal.save),
                  ]),
            ),
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (model.status == statusOK) {
      BottomSheets.showAlertMessageBottomSheet(
          Get.locale!.languageCode == "ar"
              ? model.arMessage ?? ""
              : model.message ?? "",
          null,
          Get.context!);
    }
  }
}
