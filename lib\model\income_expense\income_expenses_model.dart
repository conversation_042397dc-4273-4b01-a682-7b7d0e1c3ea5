class IncomeExpensesModel {
  final String? typename;
  final int? status;
  final String? message;
  final dynamic availableAmount;
  final dynamic totalDeliveryChargeFees;
  final dynamic totalRevenue;
  final dynamic pendingAmount;
  final List<TopSellingItems>? topSellingItems;
  final List<SupplierDeliveryFeeChargesType>? deliveryFeeCharges;


  IncomeExpensesModel({
    this.typename,
    this.status,
    this.message,
    this.availableAmount,
    this.totalDeliveryChargeFees,
    this.totalRevenue,
    this.pendingAmount,
    this.deliveryFeeCharges,
    this.topSellingItems,
  });

  IncomeExpensesModel.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        status = json['status'] as int?,
        message = json['message'] as String?,
        availableAmount = json['availableAmount'] as dynamic,
        totalDeliveryChargeFees = json['totalDeliveryChargeFees'] as dynamic,
        totalRevenue = json['totalRevenue'] as dynamic,
        pendingAmount = json['pendingAmount'] as dynamic,
       
        deliveryFeeCharges = (json['deliveryFeeCharges'] as List?)
            ?.map((dynamic e) =>
                SupplierDeliveryFeeChargesType.fromJson(e as Map<String, dynamic>))
            .toList(),
        topSellingItems = (json['topSellingItems'] as List?)
            ?.map((dynamic e) =>
                TopSellingItems.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'status': status,
        'message': message,
        'availableAmount': availableAmount,
        'totalDeliveryChargeFees': totalDeliveryChargeFees,
        'totalRevenue': totalRevenue,
        'pendingAmount': pendingAmount,
        'topSellingItems': topSellingItems?.map((e) => e.toJson()).toList(),
      };
}

class SupplierDeliveryFeeChargesType {
  String? typename;
  String? shipmentCode;
  String? feeType;
  String? date;
  int? feeAmount;

  SupplierDeliveryFeeChargesType(
      {this.typename,
      this.shipmentCode,
      this.feeType,
      this.date,
      this.feeAmount});

  factory SupplierDeliveryFeeChargesType.fromJson(Map<String, dynamic> json) {
    return SupplierDeliveryFeeChargesType(
      typename: json['__typename'],
      shipmentCode: json['shipmentCode'],
      feeType: json['feeType'],
      date: json['date'],
      feeAmount: json['feeAmount'],
    );
  }
}

class TopSellingItems {
  final String? typename;
  final Product? product;
  final Variant? variant;
  final int? sales;

  TopSellingItems({
    this.typename,
    this.product,
    this.variant,
    this.sales,
  });

  TopSellingItems.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        variant = (json['variant'] as Map<String, dynamic>?) != null
            ? Variant.fromJson(json['variant'] as Map<String, dynamic>)
            : null,
        product = (json['product'] as Map<String, dynamic>?) != null
            ? Product.fromJson(json['product'] as Map<String, dynamic>)
            : null,
        sales = json['sales'] as int?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'product': product?.toJson(),
        'variant': variant?.toJson(),
        'sales': sales
      };
}

class Variant {
  final String? typename;
  final List<String>? variantImages;

  Variant({
    this.typename,
    this.variantImages,
  });

  Variant.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        variantImages = (json['variantImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList();

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'variantImages': variantImages};
}

class Product {
  final String? typename;
  final List<String>? productImages;

  Product({
    this.typename,
    this.productImages,
  });

  Product.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        productImages = (json['productImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList();

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'productImages': productImages};
}
