import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/other/dotted_line_corner.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class ElbaabDottedBorderContainer extends StatelessWidget {
  final Function onTap;
  final String? title;
  final Widget? child;
  final EdgeInsets? padding;
  final Color? dotColors;
  final Color? backgroundColor;
  final double? height;

  const ElbaabDottedBorderContainer(
      {Key? key,
      required this.onTap,
      this.title,
      this.child,
      this.padding,
      this.height,
      this.dotColors,
      this.backgroundColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          padding ?? const EdgeInsets.symmetric(vertical: 16, horizontal: 30),
      child: InkWell(
        onTap: () => onTap(),
        child: Container(
          height: height ?? 42,
          decoration: BoxDecoration(
            color: backgroundColor ?? AppColors.colorPrimary.withOpacity(0.15),
            borderRadius: BorderRadius.circular(5),
          ),
          child: DottedLine(
            dottedLength: 5,
            space: 7,
            strokeWidth: 2,
            color: dotColors ?? AppColors.colorPrimary,
            corner: DottedLineCorner.all(5),
            child: Center(
              child: child ??
                  Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: <Widget>[
                      Container(
                        height: 20,
                        width: 20,
                        decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.14),
                            shape: BoxShape.circle),
                        child: Icon(
                          Icons.add,
                          color: AppColors.colorPrimary,
                          size: 17,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(title ?? "", style: FontStyles.fontRegular())
                    ],
                  ),
            ),
          ),
        ),
      ),
    );
  }
}
