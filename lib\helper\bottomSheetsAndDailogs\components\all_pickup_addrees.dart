import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:overolasuppliers/date_time_picker/flutter_datetime_picker.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/model/orders/pickup_address_model.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';

class AllPickupAddress extends StatefulWidget {
  final String orderId, pickupAddressId, ros;
  final bool isUpdate;

  const AllPickupAddress(
      {Key? key,
      required this.orderId,
      required this.isUpdate,
      required this.pickupAddressId,
      required this.ros})
      : super(key: key);

  @override
  State<AllPickupAddress> createState() => _AllPickupAddressState();
}

class _AllPickupAddressState extends State<AllPickupAddress>
    with InputValidationUtil
    implements ServerResponse {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late GraphQlInitilize _request;
  RxList<PickUpAddresses> arrPickupAddress = <PickUpAddresses>[].obs;

  RxString strError = "".obs, selectedAddress = "".obs, strROS = "".obs;

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => {
          if (widget.pickupAddressId.isNotEmpty)
            {
              selectedAddress.value = (widget.pickupAddressId),
              strROS.value = GlobalMethods.convertTimeStamp(
                  DateTime.parse(widget.ros).millisecondsSinceEpoch,
                  format: "EEE dd MMM yyyy")
            },
          _request.runQuery(
              context: context,
              query: GraphQlQuries.getPickupAddresses,
              type: "address")
        });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(20),
              topLeft: Radius.circular(20),
            ),
            color: AppColors.backgroundColorDark,
          ),
          child: Form(
            key: _formKey,
            child: Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              alignment: WrapAlignment.center,
              children: [
                Container(
                  margin: const EdgeInsets.all(15),
                  width: 90,
                  height: 5,
                  decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(5)),
                ),
                const SizedBox(height: 10),
                Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      'Please, assign this order to a branch that most suitable for the buyer',
                      textAlign: TextAlign.center,
                      style: FontStyles.fontSemibold(),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Obx(
                  () => Text(
                    strError.value,
                    textAlign: TextAlign.center,
                    style: FontStyles.fontSemibold(
                      color: AppColors.colorDanger,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Obx(
                  () => ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: arrPickupAddress.length,
                    itemBuilder: (context, index) {
                      return Obx(
                        () => ElbaabFeildContainerWidget(
                          borderWidth: selectedAddress.value ==
                                  arrPickupAddress[index].id.toString()
                              ? 3
                              : 1,
                          borderColor: selectedAddress.value ==
                                  arrPickupAddress[index].id.toString()
                              ? AppColors.colorPrimary
                              : null,
                          edgeInsets: const EdgeInsets.only(
                              top: 10, left: kLeftSpace, right: kRightSpace),
                          child: ListTile(
                            onTap: () => (selectedAddress.value =
                                arrPickupAddress[index].id ?? ""),
                            leading: SvgPicture.string(SvgStrings.iconPin),
                            title: Transform.translate(
                              offset: const Offset(-16, 0),
                              child: Text(
                                "${arrPickupAddress[index].pickUpAddress ?? ""}, ${arrPickupAddress[index].pickUpCity ?? ""}, United Arab Emirates",
                                style: FontStyles.fontRegular(
                                  fontSize: 12,
                                  color: Colors.white70,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                Obx(
                  () => ElbaaabInputTextField(
                    onChanged: (v) {},
                    initialValue: strROS.value,
                    hint: 'ex : 2',
                    onTap: () {
                      DatePicker.showDatePicker(
                        context,
                        showTitleActions: true,
                        minTime: DateTime.now(),
                        maxTime: DateTime.now().add(const Duration(days: 30)),
                        onConfirm: (date) {
                          strROS.value = GlobalMethods.convertTimeStamp(
                              date.millisecondsSinceEpoch,
                              format: "EEE dd MMM yyyy");
                        },
                        currentTime: DateTime.now(),
                      );
                    },
                    label: "Readiness for shipment",
                    validator: validateFieldEmpty,
                    margin: const EdgeInsets.symmetric(
                        horizontal: kLeftSpace, vertical: 15),
                    suffix: const Icon(
                      Icons.calendar_today,
                      color: Colors.white,
                    ),
                    inputType: const TextInputType.numberWithOptions(
                        signed: true, decimal: true),
                  ),
                ),
                Row(
                  children: [
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 1,
                      child: ElbaabButtonWidget(
                        onPress: () => Get.back(result: false),
                        height: 40,
                        text: EnStrings.cancel,
                        colors: AppColors.colorPrimary_40,
                      ),
                    ),
                    const SizedBox(width: 13),
                    Expanded(
                      flex: 1,
                      child: ElbaabButtonWidget(
                        onPress: () {
                          if (_formKey.currentState!.validate()) {
                            if (selectedAddress.isEmpty) {
                              strError.value =
                                  "Please select branch before accept";
                            } else {
                              DateTime parseDate = DateFormat("EEE dd MMM yyyy")
                                  .parse(strROS.value);
                              var inputDate =
                                  DateTime.parse(parseDate.toString());
                              var outputFormat = DateFormat('yyyy-MM-dd');
                              String readinessPeriode =
                                  outputFormat.format(inputDate);
                              _request.runMutation(
                                context: context,
                                query: widget.isUpdate
                                    ? GraphQlQuries.editAcceptedOrderItem
                                    : GraphQlQuries.confirmOrderItem,
                                variables: GraphQlVariables.confirmOrderItem(
                                    orderItemId: widget.orderId,
                                    pickupAddressId: selectedAddress.value,
                                    readinessPeriode: readinessPeriode),
                              );
                            }
                          }
                        },
                        height: 40,
                        text: EnStrings.submit,
                        colors: AppColors.colorPrimary,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                ),
                Container(
                  margin: const EdgeInsets.all(15),
                  width: 90,
                  height: 5,
                ),
              ],
            ),
          )),
    );
  }

  @override
  onError(error, String type) {
    log("error ${jsonEncode(error)}");
    strError.value = BaseModel.fromJson(error).message ?? "";
  }

  @override
  onSucess(response, String type) {
    log("response ${jsonEncode(response)}");
    if (type == "address") {
      AllPickupAddressModel addressModel =
          AllPickupAddressModel.fromJson(response);

      if (addressModel.status == statusOK) {
        arrPickupAddress.addAll(addressModel.pickUpAddresses ?? []);
        Future.delayed(Duration.zero, () {
          if (selectedAddress.value.isEmpty) {
            selectedAddress.value =
                addressModel.pickUpAddresses?.first.id ?? "";
          }
        });
      }
    } else {
      BaseModel model = BaseModel.fromJson(response);
      if (model.status == statusOK) {
        BottomSheets.showAlertMessageBottomSheet(
                model.message ?? "", "Notification", context)
            .then((value) {
          Get.back(result: true);
        });
      } else {
        strError.value = model.message ?? "";
      }
    }
  }
}
