import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/controller/orders_controller.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/history_orders.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/inprogress_orders.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/pending_orders.dart';
import 'package:overolasuppliers/widgets/elbaab_homepage_header.dart';
import 'package:provider/provider.dart';

class MyOrders extends StatefulWidget {
  const MyOrders({super.key});

  @override
  State<MyOrders> createState() => _MyOrdersState();
}

class _MyOrdersState extends State<MyOrders> with AutomaticKeepAliveClientMixin<MyOrders> {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final appLocal = AppLocalizations.of(context)!;
    final controller = Get.put(OrdersController());
    checkOrderUpdates(context, controller);
    if (context.watch<UpdatedInfo>().getTabPosition() == 1) {
      controller.getOrdersCount();
    }
    return Scaffold(
      appBar:  ElbaabHomePageHeader(title: appLocal.myOrders),
      body: DefaultTabController(
        length: 3,
        child: Scaffold(
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(48),
            child: AppBar(
              automaticallyImplyLeading: false,
              toolbarHeight: 0,
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(48),
                child: Obx(
                  () => TabBar(
                    indicator: BoxDecoration(
                      borderRadius: BorderRadius.circular(0),
                      color: AppColors.colotJumbo,
                    ),
                    isScrollable: true,
                    tabAlignment: TabAlignment.start,
                    indicatorPadding:
                        const EdgeInsets.symmetric(horizontal: -20),
                    labelColor: AppColors.colorPrimary,
                    unselectedLabelColor: Colors.white.withOpacity(0.6),
                    indicatorColor: AppColors.colorBlue,
                    labelStyle: FontStyles.fontSemibold(),
                    unselectedLabelStyle: FontStyles.fontRegular(),
                    tabs: [
                      Tab(
                        text: appLocal.pendingOrderTab(
                          controller.pendingOrderCount.value == 0 &&
                                  controller.newReturnOrderCount.value == 0
                              ? ''
                              : '( ${controller.formatNumber(controller.pendingOrderCount.value + controller.newReturnOrderCount.value)} )',
                        ),
                      ),
                      Tab(
                        text: appLocal.inProgressOrderTab(
                          controller.inProgressOrderCount.value == 0 &&
                                  controller.shippedOrderCount.value == 0
                              ? ''
                              : '( ${controller.formatNumber(controller.inProgressOrderCount.value + controller.shippedOrderCount.value)} )',
                        ),
                      ),

                      Tab(
                        text: appLocal.historyOrderTab(controller.deliveredOrderCount.value == 0 && controller.canceledOrderCount.value == 0 && controller.returnedOrderCount.value == 0 ? '' : '( ${controller.formatNumber(controller.returnedOrderCount.value + controller.canceledOrderCount.value + controller.deliveredOrderCount.value)} )',
                        ),
                      ),],
                  ),
                ),
              ),
            ),
          ),
          body: const TabBarView(
            physics: NeverScrollableScrollPhysics(),
            children: [PendingOrder(), InProgressOrders(), HistoryOrders()],
          ),
        ),
      ),
    );
  }

  checkOrderUpdates(BuildContext context, OrdersController controller) {
    if (Provider.of<UpdatedInfo>(context).orderRecived) {
      Future.delayed(Duration.zero, () {
        Provider.of<UpdatedInfo>(context, listen: false).isRecivedOrder(false);
      });
    }
  }
}
