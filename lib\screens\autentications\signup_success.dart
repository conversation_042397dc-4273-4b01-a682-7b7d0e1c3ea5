import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/login_model.dart';
import 'package:overolasuppliers/provider/shop_info_provider.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../helper/constant/constants.dart';

class SignupSuccess extends StatelessWidget implements ServerResponse {
  SignupSuccess({super.key});

  late GraphQlInitilize _request;

  @override
  Widget build(BuildContext context) {
    _request = GraphQlInitilize(this);
    final appLocal = AppLocalizations.of(context)!;
    FocusManager.instance.primaryFocus?.unfocus();
    return Scaffold(
      appBar: ElbaabHeader(
        title: "",
        trailingWidget: IconButton(
          onPressed: () => _checkStatus(context),
          icon: const Icon(Icons.refresh),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            SvgPicture.string(SvgStrings.signupSuccess),
            const SizedBox(height: 30),
            Text(appLocal.signupSuccessMessage1,
                textAlign: TextAlign.center,
                style: FontStyles.fontRegular(
                  color: Colors.white.withOpacity(0.5),
                  fontSize: 14,
                  height: 2,
                )),
            const SizedBox(height: 30),
            Text(
              appLocal.signupSuccessMessage2,
              style: FontStyles.fontMedium(
                color: AppColors.colorPrimary,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: SizedBox(
        height: 90,
        child: Center(
          child: InkWell(
            onTap: () => Alerts.alertView(
                context: context,
                title: appLocal.alert,
                content: appLocal.logoutMessage,
                cancelActionText: appLocal.no,
                defaultActionText: appLocal.yes,
                cancelAction: () => Get.back(),
                action: () {
                  Get.back();
                  _request.runMutation(
                    context: context,
                    query: GraphQlQuries.removeFireBaseToken,
                    type: "logout",
                    variables: {"firebaseDeviceToken": firebaseDeviceToken},
                  );
                }),
            child: Text(
              appLocal.logout,
              style: FontStyles.fontRegular(color: AppColors.colorPrimary),
            ),
          ),
        ),
      ),
    );
  }

  _checkStatus(BuildContext context) {
    supplierID = prefs.getString(merchantID) ?? "";
    Provider.of<ShopInfoProvider>(context, listen: false)
        .setShopUserName(prefs.getString(ownerName) ?? "");
    userAuthToken = prefs.getString(authToken) ?? "";
    if (prefs.getBool(requiredLogin) ?? false) {
      String email = prefs.getString(signupEmail) ?? "";
      String password = prefs.getString(signupPassword) ?? "";
      _request.runMutation(
          context: context,
          query: GraphQlQuries.login,
          variables: GraphQlVariables.login(
              email: email,
              password: password,
              lang: Get.locale?.languageCode ?? "en"));
    } else if (supplierID.isNotEmpty) {
      Future.delayed(const Duration(seconds: 3)).then((value) {
        Get.offNamed(RouteNames.shopHomeScreen);
      });
    } else {
      Future.delayed(const Duration(seconds: 3)).then((value) {
        Get.offNamed(RouteNames.loginScreen);
      });
    }
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == "logout") {
      GlobalMethods.logout();
    } else {
      String email = prefs.getString(signupEmail) ?? "";
      String password = prefs.getString(signupPassword) ?? "";
      GlobalMethods.checkLoginStatus(LoginModel.fromJson(response),
          email: email, password: password);
    }
  }
}
