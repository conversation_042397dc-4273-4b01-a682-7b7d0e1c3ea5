class DashboardModel {
  final String? shopName;
  final dynamic totalRevenue;
  final dynamic totalSoldItems;
  final dynamic ordersCount;
  final dynamic productCount;
  final int? status;
  final String? message;

  DashboardModel({
    this.shopName,
    this.totalRevenue,
    this.totalSoldItems,
    this.ordersCount,
    this.productCount,
    this.status,
    this.message,
  });

  DashboardModel.fromJson(Map<String, dynamic> json)
      : shopName = json['shopName'] as String?,
        totalRevenue = json['totalRevenue'],
        totalSoldItems = json['totalSoldItems'],
        ordersCount = json['ordersCount'],
        productCount = json['productCount'],
        status = json['status'] as int?,
        message = json['message'] as String?;

  Map<String, dynamic> toJson() => {
        'shopName': shopName,
        'totalRevenue': totalRevenue,
        'totalSoldItems': totalSoldItems,
        'ordersCount': ordersCount,
        'productCount': productCount,
        'status': status,
        'message': message
      };
}
