import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/dio/dio_client_network.dart';
import 'package:overolasuppliers/helper/other/popup_loader_component.dart';
import 'package:overolasuppliers/main.dart';

class DioApiServices {
  Future<dynamic> getRequest(
    String uri, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      var response = await serviceLocatorInstance<DioClientNetwork>().dio.get(
            uri,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onReceiveProgress: onReceiveProgress,
          );
      log("response $response");
      return response.data;
    } on DioError catch (err) {
      log("response $err");
      return _returnDioErrorResponse(err);
    }
  }

  Future<dynamic> postRequest(
    String uri, {
    data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? authToken,
    bool requiredProgressLoader = false,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      BuildContext? buildContext = MyApp.navigatorKey.currentContext;
      if (requiredProgressLoader) {
        PopupLoader.showProgressLoadingDialog(buildContext);
      } else {
        PopupLoader.showLoadingDialog(buildContext);
      }
      if (authToken != null) {
        serviceLocatorInstance<DioClientNetwork>()
            .dio
            .options
            .headers["authorization"] = "Bearer $authToken";
      }
      Response response =
          await serviceLocatorInstance<DioClientNetwork>().dio.post(
                uri,
                data: data,
                options: Options(
                  followRedirects: false,
                  validateStatus: (status) => true,
                ),
                queryParameters: queryParameters,
                cancelToken: cancelToken,
                onSendProgress: onSendProgress,
                onReceiveProgress: onReceiveProgress,
              );
      PopupLoader.hideLoadingDialog();
      log("response $response");
      return response.data;
    } on DioError catch (err) {
      log("error $err");
      PopupLoader.hideLoadingDialog();

      return _returnDioErrorResponse(err);
    }
  }

  Future<dynamic> putRequest(
    String uri, {
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      Response response =
          await serviceLocatorInstance<DioClientNetwork>().dio.put(
                uri,
                data: data,
                queryParameters: queryParameters,
                options: options,
                cancelToken: cancelToken,
                onSendProgress: onSendProgress,
                onReceiveProgress: onReceiveProgress,
              );
      log("response $response");
      return response.data;
    } on DioError catch (err) {
      log("response $err");
      return _returnDioErrorResponse(err);
    }
  }

  Future<dynamic> deleteRequest(
    String uri, {
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      Response response =
          await serviceLocatorInstance<DioClientNetwork>().dio.delete(
                uri,
                data: data,
                queryParameters: queryParameters,
                options: options,
                cancelToken: cancelToken,
              );
      log("response $response");
      return response.data;
    } on DioError catch (err) {
      log("response $err");
      return _returnDioErrorResponse(err);
    }
  }

  dynamic _returnDioErrorResponse(DioError error) {
    if (error.type == DioErrorType.connectionTimeout) {
      throw FetchDataException('Error connection timeout');
    }
    switch (error.response?.statusCode) {
      case 400:
        throw BadRequestException(
            error.response!.statusMessage ?? "Bad request");
      case 401:
        throw UnauthorisedException(
            "Unauthorised request: ${error.response!.statusCode}");
      case 403:
        throw UnauthorisedException(
            "Access forbidden: ${error.response!.statusCode}");
      case 404:
        throw FetchDataException(
            "Api not found: ${error.response!.statusCode}");
      case 500:
      default:
        throw FetchDataException(
            'Error occured while Communication with Server ');
    }
  }
}

class CustomException implements Exception {
  final _message;

  CustomException([this._message]);

  @override
  String toString() {
    return "$_message";
  }
}

class FetchDataException extends CustomException {
  FetchDataException([String? message]) : super(message);
}

class BadRequestException extends CustomException {
  BadRequestException([message]) : super(message);
}

class UnauthorisedException extends CustomException {
  UnauthorisedException([message]) : super(message);
}

class InvalidInputException extends CustomException {
  InvalidInputException([String? message]) : super(message);
}
