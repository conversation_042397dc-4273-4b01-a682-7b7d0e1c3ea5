import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/add_product/product_variation_model.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/screens/products/add_product/views/components/image_slider_widget.dart';
import 'package:overolasuppliers/screens/products/add_product/views/components/review_details.dart';
import 'package:overolasuppliers/screens/products/add_product/views/components/review_policy.dart';
import 'package:overolasuppliers/screens/products/add_product/views/components/review_product_info.dart';
import 'package:overolasuppliers/screens/products/add_product/views/components/review_shipment_detail.dart';
import 'package:overolasuppliers/widgets/elbaab_border_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';

class AddProductStepFive extends GetView<AddProductController>
    implements ServerResponse {
  late GraphQlInitilize _request;
  RxString strError = "".obs;
  bool requiredUpdate = true;

  AddProductStepFive({super.key});

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    chechkUpdates();
    _request = GraphQlInitilize(this);
    return SingleChildScrollView(
      controller: controller.stepperHideController,
      child: Column(
        children: <Widget>[
          Container(
            color: AppColors.headerColorDark,
            margin: const EdgeInsets.only(top: 10),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: IconButton(
                    onPressed: () =>
                        controller.changeTab(isNext: false, isJumpPage: 1),
                    icon: Container(
                      height: 24,
                      width: 24,
                      decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.06),
                          shape: BoxShape.circle),
                      child: Center(
                        child: SvgPicture.string(SvgStrings.iconEditGray),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: MediaQuery.of(context).size.width +
                      ((MediaQuery.of(context).size.width / 3) +
                          (controller.colorList.isNotEmpty ? 120 : 0)),
                  child: ImageSliderWidget(controller: controller),
                ),
                if (controller.colorList.isEmpty)
                  Container(
                    height: 70,
                    margin:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                    child: ListView.builder(
                        itemCount: controller.sliderImages.length,
                        scrollDirection: Axis.horizontal,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return Container(
                            height: 70,
                            width: 70,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5)),
                            margin: const EdgeInsets.only(right: 12),
                            child: GlobalMethods.netWorkImage(
                              controller.sliderImages[index],
                              BorderRadius.circular(5),
                              BoxFit.contain,
                            ),
                          );
                        }),
                  ),
              ],
            ),
          ),
          ReviewProductInfo(controller: controller),
          ReviewDetails(controller: controller),
          ReviewShipmentDetail(controller: controller),
          ReviewPolicy(controller: controller),
          ElbaabNetworkEroor(strError: strError),
          ElbaabBorderButtonWidget(
            edgeInsets: EdgeInsets.only(
                top: 24,
                left: 30,
                right: 30,
                bottom:
                    (!requiredUpdate && !controller.isDraftProduct) ? 40 : 0),
            text: appLocal.back,
            onPress: () => {
              if (controller.isDraftProduct && controller.isRequiredRematch)
                {controller.isRequiredRematch = false},
              controller.changeTab(isNext: false, isJumpPage: 5)
            },
          ),
          if (controller.isDraftProduct)
            ElbaabButtonWidget(
              margin: const EdgeInsets.only(
                top: 10,
                left: 30,
                right: 30,
              ),
              colors: AppColors.colorDanger,
              text: appLocal.deleteThisProduct,
              height: 42,
              onPress: () {
                Alerts.alertView(
                    context: context,
                    title: appLocal.alert,
                    content: appLocal.alertMessageBeforeDeleteProduct,
                    defaultActionText: appLocal.yes,
                    action: () {
                      Get.back();
                      _request.runMutation(
                          context: context,
                          query: GraphQlQuries.deleteProductById,
                          variables: GraphQlVariables.deleteProductById(
                              productId: controller.product?.id ?? ""),
                          type: "RemoveProduct");
                    },
                    cancelActionText: appLocal.no,
                    cancelAction: () => Get.back());
              },
            ),
          if (controller.isDraftProduct && checkDraftUpdate())
            ElbaabButtonWidget(
              margin: const EdgeInsets.only(
                top: 10,
                left: 30,
                right: 30,
              ),
              colors: AppColors.colorSecondaryYellow,
              text: appLocal.updateDraftProduct,
              textColor: Colors.black,
              height: 42,
              onPress: () {
                controller.uploadProduct(
                  context,
                  isSaveForLater: false,
                  publishNow: false,
                  request: _request,
                );
              },
            ),
          if (requiredUpdate)
            Padding(
              padding: EdgeInsets.only(
                  top: 10,
                  left: 30,
                  right: 30,
                  bottom: controller.product != null &&
                          !controller.isSimilarProduct &&
                          !controller.isDraftProduct
                      ? 30
                      : 0),
              child: ElbaabGradientButtonWidget(
                onPress: () {
                  checkImagesForS3Bucket();
                  if (controller.isSimilarProduct) {
                    controller.uploadMatchProduct(context,
                        isSaveForLater: false,
                        publishNow: true,
                        request: _request);
                  } else if (controller.isDraftProduct &&
                      !chechkUpdates(isDetailUpdate: true)) {
                    controller.publishSavedForlaterProduct(
                        context, controller.product?.id ?? "", _request);
                  } else {
                    if (controller.product != null) {
                      if (!requiredUpdate) {
                        Get.offAllNamed(RouteNames.shopHomeScreen);
                      } else {
                        controller.uploadProduct(
                          context,
                          isSaveForLater: false,
                          publishNow: true,
                          request: _request,
                        );
                      }
                    } else {
                      controller.uploadProduct(
                        context,
                        isSaveForLater: false,
                        publishNow: true,
                        request: _request,
                      );
                    }
                  }
                },
                text: controller.product != null &&
                        !controller.isSimilarProduct &&
                        !controller.isDraftProduct
                    ? appLocal.updateNow
                    : appLocal.publishNow,
              ),
            ),
          SizedBox(
            height: controller.isDraftProduct && chechkUpdates() ? 30 : 0,
          ),
          if ((controller.product == null) ||
              controller.isSimilarProduct ||
              controller.isDraftProduct && !chechkUpdates())
            Padding(
              padding: const EdgeInsets.only(
                  top: 10, left: 30, right: 30, bottom: 30),
              child: ElbaabBorderButtonWidget(
                onPress: () {
                  checkImagesForS3Bucket();
                  if (controller.isSimilarProduct) {
                    controller.uploadMatchProduct(
                      context,
                      isSaveForLater: true,
                      publishNow: false,
                      request: _request,
                    );
                  } else {
                    controller.uploadProduct(
                      context,
                      isSaveForLater: true,
                      publishNow: false,
                      request: _request,
                    );
                  }
                },
                text: appLocal.saveForLater,
              ),
            ),
        ],
      ),
    );
  }

  @override
  onError(error, String type) {
    BaseModel model = BaseModel.fromJson(error);
    strError.value = AppLocalizations.of(Get.context!)?.localeName == "en"
        ? model.message ?? ""
        : model.arMessage ?? "";
  }

  @override
  void onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    handleResponse(model, type);
  }

  void handleResponse(BaseModel model, String type) {
    if (model.status != statusOK) {
      strError.value = AppLocalizations.of(Get.context!)?.localeName == "en"
          ? model.message ?? ""
          : model.arMessage ?? "";
      return;
    }

    if (type == 'RemoveProduct') {
      removeProductPreferences();
      Get.offAllNamed(RouteNames.productUploadScreen, arguments: [
        AppLocalizations.of(Get.context!)?.localeName == "en"
            ? model.message ?? ""
            : model.arMessage ?? "",
        controller.isDraftProduct
      ]);
      return;
    }

    switch (type) {
      case 'updateProduct':
        if (controller.isDraftProduct) {
          controller.publishSavedForlaterProduct(
              Get.context!, controller.product?.id ?? "", _request);
        } else {
          removeProductPreferences();
          Get.offAllNamed(RouteNames.productUploadScreen, arguments: [
            AppLocalizations.of(Get.context!)?.localeName == "en"
                ? model.message ?? ""
                : model.arMessage ?? "",
            controller.isDraftProduct
          ]);
        }
        break;
      default:
        removeProductPreferences();
        Get.offAllNamed(RouteNames.productUploadScreen, arguments: [
          AppLocalizations.of(Get.context!)?.localeName == "en"
              ? model.message ?? ""
              : model.arMessage ?? "",
          controller.isDraftProduct
        ]);
    }
  }

  void removeProductPreferences() {
    prefs.remove(productInformation);
    prefs.remove(productDetails);
    prefs.remove(productShipment);
    prefs.remove(productPolicies);
  }

  bool checkDraftUpdate() {
    String info = prefs.getString(productInformation) ?? "";
    String details = prefs.getString(productDetails) ?? "";
    String shipment = prefs.getString(productShipment) ?? "";
    String policies = prefs.getString(productPolicies) ?? "";
    if (info.isEmpty &&
        details.isEmpty &&
        shipment.isEmpty &&
        policies.isEmpty) {
      return false;
    } else {
      return true;
    }
  }

  chechkUpdates({bool isDetailUpdate = false}) {
    String info = prefs.getString(productInformation) ?? "";
    String details = prefs.getString(productDetails) ?? "";
    String shipment = prefs.getString(productShipment) ?? "";
    String policies = prefs.getString(productPolicies) ?? "";
    if (info.isEmpty &&
        details.isEmpty &&
        shipment.isEmpty &&
        policies.isEmpty &&
        controller.product != null &&
        (!controller.isDraftProduct)) {
      requiredUpdate = false;
    }
    if (controller.product != null && controller.arrVariations.isNotEmpty) {
      for (Variants element in controller.arrVariations) {
        int indexWhere = (element.variations ?? [])
            .indexWhere((element) => element.isVariantModified == true);
        if (indexWhere != -1) {
          requiredUpdate = true;
        }
      }
    }
    if (info.isNotEmpty ||
        details.isNotEmpty ||
        shipment.isNotEmpty ||
        policies.isNotEmpty) {
      requiredUpdate = true;
      return true;
    } else if (controller.isDraftProduct && !isDetailUpdate) {
      requiredUpdate = true;
      return true;
    } else {
      requiredUpdate = false;
      return false;
    }
  }

  void checkImagesForS3Bucket() {
    if (controller.uploadedImages.isNotEmpty) {
      List<String> arrImages = [];
      if (controller.colorList.isNotEmpty) {
        for (String image in controller.uploadedImages) {
          if (!(controller.colorList.any((element) =>
              element.imagesUrl.any((img) => img == image) ||
              element.thumnailUrl == image))) {
            arrImages.add(image);
          }
        }
      } else {
        for (var element in controller.uploadedImages) {
          if (!controller.sliderImages.contains(element)) {
            arrImages.add(element);
          }
        }
      }
      if (arrImages.isNotEmpty) {
        GlobalMethods.deleteFilesFromS3Bucket(
            files: arrImages, request: _request);
      }
    }
  }
}
