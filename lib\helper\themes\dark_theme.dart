import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

ThemeData darkTheme = ThemeData(
  brightness: Brightness.dark,
  scaffoldBackgroundColor: AppColors.backgroundColorDark,
  appBarTheme: AppBarTheme(
    //brightness: Brightness.dark,
    backgroundColor: AppColors.headerColorDark,
    elevation: 0,
    titleTextStyle: FontStyles.fontRegular(fontSize: 17),
  ),
  hintColor: Colors.white.withOpacity(0.3),
  colorScheme: ColorScheme.dark(
    background: AppColors.backgroundColorDark,
    primary: AppColors.colorPrimary,
    onError: AppColors.colorDanger,
  ),
);
