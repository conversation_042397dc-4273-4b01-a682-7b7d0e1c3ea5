# Overola Suppliers - Desktop Development Guide

This guide provides comprehensive instructions for setting up, developing, and distributing the Overola Suppliers application across Windows, macOS, and Linux desktop platforms.

## 🚀 Quick Start

### Prerequisites
1. **Flutter SDK** (latest stable version)
2. **Platform-specific tools**:
   - **Windows**: Visual Studio 2022 with C++ tools
   - **macOS**: Xcode 12.0+
   - **Linux**: GTK 3.0 development libraries

### Enable Desktop Support
```bash
flutter config --enable-windows-desktop
flutter config --enable-macos-desktop
flutter config --enable-linux-desktop
```

### Run on Desktop
```bash
flutter run -d windows    # Windows
flutter run -d macos      # macOS
flutter run -d linux      # Linux
```

## 📁 Project Structure

The project has been configured with desktop-specific components:

- **Platform folders**: `windows/`, `macos/`, `linux/`
- **Responsive widgets**: `lib/widgets/responsive/`
- **Platform utilities**: `lib/helper/platform/`
- **Build scripts**: `scripts/`
- **Installers**: `installer/`
- **Tests**: `test/desktop_test_helper.dart`

## 🔧 Development Workflow

### 1. Platform Detection
```dart
import 'package:overolasuppliers/helper/platform/platform_helper.dart';

if (PlatformHelper.isDesktop) {
  // Desktop-specific code
} else if (PlatformHelper.isMobile) {
  // Mobile-specific code
}
```

### 2. Responsive UI
```dart
import 'package:overolasuppliers/widgets/responsive/responsive_layout.dart';

ResponsiveLayout(
  mobile: MobileWidget(),
  desktop: DesktopWidget(),
)
```

### 3. Conditional Features
```dart
// Camera support (mobile only)
if (PlatformHelper.hasCameraSupport) {
  await openCamera();
} else {
  await pickImageFile(); // Desktop alternative
}
```

## 🏗️ Building Applications

### Automated Build
```bash
# Unix systems (macOS, Linux)
./scripts/build_desktop.sh

# Windows
./scripts/build_desktop.bat
```

### Manual Build
```bash
flutter build windows --release
flutter build macos --release
flutter build linux --release
```

## 📦 Creating Installers

### Windows (NSIS)
```bash
cd installer/windows
makensis installer.nsi
```

### macOS (DMG)
```bash
cd installer/macos
./create_dmg.sh
```

### Linux (AppImage)
```bash
cd installer/linux
./create_appimage.sh
```

## 🧪 Testing

### Platform Tests
```bash
flutter test test/platform_test.dart
```

### Cross-Platform Testing
```dart
// Use desktop test helper
DesktopTestHelper.setUpDesktopEnvironment();
await DesktopTestHelper.testResponsiveLayout(tester);
DesktopTestHelper.tearDown();
```

## 📱 Platform-Specific Dependencies

### Desktop-Compatible ✅
- UI components (flutter_svg, google_fonts, etc.)
- Networking (dio, graphql)
- Storage (shared_preferences, path_provider)
- Firebase (with desktop support)

### Mobile-Only ⚠️
- Camera, GPS, Biometrics
- SMS, Push notifications
- Native maps, App settings

See `PLATFORM_DEPENDENCIES.md` for complete list.

## 🎨 UI Adaptations

### Navigation
- **Mobile**: Bottom navigation bar or drawer
- **Desktop**: Navigation rail or tab bar

### Layout
- **Mobile**: Single column, touch-optimized
- **Desktop**: Multi-column, mouse/keyboard optimized

### Typography
- **Mobile**: Standard font sizes
- **Desktop**: Slightly larger fonts for readability

## 🔧 Configuration Files

### Windows (`windows/CMakeLists.txt`)
- CMake build configuration
- Windows-specific settings
- Plugin integration

### macOS (`macos/Runner/Info.plist`)
- App metadata and permissions
- macOS-specific settings
- Bundle configuration

### Linux (`linux/CMakeLists.txt`)
- GTK dependencies
- Linux-specific settings
- Application metadata

## 🚀 Distribution

### Windows
1. **Microsoft Store**: Use MSIX packaging
2. **Direct Download**: NSIS installer
3. **Package Managers**: Chocolatey, Scoop

### macOS
1. **Mac App Store**: Xcode archive and upload
2. **Direct Download**: Signed and notarized DMG
3. **Package Managers**: Homebrew Cask

### Linux
1. **AppImage**: Universal Linux package
2. **Package Repositories**: .deb, .rpm packages
3. **Universal Packages**: Flatpak, Snap

## 🛠️ Development Tools

### Recommended IDEs
- **Visual Studio Code** with Flutter extension
- **Android Studio** with Flutter plugin
- **IntelliJ IDEA** with Flutter plugin

### Debugging
```bash
flutter run -d windows --debug
flutter logs
```

### Performance Profiling
```bash
flutter run --profile
# Use Flutter DevTools for analysis
```

## 🔒 Security Considerations

### Code Signing
- **Windows**: Use Windows SDK signing tools
- **macOS**: Use Xcode or codesign command
- **Linux**: GPG signing for packages

### Permissions
- Request minimal permissions
- Handle permission denials gracefully
- Document required permissions

## 📊 Analytics and Monitoring

### Desktop Analytics
- Firebase Analytics (desktop support)
- Custom event tracking
- Performance monitoring

### Crash Reporting
- Firebase Crashlytics (desktop support)
- Custom error handling
- User feedback collection

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Desktop Build
on: [push, pull_request]
jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter build ${{ matrix.platform }}
```

## 📚 Additional Resources

- [Flutter Desktop Documentation](https://docs.flutter.dev/desktop)
- [Platform-Specific Dependencies](PLATFORM_DEPENDENCIES.md)
- [Detailed Setup Guide](DESKTOP_SETUP.md)
- [Testing Documentation](test/README.md)

## 🤝 Contributing

1. Follow platform-specific coding standards
2. Test on all target platforms
3. Update documentation for new features
4. Use responsive design principles

## 📞 Support

For desktop-specific issues:
1. Check the troubleshooting section in `DESKTOP_SETUP.md`
2. Review platform-specific logs
3. Test on multiple desktop environments
4. Report issues with platform details

---

**Note**: This desktop setup maintains compatibility with existing mobile functionality while adding comprehensive desktop support. All mobile features continue to work, with graceful fallbacks on desktop platforms.
