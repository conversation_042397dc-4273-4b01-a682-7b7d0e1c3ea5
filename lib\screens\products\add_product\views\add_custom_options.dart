import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/view_prduct/product_detail_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/screens/products/add_product/model/product_detail_model.dart';
import 'package:overolasuppliers/widgets/elbaab_carousel_feild_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_dotted_border_container.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:translator/translator.dart';

class AddCustomOptions extends StatefulWidget {
  const AddCustomOptions({Key? key}) : super(key: key);

  @override
  State<AddCustomOptions> createState() => _AddCustomOptionsState();
}

class _AddCustomOptionsState extends State<AddCustomOptions>
    with InputValidationUtil {
  final controller = Get.find<AddProductController>();
  // final textController = TextEditingController();

  var focusNode = FocusNode();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  List<CustomOption> arrCustomOption = [CustomOption('', '', '', "", [], '')];
  List<CustomOption> arrList = [];
  String value = '';
  RxString strValuesError = ''.obs;
  List<int> returnOptionsIndex = [];
  int editIndex = -1;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    if (controller.customOptionList.isNotEmpty) {
      arrList.addAll(controller.customOptionList);
      if (arrList.length ==
          (controller.colorList.isNotEmpty &&
                  controller.size.value.sizes.isNotEmpty
              ? 2
              : controller.colorList.isNotEmpty
                  ? 3
                  : controller.size.value.sizes.isNotEmpty
                      ? 3
                      : 4)) {
        arrCustomOption.clear();
      }
    }
    if (controller.product?.productOptions?.productCustomOptions?.isNotEmpty ??
        false) {
      for (var i = 0;
          i < (controller.validationHistory?.returnValues?.length ?? 0);
          i++) {
        String returnValue = controller.validationHistory?.returnValues?[i];
        if (returnValue.contains("custom option title")) {
          returnValue = returnValue.split("custom").first;
          int returnIndex =
              int.parse(returnValue.replaceAll(RegExp(r'[^0-9]'), '')) - 1;
          if (returnIndex >= 0) {
            if (returnIndex <= (arrList.length - 1)) {
              ProductCustomOptions? customOptions = controller
                  .product?.productOptions?.productCustomOptions?[returnIndex];
              if (customOptions?.optionTitle == arrList[returnIndex].title) {
                if (!returnOptionsIndex.contains(returnIndex)) {
                  controller.returnOptionsTitles
                      .add((customOptions?.optionTitle ?? "").trimRight());
                  returnOptionsIndex.add(returnIndex);
                }
              }
            }
          }
        }
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
    _debounce?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    var size = MediaQuery.of(context).size;
    const double itemHeight = 70;
    final double itemWidth = size.width / 2;
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.customOption,
        leadingBack: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                ListView.builder(
                    itemCount: arrList.length,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, section) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 16).r,
                        padding: const EdgeInsets.only(left: 10, right: 10).r,
                        decoration: BoxDecoration(
                          color: AppColors.headerColorDark,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: (returnOptionsIndex.contains(section) &&
                                    controller
                                            .customOptionList[section].title ==
                                        arrList[section].title)
                                ? AppColors.colorDanger
                                : Colors.transparent,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                if (returnOptionsIndex.contains(section) &&
                                    controller
                                            .customOptionList[section].title ==
                                        arrList[section].title)
                                  Expanded(
                                    child: Text(
                                      appLocal.somethingWrongOnOption,
                                      style: FontStyles.fontMedium(
                                        fontSize: 12,
                                        color: AppColors.colorDanger,
                                      ),
                                    ),
                                  ),
                                IconButton(
                                  onPressed: () {
                                    if (editIndex == -1) {
                                      setState(() {
                                        arrCustomOption.clear();
                                        arrCustomOption.add(arrList[section]);
                                        arrList.removeAt(section);
                                        editIndex = section;
                                      });
                                    } else {
                                      strValuesError.value =
                                          appLocal.updateThisOptionFirst;
                                    }
                                  },
                                  icon: Container(
                                    decoration: BoxDecoration(
                                      color: AppColors.colorPrimary
                                          .withOpacity(0.12),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    padding: const EdgeInsets.all(5),
                                    child: SvgPicture.string(
                                        SvgStrings.iconEditGray,
                                        color: AppColors.colorPrimary),
                                  ),
                                ),
                                if (!controller.isApprovedProduct &&
                                    !controller.isMatchProduct)
                                  IconButton(
                                    onPressed: () {
                                      Alerts.alertView(
                                          context: context,
                                          content:
                                              appLocal.alertMessageForDelete,
                                          cancelActionText: appLocal.no,
                                          cancelAction: () => Get.back(),
                                          action: () {
                                            returnOptionsIndex.remove(section);
                                            if (controller
                                                    .serverProductCustomOptions
                                                    ?.any((element) =>
                                                        element.optionId ==
                                                        arrList[section]
                                                            .optionId) ??
                                                false) {
                                              controller
                                                  .serverProductCustomOptions
                                                  ?.removeWhere((element) =>
                                                      element.optionId ==
                                                      arrList[section]
                                                          .optionId);
                                            }
                                            setState(() {
                                              arrList.removeAt(section);
                                            });
                                            Get.back();
                                          });
                                    },
                                    icon: Icon(
                                      Icons.cancel,
                                      color: Colors.white.withOpacity(0.42),
                                    ),
                                  ),
                              ],
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              arrList[section].title,
                              style: FontStyles.fontSemibold(
                                color: Colors.white.withOpacity(0.5),
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              arrList[section].titleAr,
                              style: FontStyles.fontSemibold(
                                color: Colors.white.withOpacity(0.5),
                              ),
                            ),
                            SizedBox(height: 16.h),
                            SizedBox(
                              height: 50.h,
                              child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  itemCount: arrList[section].valueList.length,
                                  itemBuilder: (context, index) {
                                    int previousSizeIndex = -1;
                                    if (controller.product != null &&
                                        (controller
                                                .product!
                                                .productOptions
                                                ?.productCustomOptions
                                                ?.isNotEmpty ??
                                            false)) {
                                      if (section <
                                          (controller
                                                  .product!
                                                  .productOptions
                                                  ?.productCustomOptions
                                                  ?.length ??
                                              0)) {
                                        if (index <
                                            (controller
                                                    .product!
                                                    .productOptions
                                                    ?.productCustomOptions?[
                                                        section]
                                                    .optionValues
                                                    ?.length ??
                                                0)) {
                                          previousSizeIndex = arrList[section]
                                              .valueList
                                              .indexWhere((element) =>
                                                  element.value ==
                                                  (controller
                                                          .product!
                                                          .productOptions
                                                          ?.productCustomOptions?[
                                                              section]
                                                          .optionValues?[index]
                                                          .value ??
                                                      ""));
                                        }
                                      }
                                    }
                                    return Container(
                                      margin:
                                          const EdgeInsets.only(right: 12).r,
                                      decoration: BoxDecoration(
                                        color: AppColors.headerColorDark,
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all(
                                          width: 1,
                                          color: AppColors.feildBorderColorDark,
                                        ),
                                      ),
                                      child: InkWell(
                                        onTap: () {
                                          if (controller.isApprovedProduct ||
                                              controller.isMatchProduct) {
                                            if (previousSizeIndex == index) {
                                              int isLastItem = 0;
                                              for (CustomOptionValues options
                                                  in arrList[section]
                                                      .valueList) {
                                                if (options.isValueHidden) {
                                                  isLastItem++;
                                                }
                                              }
                                              if (arrList[section]
                                                  .valueList[index]
                                                  .isValueHidden) {
                                                setState(() {
                                                  arrList[section]
                                                      .valueList[index]
                                                      .isValueHidden = false;
                                                });
                                              } else if (isLastItem ==
                                                  (arrList[section]
                                                          .valueList
                                                          .length -
                                                      1)) {
                                                BottomSheets
                                                    .showAlertMessageBottomSheet(
                                                        appLocal
                                                            .allHideAleartMessage,
                                                        appLocal.alert,
                                                        context);
                                              } else {
                                                BottomSheets
                                                    .showAlertMessageBottomSheet(
                                                        appLocal
                                                            .hideOptionAlertMessgae,
                                                        appLocal.alert,
                                                        context,
                                                        onActionClick: () {
                                                  setState(() {
                                                    arrList[section]
                                                        .valueList[index]
                                                        .isValueHidden = true;
                                                  });
                                                });
                                              }
                                            }
                                          }
                                        },
                                        child: Stack(
                                          children: [
                                            Center(
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                            vertical: 7,
                                                            horizontal: 25)
                                                        .r,
                                                child: Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceEvenly,
                                                      children: [
                                                        Text(
                                                          arrList[section].valueList[index].value,
                                                          style: FontStyles
                                                              .fontMedium(
                                                            fontSize: 12,
                                                            color: Colors.grey,
                                                          ),
                                                        ),
                                                        Text(
                                                          arrList[section].valueList[index].valueAr,
                                                          style: FontStyles
                                                              .fontMedium(
                                                            fontSize: 12,
                                                            color: Colors.grey,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    if (controller
                                                            .isApprovedProduct ||
                                                        controller
                                                            .isMatchProduct)
                                                      SizedBox(width: 12.w),
                                                    if (controller
                                                            .isApprovedProduct ||
                                                        controller
                                                            .isMatchProduct)
                                                      Icon(
                                                        Icons
                                                            .remove_red_eye_sharp,
                                                        size: 18,
                                                        color: Colors.white
                                                            .withOpacity(0.42),
                                                      ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Positioned.fill(
                                              child: AnimatedContainer(
                                                curve: Curves.slowMiddle,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  color: !arrList[section]
                                                          .valueList[index]
                                                          .isValueHidden
                                                      ? Colors.transparent
                                                      : Colors.black
                                                          .withOpacity(0.6),
                                                ),
                                                width: (arrList[section]
                                                        .valueList[index]
                                                        .isValueHidden)
                                                    ? itemWidth.w
                                                    : 0,
                                                height: (arrList[section]
                                                        .valueList[index]
                                                        .isValueHidden)
                                                    ? 50.h
                                                    : 0,
                                                duration:
                                                    const Duration(seconds: 1),
                                                child: (!arrList[section]
                                                        .valueList[index]
                                                        .isValueHidden)
                                                    ? Container()
                                                    : const Icon(
                                                        Icons.visibility_off,
                                                        color: Colors.white,
                                                        size: 20,
                                                      ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  }),
                            ),
                            const SizedBox(height: 24),
                          ],
                        ),
                      );
                    }),
            
             
                if ((!controller.isApprovedProduct &&
                        !controller.isMatchProduct) ||
                    ((arrCustomOption.isNotEmpty) &&
                        arrCustomOption.first.title.isNotEmpty))
                  ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: arrCustomOption.length,
                      itemBuilder: (context, section) {
                        CustomOption option = arrCustomOption[section];
                        var txtTitle =
                            TextEditingController(text: option.title);
                        var txtTitleAr =
                            TextEditingController(text: option.titleAr);
                        var txtValue =
                            TextEditingController(text: option.value);
                        var txtValueAr =
                            TextEditingController(text: option.valueAr);
                        return Container(
                          padding: const EdgeInsets.only(left: 8, right: 8).r,
                          margin: const EdgeInsets.only(bottom: 16).r,
                          decoration: BoxDecoration(
                            color: AppColors.headerColorDark,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              if (controller.isApprovedProduct ||
                                  controller.isMatchProduct)
                                SizedBox(height: 20.h),
                              if (!controller.isApprovedProduct &&
                                  !controller.isMatchProduct)
                                IconButton(
                                  onPressed: () {
                                    Alerts.alertView(
                                        context: context,
                                        content:
                                            appLocal.alertMessageDeleteOption,
                                        action: () {
                                          Get.back();
                                          setState(() {
                                            editIndex = -1;
                                            arrCustomOption.removeAt(section);
                                            arrCustomOption.add(CustomOption(
                                                '', '', '', '', [], ''));
                                          });
                                        },
                                        cancelAction: () => Get.back(),
                                        cancelActionText: appLocal.no);
                                  },
                                  icon: Icon(
                                    Icons.cancel,
                                    color: Colors.white.withOpacity(0.42),
                                  ),
                                ),
                              ElbaabCarouselFeildWidget(
                                aspectRatio: 16.5 / 5.2,
                                children: [
                                  Center(
                                    child: Form(
                                      autovalidateMode: AutovalidateMode.always,
                                      child: ElbaaabInputTextField(
                                        focusNode: appLocal.localeName == 'en'
                                            ? focusNode
                                            : null,
                                        onChanged: (v) =>
                                            option.title = v.trimRight(),
                                        hint: appLocal.customOptionFeildHint,
                                        autovalidateMode:
                                            AutovalidateMode.always,
                                        onFieldSubmitted: (v) {
                                          if (appLocal.localeName == 'en') {
                                            option.titleAr = "";
                                            txtTitleAr.clear();
                                            GoogleTranslator()
                                                .translate(v, to: 'ar')
                                                .then((result) {
                                              option.titleAr = result.text;
                                              txtTitleAr.text = result.text;
                                            });
                                          }
                                        },
                                        editingController: txtTitle,
                                        readOnly:
                                            controller.isApprovedProduct ||
                                                controller.isMatchProduct,
                                        initialValue: txtTitle.text,
                                        label: appLocal
                                            .customOptionFeildLabelEnglish,
                                        charaterlimit: 30,
                                        validator: (v) {
                                          if (v!.length == 1) {
                                            return appLocal
                                                .customOptionFeildErrorValidTitle;
                                          } else {
                                            int index = arrList.indexWhere(
                                                (element) =>
                                                    element.title ==
                                                    v.trimRight());
                                            if (index != -1) {
                                              return appLocal
                                                  .customOptionFeildTitleAlreadyExsist;
                                            } else if (controller
                                                    .returnOptionsTitles
                                                    .isNotEmpty &&
                                                controller.returnOptionsTitles
                                                    .contains(v.trimRight())) {
                                              return appLocal
                                                  .customOptionAdminReturnTitle;
                                            } else {
                                              return null;
                                            }
                                          }
                                        },
                                        inputFormatter: '[a-zA-Z0-9 ]',
                                        inputType: TextInputType.text,
                                      ),
                                    ),
                                  ),
                                  Center(
                                    child: Form(
                                      autovalidateMode: AutovalidateMode.always,
                                      child: ElbaaabInputTextField(
                                        focusNode: appLocal.localeName == 'ar'
                                            ? focusNode
                                            : null,
                                        textDirection: TextDirection.rtl,
                                        onChanged: (v) =>
                                            option.titleAr = v.trimRight(),
                                        onFieldSubmitted: (v) {
                                          if (appLocal.localeName == 'ar') {
                                            option.title = "";
                                            txtTitle.clear();
                                            GoogleTranslator()
                                                .translate(v, to: 'en')
                                                .then((result) {
                                              option.title = result.text;
                                              txtTitle.text = result.text;
                                            });
                                          }
                                        },
                                        editingController: txtTitleAr,
                                        hint: appLocal.customOptionFeildHint,
                                        autovalidateMode:
                                            AutovalidateMode.always,
                                        readOnly:
                                            controller.isApprovedProduct ||
                                                controller.isMatchProduct,
                                        initialValue: txtTitleAr.text,
                                        label: appLocal
                                            .customOptionFeildLabelArabic,
                                        charaterlimit: 30,
                                        validator: (v) {
                                          if (v!.length == 1) {
                                            return appLocal
                                                .customOptionFeildErrorValidTitle;
                                          } else {
                                            int index = arrList.indexWhere(
                                                (element) =>
                                                    element.titleAr ==
                                                    v.trimRight());
                                            if (index != -1) {
                                              return appLocal
                                                  .customOptionFeildTitleAlreadyExsist;
                                            } else if (controller
                                                    .returnOptionsTitles
                                                    .isNotEmpty &&
                                                controller.returnOptionsTitles
                                                    .contains(option.title
                                                        .trimRight())) {
                                              return appLocal
                                                  .customOptionAdminReturnTitle;
                                            } else {
                                              return null;
                                            }
                                          }
                                        },
                                        // inputFormatter: '[a-zA-Z0-9 ]',
                                        inputType: TextInputType.text,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                                                            if (option.valueList.isNotEmpty)

                              SizedBox(height: 16.h),
                              if (option.valueList.isNotEmpty)
                                GridView.builder(
                                    itemCount: option.valueList.length,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 2,
                                            mainAxisSpacing: 8,
                                            crossAxisSpacing: 13,
                                            childAspectRatio:
                                                (itemWidth / itemHeight)),
                                    itemBuilder: (context, index) {
                                      int previousOptionIndex = -1;
                                      if (controller.isApprovedProduct ||
                                          controller.isMatchProduct) {
                                        if (controller.product != null &&
                                            (controller
                                                    .product!
                                                    .productOptions
                                                    ?.productCustomOptions
                                                    ?.isNotEmpty ??
                                                false)) {
                                          int indexWhere = (controller
                                                      .product!
                                                      .productOptions
                                                      ?.productCustomOptions ??
                                                  [])
                                              .indexWhere((element) =>
                                                  element.optionTitle ==
                                                  option.title);

                                          if (indexWhere != -1) {
                                            if (index <
                                                (controller
                                                        .product!
                                                        .productOptions
                                                        ?.productCustomOptions?[
                                                            indexWhere]
                                                        .optionValues
                                                        ?.length ??
                                                    0)) {
                                              List<OptionValues> oldOption =
                                                  controller
                                                          .product!
                                                          .productOptions
                                                          ?.productCustomOptions?[
                                                              indexWhere]
                                                          .optionValues ??
                                                      [];

                                              previousOptionIndex = oldOption
                                                  .indexWhere((element) =>
                                                      element.value ==
                                                      option.valueList[index]
                                                          .value);
                                            }
                                          }
                                        }
                                      }
                                      return Container(
                                        decoration: BoxDecoration(
                                          color: AppColors.headerColorDark,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                            width: 1,
                                            color:
                                                AppColors.feildBorderColorDark,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            const SizedBox(width: 5),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceEvenly,
                                                children: [
                                                  Text(
                                                    option.valueList[index].value,
                                                    // textAlign: TextAlign.center,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 1,
                                                    style:
                                                        FontStyles.fontMedium(),
                                                  ),
                                                  Text(
                                                    " ${option.valueList[index].valueAr}",
                                                    // textAlign: TextAlign.center,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 1,
                                                    style:
                                                        FontStyles.fontMedium(),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            if (previousOptionIndex < 0)
                                              IconButton(
                                                onPressed: () {
                                                  Alerts.alertView(
                                                    context: context,
                                                    content: appLocal
                                                        .alertMessageDeleteOptionValue,
                                                    action: () {
                                                      Get.back();
                                                      setState(() {
                                                        option.valueList
                                                            .removeAt(index);
                                                      });
                                                    },
                                                    cancelAction: () =>
                                                        Get.back(),
                                                    cancelActionText:
                                                        appLocal.no,
                                                  );
                                                },
                                                icon: Icon(
                                                  Icons.cancel,
                                                  color: Colors.white
                                                      .withOpacity(0.42),
                                                ),
                                              ),
                                          ],
                                        ),
                                      );
                                    }),
                            
                              if (option.valueList.length < 30)
                              ElbaabCarouselFeildWidget(

                                aspectRatio: 17 / 5.2,
                                children: [
                                  Center(
                                    child:   TextFormField(
                                            controller: txtValue,
                                            onChanged: (v) {
                                              if (_debounce?.isActive ??
                                                  false) {
                                                _debounce?.cancel();
                                              }
                                              _debounce = Timer(
                                                  const Duration(
                                                      milliseconds: 500), () {
                                                if (appLocal.localeName ==
                                                    "en") {
                                                  txtValueAr.text = "";
                                                  option.valueAr = "";
                                                  GoogleTranslator()
                                                      .translate(v, to: 'ar')
                                                      .then((result) => {
                                                            txtValueAr.text =
                                                                result.text,
                                                            option.valueAr =
                                                                result.text
                                                          });
                                                }
                                                option.value = v;
                                              });
                                            },
                                            textInputAction:
                                                TextInputAction.next,
                                            validator: (v) {
                                              int indexWhere = option.valueList
                                                  .indexWhere((element) =>
                                                      element.value ==
                                                      option.value.trimRight());
                                              if (v!.isEmpty) {
                                                return appLocal.fieldRequired;
                                              } else if (indexWhere == -1) {
                                                if (appLocal.localeName ==
                                                    "en") {
                                                  if (controller
                                                          .product
                                                          ?.productOptions
                                                          ?.productCustomOptions
                                                          ?.isNotEmpty ??
                                                      false) {
                                                    int index = (controller
                                                                .product
                                                                ?.productOptions
                                                                ?.productCustomOptions ??
                                                            [])
                                                        .indexWhere((element) =>
                                                            element.optionId ==
                                                            option.optionId);
                                                    if (index != -1) {
                                                      bool isContain = ((controller
                                                                          .product
                                                                          ?.productOptions
                                                                          ?.productCustomOptions ??
                                                                      [])[index]
                                                                  .optionValues ??
                                                              [])
                                                          .any((element) =>
                                                              element.value ==
                                                              v.trimRight());
                                                      if (isContain) {
                                                        option.valueList.add(
                                                            CustomOptionValues(
                                                                v.trimRight(),
                                                                txtValueAr.text,
                                                                false,
                                                                isReAddedValue:
                                                                    true));
                                                      } else {
                                                        option.valueList.add(
                                                            CustomOptionValues(
                                                                v.trimRight(),
                                                                txtValueAr.text,
                                                                false));
                                                      }
                                                    } else {
                                                      option.valueList.add(
                                                          CustomOptionValues(
                                                              v.trimRight(),
                                                              txtValueAr.text,
                                                              false));
                                                    }
                                                  } else {
                                                    option.valueList.add(
                                                        CustomOptionValues(
                                                            v.trimRight(),
                                                            txtValueAr.text,
                                                            false));
                                                  }

                                                  option.value = '';
                                                  option.valueAr = '';
                                                  txtValueAr.clear();
                                                  txtValue.clear();
                                                  setState(() {});
                                                }
                                              } else {
                                                return appLocal
                                                    .valueAlreadyInList;
                                              }
                                              return null;
                                            },
                                            style: FontStyles.fontRegular(),
                                            decoration: InputDecoration(
                                              fillColor:
                                                  AppColors.feildColorDark,
                                              filled: true,
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                borderSide: BorderSide(
                                                  width: 1,
                                                  color: AppColors
                                                      .feildBorderColor,
                                                ),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                borderSide: const BorderSide(
                                                  width: 1,
                                                  color: Colors.white,
                                                ),
                                              ),
                                              hoverColor: AppColors.colorBlue,
                                              labelText: appLocal
                                                  .customOptionValueFeildLabelEnglish,
                                              labelStyle:
                                                  FontStyles.fontRegular(),
                                            ),
                                            inputFormatters: [
                                              LengthLimitingTextInputFormatter(
                                                  40)
                                            ],
                                            onFieldSubmitted: (val) {
                                              int value = arrCustomOption
                                                  .indexWhere((element) =>
                                                      element.valueList.length <
                                                      2);
                                              if (value >= 0) {
                                                strValuesError.value = appLocal
                                                    .errorAtleastAddtwoValues;
                                              } else if (!arrList.any(
                                                  (element) =>
                                                      element.title ==
                                                      arrCustomOption
                                                          .first.title
                                                          .trimRight())) {
                                                strValuesError.value = "";
                                                setState(() {
                                                  if (arrCustomOption[0]
                                                              .title
                                                              .length <=
                                                          1 ||
                                                      controller
                                                          .returnOptionsTitles
                                                          .contains(
                                                              arrCustomOption[0]
                                                                  .title)) {
                                                    strValuesError.value = appLocal
                                                        .customOptionFeildErrorValidTitle;
                                                    return;
                                                  }
                                                  option.value = '';
                                                  strValuesError.value = '';
                                                  txtValue.clear();

                                                  if (editIndex != -1) {
                                                    if (returnOptionsIndex
                                                        .contains(editIndex)) {
                                                      ProductCustomOptions?
                                                          customOptions =
                                                          controller
                                                                  .product
                                                                  ?.productOptions
                                                                  ?.productCustomOptions?[
                                                              editIndex];
                                                      if (customOptions
                                                                  ?.optionTitle !=
                                                              arrCustomOption
                                                                  .first
                                                                  .title ||
                                                          customOptions
                                                                  ?.optionValues
                                                                  ?.length !=
                                                              arrCustomOption
                                                                  .first
                                                                  .valueList
                                                                  .length) {
                                                        returnOptionsIndex
                                                            .removeWhere(
                                                                (element) =>
                                                                    element ==
                                                                    editIndex);
                                                      }
                                                    }
                                                    arrList.insert(editIndex,
                                                        arrCustomOption[0]);
                                                    editIndex = -1;
                                                  } else {
                                                    arrList.add(
                                                        arrCustomOption[0]);
                                                  }
                                                  arrCustomOption.clear();
                                                  if (arrList.length <
                                                      (controller.colorList
                                                                  .isNotEmpty &&
                                                              controller
                                                                  .size
                                                                  .value
                                                                  .sizes
                                                                  .isNotEmpty
                                                          ? 2
                                                          : controller.colorList
                                                                  .isNotEmpty
                                                              ? 3
                                                              : controller
                                                                      .size
                                                                      .value
                                                                      .sizes
                                                                      .isNotEmpty
                                                                  ? 3
                                                                  : 4)) {
                                                    arrCustomOption.add(
                                                        CustomOption('', '', '',
                                                            '', [], ''));
                                                  }
                                                });
                                                focusNode.requestFocus();
                                              } else {
                                                strValuesError.value = appLocal
                                                    .youHaveAlreadyAddedThisOption;
                                              }
                                            },
                                          ),
                                        
                                  )
                                ,Center(child:    TextFormField(
                                            controller: txtValueAr,
                                            onChanged: (v) {
                                              if (_debounce?.isActive ??
                                                  false) {
                                                _debounce?.cancel();
                                              }
                                              _debounce = Timer(
                                                  const Duration(
                                                      milliseconds: 500), () {
                                                if (appLocal.localeName ==
                                                    "ar") {
                                                  option.value = "";
                                                  txtValue.clear();
                                                  GoogleTranslator()
                                                      .translate(v, to: 'en')
                                                      .then((result) => {
                                                            txtValue.text =
                                                                result.text,
                                                            option.value =
                                                                result.text
                                                          });
                                                }
                                              });
                                              option.valueAr = v;
                                            },
                                            textInputAction:
                                                TextInputAction.next,
                                            textDirection: TextDirection.rtl,
                                            validator: (v) {
                                              int indexWhere = option.valueList
                                                  .indexWhere((element) =>
                                                      element.value ==
                                                      option.value.trimRight());
                                              if (v!.isEmpty) {
                                                return appLocal.fieldRequired;
                                              } else if (indexWhere == -1) {
                                                if (appLocal.localeName ==
                                                    "ar") {
                                                  if (controller
                                                          .product
                                                          ?.productOptions
                                                          ?.productCustomOptions
                                                          ?.isNotEmpty ??
                                                      false) {
                                                    int index = (controller
                                                                .product
                                                                ?.productOptions
                                                                ?.productCustomOptions ??
                                                            [])
                                                        .indexWhere((element) =>
                                                            element.optionId ==
                                                            option.optionId);
                                                    if (index != -1) {
                                                      bool isContain = ((controller
                                                                          .product
                                                                          ?.productOptions
                                                                          ?.productCustomOptions ??
                                                                      [])[index]
                                                                  .optionValues ??
                                                              [])
                                                          .any((element) =>
                                                              element.value ==
                                                              v.trimRight());
                                                      if (isContain) {
                                                        option.valueList.add(
                                                            CustomOptionValues(
                                                                option.value,
                                                                v.trimRight(),
                                                                false,
                                                                isReAddedValue:
                                                                    true));
                                                      } else {
                                                        option.valueList.add(
                                                            CustomOptionValues(
                                                                option.value,
                                                                v.trimRight(),
                                                                false));
                                                      }
                                                    } else {
                                                      option.valueList.add(
                                                          CustomOptionValues(
                                                              option.value,
                                                              v.trimRight(),
                                                              false));
                                                    }
                                                  } else {
                                                    option.valueList.add(
                                                        CustomOptionValues(
                                                            option.value,
                                                            v.trimRight(),
                                                            false));
                                                  }

                                                  option.value = '';
                                                  option.valueAr = '';
                                                  txtValueAr.clear();
                                                  txtValue.clear();
                                                  setState(() {});
                                                }
                                              } else {
                                                return appLocal
                                                    .valueAlreadyInList;
                                              }
                                              return null;
                                            },
                                            style: FontStyles.fontRegular(),
                                            decoration: InputDecoration(
                                              fillColor:
                                                  AppColors.feildColorDark,
                                              filled: true,
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                borderSide: BorderSide(
                                                  width: 1,
                                                  color: AppColors
                                                      .feildBorderColor,
                                                ),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                borderSide: const BorderSide(
                                                  width: 1,
                                                  color: Colors.white,
                                                ),
                                              ),
                                              hoverColor: AppColors.colorBlue,
                                              labelText: appLocal
                                                  .customOptionValueFeildLabelArabic,
                                              labelStyle:
                                                  FontStyles.fontRegular(),
                                            ),
                                            inputFormatters: [
                                              LengthLimitingTextInputFormatter(
                                                  40)
                                            ],
                                            onFieldSubmitted: (val) {
                                              int value = arrCustomOption
                                                  .indexWhere((element) =>
                                                      element.valueList.length <
                                                      2);
                                              if (value >= 0) {
                                                strValuesError.value = appLocal
                                                    .errorAtleastAddtwoValues;
                                              } else if (!arrList.any(
                                                  (element) =>
                                                      element.title ==
                                                      arrCustomOption
                                                          .first.title
                                                          .trimRight())) {
                                                strValuesError.value = "";
                                                setState(() {
                                                  if (arrCustomOption[0]
                                                              .title
                                                              .length <=
                                                          1 ||
                                                      controller
                                                          .returnOptionsTitles
                                                          .contains(
                                                              arrCustomOption[0]
                                                                  .title)) {
                                                    strValuesError.value = appLocal
                                                        .customOptionFeildErrorValidTitle;
                                                    return;
                                                  }

                                                  option.value = '';
                                                  option.valueAr = '';
                                                  // txtValueAr.clear();
                                                  // txtValue.clear();
                                                  // txtTitleAr.clear();
                                                  // txtTitle.clear();

                                                  if (editIndex != -1) {
                                                    if (returnOptionsIndex
                                                        .contains(editIndex)) {
                                                      ProductCustomOptions?
                                                          customOptions =
                                                          controller
                                                                  .product
                                                                  ?.productOptions
                                                                  ?.productCustomOptions?[
                                                              editIndex];
                                                      if (customOptions
                                                                  ?.optionTitle !=
                                                              arrCustomOption
                                                                  .first
                                                                  .title ||
                                                          customOptions
                                                                  ?.optionValues
                                                                  ?.length !=
                                                              arrCustomOption
                                                                  .first
                                                                  .valueList
                                                                  .length) {
                                                        returnOptionsIndex
                                                            .removeWhere(
                                                                (element) =>
                                                                    element ==
                                                                    editIndex);
                                                      }
                                                    }
                                                    arrList.insert(editIndex,
                                                        arrCustomOption[0]);
                                                    editIndex = -1;
                                                  } else {
                                                    arrList.add(
                                                        arrCustomOption[0]);
                                                  }
                                                  arrCustomOption.clear();
                                                  if (arrList.length <
                                                      (controller.colorList
                                                                  .isNotEmpty &&
                                                              controller
                                                                  .size
                                                                  .value
                                                                  .sizes
                                                                  .isNotEmpty
                                                          ? 2
                                                          : controller.colorList
                                                                  .isNotEmpty
                                                              ? 3
                                                              : controller
                                                                      .size
                                                                      .value
                                                                      .sizes
                                                                      .isNotEmpty
                                                                  ? 3
                                                                  : 4)) {
                                                    arrCustomOption.add(
                                                        CustomOption('', '', '',
                                                            '', [], ''));
                                                  }
                                                });
                                                focusNode.requestFocus();
                                              } else {
                                                strValuesError.value = appLocal
                                                    .youHaveAlreadyAddedThisOption;
                                              }
                                            },
                                          ),
                                       )
                                ],
                              ),
                              if (option.valueList.length < 30)
                                 Padding(
                                   padding: const EdgeInsets.symmetric(horizontal: 26,vertical: 10).r,
                                   child: ElbaabDottedBorderContainer(
                                        onTap: () {
                                          if (_formKey.currentState!.validate()) {
                                            if (option.value.isNotEmpty) {
                                              setState(() {
                                                int indexWhere = option.valueList
                                                    .indexWhere((element) =>
                                                        element.value ==
                                                        option.value.trimRight());
                                                if (indexWhere == -1) {
                                                  if (controller
                                                          .product
                                                          ?.productOptions
                                                          ?.productCustomOptions
                                                          ?.isNotEmpty ??
                                                      false) {
                                                    int index = (controller
                                                                .product
                                                                ?.productOptions
                                                                ?.productCustomOptions ??
                                                            [])
                                                        .indexWhere((element) =>
                                                            element.optionId ==
                                                            option.optionId);
                                                    if (index != -1) {
                                                      bool isContain = ((controller
                                                                          .product
                                                                          ?.productOptions
                                                                          ?.productCustomOptions ??
                                                                      [])[index]
                                                                  .optionValues ??
                                                              [])
                                                          .any((element) =>
                                                              element.value ==
                                                              option.value
                                                                  .trimRight());
                                                      if (isContain) {
                                                        option.valueList.add(
                                                            CustomOptionValues(
                                                                option.value
                                                                    .trimRight(),
                                                                option.valueAr
                                                                    .trim(),
                                                                false,
                                                                isReAddedValue:
                                                                    true));
                                                      } else {
                                                        option.valueList.add(
                                                            CustomOptionValues(
                                                                option.value
                                                                    .trimRight(),
                                                                option.valueAr
                                                                    .trim(),
                                                                false));
                                                      }
                                                    } else {
                                                      option.valueList.add(
                                                          CustomOptionValues(
                                                              option.value
                                                                  .trimRight(),
                                                              option.valueAr
                                                                  .trim(),
                                                              false));
                                                    }
                                                  }
                                   
                                                  option.value = '';
                                                  option.valueAr = "";
                                                  txtValueAr.clear();
                                                  txtValue.clear();
                                                  setState(() {});
                                                }
                                                if (option.valueList.length > 1) {
                                                  strValuesError.value = '';
                                                }
                                              });
                                            }
                                          }
                                        },
                                        dotColors: Colors.white.withOpacity(0.2),
                                        padding: EdgeInsets.zero,
                                        height: 40.h,
                                        
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                                  horizontal: 25)
                                              .r,
                                          child: Text(
                                            "Add New Value",
                                            style: FontStyles.fontSemibold(
                                              color: AppColors.colorPrimary,
                                            ),
                                          ),
                                        ),
                                      ),
                                 ),
                                 
                                // Row(
                                //   crossAxisAlignment: CrossAxisAlignment.center,
                                //   mainAxisAlignment: MainAxisAlignment.center,
                                //   children: [
                                //     Expanded(
                                //       child: Column(
                                //         children: [
                                //           TextFormField(
                                //             controller: txtValue,
                                //             onChanged: (v) {
                                //               if (_debounce?.isActive ??
                                //                   false) {
                                //                 _debounce?.cancel();
                                //               }
                                //               _debounce = Timer(
                                //                   const Duration(
                                //                       milliseconds: 500), () {
                                //                 if (appLocal.localeName ==
                                //                     "en") {
                                //                   txtValueAr.text = "";
                                //                   option.valueAr = "";
                                //                   GoogleTranslator()
                                //                       .translate(v, to: 'ar')
                                //                       .then((result) => {
                                //                             txtValueAr.text =
                                //                                 result.text,
                                //                             option.valueAr =
                                //                                 result.text
                                //                           });
                                //                 }
                                //                 option.value = v;
                                //               });
                                //             },
                                //             textInputAction:
                                //                 TextInputAction.next,
                                //             validator: (v) {
                                //               int indexWhere = option.valueList
                                //                   .indexWhere((element) =>
                                //                       element.value ==
                                //                       option.value.trimRight());
                                //               if (v!.isEmpty) {
                                //                 return appLocal.fieldRequired;
                                //               } else if (indexWhere == -1) {
                                //                 if (appLocal.localeName ==
                                //                     "en") {
                                //                   if (controller
                                //                           .product
                                //                           ?.productOptions
                                //                           ?.productCustomOptions
                                //                           ?.isNotEmpty ??
                                //                       false) {
                                //                     int index = (controller
                                //                                 .product
                                //                                 ?.productOptions
                                //                                 ?.productCustomOptions ??
                                //                             [])
                                //                         .indexWhere((element) =>
                                //                             element.optionId ==
                                //                             option.optionId);
                                //                     if (index != -1) {
                                //                       bool isContain = ((controller
                                //                                           .product
                                //                                           ?.productOptions
                                //                                           ?.productCustomOptions ??
                                //                                       [])[index]
                                //                                   .optionValues ??
                                //                               [])
                                //                           .any((element) =>
                                //                               element.value ==
                                //                               v.trimRight());
                                //                       if (isContain) {
                                //                         option.valueList.add(
                                //                             CustomOptionValues(
                                //                                 v.trimRight(),
                                //                                 txtValueAr.text,
                                //                                 false,
                                //                                 isReAddedValue:
                                //                                     true));
                                //                       } else {
                                //                         option.valueList.add(
                                //                             CustomOptionValues(
                                //                                 v.trimRight(),
                                //                                 txtValueAr.text,
                                //                                 false));
                                //                       }
                                //                     } else {
                                //                       option.valueList.add(
                                //                           CustomOptionValues(
                                //                               v.trimRight(),
                                //                               txtValueAr.text,
                                //                               false));
                                //                     }
                                //                   } else {
                                //                     option.valueList.add(
                                //                         CustomOptionValues(
                                //                             v.trimRight(),
                                //                             txtValueAr.text,
                                //                             false));
                                //                   }

                                //                   option.value = '';
                                //                   option.valueAr = '';
                                //                   txtValueAr.clear();
                                //                   txtValue.clear();
                                //                   setState(() {});
                                //                 }
                                //               } else {
                                //                 return appLocal
                                //                     .valueAlreadyInList;
                                //               }
                                //               return null;
                                //             },
                                //             style: FontStyles.fontRegular(),
                                //             decoration: InputDecoration(
                                //               fillColor:
                                //                   AppColors.feildColorDark,
                                //               filled: true,
                                //               border: OutlineInputBorder(
                                //                 borderRadius:
                                //                     BorderRadius.circular(10),
                                //                 borderSide: BorderSide(
                                //                   width: 1,
                                //                   color: AppColors
                                //                       .feildBorderColor,
                                //                 ),
                                //               ),
                                //               focusedBorder: OutlineInputBorder(
                                //                 borderRadius:
                                //                     BorderRadius.circular(10),
                                //                 borderSide: const BorderSide(
                                //                   width: 1,
                                //                   color: Colors.white,
                                //                 ),
                                //               ),
                                //               hoverColor: AppColors.colorBlue,
                                //               labelText: appLocal
                                //                   .customOptionValueFeildLabelEnglish,
                                //               labelStyle:
                                //                   FontStyles.fontRegular(),
                                //             ),
                                //             inputFormatters: [
                                //               LengthLimitingTextInputFormatter(
                                //                   40)
                                //             ],
                                //             onFieldSubmitted: (val) {
                                //               int value = arrCustomOption
                                //                   .indexWhere((element) =>
                                //                       element.valueList.length <
                                //                       2);
                                //               if (value >= 0) {
                                //                 strValuesError.value = appLocal
                                //                     .errorAtleastAddtwoValues;
                                //               } else if (!arrList.any(
                                //                   (element) =>
                                //                       element.title ==
                                //                       arrCustomOption
                                //                           .first.title
                                //                           .trimRight())) {
                                //                 strValuesError.value = "";
                                //                 setState(() {
                                //                   if (arrCustomOption[0]
                                //                               .title
                                //                               .length <=
                                //                           1 ||
                                //                       controller
                                //                           .returnOptionsTitles
                                //                           .contains(
                                //                               arrCustomOption[0]
                                //                                   .title)) {
                                //                     strValuesError.value = appLocal
                                //                         .customOptionFeildErrorValidTitle;
                                //                     return;
                                //                   }
                                //                   option.value = '';
                                //                   strValuesError.value = '';
                                //                   txtValue.clear();

                                //                   if (editIndex != -1) {
                                //                     if (returnOptionsIndex
                                //                         .contains(editIndex)) {
                                //                       ProductCustomOptions?
                                //                           customOptions =
                                //                           controller
                                //                                   .product
                                //                                   ?.productOptions
                                //                                   ?.productCustomOptions?[
                                //                               editIndex];
                                //                       if (customOptions
                                //                                   ?.optionTitle !=
                                //                               arrCustomOption
                                //                                   .first
                                //                                   .title ||
                                //                           customOptions
                                //                                   ?.optionValues
                                //                                   ?.length !=
                                //                               arrCustomOption
                                //                                   .first
                                //                                   .valueList
                                //                                   .length) {
                                //                         returnOptionsIndex
                                //                             .removeWhere(
                                //                                 (element) =>
                                //                                     element ==
                                //                                     editIndex);
                                //                       }
                                //                     }
                                //                     arrList.insert(editIndex,
                                //                         arrCustomOption[0]);
                                //                     editIndex = -1;
                                //                   } else {
                                //                     arrList.add(
                                //                         arrCustomOption[0]);
                                //                   }
                                //                   arrCustomOption.clear();
                                //                   if (arrList.length <
                                //                       (controller.colorList
                                //                                   .isNotEmpty &&
                                //                               controller
                                //                                   .size
                                //                                   .value
                                //                                   .sizes
                                //                                   .isNotEmpty
                                //                           ? 2
                                //                           : controller.colorList
                                //                                   .isNotEmpty
                                //                               ? 3
                                //                               : controller
                                //                                       .size
                                //                                       .value
                                //                                       .sizes
                                //                                       .isNotEmpty
                                //                                   ? 3
                                //                                   : 4)) {
                                //                     arrCustomOption.add(
                                //                         CustomOption('', '', '',
                                //                             '', [], ''));
                                //                   }
                                //                 });
                                //                 focusNode.requestFocus();
                                //               } else {
                                //                 strValuesError.value = appLocal
                                //                     .youHaveAlreadyAddedThisOption;
                                //               }
                                //             },
                                //           ),
                                //           SizedBox(height: 10.h),
                                //           TextFormField(
                                //             controller: txtValueAr,
                                //             onChanged: (v) {
                                //               if (_debounce?.isActive ??
                                //                   false) {
                                //                 _debounce?.cancel();
                                //               }
                                //               _debounce = Timer(
                                //                   const Duration(
                                //                       milliseconds: 500), () {
                                //                 if (appLocal.localeName ==
                                //                     "ar") {
                                //                   option.value = "";
                                //                   txtValue.clear();
                                //                   GoogleTranslator()
                                //                       .translate(v, to: 'en')
                                //                       .then((result) => {
                                //                             txtValue.text =
                                //                                 result.text,
                                //                             option.value =
                                //                                 result.text
                                //                           });
                                //                 }
                                //               });
                                //               option.valueAr = v;
                                //             },
                                //             textInputAction:
                                //                 TextInputAction.next,
                                //             textDirection: TextDirection.rtl,
                                //             validator: (v) {
                                //               int indexWhere = option.valueList
                                //                   .indexWhere((element) =>
                                //                       element.value ==
                                //                       option.value.trimRight());
                                //               if (v!.isEmpty) {
                                //                 return appLocal.fieldRequired;
                                //               } else if (indexWhere == -1) {
                                //                 if (appLocal.localeName ==
                                //                     "ar") {
                                //                   if (controller
                                //                           .product
                                //                           ?.productOptions
                                //                           ?.productCustomOptions
                                //                           ?.isNotEmpty ??
                                //                       false) {
                                //                     int index = (controller
                                //                                 .product
                                //                                 ?.productOptions
                                //                                 ?.productCustomOptions ??
                                //                             [])
                                //                         .indexWhere((element) =>
                                //                             element.optionId ==
                                //                             option.optionId);
                                //                     if (index != -1) {
                                //                       bool isContain = ((controller
                                //                                           .product
                                //                                           ?.productOptions
                                //                                           ?.productCustomOptions ??
                                //                                       [])[index]
                                //                                   .optionValues ??
                                //                               [])
                                //                           .any((element) =>
                                //                               element.value ==
                                //                               v.trimRight());
                                //                       if (isContain) {
                                //                         option.valueList.add(
                                //                             CustomOptionValues(
                                //                                 option.value,
                                //                                 v.trimRight(),
                                //                                 false,
                                //                                 isReAddedValue:
                                //                                     true));
                                //                       } else {
                                //                         option.valueList.add(
                                //                             CustomOptionValues(
                                //                                 option.value,
                                //                                 v.trimRight(),
                                //                                 false));
                                //                       }
                                //                     } else {
                                //                       option.valueList.add(
                                //                           CustomOptionValues(
                                //                               option.value,
                                //                               v.trimRight(),
                                //                               false));
                                //                     }
                                //                   } else {
                                //                     option.valueList.add(
                                //                         CustomOptionValues(
                                //                             option.value,
                                //                             v.trimRight(),
                                //                             false));
                                //                   }

                                //                   option.value = '';
                                //                   option.valueAr = '';
                                //                   txtValueAr.clear();
                                //                   txtValue.clear();
                                //                   setState(() {});
                                //                 }
                                //               } else {
                                //                 return appLocal
                                //                     .valueAlreadyInList;
                                //               }
                                //               return null;
                                //             },
                                //             style: FontStyles.fontRegular(),
                                //             decoration: InputDecoration(
                                //               fillColor:
                                //                   AppColors.feildColorDark,
                                //               filled: true,
                                //               border: OutlineInputBorder(
                                //                 borderRadius:
                                //                     BorderRadius.circular(10),
                                //                 borderSide: BorderSide(
                                //                   width: 1,
                                //                   color: AppColors
                                //                       .feildBorderColor,
                                //                 ),
                                //               ),
                                //               focusedBorder: OutlineInputBorder(
                                //                 borderRadius:
                                //                     BorderRadius.circular(10),
                                //                 borderSide: const BorderSide(
                                //                   width: 1,
                                //                   color: Colors.white,
                                //                 ),
                                //               ),
                                //               hoverColor: AppColors.colorBlue,
                                //               labelText: appLocal
                                //                   .customOptionValueFeildLabelArabic,
                                //               labelStyle:
                                //                   FontStyles.fontRegular(),
                                //             ),
                                //             inputFormatters: [
                                //               LengthLimitingTextInputFormatter(
                                //                   40)
                                //             ],
                                //             onFieldSubmitted: (val) {
                                //               int value = arrCustomOption
                                //                   .indexWhere((element) =>
                                //                       element.valueList.length <
                                //                       2);
                                //               if (value >= 0) {
                                //                 strValuesError.value = appLocal
                                //                     .errorAtleastAddtwoValues;
                                //               } else if (!arrList.any(
                                //                   (element) =>
                                //                       element.title ==
                                //                       arrCustomOption
                                //                           .first.title
                                //                           .trimRight())) {
                                //                 strValuesError.value = "";
                                //                 setState(() {
                                //                   if (arrCustomOption[0]
                                //                               .title
                                //                               .length <=
                                //                           1 ||
                                //                       controller
                                //                           .returnOptionsTitles
                                //                           .contains(
                                //                               arrCustomOption[0]
                                //                                   .title)) {
                                //                     strValuesError.value = appLocal
                                //                         .customOptionFeildErrorValidTitle;
                                //                     return;
                                //                   }

                                //                   option.value = '';
                                //                   option.valueAr = '';
                                //                   // txtValueAr.clear();
                                //                   // txtValue.clear();
                                //                   // txtTitleAr.clear();
                                //                   // txtTitle.clear();

                                //                   if (editIndex != -1) {
                                //                     if (returnOptionsIndex
                                //                         .contains(editIndex)) {
                                //                       ProductCustomOptions?
                                //                           customOptions =
                                //                           controller
                                //                                   .product
                                //                                   ?.productOptions
                                //                                   ?.productCustomOptions?[
                                //                               editIndex];
                                //                       if (customOptions
                                //                                   ?.optionTitle !=
                                //                               arrCustomOption
                                //                                   .first
                                //                                   .title ||
                                //                           customOptions
                                //                                   ?.optionValues
                                //                                   ?.length !=
                                //                               arrCustomOption
                                //                                   .first
                                //                                   .valueList
                                //                                   .length) {
                                //                         returnOptionsIndex
                                //                             .removeWhere(
                                //                                 (element) =>
                                //                                     element ==
                                //                                     editIndex);
                                //                       }
                                //                     }
                                //                     arrList.insert(editIndex,
                                //                         arrCustomOption[0]);
                                //                     editIndex = -1;
                                //                   } else {
                                //                     arrList.add(
                                //                         arrCustomOption[0]);
                                //                   }
                                //                   arrCustomOption.clear();
                                //                   if (arrList.length <
                                //                       (controller.colorList
                                //                                   .isNotEmpty &&
                                //                               controller
                                //                                   .size
                                //                                   .value
                                //                                   .sizes
                                //                                   .isNotEmpty
                                //                           ? 2
                                //                           : controller.colorList
                                //                                   .isNotEmpty
                                //                               ? 3
                                //                               : controller
                                //                                       .size
                                //                                       .value
                                //                                       .sizes
                                //                                       .isNotEmpty
                                //                                   ? 3
                                //                                   : 4)) {
                                //                     arrCustomOption.add(
                                //                         CustomOption('', '', '',
                                //                             '', [], ''));
                                //                   }
                                //                 });
                                //                 focusNode.requestFocus();
                                //               } else {
                                //                 strValuesError.value = appLocal
                                //                     .youHaveAlreadyAddedThisOption;
                                //               }
                                //             },
                                //           ),
                                //         ],
                                //       ),
                                //     ),
                                //     const SizedBox(width: 8),
                                //     ElbaabDottedBorderContainer(
                                //       onTap: () {
                                //         if (_formKey.currentState!.validate()) {
                                //           if (option.value.isNotEmpty) {
                                //             setState(() {
                                //               int indexWhere = option.valueList
                                //                   .indexWhere((element) =>
                                //                       element.value ==
                                //                       option.value.trimRight());
                                //               if (indexWhere == -1) {
                                //                 if (controller
                                //                         .product
                                //                         ?.productOptions
                                //                         ?.productCustomOptions
                                //                         ?.isNotEmpty ??
                                //                     false) {
                                //                   int index = (controller
                                //                               .product
                                //                               ?.productOptions
                                //                               ?.productCustomOptions ??
                                //                           [])
                                //                       .indexWhere((element) =>
                                //                           element.optionId ==
                                //                           option.optionId);
                                //                   if (index != -1) {
                                //                     bool isContain = ((controller
                                //                                         .product
                                //                                         ?.productOptions
                                //                                         ?.productCustomOptions ??
                                //                                     [])[index]
                                //                                 .optionValues ??
                                //                             [])
                                //                         .any((element) =>
                                //                             element.value ==
                                //                             option.value
                                //                                 .trimRight());
                                //                     if (isContain) {
                                //                       option.valueList.add(
                                //                           CustomOptionValues(
                                //                               option.value
                                //                                   .trimRight(),
                                //                               option.valueAr
                                //                                   .trim(),
                                //                               false,
                                //                               isReAddedValue:
                                //                                   true));
                                //                     } else {
                                //                       option.valueList.add(
                                //                           CustomOptionValues(
                                //                               option.value
                                //                                   .trimRight(),
                                //                               option.valueAr
                                //                                   .trim(),
                                //                               false));
                                //                     }
                                //                   } else {
                                //                     option.valueList.add(
                                //                         CustomOptionValues(
                                //                             option.value
                                //                                 .trimRight(),
                                //                             option.valueAr
                                //                                 .trim(),
                                //                             false));
                                //                   }
                                //                 }

                                //                 option.value = '';
                                //                 option.valueAr = "";
                                //                 txtValueAr.clear();
                                //                 txtValue.clear();
                                //                 setState(() {});
                                //               }
                                //               if (option.valueList.length > 1) {
                                //                 strValuesError.value = '';
                                //               }
                                //             });
                                //           }
                                //         }
                                //       },
                                //       dotColors: Colors.white.withOpacity(0.2),
                                //       padding: EdgeInsets.zero,
                                //       height: 55.h,
                                //       child: Padding(
                                //         padding: const EdgeInsets.symmetric(
                                //                 horizontal: 25)
                                //             .r,
                                //         child: Text(
                                //           appLocal.add,
                                //           style: FontStyles.fontSemibold(
                                //             color: AppColors.colorPrimary,
                                //           ),
                                //         ),
                                //       ),
                                //     ),
                                //   ],
                                // ),
                              Obx(
                                () => Padding(
                                  padding: strValuesError.value.isEmpty
                                      ? EdgeInsets.zero
                                      : const EdgeInsets.symmetric(
                                          vertical: 20),
                                  child: Text(
                                    strValuesError.value,
                                    style: FontStyles.fontRegular(
                                      fontSize: 12,
                                      color: AppColors.colorDanger,
                                    ),
                                  ),
                                ),
                              ),
                              // SizedBox(height: 20.h),
                              ElbaabGradientButtonWidget(
                                  onPress: () {
                                    int value = arrCustomOption.indexWhere(
                                        (element) =>
                                            element.valueList.length < 2);
                                    if (value >= 0) {
                                      strValuesError.value =
                                          appLocal.errorAtleastAddtwoValues;
                                    } else if (!arrList.any((element) =>
                                        element.title ==
                                        arrCustomOption.first.title
                                            .trimRight())) {
                                      strValuesError.value = "";

                                      setState(() {
                                        if (arrCustomOption[0].title.length <=
                                                1 ||
                                            controller.returnOptionsTitles
                                                .contains(
                                                    arrCustomOption[0].title)) {
                                          strValuesError.value = appLocal
                                              .customOptionFeildErrorValidTitle;
                                          return;
                                        }
                                        option.value = '';
                                        strValuesError.value = '';
                                        txtValueAr.clear();
                                        txtValue.clear();

                                        if (editIndex != -1) {
                                          if (returnOptionsIndex
                                              .contains(editIndex)) {
                                            ProductCustomOptions?
                                                customOptions = controller
                                                        .product
                                                        ?.productOptions
                                                        ?.productCustomOptions?[
                                                    editIndex];
                                            if (customOptions?.optionTitle !=
                                                    arrCustomOption
                                                        .first.title ||
                                                customOptions?.optionValues
                                                        ?.length !=
                                                    arrCustomOption.first
                                                        .valueList.length) {
                                              returnOptionsIndex.removeWhere(
                                                  (element) =>
                                                      element == editIndex);
                                            }
                                          }
                                          arrList.insert(
                                              editIndex, arrCustomOption[0]);
                                          editIndex = -1;
                                        } else {
                                          arrList.add(arrCustomOption[0]);
                                        }
                                        arrCustomOption.clear();
                                        if (arrList.length <
                                            (controller.colorList.isNotEmpty &&
                                                    controller.size.value.sizes
                                                        .isNotEmpty
                                                ? 2
                                                : controller
                                                        .colorList.isNotEmpty
                                                    ? 3
                                                    : controller.size.value
                                                            .sizes.isNotEmpty
                                                        ? 3
                                                        : 4)) {
                                          arrCustomOption.add(CustomOption(
                                              '', '', '', '', [], ''));
                                        }
                                      });
                                      focusNode.requestFocus();
                                    } else {
                                      strValuesError.value = appLocal
                                          .youHaveAlreadyAddedThisOption;
                                    }
                                  },
                                  text: editIndex == -1
                                      ? appLocal.save
                                      : appLocal.update),
                              const SizedBox(height: 20)
                            ],
                          ),
                        );
                      }),
             
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 20),
          child: ElbaabGradientButtonWidget(
            onPress: () {
              int title = arrList.indexWhere((element) => element.title == '');
              int value =
                  arrList.indexWhere((element) => element.valueList.length < 2);
              if (title >= 0) {
                strValuesError.value = appLocal.errorAddTitleForCustomOption;
              } else if (value >= 0) {
                strValuesError.value = appLocal.errorAtleastAddtwoValue;
              } else if (arrCustomOption.isNotEmpty &&
                  (!arrList.any((element) =>
                      element.title ==
                      arrCustomOption.first.title.trimRight()))) {
                if (controller.returnOptionsTitles
                    .contains(arrCustomOption.first.title)) {
                  return;
                }
                int title = arrCustomOption
                    .indexWhere((element) => element.title == '');
                int value = arrCustomOption
                    .indexWhere((element) => element.valueList.length < 2);

                if (title >= 0 && value >= 0) {
                  Get.back(result: arrList);
                } else if (title < 0 && value < 0) {
                  if (arrCustomOption.first.title.length <= 1) {
                    strValuesError.value =
                        appLocal.customOptionFeildErrorValidTitle;
                    return;
                  } else {
                    if (editIndex == -1) {
                      arrList.add(arrCustomOption.first);
                    } else {
                      arrList.insert(editIndex, arrCustomOption.first);
                    }
                  }
                  if (requiredUpdate()) {
                    Get.back(result: arrList);
                  } else {
                    Get.back();
                  }
                } else {
                  strValuesError.value = appLocal.errorEmptyDetsilsOnAddOption;
                }
              } else {
                Get.back(result: arrList);
              }
            },
            text: appLocal.done,
          ),
        ),
      ),
    );
  }

  bool requiredUpdate() {
    bool requiredUpdate = false;
    if (controller.product != null &&
        (controller.product?.productOptions?.productCustomOptions?.isNotEmpty ??
            false)) {
      if ((controller.product?.productOptions?.productCustomOptions?.length !=
              controller.customOptionList.length) ||
          (controller.product?.productOptions?.productCustomOptions?.length !=
              arrList.length)) {
        requiredUpdate = true;
      } else {
        for (var i = 0;
            i <
                (controller.product?.productOptions?.productCustomOptions ?? [])
                    .length;
            i++) {
          ProductCustomOptions options =
              (controller.product?.productOptions?.productCustomOptions ??
                  [])[i];
          if (options.optionTitle != controller.customOptionList[i].title) {
            requiredUpdate = true;
            break;
          } else if (options.optionTitle ==
                  controller.customOptionList[i].title &&
              options.optionValues?.length !=
                  controller.customOptionList[i].valueList.length) {
            requiredUpdate = true;
            break;
          } else if (options.optionTitle ==
                  controller.customOptionList[i].title &&
              options.optionValues?.length ==
                  controller.customOptionList[i].valueList.length) {
            for (var j = 0; j < (options.optionValues ?? []).length; j++) {
              OptionValues values = (options.optionValues ?? [])[j];
              CustomOptionValues customOptionValues =
                  controller.customOptionList[i].valueList[j];
              if (values.value != customOptionValues.value ||
                  values.isValueHidden != customOptionValues.isValueHidden) {
                requiredUpdate = true;
                break;
              }
            }
          }
        }
      }
    } else if (controller.customOptionList.length != arrList.length) {
      requiredUpdate = true;
    } else if (controller.customOptionList.length == arrList.length) {
      for (var i = 0; i < arrList.length; i++) {
        CustomOption options = arrList[i];
        if (options.title != controller.customOptionList[i].title) {
          requiredUpdate = true;
          break;
        } else if (options.title == controller.customOptionList[i].title &&
            options.valueList.length !=
                controller.customOptionList[i].valueList.length) {
          requiredUpdate = true;
          break;
        } else if (options.title == controller.customOptionList[i].title &&
            options.valueList.length ==
                controller.customOptionList[i].valueList.length) {
          for (var j = 0; j < options.valueList.length; j++) {
            CustomOptionValues values = options.valueList[j];
            CustomOptionValues customOptionValues =
                controller.customOptionList[i].valueList[j];
            if (values.value != customOptionValues.value ||
                values.isValueHidden != customOptionValues.isValueHidden) {
              requiredUpdate = true;
              break;
            }
          }
        }
      }
    }
    return requiredUpdate;
  }
}
