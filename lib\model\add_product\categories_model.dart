class CategoriesModel {
  final String? typename;
  final List<Categories>? categories;
  final int? status;
  final String? message;

  CategoriesModel({
    this.typename,
    this.categories,
    this.status,
    this.message,
  });

  CategoriesModel.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        categories = (json['categories'] as List?)
            ?.map((dynamic e) => Categories.fromJson(e as Map<String, dynamic>))
            .toList(),
        status = json['status'] as int?,
        message = json['message'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'categories': categories?.map((e) => e.toJson()).toList(),
        'status': status,
        'message': message
      };
}

class Categories {
  final String? typename;
  final String? categoryName;
  final String? categoryDarkIcon;
  final String? id;
  final List<SubCategories>? subCategories;
  final List<Brands>? brands;
  final Categories? arCategories;

  Categories(
      {this.typename,
      this.categoryName,
      this.categoryDarkIcon,
      this.subCategories,
      this.brands,
      this.id,
      this.arCategories});

  Categories.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        categoryName = json['categoryName'] as String?,
        id = json['_id'] as String?,
        categoryDarkIcon = json['categoryDarkIcon'] as String?,
        arCategories = (json['ar'] as Map<String, dynamic>?) != null
            ? Categories.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        subCategories = (json['subCategories'] as List?)
            ?.map((dynamic e) =>
                SubCategories.fromJson(e as Map<String, dynamic>))
            .toList(),
        brands = (json['brands'] as List?)
            ?.map((dynamic e) => Brands.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        '_id': id,
        'categoryName': categoryName,
        'categoryDarkIcon': categoryDarkIcon,
        'ar': arCategories?.toJson(),
        'subCategories': subCategories?.map((e) => e.toJson()).toList(),
        'brands': brands?.map((e) => e.toJson()).toList()
      };
}

class SubCategories {
  final String? typename;
  final String? id;
  final String? subCategoryName;
  final String? subCategoryDarkIcon;
  final SubCategories? arSubCategories;

  SubCategories({
    this.typename,
    this.id,
    this.subCategoryName,
    this.subCategoryDarkIcon,
    this.arSubCategories,
  });

  SubCategories.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        id = json['_id'] as String?,
        subCategoryName = json['subCategoryName'] as String?,
        arSubCategories = (json['ar'] as Map<String, dynamic>?) != null
            ? SubCategories.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        subCategoryDarkIcon = json['subCategoryDarkIcon'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        '_id': id,
        'subCategoryName': subCategoryName,
        'ar': arSubCategories?.toJson(),
        'subCategoryDarkIcon': subCategoryDarkIcon
      };
}

class Brands {
  final String? typename;
  final String? brandName;
  final String? brandIcon;
  final String? id;
  final Brands ? arBrands;

  Brands({
    this.typename,
    this.brandName,
    this.brandIcon,
    this.id,
    this.arBrands
  });

  Brands.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        id = json['_id'] as String?,
        brandName = json['brandName'] as String?,
        arBrands = (json['ar'] as Map<String, dynamic>?) != null
            ? Brands.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        brandIcon = json['brandIcon'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        '_id': id,
        'ar': arBrands?.toJson(),
        'brandName': brandName,
        'brandIcon': brandIcon,
      };
}
