import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SelectManufacturers extends GetView<AddProductController>
    with InputValidationUtil {
  String customBrandName = "";
  RxBool validate = false.obs;
  var textFeildController = TextEditingController();

  SelectManufacturers({super.key});
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    controller.filteredBrands.clear();
    controller.filteredBrands.addAll(controller.brandsList);
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      appBar: ElbaabHeader(
        title: controller.brandsList.isNotEmpty
            ? appLocal.chooseBrand
            : appLocal.addYourBrandName,
        trailingWidget: Container(
          height: 36,
          width: 36,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            padding: EdgeInsets.zero,
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close, color: Colors.white, size: 20),
          ),
        ),
      ),
      body: Column(
        children: [
          Container(
            margin: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.colorPrimary.withOpacity(0.2),
                  AppColors.colorPrimary.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: AppColors.colorPrimary.withOpacity(0.1)),
            ),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black26,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 6,
                        offset: Offset(0, 3),
                      ),
                    ],
                  ),
                  child: TextFormField(
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                    onChanged: (value) => customBrandName = value,
                    onFieldSubmitted: (v) {
                      int index = controller.brandsList.indexWhere((element) =>
                          (element.brandName ?? "").toLowerCase() ==
                          v.toLowerCase());
                      if (index == -1) {
                        Get.back(result: jsonEncode([v, v, ""]));
                      } else {
                        Get.back(
                            result: jsonEncode([
                          controller.brandsList[index].brandName,
                          controller.brandsList[index].arBrands?.brandName ??
                              "",
                          controller.brandsList[index].id
                        ]));
                      }
                    },
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      border: InputBorder.none,
                      hintText: appLocal.enterYourBrandName,
                      hintStyle: TextStyle(
                        color: Colors.white.withOpacity(0.5),
                        fontSize: 14,
                      ),
                      prefixIcon: Icon(
                        Icons.add_business_rounded,
                        color: Colors.blue.withOpacity(0.7),
                        size: 20,
                      ),
                    ),
                  ),
                ),
                Obx(() => validate.value
                    ? Container(
                        margin: const EdgeInsets.symmetric(horizontal: 12),
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: AppColors.colorDanger.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          children: [
                             Icon(Icons.error_outline, color: AppColors.colorDanger, size: 14),
                            const SizedBox(width: 6),
                            Text(
                              appLocal.enterYourBrandNameFeildError,
                              style: FontStyles.fontMedium(
                                color: AppColors.colorDanger,
                                fontSize: 11,
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink()),
                if (controller.brandsList.isNotEmpty) ...[
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 12),
                    child: Row(
                      children: [
                        Expanded(child: Divider(color: Colors.white24)),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          child: Icon(Icons.compare_arrows, color: Colors.white54, size: 18),
                        ),
                        Expanded(child: Divider(color: Colors.white24)),
                      ],
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black26,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 6,
                          offset: Offset(0, 3),
                        ),
                      ],
                    ),
                    child: TextFormField(
                      controller: textFeildController,
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                      onChanged: (value) {
                        controller.filteredBrands.clear();
                        if (value.isEmpty) {
                          controller.filteredBrands
                              .addAll(controller.brandsList);
                        } else {
                          for (var element in controller.brandsList) {
                            if ((element.brandName ?? "")
                                .toLowerCase()
                                .contains(value.toLowerCase())) {
                              controller.filteredBrands.add(element);
                            }
                          }
                        }
                      },
                      decoration: InputDecoration(
                        isDense: true,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 14,
                        ),
                        border: InputBorder.none,
                        hintText: appLocal.searchBrandByName,
                        hintStyle: TextStyle(
                          color: Colors.white.withOpacity(0.5),
                          fontSize: 14,
                          height: 1,
                        ),
                        prefixIcon: const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          child: Icon(
                            Icons.search,
                            color: Colors.blue,
                            size: 20,
                          ),
                        ),
                        prefixIconConstraints: const BoxConstraints(
                          minWidth: 40,
                          minHeight: 40,
                        ),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.clear, color: Colors.white54, size: 18),
                          onPressed: () {
                            textFeildController.clear();
                            controller.filteredBrands.clear();
                            controller.filteredBrands.addAll(controller.brandsList);
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Expanded(
            child: Obx(
              () => ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                itemCount: controller.filteredBrands.length,
                itemBuilder: (context, index) {
                  final brand = controller.filteredBrands[index];
                  final isSelected = controller.manufacture.isNotEmpty || controller.manufactureAr.isNotEmpty
                      ? controller.filteredBrands.indexWhere((element) {
                          return appLocal.localeName == 'ar'
                              ? element.arBrands?.brandName == controller.manufactureAr.value
                              : element.brandName == controller.manufacture.value;
                        }) == index
                      : false;

                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(
                      gradient: isSelected
                          ? LinearGradient(
                              colors: [
                                AppColors.colorPrimary.withOpacity(0.3),
                                AppColors.colorPrimary.withOpacity(0.1),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                          : null,
                      color: isSelected ? null : Colors.white.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected 
                            ? AppColors.colorPrimary.withOpacity(0.3) 
                            : Colors.transparent,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          Get.back(result: jsonEncode([
                            brand.brandName,
                            controller.brandsList[index].arBrands?.brandName ?? "",
                            brand.id
                          ]));
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Row(
                            children: [
                              Container(
                                height: 45,
                                width: 45,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(23),
                                  border: Border.all(
                                    color: Colors.white24,
                                    width: 1.5,
                                  ),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(22),
                                  child: GlobalMethods.netWorkImage(
                                    brand.brandIcon ?? "",
                                    BorderRadius.circular(22),
                                    BoxFit.cover,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  appLocal.localeName == 'en'
                                      ? brand.brandName ?? ""
                                      : brand.arBrands?.brandName ?? "",
                                  style: FontStyles.fontRegular(
                                    fontSize: 14,
                                    color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
                                  ),
                                ),
                              ),
                              if (isSelected)
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.2),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.check,
                                    color: Colors.blue,
                                    size: 16,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
