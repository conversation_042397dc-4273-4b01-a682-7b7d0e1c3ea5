import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/other/badge_decoration.dart';
import 'package:overolasuppliers/helper/other/circular_page_indicator.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:overolasuppliers/model/add_product/product_by_shop_model.dart';

// ignore: must_be_immutable
class ProductListTile extends StatelessWidget {
  final Function(String id) onTap;
  final double width;
  final ShopProducts product;
  final bool requiredPageView;
  final _currentPageNotifier = ValueNotifier<int>(0);
  RxInt colorIndex = 0.obs;
  final PageController _controller = PageController();

  ProductListTile(
      {Key? key,
      required this.onTap,
      required this.product,
      this.width = 165,
      this.requiredPageView = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return ClipRRect(
      borderRadius: BorderRadius.circular(15),
      child: Container(
        foregroundDecoration: (product.isHidden ?? false)
            ? BadgeDecoration(
                badgeColor: AppColors.colotJumbo,
                badgeSize: 90,
                textSpan: TextSpan(
                  text: appLocal.badgeProductHidden,
                  style: FontStyles.fontBold(
                    fontSize: 10,
                  ),
                ),
              )
            : product.productStatus == null
                ? null
                : BadgeDecoration(
                    badgeColor: product.productStatus == "Pending"
                        ? AppColors.colorSecondaryYellow
                        : product.productStatus == "Returned"
                            ? AppColors.colorDanger
                            : Colors.transparent,
                    badgeSize: 80,
                    textSpan: TextSpan(
                      text: product.productStatus == "Pending"
                          ? ((product.validationHistory?.length ?? 0) > 1)
                              ? appLocal.begdeModifyRequest
                              : getBadgeTitle(appLocal, product.productStatus ?? "")
                          : getBadgeTitle(appLocal, product.productStatus ?? ""),
                      style: FontStyles.fontBold(
                        fontSize: 11,
                        color: product.productStatus == "Pending"
                            ? Colors.black
                            : product.productStatus == "Returned"
                                ? Colors.white
                                : Colors.transparent,
                      ),
                    ),
                  ),
        child: GestureDetector(
          onTap: () => onTap(product.id ?? ""),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8).r,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                SizedBox(
                  height: 320,
                  width: width,
                  child: Stack(
                    children: [
                      requiredPageView
                          ? Positioned(
                              top: 0,
                              height: 230,
                              left: 0,
                              right: 0,
                              child: PageView.builder(
                                itemCount: product.productImages?.length,
                                itemBuilder: (BuildContext context, int index) {
                                  return GlobalMethods.netWorkImage(
                                      (product.productImages ?? [])[index],
                                      BorderRadius.circular(8),
                                      BoxFit.cover);
                                },
                                onPageChanged: (int index) =>
                                    _currentPageNotifier.value = index,
                              ),
                            )
                          : Positioned(
                              top: 0,
                              height: 230,
                              left: 0,
                              right: 0,
                              child: Obx(
                                () => PageView.builder(
                                  controller: _controller,
                                  itemCount:
                                      (product.productOptions?.productColors ??
                                              [])[colorIndex.value]
                                          .colorImages
                                          ?.length,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    return GlobalMethods.netWorkImage(
                                      (product.productOptions?.productColors ??
                                                  [])[colorIndex.value]
                                              .colorImages?[index] ??
                                          "",
                                      BorderRadius.circular(8),
                                      BoxFit.cover,
                                    );
                                  },
                                  onPageChanged: (index) =>
                                      _currentPageNotifier.value = index,
                                ),
                              ),
                            ),
                      if ((product.productImages?.length ?? 0) > 1)
                        Positioned(
                          top: 0,
                          bottom: 0,
                          left: 0,
                          child: Center(
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.black26,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Icon(
                                Icons.chevron_left,
                                color: Colors.white,
                                size: 24.sp,
                              ),
                            ),
                          ),
                        ),
                      if ((product.productImages?.length ?? 0) > 1)
                        Positioned(
                          top: 0,
                          bottom: 0,
                          right: 0,
                          child: Center(
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.black26,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Icon(
                                Icons.chevron_right,
                                color: Colors.white,
                                size: 24.sp,
                              ),
                            ),
                          ),
                        ),
                      Positioned(
                        bottom: 95,
                        left: 0,
                        right: 0,
                        child: SizedBox(
                          height: 20,
                          child: Center(
                            child: (product.productOptions?.productColors
                                        ?.isNotEmpty ??
                                    false)
                                ? Obx(
                                    () => CirclePageIndicator(
                                      size: 8,
                                      selectedDotColor: AppColors.colorPrimary,
                                      dotColor: Colors.white,
                                      itemCount: (product.productOptions
                                                      ?.productColors ??
                                                  [])[colorIndex.value]
                                              .colorImages
                                              ?.length ??
                                          0,
                                      currentPageNotifier: _currentPageNotifier,
                                    ),
                                  )
                                : CirclePageIndicator(
                                    size: 8,
                                    selectedDotColor: AppColors.colorPrimary,
                                    dotColor: Colors.white,
                                    itemCount:
                                        product.productImages?.length ?? 0,
                                    currentPageNotifier: _currentPageNotifier,
                                  ),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: EdgeInsets.all(12.sp),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.bottomCenter,
                              end: Alignment.topCenter,
                              colors: [
                                Colors.black.withOpacity(0.8),
                                Colors.transparent,
                              ],
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 40.w,
                                child: Text(
                                  appLocal.localeName == "en"
                                      ? product.productName ?? ""
                                      : product.shopProductAr?.productName ?? "",
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: GoogleFonts.montserrat(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16.sp,
                                  ),
                                ),
                              ),
                              SizedBox(height: 4.h),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    appLocal.localeName == "en"
                                        ? (product.productBrand?.brandName ?? "")
                                        : (product.productBrand?.productBrandAr?.brandName ?? ""),
                                    style: GoogleFonts.montserrat(
                                      color: Colors.white70,
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    appLocal.dynamicPrice(product.productPrice ?? ""),
                                    style: GoogleFonts.montserrat(
                                      color: Colors.white,
                                      fontSize: 15.sp,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                if (!requiredPageView) const SizedBox(height: 5),
                if (!requiredPageView)
                  SizedBox(
                    height: 25,
                    child: ListView.builder(
                      itemCount: product.productOptions?.productColors?.length,
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.symmetric(horizontal: 8.w),
                      itemBuilder: (context, index) {
                        return Obx(
                          () => GestureDetector(
                            onTap: () => {
                              colorIndex.value = index,
                              _controller.animateToPage(0,
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut)
                            },
                            child: Container(
                              height: 25,
                              width: 25,
                              margin: EdgeInsets.symmetric(horizontal: 4.w),
                              padding: EdgeInsets.all(colorIndex.value == index ? 2 : 1),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: colorIndex.value == index 
                                      ? AppColors.colorPrimary 
                                      : Colors.grey.shade300,
                                  width: colorIndex.value == index ? 2 : 1,
                                ),
                              ),
                              child: ClipOval(
                                child: GlobalMethods.netWorkImage(
                                  product.productOptions?.productColors?[index].colorIcon ?? "",
                                  BorderRadius.zero,
                                  BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }


    getBadgeTitle(AppLocalizations localizations, String productStatus) {
      print("productStatus $productStatus");
   switch (productStatus) {
     case "Pending":
       return localizations.pending;
       case "Returned":
        return localizations.returned;
         
     default:
        return productStatus;
   }
  }

}
