import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/controller/orders_controller.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/cancel_order.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/delivered_orders.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/return_orders.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class HistoryOrders extends StatelessWidget {
  const HistoryOrders({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    final controller = Get.find<OrdersController>();
    return Padding(
      padding:
          const EdgeInsets.only(left: kLeftSpace, right: kRightSpace, top: 10),
      child: DefaultTabController(
        length: 3,
        child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            toolbarHeight: 0,
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(48),
              child: ColoredBox(
                color: AppColors.backgroundColorDark,
                child: TabBar(
                  tabAlignment: TabAlignment.start,
                  indicatorPadding: const EdgeInsets.symmetric(horizontal: -15),
                  isScrollable: true,
                  labelColor: AppColors.colorPrimary,
                  unselectedLabelColor: Colors.white.withOpacity(0.6),
                  indicatorColor: AppColors.colorPrimary,
                  labelStyle: FontStyles.fontSemibold(),
                  unselectedLabelStyle: FontStyles.fontRegular(),
                  tabs: [
                    Tab(text: appLocal.deliverdOrderTab(controller.deliveredOrderCount.value == 0 ? '' : '( ${controller.formatNumber(controller.deliveredOrderCount.value)} )'),),
                    Tab(text: appLocal.cancelledOrderTab(controller.canceledOrderCount.value == 0 ? '' : '( ${controller.formatNumber(controller.canceledOrderCount.value)} )'),),
                    Tab(text: appLocal.returnedOrderTab(controller.returnedOrderCount.value == 0 ? '' : '( ${controller.formatNumber(controller.returnedOrderCount.value)} )'),),
                 ],
                ),
              ),
            ),
          ),
          body: const TabBarView(
            physics: NeverScrollableScrollPhysics(),
            children: [
              DeliverdOrders(),
              CancelOrder(),
              ReturnOrder(
                isHistory: true,
              )
            ],
          ),
        ),
      ),
    );
  }
}
