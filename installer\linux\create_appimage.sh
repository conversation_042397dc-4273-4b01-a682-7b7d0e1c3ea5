#!/bin/bash

# Linux AppImage Creation Script for Overola Suppliers
# This script creates an AppImage for the Flutter Linux application

set -e

APP_NAME="Overola Suppliers"
APP_VERSION="1.0.1"
APP_EXECUTABLE="overolasuppliers"
BUILD_DIR="../../build/linux/x64/release/bundle"
DIST_DIR="../../dist/linux"

echo "🐧 Creating Linux AppImage for ${APP_NAME}..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if the build directory exists
if [ ! -d "${BUILD_DIR}" ]; then
    print_error "Build directory not found at ${BUILD_DIR}"
    print_error "Please build the Linux app first using: flutter build linux --release"
    exit 1
fi

# Check if appimagetool is available
if ! command -v appimagetool &> /dev/null; then
    print_warning "appimagetool not found. Downloading..."
    
    # Download appimagetool
    wget -O appimagetool "https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    chmod +x appimagetool
    APPIMAGETOOL="./appimagetool"
else
    APPIMAGETOOL="appimagetool"
fi

# Create distribution directory
mkdir -p "${DIST_DIR}"

# Create AppDir structure
APPDIR="${DIST_DIR}/${APP_NAME}.AppDir"
rm -rf "${APPDIR}"
mkdir -p "${APPDIR}"

print_status "Creating AppDir structure..."

# Copy application files
cp -r "${BUILD_DIR}"/* "${APPDIR}/"

# Create usr directory structure
mkdir -p "${APPDIR}/usr/bin"
mkdir -p "${APPDIR}/usr/lib"
mkdir -p "${APPDIR}/usr/share/applications"
mkdir -p "${APPDIR}/usr/share/icons/hicolor/256x256/apps"

# Move executable to usr/bin
mv "${APPDIR}/${APP_EXECUTABLE}" "${APPDIR}/usr/bin/"

# Move libraries to usr/lib
if [ -d "${APPDIR}/lib" ]; then
    mv "${APPDIR}/lib"/* "${APPDIR}/usr/lib/"
    rmdir "${APPDIR}/lib"
fi

# Create desktop file
print_status "Creating desktop file..."
cat > "${APPDIR}/usr/share/applications/${APP_EXECUTABLE}.desktop" << EOF
[Desktop Entry]
Type=Application
Name=${APP_NAME}
Comment=Overola Suppliers Desktop Application
Exec=${APP_EXECUTABLE}
Icon=${APP_EXECUTABLE}
Categories=Office;Business;
Terminal=false
StartupWMClass=${APP_EXECUTABLE}
EOF

# Copy desktop file to AppDir root
cp "${APPDIR}/usr/share/applications/${APP_EXECUTABLE}.desktop" "${APPDIR}/"

# Create or copy icon
if [ -f "app_icon.png" ]; then
    cp "app_icon.png" "${APPDIR}/usr/share/icons/hicolor/256x256/apps/${APP_EXECUTABLE}.png"
    cp "app_icon.png" "${APPDIR}/${APP_EXECUTABLE}.png"
else
    print_warning "No app icon found. Creating placeholder icon..."
    # Create a simple placeholder icon
    convert -size 256x256 xc:blue -fill white -gravity center -pointsize 48 -annotate +0+0 "OS" "${APPDIR}/${APP_EXECUTABLE}.png"
    cp "${APPDIR}/${APP_EXECUTABLE}.png" "${APPDIR}/usr/share/icons/hicolor/256x256/apps/${APP_EXECUTABLE}.png"
fi

# Create AppRun script
print_status "Creating AppRun script..."
cat > "${APPDIR}/AppRun" << 'EOF'
#!/bin/bash

# AppRun script for Overola Suppliers

HERE="$(dirname "$(readlink -f "${0}")")"

# Set up environment
export LD_LIBRARY_PATH="${HERE}/usr/lib:${LD_LIBRARY_PATH}"
export PATH="${HERE}/usr/bin:${PATH}"

# Set up Flutter environment
export FLUTTER_ROOT="${HERE}/usr"

# Run the application
exec "${HERE}/usr/bin/overolasuppliers" "$@"
EOF

chmod +x "${APPDIR}/AppRun"

# Copy additional files
if [ -f "../../README.md" ]; then
    cp "../../README.md" "${APPDIR}/"
fi

if [ -f "../../LICENSE" ]; then
    cp "../../LICENSE" "${APPDIR}/"
fi

# Create AppImage metadata
print_status "Creating AppImage metadata..."
mkdir -p "${APPDIR}/.DirIcon"
if [ -f "${APPDIR}/${APP_EXECUTABLE}.png" ]; then
    cp "${APPDIR}/${APP_EXECUTABLE}.png" "${APPDIR}/.DirIcon/icon.png"
fi

# Bundle dependencies (optional - for better compatibility)
print_status "Bundling dependencies..."

# Find and copy required libraries
LIBS=$(ldd "${APPDIR}/usr/bin/${APP_EXECUTABLE}" | grep "=> /" | awk '{print $3}' | grep -v "^/lib" | grep -v "^/usr/lib")

for lib in $LIBS; do
    if [ -f "$lib" ]; then
        cp "$lib" "${APPDIR}/usr/lib/"
    fi
done

# Create version file
echo "${APP_VERSION}" > "${APPDIR}/VERSION"

# Create AppImage
print_status "Creating AppImage..."
ARCH=x86_64 "${APPIMAGETOOL}" "${APPDIR}" "${DIST_DIR}/${APP_NAME}-${APP_VERSION}-x86_64.AppImage"

if [ $? -eq 0 ]; then
    print_success "AppImage created successfully!"
else
    print_error "AppImage creation failed!"
    exit 1
fi

# Make AppImage executable
chmod +x "${DIST_DIR}/${APP_NAME}-${APP_VERSION}-x86_64.AppImage"

# Get AppImage size
APPIMAGE_SIZE=$(du -h "${DIST_DIR}/${APP_NAME}-${APP_VERSION}-x86_64.AppImage" | cut -f1)
print_status "AppImage size: ${APPIMAGE_SIZE}"

# Clean up AppDir
rm -rf "${APPDIR}"

# Clean up downloaded appimagetool if we downloaded it
if [ -f "./appimagetool" ]; then
    rm "./appimagetool"
fi

print_success "🎉 Linux AppImage creation completed!"
echo ""
echo "AppImage Location: ${DIST_DIR}/${APP_NAME}-${APP_VERSION}-x86_64.AppImage"
echo ""
echo "Next steps:"
echo "1. Test the AppImage on different Linux distributions"
echo "2. Upload to AppImageHub for distribution"
echo "3. Create .deb and .rpm packages for package managers"
echo "4. Consider Flatpak and Snap packages for wider distribution"
echo ""
echo "To test the AppImage:"
echo "  ./${DIST_DIR}/${APP_NAME}-${APP_VERSION}-x86_64.AppImage"
