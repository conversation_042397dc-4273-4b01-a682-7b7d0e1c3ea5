class SvgStrings {

    static const String iconInvoiceSummary = '''
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
    <line x1="8" y1="8" x2="16" y2="8"/>
    <line x1="8" y1="12" x2="16" y2="12"/>
    <line x1="8" y1="16" x2="16" y2="16"/>
  </svg>
  ''';

  static const String iconShipmentInfo = '''
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M3 3h18v18H3z"/>
    <path d="M16 8l-4 4-4-4"/>
    <path d="M8 16h8"/>
  </svg>
  ''';
  
  static String scanner =
      '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="128" height="128" viewBox="0 0 128 128"><image width="128" height="128" xlink:href="../../Downloads/barcode-scanner%20(2).png"/></svg>';

  static String iconArchive =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g transform="translate(-335 -169)"><g transform="translate(335 169)"><rect width="24" height="24" rx="4" fill="#95a2fe" opacity="0.06"/></g><path d="M16,18H2a2,2,0,0,1-2-2V2A2,2,0,0,1,2,0H16a2,2,0,0,1,2,2V16A2,2,0,0,1,16,18ZM5.67,10.26H5.662a.488.488,0,0,0-.342.139l-.21.21a.508.508,0,0,0,0,.71l3.54,3.53a.481.481,0,0,0,.7,0l3.54-3.53a.508.508,0,0,0,0-.71l-.21-.21a.5.5,0,0,0-.347-.14H11V6.5a.5.5,0,0,0-.5-.5h-3a.5.5,0,0,0-.5.5v3.76ZM2,2V4H16V2Z" transform="translate(338 172)" fill="#95a2fe"/></g></svg>';

  static String iconStoryCamera =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#95a2fe" opacity="0.06"/><path d="M18,16H2a2,2,0,0,1-2-2V5A2,2,0,0,1,2,3H3.17A2.017,2.017,0,0,0,4.59,2.41L6.12.88A2.981,2.981,0,0,1,8.24,0h3.52a3.018,3.018,0,0,1,2.12.88l1.53,1.53A2.013,2.013,0,0,0,16.827,3H18a2,2,0,0,1,2,2v9A2,2,0,0,1,18,16ZM10,5a4,4,0,1,0,4,4A4,4,0,0,0,10,5Z" transform="translate(2 4)" fill="#95a2fe"/></svg>';

  static const String currentLocation =
      '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="90" height="90" viewBox="0 0 60 60"><defs><filter id="a" x="0" y="0" width="60" height="60" filterUnits="userSpaceOnUse"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="3" result="b"/><feFlood flood-opacity="0.161"/><feComposite operator="in" in2="b"/><feComposite in="SourceGraphic"/></filter></defs><g transform="translate(-314 -631)"><g transform="matrix(1, 0, 0, 1, 314, 631)" filter="url(#a)"><circle cx="21" cy="21" r="21" transform="translate(9 6)" fill="#fff"/></g><g transform="translate(-224 394)"><path d="M20.75,10H18.71A8.009,8.009,0,0,0,11.5,2.79V.75a.75.75,0,0,0-1.5,0V2.79A8.009,8.009,0,0,0,2.79,10H.75a.75.75,0,0,0,0,1.5H2.79A8.009,8.009,0,0,0,10,18.71v2.04a.75.75,0,0,0,1.5,0V18.71a8.009,8.009,0,0,0,7.21-7.21h2.04a.75.75,0,0,0,0-1.5Zm-10,3.87a3.12,3.12,0,1,1,3.12-3.12A3.124,3.124,0,0,1,10.75,13.87Z" transform="translate(557.25 253.25)" fill="#292d32"/><path d="M0,0H24V24H0Z" transform="translate(556 252)" fill="none" opacity="0"/><path d="M0,0H24V24H0Z" transform="translate(580 276) rotate(180)" fill="none" opacity="0"/></g></g></svg>';

  static String iconGallery =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="2" fill="#95a2fe" opacity="0.06"/><path d="M16,18H2a2,2,0,0,1-2-2V2A2,2,0,0,1,2,0H16a2,2,0,0,1,2,2V16A2,2,0,0,1,16,18ZM10.715,8.95h.005a.493.493,0,0,1,.39.191l4,5a.487.487,0,0,0,.387.19.51.51,0,0,0,.36-.149A.5.5,0,0,0,16,13.82V2H2V13.83a.49.49,0,0,0,.49.5h.021a.478.478,0,0,0,.389-.2L5.48,10.7a.509.509,0,0,1,.4-.2.539.539,0,0,1,.41.2l1,1.36a.509.509,0,0,0,.4.2H7.7a.485.485,0,0,0,.383-.19L10.33,9.14A.483.483,0,0,1,10.715,8.95ZM6.5,8A1.5,1.5,0,1,1,8,6.5,1.5,1.5,0,0,1,6.5,8Z" transform="translate(3 3)" fill="#95a2fe"/></svg>';

  // ADD PRODUCT SVG IMAGES START

  static String iconTick =
      '<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><rect width="32" height="32" rx="16" fill="#99d3a1"/><path d="M16.848,1.564,12.1,6.307,6.248,12.164a.5.5,0,0,1-.71,0L.148,6.774a.5.5,0,0,1,0-.71l.7-.7a.5.5,0,0,1,.71,0l4.33,4.33,9.55-9.55a.51.51,0,0,1,.71,0l.7.71a.5.5,0,0,1,0,.71Z" transform="translate(7.002 9.686)" fill="#fff"/></svg>';

  static String iconEdit =
      '<svg xmlns="http://www.w3.org/2000/svg" width="15.002" height="15" viewBox="0 0 15.002 15"><g transform="translate(-129.494 -1411.006)"><path d="M6.315,1.3C4.6-.411,2.926-.456,1.17,1.3L.1,2.369a.368.368,0,0,0-.088.353A7.175,7.175,0,0,0,4.894,7.6.443.443,0,0,0,5,7.62a.353.353,0,0,0,.256-.106L6.315,6.446A3.648,3.648,0,0,0,7.613,3.9,3.634,3.634,0,0,0,6.315,1.3Z" transform="translate(136.883 1411.006)" fill="#fff"/><path d="M10.694,4.837c-.256-.124-.5-.247-.741-.388-.194-.115-.38-.238-.565-.371a5.329,5.329,0,0,1-.494-.38,1.077,1.077,0,0,1-.15-.132,7.465,7.465,0,0,1-.909-.918A1.075,1.075,0,0,1,7.7,2.489,5.134,5.134,0,0,1,7.331,2a4.846,4.846,0,0,1-.344-.521c-.141-.238-.265-.477-.388-.724C6.475.494,6.378.238,6.29,0L.747,5.543A1.062,1.062,0,0,0,.5,6.028L.023,9.409a1.809,1.809,0,0,0,.45,1.545,1.757,1.757,0,0,0,1.236.477,1.93,1.93,0,0,0,.318-.026l3.389-.477A.982.982,0,0,0,5.9,10.68l5.543-5.543C11.2,5.049,10.959,4.951,10.694,4.837Z" transform="translate(129.494 1414.576)" fill="#fff"/></g></svg>';

  static String iconCamera =
      '<svg xmlns="http://www.w3.org/2000/svg" width="32.2" height="32.2" viewBox="0 0 32.2 32.2"><g transform="translate(-364 -316)"><g transform="translate(364 316)"><path d="M21.142,5.367A2.2,2.2,0,0,1,19.2,4.173l-.966-1.945A4.428,4.428,0,0,0,14.635,0H11.563A4.432,4.432,0,0,0,7.953,2.227L6.987,4.173A2.2,2.2,0,0,1,5.042,5.367,5.036,5.036,0,0,0,.011,10.72L.708,21.8c.161,2.764,1.65,5.031,5.353,5.031H20.123c3.7,0,5.179-2.267,5.353-5.031l.7-11.082A5.036,5.036,0,0,0,21.142,5.367ZM11.08,7.044H15.1a1.006,1.006,0,0,1,0,2.013H11.08a1.006,1.006,0,1,1,0-2.013Zm2.013,14.584a4.535,4.535,0,1,1,4.535-4.535A4.538,4.538,0,0,1,13.092,21.628Z" transform="translate(3.008 2.683)" fill="#fff"/><path d="M0,0H32.2V32.2H0Z" fill="none" opacity="0"/></g></g></svg>';

  static String shopPickupTag =
      '<svg xmlns="http://www.w3.org/2000/svg" width="326" height="70.823" viewBox="0 0 326 70.823"><g transform="translate(-1717.46 -1250.439)"><g transform="translate(-34.414 -17.229)"><g opacity="0.39"><path d="M6156.146,10616.829c-.013-.165-.019-.33-.019-.5a6,6,0,0,1,12,0c0,.168-.008.333-.021.5h71.075a6,6,0,1,1,11.887,0h0a6,6,0,0,1-11.887,0H6168.1a6,6,0,0,1-11.959,0Zm-88.943,0c-.011-.162-.016-.33-.016-.5a6,6,0,1,1,11.994,0c0,.168-.005.336-.021.5a6,6,0,0,1-11.956,0Zm-92.769-.829a6,6,0,1,1,11.942.829h0a6,6,0,0,1-11.942-.829Zm104.725.829h0Z" transform="translate(-4198.625 -9299.664)" fill="rgba(0,0,0,0)"/><path d="M6162.123,10622.331a6,6,0,0,0,5.982-5.5h71.075a6,6,0,0,0,11.887,0h0a6,6,0,1,0-11.887,0H6168.1c.014-.165.021-.33.021-.5a6,6,0,0,0-12,0c0,.168.005.333.019.5a6,6,0,0,0,5.977,5.5m-88.944,0a6,6,0,0,0,5.979-5.5c.016-.162.021-.33.021-.5a6,6,0,1,0-11.994,0c0,.168.005.336.016.5a6,6,0,0,0,5.977,5.5m-92.748-.33a6,6,0,0,0,5.944-5.172h0a6,6,0,1,0-5.944,5.172m175.714-5.172h0m5.977,7.5a8,8,0,0,1-7.6-5.5h-73.741a8,8,0,0,1-15.2,0h-77.665a8,8,0,1,1,.431-4h76.979a8,8,0,0,1,15.712,0h73.23a8,8,0,0,1,15.716,0h67.224a8,8,0,0,1,15.906.766,2,2,0,0,1-.366,2.828,8,8,0,0,1-15.11.406h-67.912a8.009,8.009,0,0,1-7.6,5.5Z" transform="translate(-4198.625 -9299.664)" fill="#707070"/></g><g transform="translate(-2.251 57.836)"><g transform="translate(7997.375 8200.682)"><g transform="translate(-1089 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.25 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5078 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700" opacity="0.6"><tspan x="-23.296" y="0">Information</tspan></text></g></g></g><g transform="translate(8112.375 8200.682)"><g transform="translate(-1023.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700" opacity="0.59"><tspan x="-30" y="0">Customer Contact</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-890.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700" opacity="0.6"><tspan x="-13.9" y="0">Review</tspan></text></g></g></g></g></g><g transform="translate(92 4)"><g transform="translate(-27.112 -102.146)"><g transform="translate(1719.238 950.682)"><g transform="translate(-0.376)"><g transform="translate(6335.25 7374)"><path d="M4.624,1.593a1,1,0,0,1,1.752,0l3.809,6.925A1,1,0,0,1,9.309,10H1.691A1,1,0,0,1,.815,8.518Z" transform="translate(-6273 -6942) rotate(180)" fill="#95a2fe"/></g></g></g><rect width="120" height="28" rx="6" transform="translate(1716.112 1348.586)" fill="#95a2fe"/><text transform="translate(1775.112 1366.586)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700"><tspan x="-43.675" y="0">Pickup Addresses</tspan></text></g><g transform="translate(1585 1106.351)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(151 177)" fill="#95a2fe"/><path d="M8.5-3.5a12,12,0,1,1-12,12A12.014,12.014,0,0,1,8.5-3.5Zm0,17a5,5,0,1,0-5-5A5.006,5.006,0,0,0,8.5,13.5Z" transform="translate(154.5 180.5)" fill="#323741"/></g></g></g></svg>';

  static String shopInfoTag =
      '<svg xmlns="http://www.w3.org/2000/svg" width="350" height="70.823" viewBox="0 0 336.46 70.823"><g transform="translate(-1707 -1250.439)"><g transform="translate(-34.414 -17.229)"><g opacity="0.39"><path d="M6156.146,10616.829c-.013-.165-.019-.33-.019-.5a6,6,0,0,1,12,0c0,.168-.008.333-.021.5h71.075a6,6,0,1,1,11.887,0h0a6,6,0,0,1-11.887,0H6168.1a6,6,0,0,1-11.959,0Zm-88.943,0c-.011-.162-.016-.33-.016-.5a6,6,0,1,1,11.994,0c0,.168-.005.336-.021.5a6,6,0,0,1-11.956,0Zm-92.769-.829a6,6,0,1,1,11.942.829h0a6,6,0,0,1-11.942-.829Zm104.725.829h0Z" transform="translate(-4198.625 -9299.664)" fill="rgba(0,0,0,0)"/><path d="M6162.123,10622.331a6,6,0,0,0,5.982-5.5h71.075a6,6,0,0,0,11.887,0h0a6,6,0,1,0-11.887,0H6168.1c.014-.165.021-.33.021-.5a6,6,0,0,0-12,0c0,.168.005.333.019.5a6,6,0,0,0,5.977,5.5m-88.944,0a6,6,0,0,0,5.979-5.5c.016-.162.021-.33.021-.5a6,6,0,1,0-11.994,0c0,.168.005.336.016.5a6,6,0,0,0,5.977,5.5m-92.748-.33a6,6,0,0,0,5.944-5.172h0a6,6,0,1,0-5.944,5.172m175.714-5.172h0m5.977,7.5a8,8,0,0,1-7.6-5.5h-73.741a8,8,0,0,1-15.2,0h-77.665a8,8,0,1,1,.431-4h76.979a8,8,0,0,1,15.712,0h73.23a8,8,0,0,1,15.716,0h67.224a8,8,0,0,1,15.906.766,2,2,0,0,1-.366,2.828,8,8,0,0,1-15.11.406h-67.912a8.009,8.009,0,0,1-7.6,5.5Z" transform="translate(-4198.625 -9299.664)" fill="#707070"/></g><g transform="translate(-2.251 57.836)"><g transform="translate(8094.375 8200.682)"><g transform="translate(-1089 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.25 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5078 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700" opacity="0.6"><tspan x="-35" y="0">Pickup Addresses</tspan></text></g></g></g><g transform="translate(8112.375 8200.682)"><g transform="translate(-1023.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700" opacity="0.59"><tspan x="-30" y="0">Customer Contact</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-890.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700" opacity="0.6"><tspan x="-13.9" y="0">Review</tspan></text></g></g></g></g></g><g transform="translate(-1 4)"><g transform="translate(-27.112 -102.146)"><g transform="translate(1719.238 950.682)"><g transform="translate(-0.376)"><g transform="translate(6335.25 7374)"><path d="M4.624,1.593a1,1,0,0,1,1.752,0l3.809,6.925A1,1,0,0,1,9.309,10H1.691A1,1,0,0,1,.815,8.518Z" transform="translate(-6273 -6942) rotate(180)" fill="#95a2fe"/></g></g></g><rect width="80" height="28" rx="6" transform="translate(1735.112 1348.586)" fill="#95a2fe"/><text transform="translate(1775.112 1366.586)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700"><tspan x="-29.12" y="0">Information</tspan></text></g><g transform="translate(1585 1106.351)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(151 177)" fill="#95a2fe"/><path d="M8.5-3.5a12,12,0,1,1-12,12A12.014,12.014,0,0,1,8.5-3.5Zm0,17a5,5,0,1,0-5-5A5.006,5.006,0,0,0,8.5,13.5Z" transform="translate(154.5 180.5)" fill="#323741"/></g></g></g></svg>';

  static String shopCustomerContactTag =
      '<svg xmlns="http://www.w3.org/2000/svg" width="327" height="69.823" viewBox="0 0 327 69.823"><g transform="translate(-1716.46 -1251.439)"><g transform="translate(-34.414 -17.229)"><g opacity="0.39"><path d="M6156.146,10616.829c-.013-.165-.019-.33-.019-.5a6,6,0,0,1,12,0c0,.168-.008.333-.021.5h71.075a6,6,0,1,1,11.887,0h0a6,6,0,0,1-11.887,0H6168.1a6,6,0,0,1-11.959,0Zm-88.943,0c-.011-.162-.016-.33-.016-.5a6,6,0,1,1,11.994,0c0,.168-.005.336-.021.5a6,6,0,0,1-11.956,0Zm-92.769-.829a6,6,0,1,1,11.942.829h0a6,6,0,0,1-11.942-.829Zm104.725.829h0Z" transform="translate(-4198.625 -9299.664)" fill="rgba(0,0,0,0)"/><path d="M6162.123,10622.331a6,6,0,0,0,5.982-5.5h71.075a6,6,0,0,0,11.887,0h0a6,6,0,1,0-11.887,0H6168.1c.014-.165.021-.33.021-.5a6,6,0,0,0-12,0c0,.168.005.333.019.5a6,6,0,0,0,5.977,5.5m-88.944,0a6,6,0,0,0,5.979-5.5c.016-.162.021-.33.021-.5a6,6,0,1,0-11.994,0c0,.168.005.336.016.5a6,6,0,0,0,5.977,5.5m-92.748-.33a6,6,0,0,0,5.944-5.172h0a6,6,0,1,0-5.944,5.172m175.714-5.172h0m5.977,7.5a8,8,0,0,1-7.6-5.5h-73.741a8,8,0,0,1-15.2,0h-77.665a8,8,0,1,1,.431-4h76.979a8,8,0,0,1,15.712,0h73.23a8,8,0,0,1,15.716,0h67.224a8,8,0,0,1,15.906.766,2,2,0,0,1-.366,2.828,8,8,0,0,1-15.11.406h-67.912a8.009,8.009,0,0,1-7.6,5.5Z" transform="translate(-4198.625 -9299.664)" fill="#707070"/></g><g transform="translate(-2.251 57.836)"><g transform="translate(8094.375 8200.682)"><g transform="translate(-1089 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.25 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5078 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700" opacity="0.6"><tspan x="-30.372" y="0">Pickup Address</tspan></text></g></g></g><g transform="translate(7931.375 8200.682)"><g transform="translate(-1023.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700" opacity="0.59"><tspan x="-23.296" y="0">Information</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-890.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700" opacity="0.6"><tspan x="-13.9" y="0">Review</tspan></text></g></g></g></g></g><g transform="translate(181 5)"><g transform="translate(-27.112 -102.146)"><g transform="translate(1719.238 950.682)"><g transform="translate(-0.376)"><g transform="translate(6335.25 7374)"><path d="M4.624,1.593a1,1,0,0,1,1.752,0l3.809,6.925A1,1,0,0,1,9.309,10H1.691A1,1,0,0,1,.815,8.518Z" transform="translate(-6273 -6942) rotate(180)" fill="#95a2fe"/></g></g></g><rect width="120" height="28" rx="6" transform="translate(1715.112 1348.586)" fill="#95a2fe"/><text transform="translate(1775.112 1366.586)" fill="rgba(255,255,255,0.8)" font-size="9" font-family="Poppins-Bold, Poppins" font-weight="700"><tspan x="-46.455" y="0">Customer Contact</tspan></text></g><g transform="translate(1585 1106.351)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(151 177)" fill="#95a2fe"/><path d="M8.5-3.5a12,12,0,1,1-12,12A12.014,12.014,0,0,1,8.5-3.5Zm0,17a5,5,0,1,0-5-5A5.006,5.006,0,0,0,8.5,13.5Z" transform="translate(154.5 180.5)" fill="#323741"/></g></g></g></svg>';

  static String specificationTag =
      '<svg xmlns="http://www.w3.org/2000/svg" width="326.46" height="74" viewBox="0 0 326.46 74"><g transform="translate(-1717 -1337.62)"><g transform="translate(0 7.347)"><g transform="translate(-34.414 65.782)"><g opacity="0.39"><path d="M6174.135,10616.828c-.008-.106-.014-.219-.014-.328a6,6,0,0,1,12,0c0,.109-.005.222-.011.328h53.067a6.083,6.083,0,0,1-.056-.829,6,6,0,1,1,11.943.829h0a6,6,0,0,1-11.887,0h-53.067a6,6,0,0,1-11.978,0Zm-132.933,0c-.013-.16-.024-.328-.024-.5a6,6,0,0,1,12.007,0c0,.168-.011.336-.027.5a6,6,0,0,1-11.957,0Zm65.976,0a6.5,6.5,0,0,1-.053-.829,6,6,0,0,1,12,0,6.5,6.5,0,0,1-.059.829,6,6,0,0,1-11.89,0Zm-132.746-.829a6,6,0,1,1,11.942.829h0a6,6,0,0,1-11.942-.829Zm144.636.829h0Zm-65.909,0h0Z" transform="translate(-4198.625 -9299.664)" fill="rgba(0,0,0,0)"/><path d="M6180.122,10622.5a6,6,0,0,0,5.99-5.673h53.067a6,6,0,0,0,11.887,0h0a6,6,0,1,0-11.943-.829,6.083,6.083,0,0,0,.056.829h-53.067c.005-.106.011-.219.011-.328a6,6,0,0,0-12,0c0,.***************.328a6,6,0,0,0,5.987,5.673m-132.937-.168a6,6,0,0,0,5.974-5.5c.016-.16.027-.328.027-.5a6,6,0,0,0-12.007,0c0,.168.011.336.024.5a6.005,6.005,0,0,0,5.982,5.5m65.941-.333a6.005,6.005,0,0,0,5.942-5.172,6.5,6.5,0,0,0,.059-.829,6,6,0,0,0-12,0,6.5,6.5,0,0,0,.053.829,6.008,6.008,0,0,0,5.948,5.172m-132.692,0a6,6,0,0,0,5.941-5.172h0a6,6,0,1,0-5.941,5.172m193.7-5.172h0m-66.957,0h0m72.944,7.673a8,8,0,0,1-7.653-5.673h-51.861a8,8,0,0,1-14.971,0h-50.855a8,8,0,0,1-15.2,0h-51.664a8,8,0,1,1,.43-4h50.974a8.005,8.005,0,0,1,15.724,0h50.166a8,8,0,0,1,15.831,0H6172.3a8,8,0,0,1,15.65,0h49.262a8,8,0,0,1,15.907.773,2,2,0,0,1-.366,2.815,8,8,0,0,1-15.111.411h-49.86A8,8,0,0,1,6180.122,10624.5Z" transform="translate(-4198.625 -9299.664)" fill="#707070"/></g><g transform="translate(-2.251 57.836)"><g transform="translate(8063.375 8200.682)"><g transform="translate(-1022.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.59"><tspan x="-19.212" y="0">Shipment</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-956.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.61"><tspan x="-13.84" y="0">Polices</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-890.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.6"><tspan x="-13.9" y="0">Review</tspan></text></g></g></g></g></g><g transform="translate(7960.25 8324.3)"><g transform="translate(-1089 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.25 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5078 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.6"><tspan x="-23.296" y="0">Information</tspan></text></g></g></g><g transform="translate(-8830.75 9723.196)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(10566.75 -8357)" fill="#99d3a1"/><g transform="translate(10415.75 -8534)"><path d="M0,0H20V20H0Z" transform="translate(153 179)" fill="none"/><path d="M5.3,7.661,12.961,0,14.14,1.178,5.3,10.021,0,4.721,1.175,3.536Z" transform="translate(156.033 183.982)" fill="#fff"/></g></g></g><g transform="translate(53.36 94.543)"><g transform="translate(1703.766 844.682)"><g transform="translate(-0.376)"><g transform="translate(6335.25 7374)"><g transform="translate(-1155 636)"><g transform="translate(-46 -53)"><g transform="translate(-5118 -7559)"><rect width="80" height="28" rx="6" transform="translate(-0.27 0.396)" fill="#95a2fe"/><text transform="translate(39.73 17.396)" fill="rgba(255,255,255,0.8)" font-size="10" font-family="Poppins-Regular, Poppins"><tspan x="-16.905" y="0">Details</tspan></text></g></g></g><path d="M4.624,1.593a1,1,0,0,1,1.752,0l3.809,6.925A1,1,0,0,1,9.309,10H1.691A1,1,0,0,1,.815,8.518Z" transform="translate(-6273 -6942) rotate(180)" fill="#95a2fe"/></g></g></g></g><g transform="translate(1649.884 1197.082)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(151 177)" fill="#95a2fe"/><path d="M8.5-3.5a12,12,0,1,1-12,12A12.014,12.014,0,0,1,8.5-3.5Zm0,17a5,5,0,1,0-5-5A5.006,5.006,0,0,0,8.5,13.5Z" transform="translate(154.5 180.5)" fill="#323741"/></g></g></svg>';

  static String shipmentTag =
      '<svg xmlns="http://www.w3.org/2000/svg" width="326" height="73.87" viewBox="0 0 326 73.87"><g transform="translate(-1712.645 -1417.457)"><g transform="translate(-39.229 152.836)"><g opacity="0.39"><path d="M6174.135,10616.828c-.008-.106-.014-.219-.014-.328a6,6,0,0,1,12,0c0,.109-.005.222-.011.328h53.067a6.083,6.083,0,0,1-.056-.829,6,6,0,1,1,11.943.829h0a6,6,0,0,1-11.887,0h-53.067a6,6,0,0,1-11.978,0Zm-132.933,0c-.013-.16-.024-.328-.024-.5a6,6,0,0,1,12.007,0c0,.168-.011.336-.027.5a6,6,0,0,1-11.957,0Zm65.976,0a6.5,6.5,0,0,1-.053-.829,6,6,0,0,1,12,0,6.5,6.5,0,0,1-.059.829,6,6,0,0,1-11.89,0Zm-132.746-.829a6,6,0,1,1,11.942.829h0a6,6,0,0,1-11.942-.829Zm144.636.829h0Zm-65.909,0h0Z" transform="translate(-4198.625 -9299.664)" fill="rgba(0,0,0,0)"/><path d="M6180.122,10622.5a6,6,0,0,0,5.99-5.673h53.067a6,6,0,0,0,11.887,0h0a6,6,0,1,0-11.943-.829,6.083,6.083,0,0,0,.056.829h-53.067c.005-.106.011-.219.011-.328a6,6,0,0,0-12,0c0,.***************.328a6,6,0,0,0,5.987,5.673m-132.937-.168a6,6,0,0,0,5.974-5.5c.016-.16.027-.328.027-.5a6,6,0,0,0-12.007,0c0,.168.011.336.024.5a6.005,6.005,0,0,0,5.982,5.5m65.941-.333a6.005,6.005,0,0,0,5.942-5.172,6.5,6.5,0,0,0,.059-.829,6,6,0,0,0-12,0,6.5,6.5,0,0,0,.053.829,6.008,6.008,0,0,0,5.948,5.172m-132.692,0a6,6,0,0,0,5.941-5.172h0a6,6,0,1,0-5.941,5.172m193.7-5.172h0m-66.957,0h0m72.944,7.673a8,8,0,0,1-7.653-5.673h-51.861a8,8,0,0,1-14.971,0h-50.855a8,8,0,0,1-15.2,0h-51.664a8,8,0,1,1,.43-4h50.974a8.005,8.005,0,0,1,15.724,0h50.166a8,8,0,0,1,15.831,0H6172.3a8,8,0,0,1,15.65,0h49.262a8,8,0,0,1,15.907.773,2,2,0,0,1-.366,2.815,8,8,0,0,1-15.111.411h-49.86A8,8,0,0,1,6180.122,10624.5Z" transform="translate(-4198.625 -9299.664)" fill="#707070"/></g><g transform="translate(-2.251 57.836)"><g transform="translate(8063.375 8200.682)"><g transform="translate(-1089 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.25 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5078.75 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.6"><tspan x="-13.524" y="0">Details</tspan></text></g></g></g><g transform="translate(7997.375 8200.682)"><g transform="translate(-1089 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.25 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5078 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.6"><tspan x="-23.296" y="0">Information</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-956.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.61"><tspan x="-13.84" y="0">Polices</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-890.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.6"><tspan x="-13.9" y="0">Review</tspan></text></g></g></g></g></g><g transform="translate(22.645 -101.668)"><g transform="translate(93 276.125)"><g transform="translate(1704.126 844.682)"><g transform="translate(-0.376)"><g transform="translate(6335.25 7374)"><g transform="translate(-1155 636)"><g transform="translate(-46 -53)"><g transform="translate(-5118 -7559)"><rect width="80" height="28" rx="6" transform="translate(0 0.318)" fill="#95a2fe"/><text transform="translate(40 18.318)" fill="rgba(255,255,255,0.8)" font-size="10" font-family="Poppins-Regular, Poppins"><tspan x="-24.015" y="0">Shipment</tspan></text></g></g></g><path d="M4.624,1.593a1,1,0,0,1,1.752,0l3.809,6.925A1,1,0,0,1,9.309,10H1.691A1,1,0,0,1,.815,8.518Z" transform="translate(-6273 -6942) rotate(180)" fill="#95a2fe"/></g></g></g></g><g transform="translate(1690 1378.082)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(151 177)" fill="#95a2fe"/><path d="M8.5-3.5a12,12,0,1,1-12,12A12.014,12.014,0,0,1,8.5-3.5Zm0,17a5,5,0,1,0-5-5A5.006,5.006,0,0,0,8.5,13.5Z" transform="translate(154.5 180.5)" fill="#323741"/></g></g><g transform="translate(-8835.75 9810.414)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(10566.75 -8357)" fill="#99d3a1"/><g transform="translate(10415.75 -8534)"><path d="M0,0H20V20H0Z" transform="translate(153 179)" fill="none"/><path d="M5.3,7.661,12.961,0,14.14,1.178,5.3,10.021,0,4.721,1.175,3.536Z" transform="translate(156.033 183.982)" fill="#fff"/></g></g><g transform="translate(-8770.605 9810.414)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(10566.75 -8357)" fill="#99d3a1"/><g transform="translate(10415.75 -8534)"><path d="M0,0H20V20H0Z" transform="translate(153 179)" fill="none"/><path d="M5.3,7.661,12.961,0,14.14,1.178,5.3,10.021,0,4.721,1.175,3.536Z" transform="translate(156.033 183.982)" fill="#fff"/></g></g></g></svg>';

  static String dimensionsBox =
      '<svg xmlns="http://www.w3.org/2000/svg" width="155.857" height="141.724" viewBox="0 0 155.857 141.724"><g transform="translate(1.649 0)"><path d="M120.927,3.054a5.156,5.156,0,0,0-2.99.831L72.213,33.2a4.72,4.72,0,0,0-2.2,4.195l2.616,52.743a4.793,4.793,0,0,0,3.323,4.278l64.5,21.637a5.19,5.19,0,0,0,5.192-1.2l37.209-36.42a4.578,4.578,0,0,0,1.412-3.281l.249-52.493a4.845,4.845,0,0,0-3.738-4.652L121.965,3.221a5.134,5.134,0,0,0-1.038-.166Zm.291,6.354q8.063,2.027,16.113,4.07l-34.1,27.16c-6.916-2.092-13.8-4.179-20.848-6.271Zm32.1,8.1q10.8,2.716,21.6,5.44L140.446,51.809c-7.073-2.1-13.99-4.171-20.932-6.271l33.8-28.033Zm24.794,11.878-.208,44.812-32.6,31.9-.581-48.756ZM76.614,39.807,99.5,46.7V63.562l3.863-.042,2.907,1.911,4.486-.5,5.482,3.24-.249-16.529,21.6,6.478.581,50.21L79.023,88.522Zm93.275,11.961-2.949,8.887,5.9-5.648-2.949-3.24Zm1.578,5.814-3.157,3.032V72.242l3.157-3.032Zm-9.095,1.454-2.949,8.887,5.9-5.689-2.949-3.2Zm1.537,5.814-3.115,2.99V79.468l3.115-2.99Zm9.967,3.821L158.344,83.663v3.406l15.533-14.993Z" transform="translate(-47.376 -3.05)" fill="#95a2fe"/><path d="M0,35.713a1,1,0,0,1-.707-.293,1,1,0,0,1,0-1.414l.772-.772a1,1,0,0,1,1.414,1.414l-.772.772A1,1,0,0,1,0,35.713ZM5.014,30.7a1,1,0,0,1-.707-1.707L8.55,24.749a1,1,0,0,1,1.414,1.414L5.721,30.406A1,1,0,0,1,5.014,30.7ZM13.5,22.213a1,1,0,0,1-.707-1.707l4.243-4.243a1,1,0,0,1,1.414,1.414L14.207,21.92A1,1,0,0,1,13.5,22.213Zm8.485-8.485a1,1,0,0,1-.707-1.707L25.52,7.778a1,1,0,0,1,1.414,1.414l-4.243,4.243A1,1,0,0,1,21.985,13.728ZM30.47,5.243a1,1,0,0,1-.707-1.707L34.006-.707a1,1,0,0,1,1.414,0,1,1,0,0,1,0,1.414L31.177,4.95A1,1,0,0,1,30.47,5.243Z" transform="translate(107.865 81.902)" fill="#99d3a1"/><text transform="translate(134.409 106.716) rotate(-45)" fill="#fff" font-size="8" font-family="Poppins-SemiBold, Poppins" font-weight="600"><tspan x="-24.108" y="0">   width</tspan></text><text transform="translate(47.66 118.697) rotate(20)" fill="#fff" font-size="8" font-family="Poppins-SemiBold, Poppins" font-weight="600"><tspan x="-26.568" y="0">   Length</tspan></text><text transform="translate(1.351 56.628) rotate(90)" fill="#fff" font-size="9" font-family="Poppins-SemiBold, Poppins" font-weight="600"><tspan x="-29.124" y="0">   Height</tspan></text><path d="M0,0,49.844,49.844" transform="translate(22.415 96.55) rotate(-25)" fill="#95a2fe"/><path d="M46.669,47.669a1,1,0,0,1-.707-.293l-4.243-4.243a1,1,0,0,1,1.414-1.414l4.243,4.243a1,1,0,0,1-.707,1.707Zm-8.485-8.485a1,1,0,0,1-.707-.293l-4.243-4.243a1,1,0,1,1,1.414-1.414l4.243,4.243a1,1,0,0,1-.707,1.707ZM29.7,30.7a1,1,0,0,1-.707-.293l-4.243-4.243a1,1,0,0,1,1.414-1.414l4.243,4.243A1,1,0,0,1,29.7,30.7Zm-8.485-8.485a1,1,0,0,1-.707-.293l-4.243-4.243a1,1,0,0,1,1.414-1.414l4.243,4.243a1,1,0,0,1-.707,1.707Zm-8.485-8.485a1,1,0,0,1-.707-.293L7.778,9.192A1,1,0,0,1,9.192,7.778l4.243,4.243a1,1,0,0,1-.707,1.707ZM4.243,5.243a1,1,0,0,1-.707-.293L-.707.707a1,1,0,0,1,0-1.414,1,1,0,0,1,1.414,0L4.95,3.536a1,1,0,0,1-.707,1.707Z" transform="translate(22.415 96.55) rotate(-25)" fill="#99d3a1"/><path d="M0,61.525a1,1,0,0,1-1-1V60a1,1,0,0,1,1-1,1,1,0,0,1,1,1v.525A1,1,0,0,1,0,61.525ZM0,55a1,1,0,0,1-1-1V48a1,1,0,0,1,1-1,1,1,0,0,1,1,1v6A1,1,0,0,1,0,55ZM0,43a1,1,0,0,1-1-1V36a1,1,0,0,1,1-1,1,1,0,0,1,1,1v6A1,1,0,0,1,0,43ZM0,31a1,1,0,0,1-1-1V24a1,1,0,0,1,1-1,1,1,0,0,1,1,1v6A1,1,0,0,1,0,31ZM0,19a1,1,0,0,1-1-1V12a1,1,0,0,1,1-1,1,1,0,0,1,1,1v6A1,1,0,0,1,0,19ZM0,7A1,1,0,0,1-1,6V0A1,1,0,0,1,0-1,1,1,0,0,1,1,0V6A1,1,0,0,1,0,7Z" transform="matrix(0.999, -0.035, 0.035, 0.999, 13.351, 31.186)" fill="#99d3a1"/></g></svg>';

  static String policyTag =
      '<svg xmlns="http://www.w3.org/2000/svg" width="326" height="75.034" viewBox="0 0 326 75.034"><g transform="translate(-1708 -1521.245)"><g transform="translate(-43.874 257.788)"><g opacity="0.39"><path d="M6174.135,10616.828c-.008-.106-.014-.219-.014-.328a6,6,0,0,1,12,0c0,.109-.005.222-.011.328h53.067a6.083,6.083,0,0,1-.056-.829,6,6,0,1,1,11.943.829h0a6,6,0,0,1-11.887,0h-53.067a6,6,0,0,1-11.978,0Zm-132.933,0c-.013-.16-.024-.328-.024-.5a6,6,0,0,1,12.007,0c0,.168-.011.336-.027.5a6,6,0,0,1-11.957,0Zm65.976,0a6.5,6.5,0,0,1-.053-.829,6,6,0,0,1,12,0,6.5,6.5,0,0,1-.059.829,6,6,0,0,1-11.89,0Zm-132.746-.829a6,6,0,1,1,11.942.829h0a6,6,0,0,1-11.942-.829Zm144.636.829h0Zm-65.909,0h0Z" transform="translate(-4198.625 -9299.664)" fill="rgba(0,0,0,0)"/><path d="M6180.122,10622.5a6,6,0,0,0,5.99-5.673h53.067a6,6,0,0,0,11.887,0h0a6,6,0,1,0-11.943-.829,6.083,6.083,0,0,0,.056.829h-53.067c.005-.106.011-.219.011-.328a6,6,0,0,0-12,0c0,.***************.328a6,6,0,0,0,5.987,5.673m-132.937-.168a6,6,0,0,0,5.974-5.5c.016-.16.027-.328.027-.5a6,6,0,0,0-12.007,0c0,.168.011.336.024.5a6.005,6.005,0,0,0,5.982,5.5m65.941-.333a6.005,6.005,0,0,0,5.942-5.172,6.5,6.5,0,0,0,.059-.829,6,6,0,0,0-12,0,6.5,6.5,0,0,0,.053.829,6.008,6.008,0,0,0,5.948,5.172m-132.692,0a6,6,0,0,0,5.941-5.172h0a6,6,0,1,0-5.941,5.172m193.7-5.172h0m-66.957,0h0m72.944,7.673a8,8,0,0,1-7.653-5.673h-51.861a8,8,0,0,1-14.971,0h-50.855a8,8,0,0,1-15.2,0h-51.664a8,8,0,1,1,.43-4h50.974a8.005,8.005,0,0,1,15.724,0h50.166a8,8,0,0,1,15.831,0H6172.3a8,8,0,0,1,15.65,0h49.262a8,8,0,0,1,15.907.773,2,2,0,0,1-.366,2.815,8,8,0,0,1-15.111.411h-49.86A8,8,0,0,1,6180.122,10624.5Z" transform="translate(-4198.625 -9299.664)" fill="#707070"/></g><g transform="translate(-2.251 57.836)"><g transform="translate(8063.375 8200.682)"><g transform="translate(-1089 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.25 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5078 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.6"><tspan x="-25.936" y="0">Specification</tspan></text></g></g></g><g transform="translate(7997.375 8200.682)"><g transform="translate(-1089 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.25 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5078 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.6"><tspan x="-23.296" y="0">Information</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-1022.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.59"><tspan x="-19.212" y="0">Shipment</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-890.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.6"><tspan x="-13.9" y="0">Review</tspan></text></g></g></g></g></g><g transform="translate(36 19.176)"><g transform="translate(8181.48 8479.043)"><g transform="translate(-1155 636)"><g transform="translate(-46 -53)"><g transform="translate(-5118 -7559)"><g transform="translate(0 -1)"><rect width="80" height="28" rx="6" transform="translate(0.25 0.026)" fill="#95a2fe"/><text transform="translate(40.25 18.026)" fill="rgba(255,255,255,0.8)" font-size="10" font-family="Poppins-Regular, Poppins"><tspan x="-17.3" y="0">Polices</tspan></text></g></g></g></g><path d="M4.624,1.593a1,1,0,0,1,1.752,0l3.809,6.925A1,1,0,0,1,9.309,10H1.691A1,1,0,0,1,.815,8.518Z" transform="translate(-6272.75 -6942) rotate(180)" fill="#95a2fe"/></g><g transform="translate(1739.73 1362.026)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(151 177)" fill="#95a2fe"/><path d="M8.5-3.5a12,12,0,1,1-12,12A12.014,12.014,0,0,1,8.5-3.5Zm0,17a5,5,0,1,0-5-5A5.006,5.006,0,0,0,8.5,13.5Z" transform="translate(154.5 180.5)" fill="#323741"/></g></g><g transform="translate(-8839.75 9915.202)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(10566.75 -8357)" fill="#99d3a1"/><g transform="translate(10415.75 -8534)"><path d="M0,0H20V20H0Z" transform="translate(153 179)" fill="none"/><path d="M5.3,7.661,12.961,0,14.14,1.178,5.3,10.021,0,4.721,1.175,3.536Z" transform="translate(156.033 183.982)" fill="#fff"/></g></g><g transform="translate(-8774.75 9915.202)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(10566.75 -8357)" fill="#99d3a1"/><g transform="translate(10415.75 -8534)"><path d="M0,0H20V20H0Z" transform="translate(153 179)" fill="none"/><path d="M5.3,7.661,12.961,0,14.14,1.178,5.3,10.021,0,4.721,1.175,3.536Z" transform="translate(156.033 183.982)" fill="#fff"/></g></g><g transform="translate(-8709.75 9915.202)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(10566.75 -8357)" fill="#99d3a1"/><g transform="translate(10415.75 -8534)"><path d="M0,0H20V20H0Z" transform="translate(153 179)" fill="none"/><path d="M5.3,7.661,12.961,0,14.14,1.178,5.3,10.021,0,4.721,1.175,3.536Z" transform="translate(156.033 183.982)" fill="#fff"/></g></g></g></svg>';

  static String reviewTag =
      '<svg xmlns="http://www.w3.org/2000/svg" width="335" height="75.273" viewBox="0 0 335 75.273"><g transform="translate(-1706 -1618)"><g transform="translate(-45.874 354.782)"><g opacity="0.39"><path d="M6174.135,10616.828c-.008-.106-.014-.219-.014-.328a6,6,0,0,1,12,0c0,.109-.005.222-.011.328h53.067a6.083,6.083,0,0,1-.056-.829,6,6,0,1,1,11.943.829h0a6,6,0,0,1-11.887,0h-53.067a6,6,0,0,1-11.978,0Zm-132.933,0c-.013-.16-.024-.328-.024-.5a6,6,0,0,1,12.007,0c0,.168-.011.336-.027.5a6,6,0,0,1-11.957,0Zm65.976,0a6.5,6.5,0,0,1-.053-.829,6,6,0,0,1,12,0,6.5,6.5,0,0,1-.059.829,6,6,0,0,1-11.89,0Zm-132.746-.829a6,6,0,1,1,11.942.829h0a6,6,0,0,1-11.942-.829Zm144.636.829h0Zm-65.909,0h0Z" transform="translate(-4198.625 -9299.664)" fill="rgba(0,0,0,0)"/><path d="M6180.122,10622.5a6,6,0,0,0,5.99-5.673h53.067a6,6,0,0,0,11.887,0h0a6,6,0,1,0-11.943-.829,6.083,6.083,0,0,0,.056.829h-53.067c.005-.106.011-.219.011-.328a6,6,0,0,0-12,0c0,.***************.328a6,6,0,0,0,5.987,5.673m-132.937-.168a6,6,0,0,0,5.974-5.5c.016-.16.027-.328.027-.5a6,6,0,0,0-12.007,0c0,.168.011.336.024.5a6.005,6.005,0,0,0,5.982,5.5m65.941-.333a6.005,6.005,0,0,0,5.942-5.172,6.5,6.5,0,0,0,.059-.829,6,6,0,0,0-12,0,6.5,6.5,0,0,0,.053.829,6.008,6.008,0,0,0,5.948,5.172m-132.692,0a6,6,0,0,0,5.941-5.172h0a6,6,0,1,0-5.941,5.172m193.7-5.172h0m-66.957,0h0m72.944,7.673a8,8,0,0,1-7.653-5.673h-51.861a8,8,0,0,1-14.971,0h-50.855a8,8,0,0,1-15.2,0h-51.664a8,8,0,1,1,.43-4h50.974a8.005,8.005,0,0,1,15.724,0h50.166a8,8,0,0,1,15.831,0H6172.3a8,8,0,0,1,15.65,0h49.262a8,8,0,0,1,15.907.773,2,2,0,0,1-.366,2.815,8,8,0,0,1-15.111.411h-49.86A8,8,0,0,1,6180.122,10624.5Z" transform="translate(-4198.625 -9299.664)" fill="#707070"/></g><g transform="translate(-2.251 57.836)"><g transform="translate(8063.375 8200.682)"><g transform="translate(-1089 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.25 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5078 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.6"><tspan x="-13.524" y="0">Details</tspan></text></g></g></g><g transform="translate(7997.375 8200.682)"><g transform="translate(-1089 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.25 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5078 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.6"><tspan x="-23.296" y="0">Information</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-1022.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.59"><tspan x="-19.212" y="0">Shipment</tspan></text></g></g></g><g transform="translate(8063.375 8200.682)"><g transform="translate(-956.75 673.5)"><g transform="translate(-46 -58)"><path d="M4.65,0h52.7C59.918,0,62,.9,62,2V9c0,1.1-2.082,2-4.65,2H4.65C2.082,11,0,10.1,0,9V2C0,.9,2.082,0,4.65,0Z" transform="translate(-5108.5 -7546.526)" fill="none" opacity="0.1"/><text transform="translate(-5077.5 -7538.526)" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Poppins-Regular, Poppins" opacity="0.61"><tspan x="-13.84" y="0">Polices</tspan></text></g></g></g></g></g><g transform="translate(-8839.75 10012.196)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(10566.75 -8357)" fill="#99d3a1"/><g transform="translate(10415.75 -8534)"><path d="M0,0H20V20H0Z" transform="translate(153 179)" fill="none"/><path d="M5.3,7.661,12.961,0,14.14,1.178,5.3,10.021,0,4.721,1.175,3.536Z" transform="translate(156.033 183.982)" fill="#fff"/></g></g><g transform="translate(-8774.75 10012.196)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(10566.75 -8357)" fill="#99d3a1"/><g transform="translate(10415.75 -8534)"><path d="M0,0H20V20H0Z" transform="translate(153 179)" fill="none"/><path d="M5.3,7.661,12.961,0,14.14,1.178,5.3,10.021,0,4.721,1.175,3.536Z" transform="translate(156.033 183.982)" fill="#fff"/></g></g><g transform="translate(-8709.75 10012.196)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(10566.75 -8357)" fill="#99d3a1"/><g transform="translate(10415.75 -8534)"><path d="M0,0H20V20H0Z" transform="translate(153 179)" fill="none"/><path d="M5.3,7.661,12.961,0,14.14,1.178,5.3,10.021,0,4.721,1.175,3.536Z" transform="translate(156.033 183.982)" fill="#fff"/></g></g><g transform="translate(-8644.75 10012.196)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(10566.75 -8357)" fill="#99d3a1"/><g transform="translate(10415.75 -8534)"><path d="M0,0H20V20H0Z" transform="translate(153 179)" fill="none"/><path d="M5.3,7.661,12.961,0,14.14,1.178,5.3,10.021,0,4.721,1.175,3.536Z" transform="translate(156.033 183.982)" fill="#fff"/></g></g><g transform="translate(98 116.176)"><g transform="translate(8181.48 8479.043)"><g transform="translate(-1155 636)"><g transform="translate(-46 -53)"><g transform="translate(-5118 -7559)"><g transform="translate(0 -1)"><rect width="80" height="28" rx="6" transform="translate(0.52 -0.219)" fill="#95a2fe"/><text transform="translate(40.52 17.781)" fill="rgba(255,255,255,0.8)" font-size="10" font-family="Poppins-Regular, Poppins"><tspan x="-17.375" y="0">Review</tspan></text></g></g></g></g><path d="M4.624,1.593a1,1,0,0,1,1.752,0l3.809,6.925A1,1,0,0,1,9.309,10H1.691A1,1,0,0,1,.815,8.518Z" transform="translate(-6272.75 -6942) rotate(180)" fill="#95a2fe"/></g><g transform="translate(1739.73 1362.026)"><path d="M24,12A12,12,0,1,1,12,0,12,12,0,0,1,24,12Z" transform="translate(151 177)" fill="#95a2fe"/><path d="M8.5-3.5a12,12,0,1,1-12,12A12.014,12.014,0,0,1,8.5-3.5Zm0,17a5,5,0,1,0-5-5A5.006,5.006,0,0,0,8.5,13.5Z" transform="translate(154.5 180.5)" fill="#323741"/></g></g></g></svg>';

  // ADD PRODUCT SVG IMAGES END

  static String iconPickUp =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#95a2fe" opacity="0.2"/><g transform="translate(-177.111 -87.434)"><path d="M356.865,196v7.822L350,207.72V199.9Z" transform="translate(-160.644 -100.134)" fill="#95a2fe"/><path d="M202.79,95.18,196.194,99,189.28,95.18l6.743-3.9Z" transform="translate(-6.961)" fill="#95a2fe"/><path d="M188.62,199.9v7.822l-6.62-3.9V196Z" transform="translate(0 -100.135)" fill="#95a2fe"/></g></svg>';
  static String iconCallBlue =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#95a2fe" opacity="0.2"/><path d="M12.443,16A8.95,8.95,0,0,1,8.23,14.942l-.4-.222A16,16,0,0,1,1.315,8.205l-.222-.4A8.95,8.95,0,0,1,0,3.557v-.6A1.778,1.778,0,0,1,.524,1.7L2.026.2A.667.667,0,0,1,3.075.33l2,3.431a.889.889,0,0,1-.142,1.075l-1.68,1.68a.444.444,0,0,0-.08.524l.311.587a12.026,12.026,0,0,0,4.888,4.88l.587.32a.444.444,0,0,0,.524-.08l1.68-1.68a.889.889,0,0,1,1.075-.142l3.431,2a.667.667,0,0,1,.133,1.049l-1.5,1.5A1.778,1.778,0,0,1,13.039,16Z" transform="translate(4 4)" fill="#95a2fe"/></svg>';
  static String iconBuyer =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#95a2fe" opacity="0.2"/><g transform="translate(4 4)"><path d="M3.167,0a3.164,3.164,0,0,0-.08,6.327.538.538,0,0,1,.147,0H3.28A3.164,3.164,0,0,0,3.167,0Z" transform="translate(4.833 1.333)" fill="#95a2fe"/><path d="M8.08.93a6.619,6.619,0,0,0-6.767,0A2.631,2.631,0,0,0,0,3.083a2.609,2.609,0,0,0,1.307,2.14,6.16,6.16,0,0,0,3.387.94,6.16,6.16,0,0,0,3.387-.94A2.63,2.63,0,0,0,9.387,3.07,2.625,2.625,0,0,0,8.08.93Z" transform="translate(3.307 8.503)" fill="#95a2fe"/><path d="M0,0H16V16H0Z" transform="translate(16 16) rotate(180)" fill="none" opacity="0"/></g></svg>';

  static String iconOrderRejected =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#F5627F" opacity="0.2"/><path d="M11.848,10.438a.5.5,0,0,1,0,.71l-.7.7a.5.5,0,0,1-.71,0L6,7.408l-4.44,4.44a.5.5,0,0,1-.71,0l-.7-.7a.5.5,0,0,1,0-.71L4.588,6,.148,1.558a.5.5,0,0,1,0-.71l.7-.7a.5.5,0,0,1,.71,0L6,4.588l4.44-4.44a.5.5,0,0,1,.71,0l.7.7a.5.5,0,0,1,0,.71L7.408,6Z" transform="translate(6.002 6.002)" fill="#F5627F"/></svg>';

  static String iconOrderCancelled =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#ffdd7e" opacity="0.2"/><path d="M11.848,10.438a.5.5,0,0,1,0,.71l-.7.7a.5.5,0,0,1-.71,0L6,7.408l-4.44,4.44a.5.5,0,0,1-.71,0l-.7-.7a.5.5,0,0,1,0-.71L4.588,6,.148,1.558a.5.5,0,0,1,0-.71l.7-.7a.5.5,0,0,1,.71,0L6,4.588l4.44-4.44a.5.5,0,0,1,.71,0l.7.7a.5.5,0,0,1,0,.71L7.408,6Z" transform="translate(6.002 6.002)" fill="#ffdd7e"/></svg>';

  static String iconShipped =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#95a2fe" opacity="0.2"/><path d="M14.146,1.313l-8.9,8.9a.42.42,0,0,1-.6,0L.124,5.687a.42.42,0,0,1,0-.6L.712,4.5a.42.42,0,0,1,.6,0L4.944,8.139,12.962.121a.428.428,0,0,1,.6,0l.588.6a.42.42,0,0,1,0,.6Z" transform="translate(5 7)" fill="#95a2fe"/></svg>';

  static String iconOrderConfirm =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g transform="translate(-1065 -9712)"><rect width="24" height="24" rx="4" transform="translate(1065 9712)" fill="#95a2fe" opacity="0.21"/><g transform="translate(641 9528)"><g transform="translate(428 188)"><path d="M11.2,4.642a3.084,3.084,0,0,0-2.054-.88V3.255A3.261,3.261,0,0,0,5.576.015a3.432,3.432,0,0,0-2.934,3.36v.387a3.084,3.084,0,0,0-2.054.88A2.932,2.932,0,0,0,.036,6.989L.5,10.7c.14,1.3.667,2.634,3.534,2.634h3.72c2.867,0,3.394-1.333,3.534-2.627l.467-3.727A2.923,2.923,0,0,0,11.2,4.642ZM5.67.942A2.324,2.324,0,0,1,8.223,3.255v.467H3.569V3.375A2.508,2.508,0,0,1,5.67.942ZM5.9,11.056A2.527,2.527,0,1,1,8.423,8.529,2.53,2.53,0,0,1,5.9,11.056Z" transform="translate(2.105 1.332)" fill="#95a2fe"/><path d="M1.158,2.312a.5.5,0,0,1-.353-.147l-.66-.66A.5.5,0,1,1,.852.8l.32.32L2.239.132a.5.5,0,0,1,.68.733L1.5,2.179A.522.522,0,0,1,1.158,2.312Z" transform="translate(6.462 8.782)" fill="#95a2fe"/><path d="M0,0H16V16H0Z" transform="translate(16.002 16.002) rotate(180)" fill="none" opacity="0"/></g></g></g></svg>';

  static String iconOrderd =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#95a2fe" opacity="0.2"/><path d="M6.88,16H1.5A1.554,1.554,0,0,1,0,14.4V1.6A1.554,1.554,0,0,1,1.5,0h9A1.554,1.554,0,0,1,12,1.6v8.936a1.667,1.667,0,0,1-.443,1.136L7.943,15.528A1.463,1.463,0,0,1,6.88,16Zm.62-5.6a.777.777,0,0,0-.75.8v3.344L10.635,10.4Zm-4.875-4a.388.388,0,0,0-.375.4v.8a.388.388,0,0,0,.375.4h3.75a.388.388,0,0,0,.375-.4V6.8a.388.388,0,0,0-.375-.4Zm0-3.2a.389.389,0,0,0-.375.4v.8a.389.389,0,0,0,.375.4h6.75a.389.389,0,0,0,.375-.4V3.6a.389.389,0,0,0-.375-.4Z" transform="translate(6 4)" fill="#95a2fe"/></svg>';

  static String iconReturned =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#ffa163" opacity="0.2"/><path d="M9.488,20a.508.508,0,0,1-.355-.144l-3-3a.5.5,0,0,1,.347-.86H8.493v-7.5a.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5v7.5h2a.5.5,0,0,1,.36.85l-3,3A.507.507,0,0,1,9.488,20Zm-5.495-8h-1a.5.5,0,0,1-.5-.5v-7.5h-2a.5.5,0,0,1-.35-.851l3-3a.51.51,0,0,1,.71,0l3,3a.5.5,0,0,1-.358.851h-2v7.5A.5.5,0,0,1,3.993,11.993Z" transform="translate(5.5 2)" fill="#ffa163"/></svg>';

  static String iconReturnedConfirmed =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#99D3A1" opacity="0.2"/><path d="M9.488,20a.508.508,0,0,1-.355-.144l-3-3a.5.5,0,0,1,.347-.86H8.493v-7.5a.5.5,0,0,1,.5-.5h1a.5.5,0,0,1,.5.5v7.5h2a.5.5,0,0,1,.36.85l-3,3A.507.507,0,0,1,9.488,20Zm-5.495-8h-1a.5.5,0,0,1-.5-.5v-7.5h-2a.5.5,0,0,1-.35-.851l3-3a.51.51,0,0,1,.71,0l3,3a.5.5,0,0,1-.358.851h-2v7.5A.5.5,0,0,1,3.993,11.993Z" transform="translate(5.5 2)" fill="#99D3A1"/></svg>';

  static String iconDelivery =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g transform="translate(1484 -9902)"><g transform="translate(0 31)"><g transform="translate(0 31)"><rect width="24" height="24" rx="4" transform="translate(-1484 9840)" fill="#95a2fe" opacity="0.2"/></g></g><g transform="translate(-1588 9653.998)"><g transform="translate(108 252)"><path d="M13,7a.33.33,0,0,1,.333.333V8a2,2,0,0,1-2,2,2,2,0,0,0-4,0H6a2,2,0,0,0-4,0A2,2,0,0,1,0,8V6.667A.669.669,0,0,1,.667,6H7A1.667,1.667,0,0,0,8.668,4.334V.667A.669.669,0,0,1,9.334,0h.56a1.344,1.344,0,0,1,1.16.673l.427.747a.168.168,0,0,1-.147.247A1.667,1.667,0,0,0,9.668,3.334v2A1.667,1.667,0,0,0,11.335,7Z" transform="translate(1.333 3.334)" fill="#95a2fe"/><path d="M2.667,1.333A1.333,1.333,0,1,1,1.333,0,1.333,1.333,0,0,1,2.667,1.333Z" transform="translate(4 12.001)" fill="#95a2fe"/><path d="M2.667,1.333A1.333,1.333,0,1,1,1.333,0,1.333,1.333,0,0,1,2.667,1.333Z" transform="translate(9.334 12.001)" fill="#95a2fe"/><path d="M2.667,2.354v.98h-2A.669.669,0,0,1,0,2.667v-2A.669.669,0,0,1,.667,0h.86l.967,1.694A1.337,1.337,0,0,1,2.667,2.354Z" transform="translate(12.001 6.001)" fill="#95a2fe"/><path d="M0,0H16V16H0Z" fill="none" opacity="0"/><path d="M7.388,0H2.46A2.459,2.459,0,0,0,0,2.46V6.714a.669.669,0,0,0,.667.667h6.1A1.235,1.235,0,0,0,8,6.147V.613A.612.612,0,0,0,7.388,0ZM5.381,3.38,3.987,4.727a.513.513,0,0,1-.347.14.489.489,0,0,1-.347-.14l-.673-.64a.5.5,0,0,1,.693-.727l.327.313L4.687,2.66a.5.5,0,1,1,.693.72Z" transform="translate(1.333 1.333)" fill="#95a2fe"/></g></g></g></svg>';

  static String iconInvoice =
      '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><rect width="16" height="16" fill="#323741"/><path d="M10.8,10H1.2A1.227,1.227,0,0,1,0,8.75V1.25A1.227,1.227,0,0,1,1.2,0H5.754a.584.584,0,0,1,.42.181l.852.888a.585.585,0,0,0,.42.181H10.8A1.227,1.227,0,0,1,12,2.5V8.75A1.227,1.227,0,0,1,10.8,10ZM1.2,1.25v7.5h9.6V2.5H7.446A1.753,1.753,0,0,1,6.18,1.95L5.5,1.25ZM5.7,7.5h-3a.307.307,0,0,1-.3-.312V6.563a.307.307,0,0,1,.3-.313h3a.307.307,0,0,1,.3.313v.625A.306.306,0,0,1,5.7,7.5ZM6.9,5H2.7a.307.307,0,0,1-.3-.313V4.062a.307.307,0,0,1,.3-.312H6.9a.306.306,0,0,1,.3.312v.625A.307.307,0,0,1,6.9,5Z" transform="translate(2 3)" fill="#fff" opacity="0.6"/></svg>';

  static String iconOrderUser =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#95a2fe" opacity="0.2"/><g transform="translate(4 4)"><path d="M3.167,0a3.164,3.164,0,0,0-.08,6.327.538.538,0,0,1,.147,0H3.28A3.164,3.164,0,0,0,3.167,0Z" transform="translate(4.833 1.333)" fill="#95a2fe"/><path d="M8.08.93a6.619,6.619,0,0,0-6.767,0A2.631,2.631,0,0,0,0,3.083a2.609,2.609,0,0,0,1.307,2.14,6.16,6.16,0,0,0,3.387.94,6.16,6.16,0,0,0,3.387-.94A2.63,2.63,0,0,0,9.387,3.07,2.625,2.625,0,0,0,8.08.93Z" transform="translate(3.307 8.503)" fill="#95a2fe"/><path d="M0,0H16V16H0Z" transform="translate(16 16) rotate(180)" fill="none" opacity="0"/></g></svg>';

  static String iconOrderLocationPin =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g transform="translate(1484 -9871)"><g transform="translate(0 31)"><rect width="24" height="24" rx="4" transform="translate(-1484 9840)" fill="#95a2fe" opacity="0.2"/></g><g transform="translate(-1480 9875)"><path d="M11.667,4.467A5.75,5.75,0,0,0,5.92,0H5.913A5.749,5.749,0,0,0,.167,4.46C-.613,7.9,1.493,10.813,3.4,12.647a3.615,3.615,0,0,0,5.033,0C10.34,10.813,12.447,7.907,11.667,4.467ZM5.92,7.807a2.1,2.1,0,1,1,2.1-2.1A2.1,2.1,0,0,1,5.92,7.807Z" transform="translate(2.08 1.167)" fill="#95a2fe"/><path d="M0,0H16V16H0Z" fill="none" opacity="0"/><path d="M0,0H16V16H0Z" transform="translate(16 16) rotate(180)" fill="none" opacity="0"/></g></g></svg>';

  static String iconPriceTagWhite =
      '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28"><rect width="28" height="28" rx="14" fill="#fff" opacity="0.06"/><path d="M17.1,14H.9a.889.889,0,0,1-.9-.875V.875A.889.889,0,0,1,.9,0H17.1a.889.889,0,0,1,.9.875v12.25A.889.889,0,0,1,17.1,14ZM5.4,1.75a3.556,3.556,0,0,1-3.6,3.5v3.5a3.556,3.556,0,0,1,3.6,3.5h7.2a3.556,3.556,0,0,1,3.6-3.5V5.25a3.556,3.556,0,0,1-3.6-3.5ZM9,9.625A2.666,2.666,0,0,1,6.3,7a2.7,2.7,0,0,1,5.4,0A2.666,2.666,0,0,1,9,9.625Z" transform="translate(5 7)" fill="#fff"/></svg>';

  static String iconPhone =
      '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28"><rect width="28" height="28" rx="14" fill="#fff" opacity="0.12"/><path d="M12.445,16a8.951,8.951,0,0,1-4.213-1.058l-.4-.222A16,16,0,0,1,1.316,8.206l-.222-.4A8.951,8.951,0,0,1,0,3.557v-.6A1.778,1.778,0,0,1,.524,1.7L2.027.2A.667.667,0,0,1,3.076.33l2,3.431a.889.889,0,0,1-.142,1.076l-1.68,1.68a.444.444,0,0,0-.08.524l.311.587a12.027,12.027,0,0,0,4.889,4.88l.587.32a.444.444,0,0,0,.524-.08l1.68-1.68a.889.889,0,0,1,1.076-.142l3.431,2a.667.667,0,0,1,.133,1.049l-1.5,1.5A1.778,1.778,0,0,1,13.04,16Z" transform="translate(6 6)" fill="#95a2fe"/></svg>';

  static String iconMail =
      '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28"><rect width="28" height="28" rx="14" fill="#fff" opacity="0.12"/><path d="M14.4,12.8H1.6A1.6,1.6,0,0,1,0,11.2V1.6A1.6,1.6,0,0,1,1.6,0H14.4A1.6,1.6,0,0,1,16,1.6v9.6A1.6,1.6,0,0,1,14.4,12.8ZM1.6,3.92V5.6L6.68,9.16a2.3,2.3,0,0,0,2.64,0L14.4,5.6V3.92L8.52,8.04a.913.913,0,0,1-1.04,0Z" transform="translate(6 7.6)" fill="#95a2fe"/></svg>';

  static String iconDelete =
      '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect width="20" height="20" fill="#323741"/><path d="M2.415,14a1.323,1.323,0,0,1-1.247-1.3L.625,4.2h8.75l-.532,8.5A1.323,1.323,0,0,1,7.6,14H2.415ZM9.687,2.8H.313A.333.333,0,0,1,0,2.45v-.7A.333.333,0,0,1,.313,1.4H3.125V.7A.666.666,0,0,1,3.75,0h2.5a.666.666,0,0,1,.625.7v.7H9.687A.333.333,0,0,1,10,1.75v.7A.333.333,0,0,1,9.687,2.8Z" transform="translate(5 3)" fill="#f5627f"/></svg>';

  static String iconGreenEdit =
      '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect width="20" height="20" fill="#323741"/><path d="M.454,14a.385.385,0,0,1-.274-.114A.387.387,0,0,1,.032,13.4l1.334-3.368a1.545,1.545,0,0,1,.349-.528L7.978,3.245a.391.391,0,0,1,.276-.115.385.385,0,0,1,.275.115l2.22,2.189a.39.39,0,0,1,0,.543l-6.254,6.28a1.536,1.536,0,0,1-.528.349L.6,13.972A.386.386,0,0,1,.454,14ZM11.948,4.633a.391.391,0,0,1-.276-.115L9.491,2.305a.39.39,0,0,1,0-.543L10.9.342A1.159,1.159,0,0,1,11.721,0h.6a1.157,1.157,0,0,1,.822.342l.512.512A1.159,1.159,0,0,1,14,1.677v.59a1.172,1.172,0,0,1-.342.839L12.223,4.518A.39.39,0,0,1,11.948,4.633Z" transform="translate(3 3)" fill="#99d3a1"/></svg>';

  static String iconReply =
      '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect width="20" height="20" fill="#323741"/><path d="M.35,14A.35.35,0,0,1,0,13.65V2.8A2.8,2.8,0,0,1,2.8,0h8.4A2.8,2.8,0,0,1,14,2.8V8.4a2.8,2.8,0,0,1-2.8,2.8H4.16a2.09,2.09,0,0,0-1.465.6l-2.1,2.1A.346.346,0,0,1,.35,14Zm2.8-7.7a.351.351,0,0,0-.35.35v.7a.35.35,0,0,0,.35.35h4.9a.35.35,0,0,0,.35-.35v-.7a.35.35,0,0,0-.35-.35Zm0-2.8a.35.35,0,0,0-.35.35v.7a.351.351,0,0,0,.35.35h7.7a.351.351,0,0,0,.35-.35v-.7a.35.35,0,0,0-.35-.35Z" transform="translate(3 3)" fill="#95a2fe"/></svg>';

  static String iconPriceTag =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#95a2fe" opacity="0.2"/><path d="M15.2,12H.8a.777.777,0,0,1-.8-.75V.75A.777.777,0,0,1,.8,0H15.2a.777.777,0,0,1,.8.75v10.5A.777.777,0,0,1,15.2,12ZM4.8,1.5a3.109,3.109,0,0,1-3.2,3v3a3.109,3.109,0,0,1,3.2,3h6.4a3.109,3.109,0,0,1,3.2-3v-3a3.109,3.109,0,0,1-3.2-3ZM8,8.25A2.331,2.331,0,0,1,5.6,6,2.331,2.331,0,0,1,8,3.75,2.331,2.331,0,0,1,10.4,6,2.331,2.331,0,0,1,8,8.25Z" transform="translate(4 6)" fill="#95a2fe"/></svg>';

  static String iconMobile =
      '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect width="20" height="20" fill="#323741"/><path d="M7.857,18H2.143A2.317,2.317,0,0,1,0,15.546V2.454A2.317,2.317,0,0,1,2.143,0H7.857A2.317,2.317,0,0,1,10,2.454V15.546A2.317,2.317,0,0,1,7.857,18ZM2.143,1.636a.772.772,0,0,0-.714.818V15.546a.772.772,0,0,0,.714.818H7.857a.772.772,0,0,0,.714-.818V2.454a.772.772,0,0,0-.714-.818.772.772,0,0,0-.714.818.772.772,0,0,1-.714.818H3.572a.773.773,0,0,1-.715-.818A.772.772,0,0,0,2.143,1.636Z" transform="translate(5 1)" fill="#c2c3c6"/></svg>';

  static String iconChangePassword =
      '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect width="20" height="20" fill="#323741"/><path d="M10.5,16h-9A1.554,1.554,0,0,1,0,14.4V8A1.554,1.554,0,0,1,1.5,6.4V4.8A4.663,4.663,0,0,1,6,0a4.663,4.663,0,0,1,4.5,4.8V6.4A1.554,1.554,0,0,1,12,8v6.4A1.554,1.554,0,0,1,10.5,16ZM5.25,9.6a.777.777,0,0,0-.75.8V12a.777.777,0,0,0,.75.8h1.5A.777.777,0,0,0,7.5,12V10.4a.777.777,0,0,0-.75-.8ZM6,1.6A3.109,3.109,0,0,0,3,4.8V6.4H9V4.8A3.108,3.108,0,0,0,6,1.6Z" transform="translate(4 2)" fill="#c2c3c6"/></svg>';

  static String iconDeactivate =
      '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect width="20" height="20" fill="#323741"/><path d="M8,16a8,8,0,1,1,8-8A8.009,8.009,0,0,1,8,16ZM2.952,4.08h0A6.393,6.393,0,0,0,8,14.4h.006a6.366,6.366,0,0,0,3.914-1.352L2.952,4.08ZM7.994,1.6A6.363,6.363,0,0,0,4.08,2.952l8.968,8.968A6.393,6.393,0,0,0,8,1.6Z" transform="translate(2 2)" fill="#c2c3c6"/></svg>';

  static String iconUser =
      '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14"><path d="M12.445,14H1.555A1.557,1.557,0,0,1,0,12.445V1.555A1.557,1.557,0,0,1,1.555,0H12.445A1.557,1.557,0,0,1,14,1.555V12.445A1.557,1.557,0,0,1,12.445,14ZM1.555,1.555V12.445H12.445V1.555Zm8.75,9.334H3.695a.583.583,0,0,1-.505-.848l.7-1.408a1.549,1.549,0,0,1,1.376-.855H8.735a1.549,1.549,0,0,1,1.376.855l.716,1.408a.582.582,0,0,1-.023.571A.589.589,0,0,1,10.305,10.889ZM7,6.417H6.992a1.754,1.754,0,0,1-1.232-.51,1.73,1.73,0,0,1-.51-1.24A1.75,1.75,0,1,1,7,6.417Z" fill="#c2c3c6"/></svg>';

  static String iconDoc =
      '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect width="20" height="20" fill="#323741"/><path d="M10.207,16a2.61,2.61,0,0,1-.935-.173L8.9,15.691a2.566,2.566,0,0,0-1.857,0l-.368.136a2.615,2.615,0,0,1-3.321-1.368L3.2,14.1a2.644,2.644,0,0,0-1.3-1.343l-.36-.161A2.617,2.617,0,0,1,.173,9.276l.136-.368a2.556,2.556,0,0,0,0-1.856L.173,6.684A2.617,2.617,0,0,1,1.542,3.365L1.9,3.2a2.65,2.65,0,0,0,1.3-1.3l.184-.36A2.616,2.616,0,0,1,6.728.181L7.1.317a2.554,2.554,0,0,0,.928.174A2.558,2.558,0,0,0,8.952.317L9.32.181a2.616,2.616,0,0,1,3.322,1.368l.16.36a2.653,2.653,0,0,0,1.3,1.3l.36.184a2.618,2.618,0,0,1,1.368,3.32l-.136.368a2.555,2.555,0,0,0,0,1.855l.136.392a2.617,2.617,0,0,1-1.368,3.319l-.36.16a2.648,2.648,0,0,0-1.345,1.3l-.16.36A2.622,2.622,0,0,1,10.207,16ZM5.223,7.691a.385.385,0,0,0-.28.121l-.424.424a.4.4,0,0,0,0,.568l1.905,1.88a.385.385,0,0,0,.56,0l4.5-4.5a.4.4,0,0,0,0-.551l-.424-.424a.4.4,0,0,0-.56,0L6.7,9,5.5,7.812A.379.379,0,0,0,5.223,7.691Z" transform="translate(2 2)" fill="#c2c3c6"/></svg>';

  static String iconEmail =
      '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect width="20" height="20" fill="#323741"/><path d="M14.4,12H1.6A1.554,1.554,0,0,1,0,10.5v-9A1.554,1.554,0,0,1,1.6,0H14.4A1.554,1.554,0,0,1,16,1.5v9A1.554,1.554,0,0,1,14.4,12ZM1.6,3.675V5.25L6.68,8.587a2.429,2.429,0,0,0,2.64,0L14.4,5.25V3.675L8.52,7.538a.963.963,0,0,1-1.04,0Z" transform="translate(2 4)" fill="#c2c3c6"/></svg>';

  static String iconPin =
      '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="27" viewBox="0 0 28 27"><rect width="28" height="27" rx="13.5" fill="#fff" opacity="0.12"/><path d="M7.335,16H6.752a.872.872,0,0,1-.829-.545l-.446-1.232A5.775,5.775,0,0,0,3.13,11.682C1.591,10.565,0,9.41,0,6.4,0,2.871,3.14,0,7,0s7,2.871,7,6.4c0,3-1.567,4.159-3.083,5.276A5.665,5.665,0,0,0,8.61,14.223l-.446,1.232A.872.872,0,0,1,7.335,16ZM7,4A2.523,2.523,0,0,0,4.375,6.4,2.523,2.523,0,0,0,7,8.8a2.737,2.737,0,0,0,1.856-.7,2.285,2.285,0,0,0,.769-1.7A2.523,2.523,0,0,0,7,4Z" transform="translate(7 6)" fill="#95a2fe"/></svg>';

  static String iconMapWithBackground =
      '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="27" viewBox="0 0 28 27"><g transform="translate(6 5.5)" opacity="0.7"><path d="M0,0H16V16H0Z" fill="none" opacity="0"/><path d="M.729,0a.729.729,0,1,0,.729.729A.727.727,0,0,0,.729,0Z" transform="translate(5.387 4.951)" fill="#95a2fe"/><path d="M12.693,1.983A3.447,3.447,0,0,0,9.256,0H3.79A3.789,3.789,0,0,0,0,3.789V9.255a3.447,3.447,0,0,0,1.983,3.437.331.331,0,0,0,.359-.072L12.621,2.342A.313.313,0,0,0,12.693,1.983Zm-7.129,4.7a1.307,1.307,0,0,1-.92.365,1.337,1.337,0,0,1-.92-.365A3.007,3.007,0,0,1,2.609,3.868,2.031,2.031,0,0,1,4.644,2.309,2.03,2.03,0,0,1,6.679,3.874,3.03,3.03,0,0,1,5.564,6.679Z" transform="translate(1.458 1.458)" fill="#95a2fe"/><path d="M8.727,3.835a.333.333,0,0,1-.058.532,4.738,4.738,0,0,1-2.333.54H.225a.227.227,0,0,1-.153-.394l4.4-4.4a.361.361,0,0,1,.518,0Z" transform="translate(4.523 9.634)" fill="#95a2fe"/><path d="M4.915.225v6.11a4.718,4.718,0,0,1-.54,2.333.337.337,0,0,1-.532.058L.109,4.994a.361.361,0,0,1,0-.518l4.4-4.4A.231.231,0,0,1,4.915.225Z" transform="translate(9.627 4.523)" fill="#95a2fe"/></g><rect width="28" height="27" rx="13.5" fill="#fff" opacity="0.12"/></svg>';

  static String iconDocument =
      '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28"><g transform="translate(0 0)"><rect width="28" height="28" rx="14" transform="translate(0 0)" fill="#fff" opacity="0.12"/><path d="M8.027,16H1.75A1.682,1.682,0,0,1,0,14.4V1.6A1.682,1.682,0,0,1,1.75,0h10.5A1.682,1.682,0,0,1,14,1.6v8.936a1.545,1.545,0,0,1-.517,1.136L9.266,15.528A1.848,1.848,0,0,1,8.027,16Zm.723-5.6a.841.841,0,0,0-.875.8v3.344L12.408,10.4Zm-5.687-4a.421.421,0,0,0-.438.4v.8a.421.421,0,0,0,.438.4H7.437a.421.421,0,0,0,.438-.4V6.8a.421.421,0,0,0-.438-.4Zm0-3.2a.421.421,0,0,0-.438.4v.8a.421.421,0,0,0,.438.4h7.875a.42.42,0,0,0,.437-.4V3.6a.42.42,0,0,0-.437-.4Z" transform="translate(7 6)" fill="#95a2fe"/></g></svg>';

  static String iconShare =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M9.438,8.042,12,5.48V19.034a1.241,1.241,0,1,0,2.483,0V5.48l2.562,2.562A1.241,1.241,0,1,0,18.8,6.286L14.119,1.6a1.241,1.241,0,0,0-1.756,0L7.682,6.286A1.241,1.241,0,1,0,9.438,8.042Z" transform="translate(-1.241 -1.241)" fill="#fff"/><path d="M24,12a1.241,1.241,0,0,0-1.241,1.241v9.517H3.724V13.241a1.241,1.241,0,0,0-2.483,0v9.931A2.069,2.069,0,0,0,3.31,25.241H23.172a2.069,2.069,0,0,0,2.069-2.069V13.241A1.241,1.241,0,0,0,24,12Z" transform="translate(-1.241 -1.241)" fill="#fff"/></svg>';

  static String iconFaqs =
      '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><path d="M8,16a8,8,0,1,1,8-8A8.009,8.009,0,0,1,8,16Zm-.4-4.8a.4.4,0,0,0-.4.4v.8a.4.4,0,0,0,.4.4h.8a.4.4,0,0,0,.4-.4v-.8a.4.4,0,0,0-.4-.4ZM8.832,4.7a.9.9,0,0,1,.9.9v.448A.905.905,0,0,1,9.12,6.9l-.76.248A1.5,1.5,0,0,0,7.328,8.576V9.2a.4.4,0,0,0,.4.4h.608a.4.4,0,0,0,.4-.4V8.576a.1.1,0,0,1,.064-.1l.7-.248a2.307,2.307,0,0,0,1.6-2.184V5.6A2.3,2.3,0,0,0,8.8,3.3H7.2A2.307,2.307,0,0,0,4.9,5.6V6a.4.4,0,0,0,.4.4H5.9A.4.4,0,0,0,6.3,6V5.6a.9.9,0,0,1,.9-.9H8.832Z" fill="#c2c3c6"/></svg>';

  static String iconLogout =
      '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="14" viewBox="0 0 12 14"><path d="M6,14a5.865,5.865,0,0,1-4.153-1.73,6.376,6.376,0,0,1-.347-8.6.393.393,0,0,1,.277-.132h.007a.376.376,0,0,1,.27.116l.533.552a.406.406,0,0,1,0,.528A4.7,4.7,0,0,0,1.5,7.769,4.588,4.588,0,0,0,6,12.43a4.588,4.588,0,0,0,4.5-4.661,4.7,4.7,0,0,0-1.08-3.03.406.406,0,0,1,0-.528l.532-.552a.379.379,0,0,1,.271-.116h.007a.4.4,0,0,1,.27.132,6.376,6.376,0,0,1-.347,8.6A5.865,5.865,0,0,1,6,14Zm.375-5.455h-.75a.382.382,0,0,1-.375-.388V.388A.382.382,0,0,1,5.625,0h.75A.382.382,0,0,1,6.75.388V8.157A.382.382,0,0,1,6.375,8.545Z" fill="#f5627f"/></svg>';

  static String iconCustomerService =
      '<svg xmlns="http://www.w3.org/2000/svg" width="12.521" height="16.099" viewBox="0 0 12.521 16.099"><path d="M12.521,8.781v2.927a1.429,1.429,0,0,1-1.391,1.464V13.9a2.143,2.143,0,0,1-2.087,2.2H6.956a.714.714,0,0,1-.7-.732V15a.357.357,0,0,1,.348-.366H9.043a.714.714,0,0,0,.7-.732v-.732a.714.714,0,0,1-.7-.732V8.049a.714.714,0,0,1,.7-.732v-2.2A3.572,3.572,0,0,0,6.261,1.464,3.572,3.572,0,0,0,2.782,5.122v2.2a.714.714,0,0,1,.7.732V12.44a.714.714,0,0,1-.7.732H1.391A1.429,1.429,0,0,1,0,11.708V8.781A1.429,1.429,0,0,1,1.391,7.318v-2.2A5,5,0,0,1,6.261,0,5,5,0,0,1,11.13,5.122v2.2a1.357,1.357,0,0,1,.984.429A1.5,1.5,0,0,1,12.521,8.781Z" fill="#c2c3c6"/></svg>';

  static String iconPrivacy =
      '<svg xmlns="http://www.w3.org/2000/svg" width="12.444" height="16" viewBox="0 0 12.444 16"><path d="M6.64,16H5.807a1.348,1.348,0,0,1-.85-.305L2.136,13.382A5.8,5.8,0,0,1,.564,11.347,6.1,6.1,0,0,1,0,8.793V4.538A2.254,2.254,0,0,1,.609,3L3.049.429A1.361,1.361,0,0,1,4.031,0H8.414A1.361,1.361,0,0,1,9.4.429L11.836,3a2.254,2.254,0,0,1,.609,1.541V8.793a6.1,6.1,0,0,1-.564,2.555,5.8,5.8,0,0,1-1.573,2.034L7.487,15.695A1.346,1.346,0,0,1,6.64,16ZM6.195,3.874a.347.347,0,0,0-.319.221L5.185,5.862l-1.8.145a.358.358,0,0,0-.295.252.387.387,0,0,0,.095.388L4.57,7.883,4.148,9.731a.376.376,0,0,0,.13.386.333.333,0,0,0,.388.014l1.556-1,1.562,1a.331.331,0,0,0,.382-.022A.371.371,0,0,0,8.3,9.731L7.861,7.869,9.244,6.633v.015a.383.383,0,0,0,.085-.407A.351.351,0,0,0,9,6.008L7.2,5.862,6.513,4.095A.346.346,0,0,0,6.195,3.874Z" fill="#c2c3c6"/></svg>';

  static String iconBookMark =
      '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><path d="M10.5,13.3a.709.709,0,0,1-.338.613.621.621,0,0,1-.666-.018l-3.9-2.6a.622.622,0,0,0-.7,0L1,13.894a.621.621,0,0,1-.666.018A.709.709,0,0,1,0,13.3V1.4A1.358,1.358,0,0,1,1.312,0H9.186A1.358,1.358,0,0,1,10.5,1.4Z" transform="translate(5 3)" fill="#c2c3c6"/></svg>';

  static String iconBranch =
      '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="13.714" viewBox="0 0 16 13.714"><path d="M13.6,13.714H10.4a.782.782,0,0,1-.8-.762V9.333a.2.2,0,0,0-.2-.191H6.6a.2.2,0,0,0-.2.191v3.619a.782.782,0,0,1-.8.762H2.4a.782.782,0,0,1-.8-.762V7.619H.8A.782.782,0,0,1,0,6.857v-.4a1.469,1.469,0,0,1,.168-.678L1.376,3.49a.8.8,0,0,1,.714-.419H13.91a.8.8,0,0,1,.714.419l1.208,2.285A1.469,1.469,0,0,1,16,6.453v.4a.782.782,0,0,1-.8.762h-.8v5.333A.782.782,0,0,1,13.6,13.714ZM14,1.524H2a.391.391,0,0,1-.4-.381V.381A.392.392,0,0,1,2,0H14a.392.392,0,0,1,.4.381v.762A.391.391,0,0,1,14,1.524Z" fill="#c2c3c6"/></svg>';

  static String iconAbout =
      '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20"><image width="20" height="20" xlink:href="data:image/png;base64,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"/></svg>';

  static String iconCard =
      '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="12" viewBox="0 0 16 12"><path d="M14.4,12H1.6A1.554,1.554,0,0,1,0,10.5v-9A1.554,1.554,0,0,1,1.6,0H14.4A1.554,1.554,0,0,1,16,1.5v9A1.554,1.554,0,0,1,14.4,12ZM3.6,8.25a.389.389,0,0,0-.4.375v.75a.389.389,0,0,0,.4.375H6a.389.389,0,0,0,.4-.375v-.75A.389.389,0,0,0,6,8.25ZM2,3.75a.389.389,0,0,0-.4.375v1.5A.389.389,0,0,0,2,6H14a.389.389,0,0,0,.4-.375v-1.5A.389.389,0,0,0,14,3.75Z" fill="#c2c3c6"/></svg>';

  static String iconBank =
      '<svg xmlns="http://www.w3.org/2000/svg" width="16.465" height="17.32" viewBox="0 0 16.465 17.32"><g transform="translate(-117.89 -35.756)"><path d="M134.2,41.851,126.677,36.2a5.408,5.408,0,0,1-.5-.4l-.02-.02c-.02-.041-.04-.02-.06,0l-.02.02a5.372,5.372,0,0,1-.5.4l-7.526,5.647c-.278.2-.2.364.139.364h1.132v.992h13.563v-.992h1.132c.357,0,.437-.162.179-.364Zm-8.063-4.412a2.024,2.024,0,1,1-1.986,2.024A2,2,0,0,1,126.141,37.439Z" transform="translate(0)" fill="#c2c3c6"/><path d="M138.548,455.089v-.931H124.992v.931h-1.231v1.6H139.8v-1.6Z" transform="translate(-5.659 -403.613)" fill="#c2c3c6"/><path d="M324.208,92.633h-.02c-.3-.1-.344-.142-.344-.243,0-.041,0-.142.243-.142a.772.772,0,0,1,.385.121.245.245,0,0,0,.344-.061l.041-.041.1-.162a.183.183,0,0,0,.02-.1.226.226,0,0,0-.142-.243,1.331,1.331,0,0,0-.425-.162v-.121a.191.191,0,0,0-.2-.2h-.263a.191.191,0,0,0-.2.2V91.6a.793.793,0,0,0-.627.83c0,.607.465.769.85.911l.041.02c.283.121.324.142.324.2,0,.041,0,.182-.263.182a.94.94,0,0,1-.466-.121.183.183,0,0,0-.1-.02h-.02a.31.31,0,0,0-.2.081c-.02.02-.02.041-.041.061l-.1.182a.122.122,0,0,0-.02.081.292.292,0,0,0,.041.142c.02.02.041.061.061.081a1.017,1.017,0,0,0,.547.223v.061a.191.191,0,0,0,.2.2h.263a.191.191,0,0,0,.2-.2v-.121a.83.83,0,0,0,.627-.85c0-.648-.506-.809-.85-.911Z" transform="translate(-197.965 -53.514)" fill="#c2c3c6"/><path d="M182.56,255.36h1.822v6.74H182.56Z" transform="translate(-62.333 -211.909)" fill="#c2c3c6"/><path d="M467.04,255.36h1.822v6.74H467.04Z" transform="translate(-336.847 -211.909)" fill="#c2c3c6"/><path d="M278.32,255.36h1.822v6.74H278.32Z" transform="translate(-154.754 -211.909)" fill="#c2c3c6"/><path d="M374.08,254.8H375.9v6.74H374.08Z" transform="translate(-247.126 -211.369)" fill="#c2c3c6"/></g></svg>';

  static String iconEmailBlue =
      '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22"><rect width="22" height="22" rx="4" fill="#fff" opacity="0.1"/><path d="M14.4,12H1.6A1.554,1.554,0,0,1,0,10.5v-9A1.554,1.554,0,0,1,1.6,0H14.4A1.554,1.554,0,0,1,16,1.5v9A1.554,1.554,0,0,1,14.4,12ZM1.6,3.675V5.25L6.68,8.587a2.429,2.429,0,0,0,2.64,0L14.4,5.25V3.675L8.52,7.538a.963.963,0,0,1-1.04,0Z" transform="translate(3 5)" fill="#95a2fe"/></svg>';

  static String iconWhatsapp =
      '<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22"><g transform="translate(23 -1)"><g transform="translate(71 2)"><g transform="translate(-94 -1)"><path d="M11,0H11a10.993,10.993,0,0,0-8.9,17.447L.723,21.534l4.228-1.352A11,11,0,1,0,11,0Z" fill="#fff" opacity="0.1"/><path d="M118.99,126.554a2.842,2.842,0,0,1-1.979,1.4c-.527.11-1.215.2-3.531-.746a12.548,12.548,0,0,1-5.019-4.36,5.614,5.614,0,0,1-1.2-2.991,3.144,3.144,0,0,1,1.031-2.413,1.48,1.48,0,0,1,1.031-.356c.125,0,.237.006.338.011.3.012.445.03.64.49.243.576.836,2,.906,2.145a.582.582,0,0,1,.043.537,1.714,1.714,0,0,1-.326.455c-.149.169-.29.3-.439.478-.136.157-.29.326-.118.617a8.823,8.823,0,0,0,1.636,2,7.44,7.44,0,0,0,2.364,1.434.646.646,0,0,0,.711-.11,12.019,12.019,0,0,0,.788-1.026.568.568,0,0,1,.723-.216c.272.093,1.713.793,2.009.938s.491.216.563.338A2.431,2.431,0,0,1,118.99,126.554Z" transform="translate(-102.096 -111.447)" fill="#25d366"/></g></g></g></svg>';

  static String amountTag =
      '<svg xmlns="http://www.w3.org/2000/svg" width="44.531" height="22.496" viewBox="0 0 44.531 22.496"><path d="M9.6,0H34.926a9.6,9.6,0,1,1,0,19.21H9.6A9.6,9.6,0,0,1,9.6,0Z" fill="#FFDD7E"/><path d="M2.172.483a.873.873,0,0,1,1.562,0l2.079,4.16a.873.873,0,0,1-.781,1.263H.873A.873.873,0,0,1,.092,4.643Z" transform="translate(25.654 22.496) rotate(-180)" fill="#FFDD7E"/></svg>';

  static String iconUpload2 =
      '<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><rect width="32" height="32" rx="16" fill="#fff" opacity="0.06"/><path d="M16.5,16H5A5,5,0,1,1,5,6a6,6,0,0,1,11.912-.979A5.5,5.5,0,0,1,16.5,16ZM11,4a.5.5,0,0,0-.354.148L7.112,7.684a.5.5,0,0,0,0,.707l.207.207a.493.493,0,0,0,.353.148H9V12.5a.5.5,0,0,0,.5.5h3a.5.5,0,0,0,.5-.5V8.745h1.328a.494.494,0,0,0,.354-.148l.207-.207a.5.5,0,0,0,0-.707L11.354,4.148A.5.5,0,0,0,11,4Z" transform="translate(5 8)" fill="#fff" opacity="0.69"/></svg>';

  static String iconCancel =
      '<svg xmlns="http://www.w3.org/2000/svg" width="11.996" height="11.996" viewBox="0 0 11.996 11.996"><path d="M11.848,10.438a.5.5,0,0,1,0,.71l-.7.7a.5.5,0,0,1-.71,0L6,7.408l-4.44,4.44a.5.5,0,0,1-.71,0l-.7-.7a.5.5,0,0,1,0-.71L4.588,6,.148,1.558a.5.5,0,0,1,0-.71l.7-.7a.5.5,0,0,1,.71,0L6,4.588l4.44-4.44a.5.5,0,0,1,.71,0l.7.7a.5.5,0,0,1,0,.71L7.408,6Z" fill="#0a84ff"/></svg>';

  static String iconLoctionPin =
      '<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28"><g opacity="0.8"><g transform="translate(-2 -2)"><rect width="28" height="28" rx="14" transform="translate(2 2)" fill="rgba(255,255,255,0.19)"/><path d="M8.383,20H7.717a1,1,0,0,1-.947-.681L6.26,17.78A7.016,7.016,0,0,0,3.577,14.6C1.819,13.207,0,11.764,0,8A8,8,0,0,1,16,8c0,3.756-1.791,5.2-3.524,6.6A6.9,6.9,0,0,0,9.84,17.78L9.33,19.32A1,1,0,0,1,8.383,20ZM8,5a3,3,0,1,0,3,3A3,3,0,0,0,8,5Z" transform="translate(8 6)" fill="#22242a"/></g></g></svg>';

  static String iconBell =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12,0A12,12,0,1,1,0,12,12,12,0,0,1,12,0Z" fill="#95a2fe" opacity="0.15"/><path d="M7,18a1.777,1.777,0,0,1-1.75-1.8h3.5A1.777,1.777,0,0,1,7,18Zm6.125-3.6H.875A.889.889,0,0,1,0,13.5v-.531a.92.92,0,0,1,.254-.63l.726-.747a2.756,2.756,0,0,0,.77-1.908V7.2A5.535,5.535,0,0,1,2.8,3.96l.788-1.08A2.611,2.611,0,0,1,5.687,1.8h.438V.45A.444.444,0,0,1,6.562,0h.875a.445.445,0,0,1,.438.45V1.8h.438a2.611,2.611,0,0,1,2.1,1.081L11.2,3.96A5.535,5.535,0,0,1,12.25,7.2V9.684a2.754,2.754,0,0,0,.769,1.908l.727.747a.921.921,0,0,1,.254.63V13.5A.889.889,0,0,1,13.125,14.4Z" transform="translate(5 3)" fill="#95a2fe"/></svg>';

  static String iconUpload =
      '<svg xmlns="http://www.w3.org/2000/svg" width="30.003" height="30.003" viewBox="0 0 30.003 30.003"><path d="M0,0H30V30H0Z" fill="none" opacity="0"/><path d="M17.739,0H7.263C2.713,0,0,2.713,0,7.263V17.727C0,22.29,2.713,25,7.263,25H17.727c4.55,0,7.263-2.713,7.263-7.263V7.263C25,2.713,22.29,0,17.739,0ZM8.088,11.226a.943.943,0,0,1,1.325,0l2.15,2.15V5.638a.938.938,0,1,1,1.875,0v7.738l2.15-2.15a.937.937,0,1,1,1.325,1.325l-3.75,3.75a.97.97,0,0,1-.3.2.9.9,0,0,1-.363.075.942.942,0,0,1-.363-.075.97.97,0,0,1-.3-.2l-3.75-3.75A.943.943,0,0,1,8.088,11.226Zm12.214,7.8a24.683,24.683,0,0,1-7.8,1.263,24.683,24.683,0,0,1-7.8-1.263.941.941,0,0,1-.588-1.188A.926.926,0,0,1,5.3,17.252a22.919,22.919,0,0,0,14.414,0,.941.941,0,0,1,1.188.588A.953.953,0,0,1,20.3,19.027Z" transform="translate(2.5 2.5)" fill="#95a2fe"/></svg>';

  static String signupSuccess =
      '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="265.547" height="171.478" viewBox="0 0 265.547 171.478"><defs><linearGradient id="a" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#e2b0ff"/><stop offset="0.161" stop-color="#dca6fb"/><stop offset="0.426" stop-color="#cc8df1"/><stop offset="0.761" stop-color="#b365e0"/><stop offset="1" stop-color="#9f44d3"/></linearGradient><linearGradient id="b" y1="1" x2="1.506" y2="-0.155" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#fff" stop-opacity="0"/><stop offset="0.253" stop-color="#eef" stop-opacity="0.404"/><stop offset="0.629" stop-color="#d9d9ff"/></linearGradient><clipPath id="c"><rect width="29.207" height="29.207" rx="14.603" fill="none"/></clipPath><linearGradient id="d" x1="1.112" y1="0.823" x2="-0.111" y2="0.177" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#1a1a1a"/><stop offset="0.338" stop-color="#232323"/><stop offset="0.9" stop-color="#3c3c3c"/><stop offset="1" stop-color="#424242"/></linearGradient><linearGradient id="f" x1="0.998" y1="0.787" x2="0.002" y2="0.213" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#f8bbd0"/><stop offset="0.139" stop-color="#eaaed2"/><stop offset="0.377" stop-color="#da9ed5"/><stop offset="0.645" stop-color="#d195d7"/><stop offset="1" stop-color="#ce93d8"/></linearGradient><clipPath id="g"><rect width="29.207" height="29.207" rx="14.603" fill="url(#a)"/></clipPath><linearGradient id="h" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#95a2fe"/><stop offset="1" stop-color="#4b517f"/></linearGradient><clipPath id="i"><rect width="20.036" height="20.036" rx="10.018" fill="#fff"/></clipPath></defs><g transform="translate(-63.888 -120.707)"><g transform="translate(63.888 120.707)"><g transform="translate(0 0)"><g transform="translate(12.96)"><path d="M506.918,279.794c-26.847-47.8-48.978-2.1-118.641-53.627-28.329-20.956-94.39-17.3-118.905,8.18-26.984,28.047,4.068,60.557,0,95.124-9.11,77.417,100.654,3.045,162.443,32.893C475.413,388.9,525.253,312.435,506.918,279.794Z" transform="translate(-258.233 -212.562)" opacity="0.38" fill="url(#b)"/><g transform="translate(194.553 26.194)"><path d="M7.372,0H21.835a7.372,7.372,0,0,1,7.372,7.372V21.834a7.372,7.372,0,0,1-7.372,7.372H7.372A7.372,7.372,0,0,1,0,21.834V7.372A7.372,7.372,0,0,1,7.372,0Z" transform="translate(0 0)" fill="#f5627f"/><g transform="translate(0 0)"><g clip-path="url(#c)"><g transform="translate(2.911 3.567)"><path d="M1233.759,437.9c2.736,3.188-7.191,2.46-12.727,2.533-4.87.064-5.166-1.2-4.493-3.954,1.078-4.41-1.261-5.654-.219-9.152,1.5-5.049,4.348-7.153,9.768-6.542C1235.678,421.866,1231.587,435.369,1233.759,437.9Z" transform="translate(-1213.528 -420.681)" fill="#37474f"/><rect width="5.115" height="5.693" transform="matrix(1, -0.013, 0.013, 1, 9.011, 15.362)" fill="#ffdfae"/><path d="M1234.527,468.479l-.035-2.637,5.115-.068.03,2.293s-.319,1.017-2.323,1.043A4.261,4.261,0,0,1,1234.527,468.479Z" transform="translate(-1225.474 -449.883)" fill="#f4ca92"/><path d="M1232,447.284c-.145,3.389-3.552,6.579-6.313,6.606-2.443.024-6.427-3.215-6.953-6.062-.057-.306-1.351-.211-1.49-1.914-.13-1.594,1.132-.146,1.13-.341-.036-2.725.952-2.091,1.949-1.239,1.245,1.065,7.254.456,8.861-.235.848-.365,1.737.451,2.311,1.314.156.234,1.026-1.144,1.238-.3C1233.1,446.562,1232.022,446.882,1232,447.284Z" transform="translate(-1214.301 -435.525)" fill="#ffdfae"/><path d="M1230.443,487.8c-.031-2.3-6.508-8.3-6.508-8.3l-6.965.178s-8.1,5.863-8.068,8.4Z" transform="translate(-1208.901 -458.774)" fill="#92aed9"/></g></g></g></g><path d="M1200.647,440.93l.011-8.37h8.554c3.729,0,6.752,2.576,6.752,5.754v8.5s-2.472-.039-2.965,0H1206.8C1205.216,446.817,1200.647,445.266,1200.647,440.93Z" transform="translate(-1006.238 -389.403)" opacity="0.301" fill="url(#d)"/><g transform="translate(172.483 37.118)"><path d="M7.372,0H21.835a7.372,7.372,0,0,1,7.372,7.372V21.834a7.372,7.372,0,0,1-7.372,7.372H7.372A7.372,7.372,0,0,1,0,21.834V7.372A7.372,7.372,0,0,1,7.372,0Z" transform="translate(0 0)" fill="#fff"/><g transform="translate(0 0)"><g clip-path="url(#c)"><g transform="translate(2.911 3.567)"><path d="M1187.2,465.716c2.743,3.711-7.186,2.844-12.722,2.917-4.87.064-5.168-1.409-4.5-4.606,1.068-5.124-1.273-6.575-.239-10.639,1.492-5.865,4.333-8.305,9.755-7.583C1189.086,447.083,1185.024,462.769,1187.2,465.716Z" transform="translate(-1166.933 -445.682)" fill="#37474f"/><path d="M0,0H5.115V5.693H0Z" transform="matrix(1, -0.013, 0.013, 1, 9.01, 15.362)" fill="#ffdfae"/><path d="M1187.9,493.479l-.035-2.637,5.115-.068.031,2.293s-.32,1.017-2.322,1.043A4.263,4.263,0,0,1,1187.9,493.479Z" transform="translate(-1178.846 -474.883)" fill="#f4ca92"/><path d="M1185.377,467.459c-.145,3.389-3.551,6.579-6.312,6.606-2.443.024-6.427-3.215-6.953-6.062-.056-.306-1.351-.212-1.49-1.914-.13-1.594,1.132-.146,1.129-.341-.036-2.725,1.4-2.1,2.643-2.515a2.392,2.392,0,0,0,1.786-2.082C1177.188,465.235,1185.584,462.624,1185.377,467.459Z" transform="translate(-1167.674 -455.7)" fill="#ffdfae"/><path d="M1183.815,512.8c-.03-2.3-6.507-8.3-6.507-8.3l-6.965.178s-8.1,5.863-8.068,8.4Z" transform="translate(-1162.274 -483.774)" fill="#eceff1"/></g></g></g></g><g transform="translate(202.445 59.427)"><rect width="29.207" height="29.207" rx="14.603" transform="translate(0 0)" fill="url(#f)"/><g transform="translate(0 0)"><path d="M7.372,0H21.835a7.372,7.372,0,0,1,7.372,7.372V21.834a7.372,7.372,0,0,1-7.372,7.372H7.372A7.372,7.372,0,0,1,0,21.834V7.372A7.372,7.372,0,0,1,7.372,0Z" transform="translate(0 0)" fill="#ffdd7e"/><g transform="translate(0 0)" clip-path="url(#g)"><g transform="translate(2.911 5.477)"><path d="M1271.077,530.144c-2.917-11.515,3.549-6.586,8.056-9.684.447-.307,2.923.839,3.667,1.278,2.415,1.425,2.512,6.919,1.025,8.2C1280.326,532.95,1271.726,532.7,1271.077,530.144Z" transform="translate(-1266.101 -520.408)" fill="#37474f"/><path d="M1285.771,566.413l-11.728.507,3.031-3.439.43-5.026,5.115-.068.253,4.96Z" transform="translate(-1268.494 -545.003)" fill="#ffdfae"/><path d="M1283.923,562.785l-.035-2.637,5.115-.068.031,2.293s-.319,1.017-2.322,1.043A4.264,4.264,0,0,1,1283.923,562.785Z" transform="translate(-1274.87 -546.1)" fill="#ffe082"/><path d="M1283.919,542.567c-.44,3.308-2.094,4.665-3.339,4.64-.976-.02-4.287.556-5.436-.242a5.487,5.487,0,0,1-2.638-3.99c-.056-.305-1.523.1-1.663-1.6-.13-1.594,1.177-.605,1.175-.8-.036-2.726,1.86-4.288,3.091-3.837,1.921.7,8.242-.142,8.809,3.322.069.425.553-.547.875.185C1285.556,541.976,1284,541.968,1283.919,542.567Z" transform="translate(-1266.417 -530.934)" fill="#ffdfae"/><path d="M1279.838,582.106c-.031-2.3-6.508-8.3-6.508-8.3s-2.058,2.2-3,2.276c-1.119.092-3.97-2.1-3.97-2.1s-8.1,5.863-8.068,8.4Z" transform="translate(-1258.297 -554.991)" fill="#455a64"/><path d="M1282.123,549.421h-4.585c-2.326,0-3.557-2.959-3.918-4.613a19.031,19.031,0,0,1-.422-5.924s.922,6.942,3.813,8.539c1.24.686,4.181.124,5.571.017,2.32-.178,3.109-11.113,3.109-8.594l-.3,5.449C1285.386,546.814,1284.448,549.421,1282.123,549.421Z" transform="translate(-1267.901 -532.105)" fill="#455a64"/></g></g></g></g></g><g transform="translate(105.282 68.465)"><path d="M790.2,598.49q-2.549,0-5.186-.114l.019-.438c13.116.568,24.689-1,33.469-4.54l.164.407C811,596.891,801.241,598.49,790.2,598.49Z" transform="translate(-686.216 -520.953)" fill="#f0f0f0"/><path d="M506.519,189.676c-2.723-.887-5.524-1.712-8.326-2.454-16.12-4.267-31.966-5.783-45.826-4.381l-.044-.436c13.911-1.406,29.812.112,45.982,4.393,2.809.744,5.618,1.572,8.349,2.461Z" transform="translate(-412.099 -181.896)" fill="#f0f0f0"/><path d="M448.027,193.409c-2.723-.887-5.525-1.712-8.326-2.454-16.12-4.267-31.966-5.783-45.826-4.381l-.044-.436c13.911-1.406,29.812.112,45.982,4.393,2.809.744,5.618,1.572,8.349,2.461Z" transform="translate(-363.905 -184.972)" fill="#f0f0f0"/><path d="M252.981,327.689a18.63,18.63,0,0,0-9.771,5.994,8.024,8.024,0,0,0-1.353,2.119,4.6,4.6,0,0,0-.272,2.406,9.523,9.523,0,0,1,.211,2.3,2.817,2.817,0,0,1-1.194,1.971,7.321,7.321,0,0,1-2.71,1.069,7.187,7.187,0,0,0-3.011,1.189c-.1.085-.23-.082-.126-.166,1.495-1.218,3.572-1.062,5.225-1.947a2.854,2.854,0,0,0,1.582-1.95,7.389,7.389,0,0,0-.169-2.335,4.911,4.911,0,0,1,.178-2.363,7.416,7.416,0,0,1,1.266-2.163,18.073,18.073,0,0,1,4.208-3.769,18.96,18.96,0,0,1,5.911-2.557C253.087,327.45,253.11,327.657,252.981,327.689Z" transform="translate(-232.802 -301.848)" fill="#fff"/><path d="M283.984,339.888a2.8,2.8,0,0,1-.086-3.618c.085-.1.25.023.165.127a2.588,2.588,0,0,0,.087,3.366C284.24,339.862,284.073,339.987,283.984,339.888Z" transform="translate(-272.812 -309.06)" fill="#fff"/><path d="M272.612,380.618a5.387,5.387,0,0,0,3.743-1.185c.1-.084.23.082.126.166a5.6,5.6,0,0,1-3.9,1.225C272.449,380.815,272.478,380.609,272.612,380.618Z" transform="translate(-263.93 -344.637)" fill="#fff"/><path d="M314.424,336.656a1.582,1.582,0,0,0,1.277.79c.134.008.1.214-.029.207a1.773,1.773,0,0,1-1.414-.87.108.108,0,0,1,.02-.146A.1.1,0,0,1,314.424,336.656Z" transform="translate(-298.324 -309.377)" fill="#fff"/><path d="M258.718,403.6a18.631,18.631,0,0,0-11.41-1.1,8.023,8.023,0,0,0-2.356.877,4.6,4.6,0,0,0-1.666,1.758,9.521,9.521,0,0,1-1.214,1.96,2.817,2.817,0,0,1-2.14.854,7.32,7.32,0,0,1-2.807-.778,7.188,7.188,0,0,0-3.12-.864c-.134.005-.134-.2,0-.208,1.927-.073,3.491,1.3,5.344,1.591a2.853,2.853,0,0,0,2.437-.6,7.388,7.388,0,0,0,1.27-1.966,4.911,4.911,0,0,1,1.565-1.78,7.418,7.418,0,0,1,2.313-.964,18.075,18.075,0,0,1,5.629-.475,18.962,18.962,0,0,1,6.259,1.517C258.946,403.475,258.84,403.655,258.718,403.6Z" transform="translate(-232.133 -363.133)" fill="#fff"/><path d="M316.9,389.8a2.8,2.8,0,0,1,2.11-2.941c.13-.032.186.169.056.2a2.588,2.588,0,0,0-1.957,2.74C317.117,389.938,316.908,389.938,316.9,389.8Z" transform="translate(-300.507 -350.776)" fill="#fff"/><path d="M287.174,418.312a5.387,5.387,0,0,0,3.7,1.307c.134,0,.134.2,0,.209a5.6,5.6,0,0,1-3.85-1.368c-.1-.088.046-.235.147-.148Z" transform="translate(-275.875 -376.668)" fill="#fff"/><path d="M350.4,403.861a1.582,1.582,0,0,0,.544,1.4c.1.087-.045.234-.147.148a1.773,1.773,0,0,1-.605-1.547.108.108,0,0,1,.1-.1A.1.1,0,0,1,350.4,403.861Z" transform="translate(-327.936 -364.698)" fill="#fff"/><path d="M907.093,469.271a18.631,18.631,0,0,1,7.188,8.929,8.022,8.022,0,0,1,.562,2.45,4.6,4.6,0,0,1-.553,2.358,9.525,9.525,0,0,0-.97,2.091,2.818,2.818,0,0,0,.463,2.257,7.321,7.321,0,0,0,2.193,1.917,7.189,7.189,0,0,1,2.436,2.132c.069.115.244,0,.174-.114-1-1.65-3.007-2.2-4.267-3.59a2.853,2.853,0,0,1-.835-2.368,7.389,7.389,0,0,1,.944-2.142,4.911,4.911,0,0,0,.627-2.285,7.415,7.415,0,0,0-.466-2.463,18.073,18.073,0,0,0-2.7-4.964,18.963,18.963,0,0,0-4.708-4.4c-.112-.073-.2.114-.093.187Z" transform="translate(-786.765 -418.511)" fill="#fff"/><path d="M945.226,497.965a2.8,2.8,0,0,0,1.3-3.379c-.045-.126-.243-.063-.2.064a2.588,2.588,0,0,1-1.213,3.141C944.993,497.855,945.108,498.029,945.226,497.965Z" transform="translate(-818.09 -439.48)" fill="#fff"/><path d="M933.952,535.089a5.387,5.387,0,0,1-3.127-2.374c-.07-.115-.244,0-.174.114a5.6,5.6,0,0,0,3.259,2.464c.129.036.171-.168.042-.2Z" transform="translate(-806.203 -470.913)" fill="#fff"/><path d="M918.967,485.631a1.582,1.582,0,0,1-1.468.315c-.129-.038-.171.167-.042.2a1.773,1.773,0,0,0,1.625-.344.108.108,0,0,0,.03-.144.1.1,0,0,0-.144-.03Z" transform="translate(-795.281 -432.144)" fill="#fff"/><g transform="translate(0 7.387)"><path d="M259.593,428.066c-16.477-7.308-29.174-16.789-35.751-26.7l.365-.242c6.532,9.839,19.162,19.264,35.564,26.538Z" transform="translate(-223.842 -369.918)" fill="#fff"/><g transform="translate(10.298)"><path d="M359.713,257.686c-6.719-6.614-9.556-13.514-7.991-19.429,2.01-7.591,10.937-12.839,24.493-14.4l.05.435c-13.367,1.538-22.159,6.669-24.12,14.076-1.524,5.756,1.273,12.506,7.874,19.005Z" transform="translate(-339.16 -223.857)" fill="#fff"/><path d="M504.142,470.083a105.176,105.176,0,0,1-26.549-3.642,100.013,100.013,0,0,1-29.678-12.934l.243-.364a99.576,99.576,0,0,0,29.547,12.875,102.417,102.417,0,0,0,29.345,3.576l.016.438Q505.621,470.083,504.142,470.083Z" transform="translate(-418.764 -412.776)" fill="#fff"/><path d="M724.318,293.244l-.14-.415c7.5-2.536,12.19-6.62,13.565-11.813,1.747-6.6-2.049-14.261-10.69-21.569-8.685-7.345-21.209-13.44-35.265-17.161l.112-.424c14.118,3.738,26.7,9.864,35.436,17.25,8.777,7.423,12.624,15.242,10.831,22.015C736.752,286.47,731.963,290.66,724.318,293.244Z" transform="translate(-619.703 -238.693)" fill="#fff"/><path d="M318.085,424.332c-16.477-7.308-29.174-16.788-35.751-26.7l.365-.242c6.532,9.839,19.162,19.264,35.563,26.538Z" transform="translate(-282.334 -366.841)" fill="#fff"/><path d="M301.221,261.419c-6.719-6.614-9.556-13.514-7.991-19.429,2.01-7.591,10.937-12.839,24.493-14.4l.05.435c-13.367,1.538-22.159,6.669-24.119,14.076-1.524,5.757,1.273,12.506,7.874,19.005Z" transform="translate(-290.966 -226.933)" fill="#fff"/><path d="M445.649,473.816a105.179,105.179,0,0,1-26.549-3.642,100.013,100.013,0,0,1-29.678-12.934l.243-.364a99.576,99.576,0,0,0,29.547,12.875,102.42,102.42,0,0,0,29.344,3.576l.016.438Q447.128,473.816,445.649,473.816Z" transform="translate(-370.569 -415.852)" fill="#fff"/><path d="M665.826,296.978l-.14-.415c7.5-2.536,12.19-6.62,13.565-11.813,1.747-6.6-2.049-14.261-10.69-21.569-8.685-7.345-21.209-13.44-35.265-17.161l.112-.424c14.118,3.737,26.7,9.864,35.436,17.25,8.777,7.423,12.624,15.242,10.831,22.015C678.26,290.2,673.471,294.393,665.826,296.978Z" transform="translate(-571.508 -241.77)" fill="#fff"/><path d="M731.7,602.224q-2.549,0-5.186-.114l.019-.438c13.116.568,24.689-1,33.469-4.54l.164.406C752.511,600.625,742.749,602.224,731.7,602.224Z" transform="translate(-648.32 -531.417)" fill="#fff"/><path d="M917.949,478.086c-1.779-4.383-5.787-8.1-10.507-8.415a21.8,21.8,0,0,1,3.872,14.455c-.183,1.976-.592,4.154.519,5.8a4.873,4.873,0,0,0,3.082,1.885,14.47,14.47,0,0,0,3.69.134l.28.128C919.617,487.4,919.729,482.47,917.949,478.086Z" transform="translate(-797.391 -426.395)" fill="#fff"/><path d="M844.893,527.783l.24.08a20.853,20.853,0,0,1,3.112,1.32c.078.039.156.08.233.122a21.968,21.968,0,0,1,6.3,5.073,21.336,21.336,0,0,1,1.911,2.618,13.888,13.888,0,0,0,2.666,3.62,3.677,3.677,0,0,0,.366.266l7.45-2,.045-.037.3-.066-.08-.15c-.015-.029-.032-.057-.047-.086s-.021-.038-.03-.056l-.01-.017c-.008-.018-.018-.033-.026-.049q-.234-.426-.476-.851s0,0,0,0a31.358,31.358,0,0,0-4.343-6.01c-.051-.054-.1-.109-.156-.162a20.358,20.358,0,0,0-2.484-2.206,18.031,18.031,0,0,0-1.512-1.014,14.965,14.965,0,0,0-4.343-1.74,11.882,11.882,0,0,0-8.913,1.221C845.025,527.7,844.96,527.739,844.893,527.783Z" transform="translate(-745.854 -472.874)" fill="#fff"/></g></g><path d="M844.566,535.1a18.63,18.63,0,0,1,11.115,2.8,8.02,8.02,0,0,1,1.924,1.618,4.6,4.6,0,0,1,.978,2.215,9.526,9.526,0,0,0,.485,2.254,2.817,2.817,0,0,0,1.728,1.524,7.32,7.32,0,0,0,2.905.21,7.187,7.187,0,0,1,3.229.235c.125.05.195-.147.07-.2-1.79-.716-3.726.053-5.568-.3a2.853,2.853,0,0,1-2.092-1.389,7.388,7.388,0,0,1-.536-2.279,4.911,4.911,0,0,0-.876-2.2,7.418,7.418,0,0,0-1.855-1.686,18.073,18.073,0,0,0-5.142-2.34,18.961,18.961,0,0,0-6.4-.675C844.394,534.908,844.433,535.113,844.566,535.1Z" transform="translate(-735.187 -472.719)" fill="#fff"/><path d="M896.976,531.643a2.8,2.8,0,0,0-1-3.479c-.112-.074-.232.1-.12.171a2.588,2.588,0,0,1,.922,3.238c-.***************.2.07Z" transform="translate(-777.512 -467.188)" fill="#fff"/><path d="M905.653,571.621a5.388,5.388,0,0,1-3.926-.013c-.125-.049-.2.147-.07.2a5.6,5.6,0,0,0,4.086.005C905.867,571.76,905.778,571.572,905.653,571.621Z" transform="translate(-782.273 -502.992)" fill="#fff"/><path d="M864.8,536.815a1.582,1.582,0,0,1-.983,1.135c-.126.047-.036.236.089.189a1.773,1.773,0,0,0,1.09-*************,0,0,0-.063-.133A.1.1,0,0,0,864.8,536.815Z" transform="translate(-751.099 -474.274)" fill="#fff"/><ellipse cx="1.753" cy="1.753" rx="1.753" ry="1.753" transform="translate(100.537 43.847) rotate(-80.783)" fill="#e6e6e6"/><ellipse cx="1.753" cy="1.753" rx="1.753" ry="1.753" transform="translate(35.362 2.156)" fill="#e6e6e6"/><path d="M519.241,219.984h-.5V206.279a7.932,7.932,0,0,0-7.932-7.932H481.772a7.932,7.932,0,0,0-7.932,7.932h0v75.19a7.932,7.932,0,0,0,7.932,7.932h29.037a7.932,7.932,0,0,0,7.932-7.932V229.74h.5Z" transform="translate(-429.827 -195.45)" fill="#3f3d56"/><path d="M527.3,215.993v75.08A5.923,5.923,0,0,1,521.374,297H492.2a5.923,5.923,0,0,1-5.923-5.923v-75.08a5.923,5.923,0,0,1,5.923-5.923h3.541a2.814,2.814,0,0,0,2.605,3.877h16.636a2.815,2.815,0,0,0,2.605-3.877h3.79a5.923,5.923,0,0,1,5.923,5.923h0Z" transform="translate(-440.071 -205.11)" fill="#fff"/><rect width="9.052" height="0.405" transform="translate(48.412 12.127)" fill="#134284"/><rect width="9.052" height="0.405" transform="translate(62.188 12.127)" fill="#134284"/><path d="M541.752,519.252H512.246a.757.757,0,0,1,0-1.514h29.506a.757.757,0,1,1,0,1.514Z" transform="translate(-460.848 -458.612)" fill="#ccc"/><path d="M541.752,549.17H512.246a.757.757,0,0,1,0-1.514h29.506a.757.757,0,1,1,0,1.514Z" transform="translate(-460.848 -483.263)" fill="#ccc"/><path d="M541.752,579.088H512.246a.757.757,0,0,1,0-1.514h29.506a.757.757,0,1,1,0,1.514Z" transform="translate(-460.848 -507.914)" fill="#ccc"/><rect width="9.052" height="0.405" transform="translate(75.963 12.127)" fill="#134284"/><rect width="9.052" height="0.405" transform="translate(48.412 84.853)" fill="#fff"/><rect width="9.052" height="0.405" transform="translate(62.188 84.853)" fill="#fff"/><rect width="9.052" height="0.405" transform="translate(75.963 84.853)" fill="#fff"/><ellipse cx="13.72" cy="13.72" rx="13.72" ry="13.72" transform="translate(52.993 22.828)" fill="#d1d3d4"/><path d="M12.517,0A12.517,12.517,0,1,1,0,12.517,12.517,12.517,0,0,1,12.517,0Z" transform="translate(54.196 24.639)" fill="url(#h)"/><path d="M579.537,374.206l-.082,0a1.667,1.667,0,0,1-1.236-.642l-1.973-2.536a1.67,1.67,0,0,1,.292-2.341l.071-.055a1.671,1.671,0,0,1,2.342.292,1,1,0,0,0,.742.386.982.982,0,0,0,.776-.311l4.006-4.228a1.671,1.671,0,0,1,2.359-.064l.065.062a1.67,1.67,0,0,1,.064,2.359l-6.216,6.561a1.666,1.666,0,0,1-1.211.521Z" transform="translate(-513.917 -332.144)" fill="#fff"/><rect width="1.621" height="8.711" transform="translate(43.17 17.346)" fill="#3f3d56"/><rect width="1.621" height="5.064" transform="translate(43.17 27.07)" fill="#3f3d56"/><rect width="1.621" height="5.064" transform="translate(43.17 33.147)" fill="#3f3d56"/><ellipse cx="0.81" cy="0.81" rx="0.81" ry="0.81" transform="translate(57.959 5.394)" fill="#fff"/><path d="M544.2,399.741a12.515,12.515,0,0,1-12.483-11.634c-.02.29-.034.583-.034.879a12.517,12.517,0,1,0,25.034,0c0-.3-.014-.59-.034-.881A12.516,12.516,0,0,1,544.2,399.741Z" transform="translate(-477.489 -351.802)" fill="#134284" opacity="0.45"/></g><g transform="translate(0 34.01)"><path d="M4.223,0H139.192a4.223,4.223,0,0,1,4.223,4.223V93.85a4.223,4.223,0,0,1-4.223,4.223H4.223A4.223,4.223,0,0,1,0,93.85V4.223A4.223,4.223,0,0,1,4.223,0Z" transform="translate(0 0)" fill="#e5e8f5"/><path d="M4.223,0H139.192a4.223,4.223,0,0,1,4.223,4.223v88.57a4.223,4.223,0,0,1-4.223,4.223H4.223A4.223,4.223,0,0,1,0,92.793V4.223A4.223,4.223,0,0,1,4.223,0Z" transform="translate(1.056 0)" fill="#fff"/><path d="M295.7,281.154v-7.467c0-1.461,1.656-2.656,3.68-2.656H436.493c2.024,0,3.68,1.2,3.68,2.656v7.467Z" transform="translate(-295.702 -271.031)" fill="#95a2fe"/><g transform="translate(9.136 18.376)"><g transform="translate(27.808 3.159)"><path d="M.845,0H60.739a.845.845,0,0,1,.845.845V2.108a.845.845,0,0,1-.845.845H.845A.845.845,0,0,1,0,2.108V.845A.845.845,0,0,1,.845,0Z" transform="translate(0)" fill="#e5e8f5"/><path d="M.845,0H94.906a.845.845,0,0,1,.845.845V2.108a.845.845,0,0,1-.845.845H.845A.845.845,0,0,1,0,2.108V.845A.845.845,0,0,1,.845,0Z" transform="translate(0.024 5.475)" fill="#e5e8f5"/><path d="M.845,0H94.906a.845.845,0,0,1,.845.845V2.108a.845.845,0,0,1-.845.845H.845A.845.845,0,0,1,0,2.108V.845A.845.845,0,0,1,.845,0Z" transform="translate(0.024 10.951)" fill="#e5e8f5"/></g><path d="M5.182,0h9.673a5.182,5.182,0,0,1,5.182,5.182v9.673a5.182,5.182,0,0,1-5.182,5.182H5.182A5.182,5.182,0,0,1,0,14.854V5.182A5.182,5.182,0,0,1,5.182,0Z" fill="#fff"/><g clip-path="url(#i)"><path d="M359.723,414.951c1.1,3.449-2.795,5.4-6.108,5.449s-7.828.351-6.086-6.5c.893-3.51,2.6-6.616,5.911-6.66S358.889,412.331,359.723,414.951Z" transform="translate(-343.53 -403.721)" fill="#37474f"/><rect width="3.061" height="3.407" transform="matrix(1, -0.013, 0.013, 1, 8.708, 12.699)" fill="#ffdfae"/><path d="M371.346,453.893l-.021-1.578,3.061-.041.018,1.372s-.191.608-1.39.624A2.551,2.551,0,0,1,371.346,453.893Z" transform="translate(-362.612 -439.258)" fill="#f4ca92"/><path d="M362.607,419.4c.038,2.9-1.925,5.122-3.577,5.138-1.323.013-3.029-1.38-3.79-3.593-.085-.246-1.248-.546-1.293-1.4-.05-.94.919-.272.915-.57-.058-4.35,3.846-4.337,3.734-2.751C358.343,419.774,362.568,416.5,362.607,419.4Z" transform="translate(-348.897 -410.039)" fill="#ffdfae"/><path d="M358.625,468.628c-.025-1.853-2.867-5.639-6.513-5.591s-6.4,3.909-6.378,5.762Z" transform="translate(-341.996 -447.54)" fill="#9bd075"/></g></g><g transform="translate(9.136 43.274)"><g transform="translate(27.808 3.159)"><path d="M.845,0H60.739a.845.845,0,0,1,.845.845V2.108a.845.845,0,0,1-.845.845H.845A.845.845,0,0,1,0,2.108V.845A.845.845,0,0,1,.845,0Z" transform="translate(0)" fill="#e5e8f5"/><path d="M.845,0H94.906a.845.845,0,0,1,.845.845V2.108a.845.845,0,0,1-.845.845H.845A.845.845,0,0,1,0,2.108V.845A.845.845,0,0,1,.845,0Z" transform="translate(0.024 5.475)" fill="#e5e8f5"/><path d="M.845,0H94.906a.845.845,0,0,1,.845.845V2.108a.845.845,0,0,1-.845.845H.845A.845.845,0,0,1,0,2.108V.845A.845.845,0,0,1,.845,0Z" transform="translate(0.024 10.951)" fill="#e5e8f5"/></g><g transform="translate(0 0)"><path d="M5.49,0h9.056a5.49,5.49,0,0,1,5.49,5.49v9.056a5.49,5.49,0,0,1-5.49,5.49H5.49A5.49,5.49,0,0,1,0,14.546V5.49A5.49,5.49,0,0,1,5.49,0Z" transform="translate(0 0)" fill="#fff"/><g transform="translate(0 0)" clip-path="url(#i)"><path d="M361.361,541.53c-.2,3.613-4.912,4.174-8.225,4.218s-5.965-5-4.223-11.849c.893-3.51,2.6-6.616,5.911-6.66S361.783,533.938,361.361,541.53Z" transform="translate(-344.914 -523.721)" fill="#37474f"/><rect width="3.061" height="4.103" transform="matrix(1, -0.013, 0.013, 1, 8.708, 12.699)" fill="#ffdfae"/><path d="M373.346,573.893l-.021-1.578,3.061-.041.018,1.372s-.191.609-1.39.624A2.552,2.552,0,0,1,373.346,573.893Z" transform="translate(-364.612 -559.061)" fill="#f4ca92"/><path d="M366.584,548.471a10.017,10.017,0,0,1-.045,1.112c-.035.356.513-.651.634.1.151.933-.911,1.35-.98,1.55a3.522,3.522,0,0,1-2.972,2.579c-1.348.16-2.617-.975-3.358-2.5-.1-.2-1.048-.2-1.129-.988-.12-1.174.693-.312.64-.5-.383-1.355-.16-2.426,1.094-1.979.253.09-.056-.888.211-.844.212.035.187.837.422.844a5.162,5.162,0,0,0,.6-.022C363.854,547.621,366.556,546.341,366.584,548.471Z" transform="translate(-353.089 -539.309)" fill="#ffdfae"/><path d="M360.625,591.632c-.021-1.583-2.2-4.962-3.789-4.781-.678.077-1.709.423-2.137.437l-2.179-.381c-3.423.79-4.807,3.346-4.787,4.9Z" transform="translate(-344.418 -570.756)" fill="#f0af56"/><path d="M370.9,586.56l-2.636,2.41.218-2.948Z" transform="translate(-360.617 -570.107)" fill="#fff"/><path d="M380.56,586.5l2.637,2.41-.218-2.948Z" transform="translate(-370.321 -570.06)" fill="#fff"/></g></g></g><g transform="translate(9.136 68.023)"><g transform="translate(27.808 3.159)"><path d="M.845,0H60.739a.845.845,0,0,1,.845.845V2.108a.845.845,0,0,1-.845.845H.845A.845.845,0,0,1,0,2.108V.845A.845.845,0,0,1,.845,0Z" transform="translate(0)" fill="#e5e8f5"/><path d="M.845,0H94.906a.845.845,0,0,1,.845.845V2.108a.845.845,0,0,1-.845.845H.845A.845.845,0,0,1,0,2.108V.845A.845.845,0,0,1,.845,0Z" transform="translate(0.024 5.475)" fill="#e5e8f5"/><path d="M.845,0H94.906a.845.845,0,0,1,.845.845V2.108a.845.845,0,0,1-.845.845H.845A.845.845,0,0,1,0,2.108V.845A.845.845,0,0,1,.845,0Z" transform="translate(0.024 10.951)" fill="#e5e8f5"/></g><g transform="translate(0 0)"><path d="M5.49,0h9.056a5.49,5.49,0,0,1,5.49,5.49v9.056a5.49,5.49,0,0,1-5.49,5.49H5.49A5.49,5.49,0,0,1,0,14.546V5.49A5.49,5.49,0,0,1,5.49,0Z" transform="translate(0 0)" fill="#fff"/><g transform="translate(0 0)" clip-path="url(#i)"><path d="M363.269,652.216c-1.265,0-7.845,2.216-8.647-2.742-.491-3.031.844-4.429,3.164-4.851,0,0-.4-.637,0-.633.319,0,.434.365.844.422.3.041-.121-.464.211-.422.363.047.629.564,1.055.422,1.265-.422,2.719,1.025,3.164,1.265C364.624,646.525,364.745,652.216,363.269,652.216Z" transform="translate(-349.774 -641.156)" fill="#37474f"/><rect width="3.061" height="4.709" transform="matrix(1, -0.013, 0.013, 1, 8.708, 12.699)" fill="#ffdfae"/><path d="M373.346,693.893l-.021-1.578,3.061-.041.018,1.372s-.191.608-1.39.624A2.551,2.551,0,0,1,373.346,693.893Z" transform="translate(-364.612 -679.258)" fill="#f4ca92"/><path d="M364.187,662.464c-.009.118.688-.427.721-.132.211,1.9-.771,1.782-.844,2.109a3.625,3.625,0,0,1-2.32,2.953,4.185,4.185,0,0,1-2.742,0,5.055,5.055,0,0,1-2.32-2.32c-.355-.829-1.476-.422-1.687-2.32-.045-.4,1.055,0,1.055,0s-.209-.9-.211-1.055c-.058-4.35,2.444-1.605,4.007-1.9C363.22,659.168,364.486,658.536,364.187,662.464Z" transform="translate(-350.147 -653.17)" fill="#ffdfae"/><path d="M361.92,708.273c-.013-.97-1.465-2.377-2.742-3.585-.664-.628-2.05-1.687-2.953-1.687a1.256,1.256,0,0,1-1.476,1.055c-1.307-.218-1.374-.716-1.588-.947a5.289,5.289,0,0,0-2.63,1.158c-1.539,1.308-2.811,3.516-2.8,4.5Z" transform="translate(-344.418 -687.722)" fill="#f74e88"/></g></g></g></g></g></g><g transform="translate(164.389 223.138)"><ellipse cx="24.942" cy="24.942" rx="24.942" ry="24.942" transform="translate(0 5.32)" fill="#e6e6e6"/><path d="M399.087,231.354a21.432,21.432,0,0,1-8.662,17.228c-.18.135-.363.266-.548.393a21.465,21.465,0,0,1-24.5,0c-.184-.128-.368-.259-.548-.393a21.464,21.464,0,0,1,0-34.457c.18-.135.363-.266.548-.393a21.465,21.465,0,0,1,24.5,0c.184.128.368.259.548.393A21.432,21.432,0,0,1,399.087,231.354Z" transform="translate(-352.683 -201.091)" fill="#fff"/><ellipse cx="19.606" cy="19.606" rx="19.606" ry="19.606" transform="translate(5.336 10.657)" fill="#95a2fe"/><path d="M528.522,226.365c-.3,0-.593.008-.887.021a19.6,19.6,0,0,1-.83,39.114c.566.049,1.138.076,1.717.076a19.606,19.606,0,1,0,0-39.211Z" transform="translate(-503.529 -215.653)" opacity="0.2"/><path d="M536.8,213.892a.639.639,0,0,0-.638.638v2.784a.638.638,0,1,0,1.276,0V214.53A.639.639,0,0,0,536.8,213.892Z" transform="translate(-511.801 -204.627)" fill="#fff"/><path d="M630.2,243.375c-.18-.135-.363-.266-.548-.393a.638.638,0,0,0-.568.277l-1.567,2.3a.638.638,0,0,0,1.055.718l1.567-2.3A.634.634,0,0,0,630.2,243.375Z" transform="translate(-592.462 -230.341)" fill="#fff"/><path d="M683.851,333.773a.637.637,0,0,0-.831-.35l-2.58,1.048a.638.638,0,0,0,.48,1.182l2.58-1.048A.64.64,0,0,0,683.851,333.773Z" transform="translate(-638.989 -310.249)" fill="#fff"/><path d="M433.143,245.559l-1.567-2.3a.638.638,0,0,0-.568-.277c-.184.128-.368.259-.548.393a.634.634,0,0,0,.061.6l1.567,2.3a.638.638,0,1,0,1.055-.718Z" transform="translate(-418.317 -230.341)" fill="#fff"/><path d="M372.5,334.471l-2.58-1.048a.638.638,0,1,0-.48,1.182l2.58,1.048a.638.638,0,1,0,.48-1.182Z" transform="translate(-364.068 -310.25)" fill="#fff"/><path d="M536.8,540.892a.639.639,0,0,0-.638.638v2.784a.638.638,0,0,0,1.276,0V541.53A.639.639,0,0,0,536.8,540.892Z" transform="translate(-511.801 -493.693)" fill="#fff"/><path d="M630.141,518.557l-1.567-2.3a.638.638,0,0,0-1.055.718l1.567,2.3a.638.638,0,0,0,.568.277c.184-.128.368-.259.548-.393A.634.634,0,0,0,630.141,518.557Z" transform="translate(-592.462 -471.668)" fill="#fff"/><path d="M683.5,437.471l-2.58-1.048a.638.638,0,1,0-.48,1.182l2.58,1.048a.638.638,0,1,0,.48-1.182Z" transform="translate(-638.989 -401.301)" fill="#fff"/><path d="M432.974,516.089a.638.638,0,0,0-.886.168l-1.567,2.3a.634.634,0,0,0-.062.6c.18.135.363.266.548.393a.638.638,0,0,0,.568-.277l1.567-2.3A.638.638,0,0,0,432.974,516.089Z" transform="translate(-418.317 -471.669)" fill="#fff"/><path d="M372.851,436.773a.637.637,0,0,0-.831-.35l-2.58,1.048a.638.638,0,1,0,.48,1.182l2.58-1.048A.639.639,0,0,0,372.851,436.773Z" transform="translate(-364.068 -401.301)" fill="#fff"/><path d="M442.334,383.7a1.336,1.336,0,0,1-1.092,1.539l-11.322,1.926a1.334,1.334,0,0,1-.447-2.63l11.322-1.926A1.336,1.336,0,0,1,442.334,383.7Z" transform="translate(-416.506 -353.756)" fill="#3f3d56"/><path d="M547.307,396.848a.871.871,0,0,1-1.23,0L533.888,384.76a.87.87,0,1,1,1.226-1.235L547.3,395.618A.871.871,0,0,1,547.307,396.848Z" transform="translate(-509.563 -354.358)" fill="#3f3d56"/><ellipse cx="1.508" cy="1.508" rx="1.508" ry="1.508" transform="translate(23.434 28.754)" fill="#ccc"/><path d="M353.939,138.142c-2.315,3.639-5.847,6.055-10.2,7.642a.819.819,0,0,1-1.146-.165h0a7.193,7.193,0,1,1,11.448-8.711l.065.087A.819.819,0,0,1,353.939,138.142Z" transform="translate(-339.385 -134.031)" fill="#e6e6e6"/><path d="M628.108,136.908a7.193,7.193,0,1,1,11.448,8.711h0a.819.819,0,0,1-1.146.165,24.884,24.884,0,0,1-10.2-7.642.819.819,0,0,1-.165-1.146Z" transform="translate(-592.877 -134.031)" fill="#e6e6e6"/><ellipse cx="1.972" cy="1.972" rx="1.972" ry="1.972" transform="translate(37.819 6.596)" fill="#e6e6e6"/><ellipse cx="1.972" cy="1.972" rx="1.972" ry="1.972" transform="translate(22.854 1.956)" fill="#e6e6e6"/><ellipse cx="1.972" cy="1.972" rx="1.972" ry="1.972" transform="translate(8.585 6.364)" fill="#e6e6e6"/><path d="M458.7,593.34l.085.214H458.7Z" transform="translate(-412.914 -524.508)" fill="#2f2e41"/></g></g></svg>';

  static String uploadIcon =
      '<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><rect width="32" height="32" rx="16" fill="#fff" opacity="0.06"/><path d="M16.5,16H5A5,5,0,1,1,5,6a6,6,0,0,1,11.912-.979A5.5,5.5,0,0,1,16.5,16ZM11,4a.5.5,0,0,0-.354.148L7.112,7.684a.5.5,0,0,0,0,.707l.207.207a.493.493,0,0,0,.353.148H9V12.5a.5.5,0,0,0,.5.5h3a.5.5,0,0,0,.5-.5V8.745h1.328a.494.494,0,0,0,.354-.148l.207-.207a.5.5,0,0,0,0-.707L11.354,4.148A.5.5,0,0,0,11,4Z" transform="translate(5 8)" fill="#fff" opacity="0.69"/></svg>';

  static String iconMap =
      '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14"><path d="M.628,14H.389A.39.39,0,0,1,0,13.611V2.077a.775.775,0,0,1,.482-.724L3.305.179A2.311,2.311,0,0,1,4.2,0h.933a2.311,2.311,0,0,1,.895.179L9.333,1.555,12.779.117A1.545,1.545,0,0,1,13.372,0h.238A.39.39,0,0,1,14,.389V11.923a.775.775,0,0,1-.482.724l-2.823,1.174A2.311,2.311,0,0,1,9.8,14H8.867a2.311,2.311,0,0,1-.895-.179L4.667,12.445,1.221,13.883A1.545,1.545,0,0,1,.628,14ZM4.667,1.555v9.2l.6.248,3.29,1.377a.778.778,0,0,0,.306.062h.471v-9.2l-.6-.248L5.429,1.618a.78.78,0,0,0-.3-.062Z" fill="#c2c3c6"/></svg>';

  static String iconCall =
      '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><rect width="24" height="24" rx="4" fill="#22242a"/><path d="M11.02,14.17a7.927,7.927,0,0,1-3.731-.937l-.354-.2a14.169,14.169,0,0,1-5.77-5.77l-.2-.354A7.927,7.927,0,0,1,0,3.15V2.623A1.574,1.574,0,0,1,.464,1.5L1.795.175a.59.59,0,0,1,.929.118L4.495,3.331a.787.787,0,0,1-.126.952L2.881,5.771a.394.394,0,0,0-.071.464l.276.52a10.65,10.65,0,0,0,4.329,4.321l.52.283a.394.394,0,0,0,.464-.071L9.887,9.8a.787.787,0,0,1,.952-.126l3.038,1.771a.59.59,0,0,1,.118.929l-1.33,1.33a1.574,1.574,0,0,1-1.118.464Z" transform="translate(4.915 4.915)" fill="#fff" opacity="0.6"/></svg>';

  static String iconEditGray =
      '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14"><path d="M.454,14a.385.385,0,0,1-.274-.114.387.387,0,0,1-.147-.482l1.334-3.368a1.545,1.545,0,0,1,.349-.528L7.978,3.244a.391.391,0,0,1,.276-.115.385.385,0,0,1,.275.115l2.22,2.189a.39.39,0,0,1,0,.543l-6.255,6.28a1.536,1.536,0,0,1-.528.349L.6,13.972A.386.386,0,0,1,.454,14ZM11.948,4.633a.391.391,0,0,1-.276-.115L9.491,2.305a.39.39,0,0,1,0-.543L10.9.342A1.159,1.159,0,0,1,11.721,0h.6a1.157,1.157,0,0,1,.823.342l.512.512A1.16,1.16,0,0,1,14,1.676v.59a1.172,1.172,0,0,1-.342.839L12.223,4.518A.39.39,0,0,1,11.948,4.633Z" transform="translate(0 0)" fill="#b7b7b7"/></svg>';

  /*DASBOARD SERVICES*/

  static String income =
      '<svg xmlns="http://www.w3.org/2000/svg" width="68.616" height="54.944" viewBox="0 0 68.616 54.944"><g transform="translate(-147 -911.136)"><g transform="translate(-328.07 -1295.392)"><path d="M5.807.276a.733.733,0,0,0-1.146.916l.735.921H.733a.733.733,0,1,0,0,1.467H5.4L4.661,4.5a.733.733,0,0,0,1.146.916L7.494,3.3a.733.733,0,0,0,0-.916Z" transform="translate(475.07 2221.263) rotate(-90)" fill="#f2f6f9" opacity="0.57"/><path d="M9.364.444A1.182,1.182,0,1,0,7.516,1.921L8.7,3.405H1.182a1.182,1.182,0,1,0,0,2.365H8.7L7.516,7.254A1.182,1.182,0,1,0,9.364,8.731l2.72-3.405a1.182,1.182,0,0,0,0-1.476Z" transform="translate(476.814 2219.78) rotate(-90)" fill="#95a2fe"/></g><g transform="translate(160.683 911.136)"><g transform="translate(0 0)"><path d="M27.473,0h1.41l.031,0h.046l.03,0h0l.03,0h0q.225.013.45.029l-.036.5C28.786.524,28.126.5,27.473.5V0Zm3.986.287q.409.059.813.131h0L32.3.423h0l.03.005.011,0h0l.03.005h0l.029.005h0l.029.005h0l.028.005.012,0,.019,0,.013,0,.019,0,.012,0,.019,0,.012,0,.012,0,.018,0,.013,0,.018,0,.013,0,.017,0,.014,0L32.7.5l.014,0,.014,0,.016,0,.013,0,.017,0,.014,0,.016,0,.015,0,.015,0,.015,0,.027.005.016,0,.015,0,.016,0h0l.017,0,.013,0h0l.03.006h0l.03.006h0q.206.042.411.087l-.108.488c-.636-.14-1.286-.259-1.931-.353l.072-.495Zm3.9.862q.89.266,1.754.591h0l.029.011h0l.028.011h0l.027.01h0l.025.01.012,0,.008,0h0l-.178.467c-.609-.232-1.234-.445-1.859-.631l.143-.479ZM39.1,2.573l.007,0,.013.006.011.005.007,0,.007,0,.009,0,0,0,.01,0h0l.011.005,0,0,.025.012h0l.027.013h0q.311.148.618.3h0l.027.014h0l.027.014h0l.013.007h0l.013.006h0l.013.006h0L39.965,3l0,0,.012.006h0l.01.005,0,0,.011.006,0,0,.008,0,0,0,.011.005.005,0,.006,0,.006,0,.008,0,.008,0,.005,0,.007,0,0,0,.012.006,0,0,.008,0h0l.016.008,0,0,.026.013h0l.027.014h0q.349.182.692.375l-.244.436c-.569-.319-1.157-.62-1.747-.9l.212-.453Zm3.487,1.952q.836.551,1.628,1.161l-.3.4c-.517-.4-1.055-.781-1.6-1.14Zm3.168,2.437q.3.27.6.549h0l.022.021h0l.022.021h0l.022.021h0l.022.021h0l.022.021h0l.011.01h0l.01.01h0l.011.01h0l.01.009h0l.011.01h0l.01.009h0l.011.01h0l.011.01h0l.011.01h0l.01.01h0l.01.01h0l.01.01h0l.011.01h0l.021.02,0,0,.02.02,0,0,.02.02,0,0,.01.01h0l.009.009,0,0,.01.01h0l.009.009,0,0,.01.01,0,0,.008.008,0,0,.009.009,0,0,.008.008,0,0,.009.009,0,0,.008.008,0,0,.009.009,0,0,.007.007,0,0,.009.009,0,0,.007.007,0,0,.009.009,0,0L46.851,8l0,0,.009.009,0,0,.007.007,0,0,.009.009,0,0,.007.007,0,0,.009.009,0,0,.007.007,0,0,.009.009,0,0,.007.007,0,0,.008.008,0,0,.007.007,0,0,.007.007,0,0,.007.007,0,0,.007.007,0,0,.007.007,0,0,.006.006,0,0,.006.007.005.005.006.006,0,0,.007.007.005.005.005.005,0,0,.008.008,0,0,.005.005,0,0,.008.008,0,0,.005.006,0,0,.008.008,0,0,.005.006,0,0,.008.008,0,0,.005.005,0,0,.008.008,0,0,0,0,0,0,.008.008,0,.005,0,0,0,0-.359.348c-.454-.468-.931-.925-1.417-1.359Zm2.783,2.869.05.06h0l.019.023v0l.009.011h0l.008.01,0,0,.008.01,0,0,.006.007,0,0,.007.008,0,.006,0,.005,0,.005,0,.006.007.009,0,0,.018.022h0q.553.673,1.063,1.379l-.405.293c-.381-.528-.787-1.049-1.206-1.549Zm2.339,3.24q.374.607.717,1.233h0l.03.054h0l.01.018h0l0,.009h0l.015.027h0l.015.027h0l.01.018h0l.014.027h0l.014.027h0l0,.009h0l.009.017h0l0,.009h0l.009.018h0l.014.026v0l.014.026h0l0,.009h0l.009.017v0l.014.026v0l.014.026h0l.014.026v0l.013.026v0l.014.026h0l.01.019-.444.231c-.3-.578-.627-1.152-.969-1.707l.426-.262Zm1.844,3.545q.108.25.211.5h0l.005.013h0l.006.014h0l.005.013h0l.006.014h0l0,.012v0l.005.013v0l0,.011v0l.005.013v0l0,.011v0l.005.013v0l0,.011v0l0,.012v0l0,.01,0,0,0,.012v0l0,.01,0,0,0,.012v0l0,.009,0,0,0,.011v0l0,.009,0,0,0,.011,0,.005,0,.008,0,.006,0,.009,0,.006,0,.008,0,.006,0,.009,0,.007,0,.008,0,.006,0,.008,0,.008,0,.007,0,.006,0,.007,0,.009,0,.006,0,.007,0,.006,0,.009,0,.007,0,.007,0,.006,0,.01,0,.007,0,.007,0,.006,0,.01,0,.006,0,.006,0,.007,0,.01,0,.007,0,.007,0,.006,0,.009,0,.008,0,.006,0,.007,0,.008,0,.009,0,.006,0,.007,0,.007,0,.009,0,.006,0,.007,0,.006,0,.01,0,.006,0,.007,0,.006,0,.011,0,.006,0,.007,0,.005,0,.011,0,.005,0,.008,0,0,0,.012,0,0,0,.009,0,0,0,.012,0,0,0,.008,0,0,0,.012,0,0,0,.009,0,0,0,.012v0l0,.01v0l0,.012v0l0,.011v0l0,.013v0l0,.011v0l0,.013v0l0,.012v0l0,.014h0l0,.013v0l.01.028h0l.01.029h0q.061.169.12.34l-.472.164c-.213-.616-.452-1.231-.71-1.83Zm1.307,3.776c.018.067.036.135.053.2v0l.007.027v0l0,.008,0,.006,0,.011,0,.007,0,.006,0,.009v.006l0,.01v0l0,.012h0l0,.013v0l0,.012h0l0,.014v0l.007.029h0q.119.476.221.958h0l.006.03h0l.006.029v0l0,.013h0l0,.014v0l0,.01v0l0,.013v0l0,.009v.005l0,.012V21.9l0,.007v.006l0,.011v.025l0,.014v0l0,.009h0c.025.121.048.242.071.364l-.491.093c-.121-.641-.267-1.285-.435-1.914l.483-.129Zm.742,3.927q.113.987.155,1.994l-.5.021c-.027-.653-.078-1.311-.152-1.958ZM54.433,28.3l.5.015c0,.091-.006.184-.01.274v.149h0q-.035.768-.111,1.524h0v.015h0l0,.015h0v.015l-.5-.051C54.37,29.611,54.413,28.952,54.433,28.3Zm-.4,3.9.492.087c-.035.2-.072.4-.112.594h0l-.006.03v0l0,.01h0l0,.019v0l0,.009v0l0,.017v0l0,.008v0l0,.014v0l0,.008v0l0,.015v0l0,.008v.005l0,.008,0,.011v.02l0,.009,0,.008v.013l0,.01v.007l0,.008v.006l0,.01v.005l0,.01v0l0,.01v0l0,.013v0l0,.01v0l0,.013v0l0,.01v0l0,.013v0l0,.011v0l0,.014v0l0,.013v0l-.006.029h0l-.006.029h0l-.007.03h0q-.092.419-.2.834l-.485-.123C53.78,33.49,53.919,32.844,54.032,32.2Zm-.964,3.8.474.158q-.1.295-.2.587h0l0,.009v0l-.005.015v0l0,.008,0,0,0,.013,0,0,0,.007,0,.006,0,.01,0,.008,0,.006,0,.007,0,.006,0,.01,0,.005,0,.009v0l0,.012v0l0,.011v0l0,.013v0l0,.011v0l0,.013v0l0,.012h0l-.005.014v0l0,.013h0l-.005.014h0l-.005.014h0l-.005.014h0q-.185.5-.389.987h0l-.461-.192C52.63,37.242,52.862,36.623,53.068,36.005Zm-1.51,3.621.446.226q-.141.279-.289.555h0l-.029.055h0l-.029.055h0q-.3.55-.619,1.085l-.429-.258C50.945,40.786,51.265,40.208,51.558,39.627ZM49.538,42.99l.409.288-.178.249h0l-.017.024v0l-.016.022,0,0,0,.007,0,0-.007.01,0,.005,0,.005-.007.009,0,0-.005.007,0,0-.007.01,0,0-.006.009,0,0-.008.011,0,0-.007.009,0,0-.008.011v0l-.008.011h0l-.009.012h0l-.018.024h0q-.422.573-.872,1.122l-.387-.317C48.763,44.048,49.162,43.523,49.538,42.99Zm-2.487,3.036.363.344q-.317.334-.645.658h0l-.01.01,0,0-.009.009h0l-.01.01,0,0-.008.008,0,0-.009.009,0,0-.007.006,0,0-.008.008,0,0-.006.006-.006.006-.006.006,0,0-.005,0-.008.008,0,0-.006.006,0,0-.012.012h0l-.007.007h0l-.013.013h0q-.3.3-.618.582L45.65,47.4C46.131,46.961,46.6,46.5,47.051,46.026Zm-2.9,2.645.309.393h0l-.023.018h0l-.023.018h0l-.023.018h0l-.012.009h0l-.011.009h0l-.011.009h0l-.011.008h0l-.011.009h0l-.011.008h0l-.012.009h0l-.011.009h0l-.011.009h0l-.01.008h0l-.011.009h0l-.011.008h0l-.012.009h0l-.011.009h0l-.012.009h0l-.011.009h0l-.012.009h0l-.011.009h0l-.012.009h0l-.011.009h0l-.012.009h0l-.023.018h0l-.023.018h0l-.023.017h0l-.023.018h0l-.023.018h0l-.023.017h0l-.011.009h0l-.011.008,0,0-.011.009h0l-.011.008h0l-.011.009h0l-.011.008h0l-.011.009h0l-.01.008,0,0-.011.008,0,0-.009.007,0,0-.011.008h0l-.01.007,0,0-.011.008,0,0-.009.006,0,0-.011.008,0,0-.009.007,0,0-.011.008,0,0-.008.006,0,0-.01.007,0,0-.007.005,0,0-.01.007,0,0-.007.005,0,0-.01.007,0,0-.006,0-.006,0-.007.005-.005,0-.006,0-.007.005-.007.005-.005,0-.006,0-.008.006-.005,0-.006,0,0,0-.01.007,0,0-.006,0,0,0-.01.008,0,0-.007,0,0,0-.012.009,0,0-.007.005h0l-.014.01h0l-.008.005h0l-.015.011h0l-.025.018h0l-.025.018h0q-.295.211-.6.415l-.28-.414C43.107,49.464,43.64,49.075,44.152,48.671Zm-3.25,2.2.249.433q-.27.155-.544.3h0l-.027.015h0l-.026.014h0l-.013.007h0l-.011.006,0,0-.012.006,0,0-.009,0,0,0-.011.006,0,0-.008,0,0,0-.011.006,0,0-.007,0-.006,0-.01.005-.006,0-.006,0-.006,0,0,0-.012.006,0,0-.025.013h0l-.026.014h0l-.027.014h0q-.459.243-.928.469l-.217-.45C39.753,51.5,40.337,51.195,40.9,50.87Zm-3.535,1.7.184.465q-.405.16-.817.307h0l-.029.01h0l-.029.01h0l-.029.01h0l-.028.01h0l-.028.01h0l-.028.01h0l-.028.01h0l-.028.01h0l-.028.01h0l-.028.01h0l-.028.01h0l-.028.01h0l-.028.01h0l-.029.01h0q-.326.111-.655.213l-.149-.477C36.139,53.032,36.762,52.812,37.368,52.573Zm-3.745,1.169.114.487-.136.031h0l-.03.007h0l-.015,0h0l-.015,0h0l-.015,0-.014,0h0l-.016,0-.015,0-.016,0-.013,0h0l-.017,0-.014,0-.017,0-.014,0-.017,0-.014,0-.017,0-.014,0-.017,0-.014,0-.017,0-.014,0-.017,0-.014,0-.014,0-.017,0-.013,0-.017,0-.014,0-.017,0-.014,0-.017,0-.014,0-.016,0-.015,0-.014,0-.016,0-.013,0-.017,0-.013,0-.017,0-.014,0-.017,0-.014,0-.016,0-.015,0-.016,0-.015,0-.014,0-.017,0-.014,0-.017,0-.014,0-.017,0-.014,0-.017,0-.014,0-.015,0-.016,0-.013,0-.017,0-.014,0-.017,0-.014,0-.017,0-.014,0-.017,0-.014,0-.016,0-.015,0-.016,0-.015,0-.014,0-.018,0-.014,0-.018,0-.014,0-.018,0-.014,0-.018,0-.014,0-.018,0-.014,0-.02,0-.012,0h0l-.028,0h0l-.03.005h0c-.152.026-.3.052-.456.076l-.078-.494C32.34,54.015,32.989,53.889,33.623,53.741Zm-3.876.61.042.5-.146.012h-.118l-.029,0h0q-.844.061-1.7.071l-.006-.5C28.439,54.436,29.1,54.4,29.747,54.351Zm-5.88-.144c.645.086,1.3.15,1.954.189l-.03.5c-.17-.01-.34-.022-.508-.035l-.031,0h-.012l-.019,0H25.1l-.031,0h-.045l-.031,0-.031,0h0l-.031,0h0q-.327-.03-.651-.068h0l-.03,0h0l-.029,0-.015,0-.016,0-.014,0-.017,0-.014,0-.015,0-.016,0-.014,0-.017,0-.013,0-.012,0-.018,0h-.011l-.02,0h-.011L23.8,54.7Zm-3.84-.8c.626.179,1.267.338,1.905.471l-.1.489c-.2-.042-.4-.086-.6-.133h0l-.031-.007h0l-.03-.007h0l-.03-.007h0L21.1,54.2h0l-.03-.007h0l-.031-.007h0L21,54.178h0l-.03-.007h0l-.029-.007h0l-.029-.007h0l-.029-.007h0l-.03-.007h0l-.03-.007-.011,0h0l-.02,0-.011,0h0l-.018,0-.01,0h0l-.029-.007h0L20.68,54.1h0l-.028-.007h0l-.028-.007-.012,0h0l-.019,0-.012,0h0l-.019,0-.011,0h0l-.019,0-.011,0h0l-.029-.008h0l-.029-.008h0l-.029-.008h0l-.029-.008h0l-.03-.008h0l-.03-.008h0L20.3,54l-.01,0h0l-.031-.008h0l-.061-.017h0l-.307-.086ZM16.345,52.05c.594.269,1.2.52,1.816.745l-.173.469c-.174-.064-.348-.13-.521-.2h0l-.028-.011h0l-.009,0h0l-.017-.007-.011,0-.005,0-.011,0-.012,0-.016-.006-.013-.005-.008,0-.007,0-.008,0-.005,0-.01,0,0,0-.009,0-.005,0-.011,0h0l-.01,0,0,0-.012,0h0l-.011,0,0,0-.013-.005h0l-.012-.005h0l-.026-.01h0l-.027-.011h0l-.027-.011h0l-.028-.011h0l-.028-.011h0l-.028-.012h0L17,52.877h0q-.433-.179-.859-.372ZM12.9,50.173c.548.353,1.117.69,1.689,1l-.239.439-.328-.182h0l-.026-.015h0l-.025-.014h0l-.012-.007h0l-.012-.007h0l-.011-.006h0l-.012-.007,0,0-.01-.006h0l-.011-.007,0,0-.011-.006h0l-.011-.006,0,0-.009-.005,0,0-.01-.006,0,0-.009-.005,0,0-.009-.005,0,0-.009,0-.006,0-.008,0-.005,0-.007,0-.007,0-.007,0-.006,0-.006,0-.009-.005-.006,0-.006,0-.006,0-.01-.006,0,0-.007,0,0,0-.011-.007,0,0-.007,0,0,0-.011-.006,0,0-.007,0,0,0-.012-.007,0,0-.007,0h0l-.015-.009h0l-.008,0h0l-.015-.009,0,0-.024-.014h0l-.025-.015h0l-.026-.015h0l-.026-.016h0q-.42-.249-.829-.513ZM9.762,47.816c.492.428,1.005.844,1.526,1.235l-.3.4q-.394-.3-.777-.605h0l-.012-.01h0l-.036-.03h0l-.024-.02h0l-.024-.02h0l-.024-.02h0l-.024-.02h0l-.024-.02h0q-.308-.253-.608-.515ZM7,45.031c.424.495.873.98,1.332,1.443l-.355.352q-.316-.318-.621-.646h0l-.021-.022h0l-.013-.014h0l-.007-.007h0L7.3,46.12v0l-.006-.007h0L7.278,46.1l0,0-.006-.006,0,0-.01-.011,0,0,0-.005,0,0-.008-.008,0,0,0-.005-.006-.007-.006-.006,0-.005,0,0L7.2,46.013l0,0L7.19,46l0,0-.008-.009,0,0-.007-.007,0,0-.009-.009,0,0-.007-.008,0,0-.009-.01,0,0-.008-.008,0,0-.009-.01,0,0L7.1,45.909l0,0-.009-.01,0,0-.009-.01h0l-.01-.011,0,0-.009-.01h0l-.01-.011h0l-.02-.022h0L7.01,45.8h0l-.02-.022h0l-.02-.023h0l-.021-.023h0q-.166-.187-.329-.376ZM4.664,41.876c.348.551.722,1.1,1.109,1.62l-.4.3q-.594-.8-1.13-1.65ZM2.813,38.417c.265.6.555,1.189.862,1.763l-.441.236q-.2-.382-.4-.771h0l-.014-.028h0L2.81,39.59h0L2.8,39.562h0l-.013-.026v0l0-.008h0l-.008-.016v0l0-.008v0l-.007-.014,0,0,0-.008v0l-.006-.013,0,0,0-.007,0,0-.006-.012,0,0,0-.007,0,0L2.715,39.4l0-.006,0-.006,0-.007,0-.009,0-.006,0-.006,0-.008,0-.008,0-.006,0-.006,0-.008,0-.008,0-.006,0-.006,0-.009,0-.007,0-.006,0-.006,0-.009,0-.007,0-.007,0-.006-.005-.011,0,0,0-.007,0-.006,0-.009,0-.006,0-.007,0-.006,0-.009,0-.006,0-.007,0-.005,0-.011,0-.005,0-.007,0-.006-.005-.011,0,0,0-.007,0-.005-.005-.011,0,0,0-.008,0-.005,0-.011,0,0,0-.007,0-.005-.005-.011,0,0,0-.007,0-.006-.005-.011,0,0,0-.008,0-.005,0-.01,0-.005,0-.007,0-.006,0-.01,0-.005,0-.007,0-.005,0-.011,0-.006,0-.007,0-.006,0-.01,0-.006,0-.007,0-.006,0-.008,0-.008,0-.006,0-.006,0-.007,0-.01,0-.006,0-.006,0-.008,0-.009,0-.006,0-.007,0-.007,0-.01,0,0,0-.007,0,0-.006-.014,0,0,0-.008h0L2.4,38.714v0l0-.008h0l-.008-.017v0l-.012-.027v0l-.012-.027h0l0-.006ZM1.486,34.725c.175.628.375,1.257.6,1.87l-.471.169Q1.274,35.827,1,34.859ZM.713,30.878c.082.647.188,1.3.316,1.937l-.49.1Q.462,32.534.4,32.149h0l-.005-.03h0l-.005-.029v0l0-.029v0l0-.013h0l0-.014v0l0-.012v0l0-.014v0l0-.011v0l0-.013v0l0-.01v0l0-.01v-.006l0-.01V31.9l0-.012v-.02l0-.011v-.028l0-.011V31.8l0-.015v-.016l0-.017V31.74l0-.029v0l0-.03v0l0-.03v0l0-.01h0l0-.032h0l0-.032h0c-.015-.1-.03-.206-.044-.309h0l0-.031v0l0-.03v0l0-.03v-.015l0-.015v-.018l0-.012v-.027l0-.012v-.02l0-.013v-.019l0-.013v0l0-.013v0l0-.029h0l0-.015ZM0,26.949l.5.009c0,.17,0,.343,0,.514,0,.482.013.97.038,1.45l-.5.026q-.02-.377-.03-.756h0v-.22H0v-.437s0,0,0-.005,0,0,0-.006,0,0,0-.005v-.006s0,0,0-.005V27.5s0,0,0-.006,0,0,0-.006,0,0,0,0,0-.008,0-.011H0s0-.01,0-.014v-.016s0,0,0,0,0-.009,0-.014V27.24H0V27.08H0Q0,27.014,0,26.949Zm.362-3.98.493.081c-.106.642-.19,1.3-.249,1.947l-.5-.045v-.008l0-.03v0l0-.03v-.015l0-.017v-.016l0-.016v-.3l0-.014v-.017l0-.014h0l0-.014v0l0-.029v0l0-.029h0l0-.014h0l0-.031h0Q.255,23.651.367,22.969ZM1.3,19.084l.476.153c-.2.621-.377,1.256-.53,1.89L.765,21.01l.011-.045h0l.007-.03h0l0-.013v0l0-.014v0l0-.011v0l0-.013v0l0-.009v-.005l0-.012v-.006l0-.007,0-.007,0-.009,0-.008,0-.006,0-.007v-.005l0-.013v0l0-.008v0l0-.016v0l0-.009v0l0-.018h0l0-.01h0l.008-.03h0q.089-.35.187-.7h0l.008-.029v0l.008-.028v0l0-.009h0l0-.017v0l0-.008v0l0-.014v0l0-.008v0l0-.012,0-.006,0-.007,0-.006,0-.01,0-.008,0-.006,0-.008,0-.006,0-.011,0-.005,0-.009v0l0-.012v0l0-.01v0l0-.013v0l0-.011v0l0-.013v0l0-.012v0l0-.014h0l0-.013v0l.009-.029h0l.009-.029h0l.009-.029h0Q1.233,19.306,1.3,19.084ZM2.8,15.378l.449.22c-.287.585-.557,1.188-.8,1.792L1.984,17.2v0l0-.008,0-.005,0-.009,0-.007,0-.007,0-.006,0-.005,0-.012,0,0,0-.007,0,0,.005-.013v0l0-.008v0l.007-.017v0l0-.009h0l.008-.019h0Q2.4,16.2,2.8,15.378Zm2.018-3.449.412.283c-.369.537-.724,1.095-1.053,1.657l-.432-.253Q4.251,12.752,4.817,11.928Zm2.5-3.121.367.34c-.443.478-.874.979-1.281,1.488l-.39-.312.2-.243h0l.019-.023h0l.009-.012h0l.009-.011h0l.009-.011h0l.008-.01h0L6.275,10v0l.008-.009,0,0,.008-.01,0,0L6.3,9.961l0,0,.008-.01,0,0,.006-.007,0,0,.008-.009,0,0,.005-.007,0,0L6.353,9.9l0,0,0-.006.005-.006.006-.007,0,0,.005-.006.005-.007.005-.006,0-.005,0-.005.007-.009,0,0,0-.005,0-.005.007-.008,0-.005,0-.005,0,0,.008-.009,0,0,0-.005,0-.005.007-.008,0,0,0-.006,0,0,.008-.009,0,0,0-.006,0-.005.007-.008,0,0,0-.006,0,0,.008-.01,0,0,0-.006.006-.007.006-.007,0,0,.005-.006.007-.008.005-.006,0,0,0-.006.007-.008,0-.005,0,0L6.6,9.609,6.605,9.6l0-.006,0,0,.006-.007.005-.006,0-.006,0,0,.007-.008,0-.006,0-.005,0,0,.006-.007.005-.006,0-.005,0-.006.006-.006.006-.007,0,0L6.7,9.5l0,0,.007-.008,0,0,.006-.007,0,0,.007-.008,0,0,.007-.008,0,0,.007-.008,0,0,.008-.009,0,0,.007-.008,0,0L6.781,9.4l0,0L6.79,9.39l0,0,.008-.01,0,0,.007-.008,0,0,.008-.01,0,0,.007-.008,0,0,.009-.01,0,0,.007-.008,0,0,.009-.01,0,0L6.873,9.3l0,0,.009-.01,0,0,.008-.009,0,0L6.9,9.26l0,0,.009-.01h0l.01-.011h0l.009-.01h0l.01-.011h0L6.957,9.2h0l.01-.011h0l.01-.011h0l.01-.011h0l.02-.022h0l.02-.023h0L7.05,9.1h0l.021-.023h0Q7.192,8.939,7.314,8.808ZM10.235,6.08l.314.389c-.507.409-1.006.842-1.482,1.287l-.341-.365Q9.456,6.709,10.235,6.08ZM13.519,3.8l.254.43c-.561.331-1.117.688-1.653,1.059l-.285-.411.025-.018h0l.024-.017h0l.023-.016,0,0,.011-.008h0l.009-.007,0,0L11.95,4.8l0,0,.008-.006,0,0,.01-.007,0,0,.007-.005,0,0L12,4.768l.005,0,.006,0,.006,0,.007,0,.008-.006,0,0,.006,0,0,0,.011-.007,0,0,.022-.015,0,0,.022-.015,0,0,.023-.016h0l.025-.017h0q.506-.34,1.027-.658h0l.025-.016h0l.008-.005h0l.016-.01h0l.025-.015h0l.025-.015h0l.024-.015,0,0,.024-.014,0,0,.023-.014,0,0,.023-.014,0,0,.023-.014,0,0,.011-.006.005,0,.006,0,0,0,.009-.005.007,0,.006,0,.005,0,.007,0,.008,0,.006,0,.006,0ZM17.1,2.026l.189.463c-.6.246-1.205.518-1.789.808l-.222-.448Q16.17,2.405,17.1,2.026ZM20.9.791l.119.486c-.633.155-1.268.336-1.888.538l-.154-.476c.2-.064.391-.125.588-.184h0l.029-.009h0l.009,0h0l.016,0h0l.008,0h0l.014,0,.012,0h.005l.013,0,.012,0,.013,0,.016,0,.013,0,.011,0,.013,0,.005,0,.012,0,.014,0h0l.012,0,.014,0h0l.013,0h0l.01,0h0l.013,0h0l.012,0h0l.014,0h0l.014,0h0l.028-.008h0l.028-.008h0l.029-.008h0Q20.45.9,20.9.791ZM24.84.124l.047.5c-.65.062-1.3.148-1.946.257L22.858.386Q23.838.22,24.84.124ZM27.473,0V.5c-.207,0-.418,0-.625.007l-.011-.5h.049c.17,0,.337,0,.509-.006h.077Z" fill="#95a2fe"/><path d="M17.776,1A16.776,16.776,0,0,0,5.914,29.639,16.776,16.776,0,0,0,29.639,5.914,16.667,16.667,0,0,0,17.776,1m0-1A17.776,17.776,0,1,1,0,17.776,17.776,17.776,0,0,1,17.776,0Z" transform="translate(9.696 9.697)" fill="#ffdd7e"/></g></g><g transform="translate(172.124 917.367)" opacity="0.88"><path d="M11.4,9.737,12.835,8.3v6.616a.6.6,0,0,0,.573.6.581.581,0,0,0,.573-.6V8.3l1.432,1.432a.554.554,0,0,0,.8,0,.588.588,0,0,0,0-.831L13.78,6.472a.588.588,0,0,0-.831,0L10.572,8.906a.587.587,0,1,0,.831.831Z" transform="translate(-5.637)" fill="#fff"/><path d="M23.523,32.9a5.679,5.679,0,0,0-1,.086V28.263c0-1.633-2.664-2.463-5.3-2.463s-5.3.859-5.3,2.463v3.609A8.968,8.968,0,0,0,7.8,31.013c-2.635,0-5.3.859-5.3,2.463v5.213c0,1.633,2.664,2.463,5.3,2.463,1.89,0,3.781-.43,4.7-1.289a7.64,7.64,0,0,0,4.7,1.289c.172,0,.315-.029.487-.029a6.175,6.175,0,1,0,5.843-8.22Zm-11.6,5.786c0,.458-1.575,1.289-4.124,1.289s-4.124-.831-4.124-1.289v-1a8.968,8.968,0,0,0,4.124.859,8.968,8.968,0,0,0,4.124-.859Zm0-2.606c0,.458-1.575,1.289-4.124,1.289s-4.124-.831-4.124-1.289v-1a8.968,8.968,0,0,0,4.124.859,8.968,8.968,0,0,0,4.124-.859ZM7.8,34.765c-2.549,0-4.124-.831-4.124-1.289S5.25,32.187,7.8,32.187s4.124.831,4.124,1.289S10.348,34.765,7.8,34.765Zm9.423-7.819c2.549,0,4.124.831,4.124,1.289s-1.575,1.289-4.124,1.289S13.1,28.693,13.1,28.235,14.644,26.946,17.222,26.946ZM13.1,29.867a8.968,8.968,0,0,0,4.124.859,8.968,8.968,0,0,0,4.124-.859v1c0,.458-1.575,1.289-4.124,1.289S13.1,31.328,13.1,30.87Zm0,2.606a8.968,8.968,0,0,0,4.124.859,8.968,8.968,0,0,0,4.124-.859V33.3a6.532,6.532,0,0,0-2.034,1.26,8.974,8.974,0,0,1-2.091.229c-2.549,0-4.124-.831-4.124-1.289V32.474Zm0,2.606a8.968,8.968,0,0,0,4.124.859c.344,0,.687-.029,1-.057a5.5,5.5,0,0,0-.63,1.461c-.115,0-.229.029-.372.029-2.549,0-4.124-.831-4.124-1.289Zm4.124,4.926c-2.549,0-4.124-.831-4.124-1.289v-1a8.968,8.968,0,0,0,4.124.859h.143a3.113,3.113,0,0,0-.029.516,4.964,4.964,0,0,0,.086.917A.468.468,0,0,0,17.222,40.007Zm6.3,4.067a5.012,5.012,0,1,1,5.012-5.012A5.018,5.018,0,0,1,23.523,44.074Z" transform="translate(0 -13.915)" fill="#fff"/><path d="M70.147,61.162h.8a.544.544,0,0,1,.544.544h1.174a1.709,1.709,0,0,0-1.547-1.69V59.1H69.947v.917a1.708,1.708,0,0,0,.172,3.408h.8a.544.544,0,0,1,.544.544.52.52,0,0,1-.516.544h-.8a.544.544,0,0,1-.544-.544H68.429a1.709,1.709,0,0,0,1.547,1.69v.888H71.15v-.888a1.708,1.708,0,0,0-.172-3.408h-.8a.545.545,0,0,1-.029-1.088Z" transform="translate(-47.025 -37.677)" fill="#fff"/></g></g></svg>';

  static String review =
      '<svg xmlns="http://www.w3.org/2000/svg" width="88.075" height="61.702" viewBox="0 0 88.075 61.702"><g transform="translate(-27.052 -694.897)"><g transform="translate(-153.948 158.336)" opacity="0.7"><rect width="3.263" height="3.263" transform="translate(181 595)" fill="#ffd869"/><rect width="3.263" height="3.263" transform="translate(185.895 595)" fill="#c3dcf5"/><rect width="3.263" height="3.263" transform="translate(190.79 595)" fill="#ffd869"/><rect width="3.263" height="3.263" transform="translate(195.684 595)" fill="#c3dcf5"/></g><g transform="translate(42 732)"><rect width="14" height="14" fill="none"/><path d="M4,1.857v.286a.143.143,0,0,1-.143.143H2.286V3.857A.143.143,0,0,1,2.143,4H1.857a.143.143,0,0,1-.143-.143V2.286H.143A.143.143,0,0,1,0,2.143V1.857a.143.143,0,0,1,.143-.143H1.714V.143A.143.143,0,0,1,1.857,0h.286a.143.143,0,0,1,.143.143V1.714H3.857A.143.143,0,0,1,4,1.857Z" transform="translate(5 5)" fill="#fff"/></g><g transform="translate(84.5 705.505) rotate(-45)"><rect width="15.002" height="15.002" fill="none"/><path d="M5,2.322v.357a.179.179,0,0,1-.179.179H2.858V4.823A.179.179,0,0,1,2.679,5H2.322a.179.179,0,0,1-.179-.179V2.858H.179A.179.179,0,0,1,0,2.679V2.322a.179.179,0,0,1,.179-.179H2.144V.179A.179.179,0,0,1,2.322,0h.357a.179.179,0,0,1,.179.179V2.144H4.823A.179.179,0,0,1,5,2.322Z" transform="translate(5 5)" fill="#fff"/></g><g transform="translate(-1551 -155)"><g transform="translate(1611 867)"><rect width="31" height="31" fill="none"/><path d="M11.467,26H2.5A2.554,2.554,0,0,1,0,23.4V2.6A2.554,2.554,0,0,1,2.5,0h15A2.554,2.554,0,0,1,20,2.6V17.121a2.672,2.672,0,0,1-.738,1.846l-6.024,6.267A2.468,2.468,0,0,1,11.467,26ZM12.5,16.9a1.278,1.278,0,0,0-1.25,1.3v5.434L17.725,16.9ZM4.375,10.4a.638.638,0,0,0-.625.649v1.3A.638.638,0,0,0,4.375,13h6.249a.638.638,0,0,0,.625-.649v-1.3a.638.638,0,0,0-.625-.649Zm0-5.2a.639.639,0,0,0-.625.651v1.3a.639.639,0,0,0,.625.651h11.25a.639.639,0,0,0,.624-.651V5.85a.639.639,0,0,0-.624-.651Z" transform="translate(5 3)" fill="#95a2fe"/></g><g transform="translate(1603 864)"><rect width="34" height="34" fill="none"/></g></g><g transform="translate(-1367.902 4.098)"><path d="M2.8.3H0A.3.3,0,0,1-.3,0,.3.3,0,0,1,0-.3H2.8a.3.3,0,0,1,.3.3A.3.3,0,0,1,2.8.3Z" transform="translate(1456.5 732.5)" fill="#fff"/><path d="M2.8.3H0A.3.3,0,0,1-.3,0,.3.3,0,0,1,0-.3H2.8a.3.3,0,0,1,.3.3A.3.3,0,0,1,2.8.3Z" transform="translate(1456.5 733.902)" fill="#fff"/></g><path d="M367.892,111.806a1.267,1.267,0,0,1,1.266-1.292h0a1.294,1.294,0,0,1,1.292,1.279h0a1.283,1.283,0,0,1-1.279,1.279h0A1.271,1.271,0,0,1,367.892,111.806Zm-.025-3.85a1.283,1.283,0,0,1,1.279-1.279h0a1.275,1.275,0,0,1,1.279,1.279h0a1.264,1.264,0,0,1-1.266,1.279h-.013A1.282,1.282,0,0,1,367.866,107.956Zm-.013-3.824a1.278,1.278,0,0,1,1.279-1.292h0a1.283,1.283,0,0,1,1.279,1.279h0a1.274,1.274,0,0,1-1.279,1.279h0A1.272,1.272,0,0,1,367.853,104.132Zm-.013-3.837A1.275,1.275,0,0,1,369.106,99h0a1.278,1.278,0,0,1,1.292,1.279h0a1.275,1.275,0,0,1-1.279,1.279h0A1.271,1.271,0,0,1,367.841,100.294Zm-.013-3.85a1.264,1.264,0,0,1,1.266-1.279h0a1.294,1.294,0,0,1,1.292,1.279h0a1.282,1.282,0,0,1-1.279,1.279h0A1.275,1.275,0,0,1,367.827,96.444Zm-.013-3.824a1.267,1.267,0,0,1,1.266-1.292h0a1.294,1.294,0,0,1,1.292,1.279h0a1.283,1.283,0,0,1-1.279,1.279h0A1.272,1.272,0,0,1,367.815,92.62Z" transform="translate(-255.322 603.569)" fill="#f2f6f9" opacity="0.5"/><g transform="translate(49 715)"><rect width="24" height="24" fill="none"/><path d="M15.727,19h-.021a.481.481,0,0,1-.251-.07L10,15.6,4.575,18.93a.475.475,0,0,1-.25.07H4.3a.466.466,0,0,1-.287-.1l-.249-.15a.508.508,0,0,1-.19-.529L5.014,12,.177,7.86a.492.492,0,0,1-.149-.54l.11-.249a.489.489,0,0,1,.438-.35l6.373-.51L9.392.32A.506.506,0,0,1,9.864,0h.276A.49.49,0,0,1,10.6.32l2.483,5.89,6.373.51a.49.49,0,0,1,.438.35l.08.249a.489.489,0,0,1-.139.53L14.987,12l1.436,6.221a.509.509,0,0,1-.189.529l-.22.15A.466.466,0,0,1,15.727,19ZM10,13.55h0l4.109,2.51-1.117-4.68,3.659-3.13-4.8-.38L10,3.41,8.145,7.871l-4.8.38,3.659,3.13-1.127,4.68L10,13.551Z" transform="translate(2 2)" fill="#ffdd7e"/></g></g></svg>';

  static String addProduct =
      '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="63.009" height="54.77" viewBox="0 0 63.009 54.77"><defs><linearGradient id="a" x1="0.5" y1="-0.152" x2="0.5" y2="1.324" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#95a2fe"/><stop offset="1" stop-color="#fff"/></linearGradient></defs><g transform="translate(-275 -602.73)"><path d="M0,0H4V1H1V4.081H0ZM8,0h4V1H8Zm8,0h4V1H16Zm8,0h4V1H24Zm8,0h4V1H32Zm5.5,1.5h1v4h-1Zm0,8h1v4h-1Zm0,8h1v4h-1Zm0,8h1v4h-1Zm0,8h1v4h-1Zm0,8h1v4h-1Zm0,8h1v2.04H36.54v-1h.96Zm-8.96,1.04h4v1h-4Zm-8,0h4v1h-4Zm-8,0h4v1h-4Zm-8,0h4v1h-4ZM0,48.081H1v3.46H0Zm0-8H1v4H0Zm0-8H1v4H0Zm0-8H1v4H0Zm0-8H1v4H0Zm0-8H1v4H0Z" transform="translate(295.5 602.73)" fill="#ffda73"/><path d="M0,0H32.009V50H0Z" transform="translate(306 607.5)" opacity="0.3" fill="url(#a)"/><g transform="translate(299.491 610.986)"><path d="M108.687,8.03a.465.465,0,0,0-.555.169l-1.284,1.739L105.564,8.2a.465.465,0,0,0-.769,0l-1.284,1.739L102.228,8.2a.465.465,0,0,0-.769,0l-1.284,1.739L98.892,8.2a.465.465,0,0,0-.769,0L96.84,9.938,95.556,8.2a.465.465,0,0,0-.769,0L93.5,9.938,92.22,8.2a.465.465,0,0,0-.769,0L90.168,9.938,88.885,8.2a.465.465,0,0,0-.555-.17.566.566,0,0,0-.33.532v33.9a.566.566,0,0,0,.33.532.465.465,0,0,0,.555-.17l1.284-1.739,1.284,1.739a.465.465,0,0,0,.769,0l1.284-1.739,1.284,1.739a.465.465,0,0,0,.769,0l1.284-1.739,1.284,1.739a.465.465,0,0,0,.769,0l1.284-1.739,1.284,1.739a.465.465,0,0,0,.769,0l1.284-1.739,1.284,1.739a.465.465,0,0,0,.769,0l1.284-1.739,1.283,1.739a.465.465,0,0,0,.555.17.566.566,0,0,0,.33-.532V8.561A.565.565,0,0,0,108.687,8.03ZM108.016,40.9l-.783-1.061a.465.465,0,0,0-.769,0l-1.284,1.739L103.9,39.838a.465.465,0,0,0-.769,0l-1.284,1.739-1.283-1.739a.465.465,0,0,0-.769,0L98.51,41.577l-1.284-1.739a.465.465,0,0,0-.769,0l-1.284,1.739L93.89,39.838a.465.465,0,0,0-.769,0l-1.284,1.739-1.284-1.739a.465.465,0,0,0-.769,0L89,40.9V10.122l.783,1.061a.465.465,0,0,0,.769,0l1.284-1.739,1.284,1.739a.465.465,0,0,0,.769,0l1.284-1.739,1.283,1.739a.465.465,0,0,0,.769,0l1.283-1.739,1.284,1.739a.465.465,0,0,0,.769,0l1.284-1.739,1.284,1.739a.465.465,0,0,0,.769,0l1.284-1.739,1.284,1.739a.465.465,0,0,0,.769,0l.784-1.061Z" transform="translate(-88 -7.996)" fill="#fff"/><path d="M228.151,68.151a1.559,1.559,0,0,1-1.557,1.557.519.519,0,1,1-1.038,0h-1.038a.519.519,0,1,1,0-1.038h2.075a.52.52,0,0,0,.519-.519.576.576,0,0,0-.584-.519h-.973a1.557,1.557,0,1,1,0-3.113.519.519,0,0,1,1.038,0h1.038a.519.519,0,0,1,0,1.038h-2.075a.519.519,0,0,0,0,1.038h.973A1.611,1.611,0,0,1,228.151,68.151Z" transform="translate(-215.567 -60.368)" fill="#fff"/><path d="M200.3,184.519a.519.519,0,0,0-.519-.519h-7.264a.519.519,0,1,0,0,1.038h7.264A.519.519,0,0,0,200.3,184.519Z" transform="translate(-185.642 -171.537)" fill="#fff"/><path d="M152.3,224h-7.783a.519.519,0,0,0,0,1.038H152.3a.519.519,0,0,0,0-1.038Z" transform="translate(-140.585 -208.704)" fill="#fff"/><path d="M296,216.519v7.264a.519.519,0,1,0,1.038,0v-7.264a.519.519,0,0,0-1.038,0Z" transform="translate(-283.013 -200.894)" fill="#fff"/><path d="M328.519,225.038h1.557a.519.519,0,1,0,0-1.038h-1.557a.519.519,0,0,0,0,1.038Z" transform="translate(-313.063 -208.704)" fill="#fff"/><path d="M152.3,256h-7.783a.519.519,0,1,0,0,1.038H152.3a.519.519,0,0,0,0-1.038Z" transform="translate(-140.585 -238.438)" fill="#fff"/><path d="M328.519,257.038h1.557a.519.519,0,1,0,0-1.038h-1.557a.519.519,0,0,0,0,1.038Z" transform="translate(-313.063 -238.438)" fill="#fff"/><path d="M152.3,288h-7.783a.519.519,0,1,0,0,1.038H152.3a.519.519,0,0,0,0-1.038Z" transform="translate(-140.585 -268.172)" fill="#fff"/><path d="M328.519,289.038h1.557a.519.519,0,1,0,0-1.038h-1.557a.519.519,0,0,0,0,1.038Z" transform="translate(-313.063 -268.172)" fill="#fff"/><path d="M152.3,320h-7.783a.519.519,0,0,0,0,1.038H152.3a.519.519,0,0,0,0-1.038Z" transform="translate(-140.585 -297.906)" fill="#fff"/><path d="M328.519,321.038h1.557a.519.519,0,1,0,0-1.038h-1.557a.519.519,0,0,0,0,1.038Z" transform="translate(-313.063 -297.906)" fill="#fff"/><path d="M294.226,376h-5.707a.519.519,0,0,0-.519.519v2.075a.519.519,0,0,0,.519.519h5.707a.519.519,0,0,0,.519-.519v-2.075A.519.519,0,0,0,294.226,376Zm-.519,2.075h-4.67v-1.038h4.67Z" transform="translate(-275.803 -349.784)" fill="#fff"/></g><g transform="translate(275 626)"><path d="M12,0A12,12,0,1,1,0,12,12,12,0,0,1,12,0Z" fill="#fff" opacity="0.98"/><path d="M12.571,5.837v.9a.449.449,0,0,1-.449.449H7.184v4.939a.449.449,0,0,1-.449.449h-.9a.449.449,0,0,1-.449-.449V7.184H.449A.449.449,0,0,1,0,6.735v-.9a.449.449,0,0,1,.449-.449H5.388V.449A.449.449,0,0,1,5.837,0h.9a.449.449,0,0,1,.449.449V5.388h4.939A.449.449,0,0,1,12.571,5.837Z" transform="translate(5.714 5.714)" fill="#95a2fe"/></g></g></svg>';

  static String support =
      '<svg xmlns="http://www.w3.org/2000/svg" width="68" height="56.836" viewBox="0 0 68 56.836"><g transform="translate(-47 -607)"><g transform="translate(-310 -1392.164)" opacity="0.38"><circle cx="0.5" cy="0.5" r="0.5" transform="translate(412 2055)" fill="#95a2fe"/><circle cx="1" cy="1" r="1" transform="translate(414 2054)" fill="#95a2fe"/><circle cx="1.5" cy="1.5" r="1.5" transform="translate(417 2053)" fill="#95a2fe"/><circle cx="2" cy="2" r="2" transform="translate(421 2052)" fill="#95a2fe"/></g><g transform="translate(-249.932 -92.51)"><g transform="translate(296.932 699.51)"><g transform="translate(0 0)"><path d="M23.164,0h.574q.718.017,1.424.078l-.043.5C24.474.528,23.816.5,23.164.5V0Zm3.981.341.124.022h.008l.03.005h0l.008,0,.009,0,.009,0,.009,0h0l.017,0,.008,0,.009,0,.009,0h0q.756.139,1.493.327l.009,0,.009,0h0l.025.006.008,0,.008,0,.008,0,.008,0,.009,0,.011,0,.008,0,.008,0,.008,0,.008,0,.007,0,.018,0,.008,0,.008,0,.008,0,.008,0h0L29.1.767h0l-.128.483C28.343,1.085,27.7.944,27.059.833Zm3.863,1.021q.576.207,1.137.443h0l.014.006h0l.029.012h0l.013.006h0l.013.005h0l.012.005h0l.011,0h0l.011,0,0,0,.01,0,0,0,.01,0,.012.005.008,0,.006,0,.01,0,0,0,.007,0h0l.014.006h0l.015.006h0q.247.107.49.219l-.209.454c-.59-.272-1.2-.522-1.81-.742Zm3.628,1.674q.666.38,1.3.8h0l.013.008h0l.013.008h0L36,3.881h0l.013.008h0l.051.034h0l.013.009h0l.039.026h0l.013.009h0l.013.008h0L36.185,4h0l.013.009h0l.012.009h0l.038.026h0l.012.008h0l.012.009h0l.025.017h0l.012.008h0l.012.008h0l-.284.411c-.535-.37-1.092-.721-1.655-1.043ZM37.92,5.308l0,0,0,0,.008.007,0,0,.007.005.007.005,0,0,.009.007,0,0,.009.007,0,0,.01.008,0,0L38,5.377h0l.01.009,0,0,.01.009h0l.011.009h0l.035.029h0l.012.01h0q.681.574,1.316,1.2L39.054,7c-.463-.456-.952-.9-1.453-1.311l.319-.385Zm2.849,2.8q.609.711,1.16,1.472h0l.009.012h0l.018.025h0l.009.012h0l.008.012h0l.008.012h0L42,9.681h0l-.406.291c-.378-.528-.785-1.046-1.207-1.54ZM43.1,11.356q.175.295.342.6h0l.038.068h0l.007.013h0l.022.041h0l.007.013h0l.007.013h0l.015.027h0l.007.013v0l.007.012v0l.007.012v0l.013.025v0l.006.012v0l.006.011v0l.006.012v0l.006.011v0l.006.012v0l.006.011,0,0,.006.011v0l.006.012h0l.006.011v0l.006.011,0,0,.006.011,0,0,.006.011v0l.006.011v0l.006.011,0,0,.006.011,0,0,.006.011,0,0,.006.011v0l.005.01,0,0,.006.011,0,0,.006.011,0,0,.006.011,0,0,.006.011,0,0,.005.01,0,0,.006.011,0,0,.006.011,0,0,.006.011v0l.005.011v0l.006.011v0l.006.011v0l.006.012v0l.006.012h0l.006.012v0l.006.012v0l.006.012v0l.006.012h0l.007.013h0l.006.013h0l.006.013v0l.006.013h0l.007.013h0l.014.027h0l.007.013h0q.081.161.159.323l-.45.217c-.282-.585-.593-1.165-.925-1.724Zm1.735,3.6q.088.232.171.467h0l0,.014h0l0,.013v0l.009.027v0l0,.012v0l0,.012v0l0,.012v0l0,.01v0l0,.011,0,0,0,.011,0,0,0,.01,0,.006,0,.008,0,.006,0,.009,0,.005,0,.01,0,.006,0,.007,0,.009,0,.005,0,.01,0,.005,0,.009,0,.006,0,.007v0l0,.012v0l0,.012v0l0,.014h0l0,.008v0l0,.014v0l.005.015h0q.177.528.33,1.068l-.481.136c-.177-.625-.383-1.25-.613-1.857ZM45.917,18.8q.186.975.289,1.978l-.5.051c-.066-.647-.161-1.3-.283-1.936Zm.407,3.974c0,.03,0,.06,0,.091h0v.009h0v.045h0v.012h0v.011h0q0,.018,0,.036h0q0,.024,0,.049h0v.024h0v.024s0,0,0,0v.012h0V23.1s0,.006,0,.009v0s0,.007,0,.011,0,0,0,0v.013s0,.005,0,.008v0s0,.007,0,.011h0s0,.008,0,.011v0s0,.006,0,.008v0s0,.007,0,.011v0s0,.006,0,.01,0,0,0,0,0,.006,0,.009V23.3h0v.088h0V23.4h0v.024h0v.012h0v.025h0v.037h0q-.009.638-.053,1.267l-.5-.034c.036-.521.054-1.05.054-1.575q0-.191,0-.382l.5-.008Zm-.768,3.909.494.077q-.031.2-.066.4h0v.007h0v.015h0l0,.02v.04l0,.017h0v.008h0v.008h0q-.13.727-.306,1.438l-.486-.12C45.327,27.97,45.456,27.325,45.556,26.683Zm-.934,3.795.473.161q-.195.572-.418,1.13h0l-.023.058h0l-.006.014h0l-.023.057h0l-.023.057h0l-.006.014h0l-.018.042h0l-.006.014h0l-.006.014h0l-.012.028h0l-.006.013h0l-.006.014h0l-.012.028h0l-.006.014h0l-.006.014h0L44.5,32.2h0l-.006.014h0l-.018.042h0l-.006.014h0l-.024.056h0l-.025.057h0l-.006.014h0l-.044.1-.457-.2C44.173,31.705,44.413,31.092,44.622,30.478Zm-1.577,3.576.438.241Q43,35.173,42.447,36h0l-.416-.278C42.391,35.186,42.733,34.623,43.045,34.054Zm-2.169,3.252.391.312,0,0-.007.009,0,0-.006.008,0,0-.007.008,0,0-.008.01,0,0-.008.01,0,0-.008.01,0,0L41.2,37.7h0l-.009.011,0,0-.008.01,0,0-.009.011v0l-.009.011h0l-.019.023h0l-.009.011h0l-.009.011h0l-.009.011h0l-.029.036h0l-.01.012h0l-.039.048h0l-.1.12h0l-.05.06h0l-.01.012h0l-.04.047h0l-.01.011h0l-.03.035h0l-.01.011h0l-.01.011h0l-.03.035h0l-.009.011h0l-.009.011h0l-.009.011h0l-.01.011h0l-.01.011h0l-.009.011h0l-.009.011v0l-.009.011h0l-.009.01h0l-.009.01,0,0-.009.01,0,0-.008.01,0,0-.009.01v0l-.009.01h0l-.009.01,0,0-.008.01,0,0-.008.009,0,0-.008.01,0,0-.008.009,0,0-.009.01,0,0-.008.009,0,0-.008.009,0,0-.009.01,0,0-.008.009v0l-.009.01,0,0-.008.009,0,0-.008.009,0,0-.008.009,0,0-.007.008,0,0-.008.009,0,0-.008.009,0,0-.008.009,0,0-.008.009,0,0-.007.008,0,0-.008.009,0,0-.008.009,0,0-.008.009,0,0-.008.009,0,0-.007.008,0,0-.008.009,0,0-.008.009,0,0-.008.009,0,0-.008.008,0,0-.007.008,0,0-.008.009,0,0-.008.009,0,0-.008.009,0,0-.009.01h0l-.008.009,0,0-.009.01,0,0-.009.01,0,0-.009.01,0,0-.009.01h0l-.009.01h0l-.009.01v0l-.009.01h0l-.01.01h0l-.02.022h0l-.01.011h0l-.01.011h0l-.15.159-.362-.345C40.037,38.309,40.47,37.813,40.876,37.306Zm-2.694,2.833.331.374-.141.124h0l-.012.01h0l-.035.03h0l-.011.009h0L38.3,40.7h0l-.011.009h0l-.011.009h0l-.01.009,0,0-.01.008,0,0-.01.008,0,0-.01.008,0,0-.009.008,0,0-.01.008,0,0-.009.008,0,0-.009.007,0,0-.007.006,0,0-.007.006,0,0-.007.006,0,0-.008.007,0,0-.007.006-.005,0-.006,0-.007.006-.005,0-.007.006,0,0-.006,0-.007.006-.006,0,0,0-.008.007,0,0-.009.008,0,0-.011.009,0,0-.019.016h0l-.011.01h0l-.012.01h0q-.5.417-1.024.806l-.3-.4C37.184,40.984,37.7,40.569,38.182,40.138Zm-3.139,2.331.262.426-.272.165h0l-.011.007,0,0-.01.006,0,0-.006,0-.008,0-.006,0-.008,0-.006,0-.008,0,0,0-.009.005,0,0-.01.006,0,0-.011.006,0,0-.011.007h0l-.011.006h0l-.012.007h0l-.012.007h0l-.012.007h0l-.026.015h0l-.013.008h0L34.8,43.2h0q-.371.216-.751.418h0l-.014.007h0l-.055.029h0l-.069.036h0q-.173.091-.348.179l-.225-.447C33.915,43.131,34.49,42.81,35.043,42.469Zm-3.492,1.756.185.464c-.2.081-.409.16-.616.235h0l-.014.005h0l-.013,0h0l-.01,0-.013,0-.007,0-.009,0-.006,0-.009,0-.012,0-.01,0,0,0-.011,0h0l-.01,0h0l-.011,0h0l-.012,0h0l-.012,0h0l-.013,0h0l-.014,0h0l-.014,0h0l-.014.005h0q-.508.179-1.027.336L29.7,44.87C30.327,44.683,30.948,44.466,31.551,44.225Zm-3.742,1.126.1.49q-.972.2-1.973.323l-.059-.5C26.524,45.59,27.174,45.484,27.809,45.351ZM21.97,45.8c.648.034,1.309.04,1.957.018l.017.5-.157,0h-.9q-.383,0-.763-.021H22l-.057,0ZM18.1,45.26c.633.144,1.28.263,1.924.352l-.069.5q-.827-.115-1.634-.287l-.008,0-.008,0-.008,0-.008,0h-.006l-.014,0-.008,0h-.015l-.257-.057Zm-3.72-1.2c.6.252,1.216.481,1.834.68l-.153.476-.215-.07h0l-.016-.005h0l-.016-.005-.009,0h0l-.014,0h0l-.011,0-.005,0-.009,0-.013,0-.01,0-.014,0-.014,0-.006,0-.009,0,0,0-.011,0,0,0-.011,0h0l-.01,0,0,0-.011,0h0l-.012,0h0l-.012,0h0l-.012,0h0l-.012,0h0l-.013,0h0l-.013,0h0l-.014,0h0l-.014,0h0l-.014,0h0l-.014,0h0l-.043-.015h0L15.421,45h0q-.627-.222-1.237-.479ZM10.921,42.24c.547.352,1.115.683,1.689.986l-.233.442h0l-.009,0,0,0-.01-.006,0,0-.01-.005,0,0-.01-.005,0,0-.009,0,0,0-.009,0,0,0-.01-.005,0,0-.01-.005,0,0-.01-.005,0,0-.009,0,0,0-.009,0,0,0-.01-.005,0,0-.01-.005,0,0-.01-.005,0,0-.008,0,0,0-.009,0,0,0-.01-.005,0,0-.01-.005,0,0-.009-.005,0,0-.009,0,0,0-.01-.005,0,0-.01-.005,0,0-.01-.005,0,0-.008,0-.006,0-.008,0,0,0-.01-.005,0,0-.01-.005,0,0-.009-.005-.005,0-.008,0-.005,0-.009,0,0,0-.009-.005-.005,0-.009-.005,0,0-.008,0-.006,0-.008,0,0,0-.009-.005,0,0-.01-.005,0,0-.01-.006,0,0-.008,0-.006,0-.008,0,0,0-.01-.005,0,0-.01-.005,0,0-.009-.005,0,0-.008,0,0,0-.009-.005,0,0-.01-.006,0,0-.01-.006,0,0-.009,0-.005,0-.008,0-.006,0-.008,0,0,0-.01-.005,0,0-.01-.006,0,0-.008,0-.005,0-.008,0,0,0-.01-.006,0,0-.01-.006,0,0-.01-.006,0,0-.008,0-.005,0-.008,0,0,0-.01-.006,0,0-.01-.006,0,0-.009-.005,0,0-.008,0-.005,0-.008,0,0,0-.009-.006,0,0-.009-.005,0,0-.008,0-.006,0-.008,0,0,0-.01-.006,0,0-.01-.006,0,0-.01-.006,0,0-.009-.005,0,0L11.4,43.12l0,0-.01-.006,0,0-.01-.006,0,0-.01-.006,0,0-.009-.005,0,0-.009-.006,0,0-.01-.006,0,0-.01-.006,0,0-.009-.006,0,0-.009-.005,0,0-.011-.006,0,0-.01-.006,0,0-.01-.006,0,0-.01-.006,0,0-.01-.006,0,0L11.195,43l0,0-.01-.006,0,0-.011-.006,0,0-.01-.006h0l-.011-.007,0,0-.011-.006,0,0-.01-.006,0,0-.01-.006,0,0-.009-.006,0,0-.01-.006,0,0-.01-.006,0,0-.01-.006,0,0-.011-.007h0l-.01-.006,0,0-.011-.006,0,0-.01-.006,0,0-.011-.007,0,0-.01-.006,0,0-.009-.006,0,0-.01-.006,0,0-.01-.006,0,0-.011-.007h0l-.01-.006h0l-.011-.007,0,0-.01-.006,0,0-.01-.006,0,0-.01-.006,0,0-.009-.006,0,0-.011-.007,0,0-.01-.007,0,0-.011-.007,0,0-.01-.006,0,0-.009-.006,0,0-.01-.006,0,0-.01-.006,0,0-.011-.007,0,0-.01-.006h0L10.7,42.7l0,0-.01-.007,0,0-.01-.007,0,0-.011-.007,0,0-.009-.006h0ZM7.827,39.85c.478.44.982.864,1.5,1.261l-.306.4q-.287-.222-.567-.452h0l-.013-.011h0l-.013-.01h0l-.02-.016h0l-.013-.011h0L8.377,41l0,0-.01-.009,0,0-.012-.01h0l-.006-.005h0l-.011-.009,0,0-.01-.008,0,0-.01-.008,0,0-.019-.016,0,0-.009-.008,0,0L8.258,40.9l0,0-.009-.008,0,0-.01-.008,0,0-.006,0,0,0-.008-.007,0,0L8.2,40.845l0,0-.008-.007,0,0-.006,0-.007-.006-.006,0,0,0-.008-.007,0,0-.009-.007,0,0-.008-.007,0,0-.005,0L8.11,40.77l-.005,0,0,0-.008-.007,0,0-.008-.007,0,0-.008-.007,0,0-.005,0-.007-.006-.006,0,0,0L8.033,40.7l0,0-.007-.006,0,0-.006-.005L8,40.678l0,0-.007-.006-.005,0-.005,0-.007-.006,0,0-.008-.007,0,0-.007-.006-.006,0-.005,0-.007-.006,0,0-.006-.005L7.915,40.6l0,0L7.9,40.591l0,0-.007-.006-.005,0-.005,0-.007-.006-.005,0,0,0-.008-.007,0,0-.007-.006,0,0-.006,0-.007-.006,0,0-.008-.007,0,0L7.8,40.5,7.8,40.5l-.006-.005-.006-.005-.006-.005,0,0-.008-.007,0,0-.008-.007,0,0-.007-.006,0,0-.006-.005-.006,0-.006-.005,0,0L7.71,40.42l0,0L7.7,40.409l0,0L7.687,40.4l0,0-.006-.006,0,0-.007-.006,0,0-.008-.008,0,0-.009-.008,0,0-.009-.008,0,0-.008-.007,0,0-.01-.009,0,0-.009-.009,0,0L7.582,40.3l0,0-.01-.009h0l-.009-.009h0l-.01-.009h0l-.01-.009,0,0-.01-.009h0l-.011-.01h0l-.023-.021h0Zm-2.64-2.883c.4.515.819,1.019,1.258,1.5l-.369.338-.021-.023h0l-.01-.011h0q-.231-.254-.455-.514h0l-.01-.011h0l-.03-.035h0l-.01-.011h0l-.01-.011h0L5.5,38.148h0l-.009-.011h0l-.009-.011h0L5.458,38.1h0l-.009-.011v0l-.009-.01,0,0-.009-.01,0,0-.009-.01v0l-.008-.01,0,0-.008-.01,0,0-.008-.01,0,0L5.377,38l0,0-.009-.01h0l-.008-.01,0,0-.008-.01,0,0-.008-.009,0,0-.008-.009,0,0-.007-.009,0,0-.007-.008,0,0L5.3,37.909l0,0L5.288,37.9l0,0-.007-.009,0,0-.006-.008,0,0-.005-.006,0-.006-.006-.007,0,0-.007-.008,0,0-.007-.008,0,0,0-.006-.005-.007,0-.006,0-.006L5.2,37.789l0,0-.006-.007,0-.005,0,0-.007-.008,0,0-.007-.009,0,0-.007-.009,0,0-.015-.019,0,0-.008-.01,0,0-.008-.01,0,0L5.1,37.67h0l-.016-.02h0l-.01-.013h0l-.01-.012h0c-.093-.116-.184-.232-.274-.35ZM3.079,33.675c.3.575.632,1.144.983,1.692l-.421.27,0,0-.005-.008,0,0-.006-.009,0,0L3.616,35.6l0,0-.005-.008,0,0L3.6,35.572l0,0-.006-.01,0,0-.006-.01,0,0-.007-.011v0l-.007-.011v0l-.007-.012v0l-.007-.011v0l-.007-.012v0l-.016-.025h0l-.008-.012h0l-.008-.012h0l-.033-.052h0q-.346-.557-.661-1.134h0l-.007-.014h0L2.8,34.211h0l-.015-.027h0l-.007-.012v0l-.006-.012v0l-.006-.012v0l-.006-.011,0,0-.005-.01,0,0L2.74,34.1l0,0-.006-.01,0,0-.005-.01,0,0,0-.008,0-.006,0-.007,0-.005,0-.009,0-.006,0-.008,0-.007,0,0L2.685,34l0,0-.006-.012v0l-.012-.022h0l-.008-.015h0l-.019-.036ZM1.571,30.069c.2.619.425,1.236.676,1.835l-.461.193q-.222-.531-.419-1.075h0l-.005-.014h0l-.005-.014h0l-.01-.029h0l0-.014v0l0-.013v0l0-.013h0l0-.013h0l0-.013v0l0-.012v0l0-.013v0l0-.012v0l0-.012v0l0-.011v0l0-.011v0l0-.01,0,0,0-.01,0,0,0-.011,0-.005,0-.01,0,0,0-.009,0-.006,0-.008,0-.005,0-.01,0-.006,0-.01,0-.006,0-.007,0-.008,0-.007,0-.009,0-.006,0-.007,0-.009,0-.007,0-.006,0-.009,0-.006,0-.01,0-.005,0-.011,0-.005,0-.007,0,0,0-.012v0l0-.012v0l0-.015h0l-.008-.025h0l-.005-.016h0q-.041-.122-.08-.245ZM.709,26.257c.088.644.2,1.292.348,1.925l-.488.11q-.219-.969-.356-1.967ZM.015,22.334l.5.018C.5,22.621.5,22.894.5,23.164c0,.381.01.766.028,1.145l-.5.025q-.01-.2-.016-.4h0v-.009h0v-.008h0v-.008h0v-.17h0v-.018h0v-.009h0c0-.043,0-.087,0-.13H0v-.009H0v-.009H0v-.008H0v-.017H0v-.3s0,0,0,0v-.006s0,0,0,0V23.21s0,0,0-.006,0,0,0,0,0,0,0-.006v0s0,0,0-.007,0,0,0,0,0,0,0-.006v0s0-.006,0-.009H0s0-.008,0-.012v0s0-.006,0-.009,0,0,0,0v-.014s0-.006,0-.009v-.258H0v-.036H0v-.009H0v-.028H0c0-.149.006-.3.011-.446ZM.5,18.368l.489.1c-.134.635-.241,1.284-.32,1.93l-.5-.06q.07-.578.169-1.147h0l.006-.037h0v-.03h0l0-.012v-.056h0v-.035h0c.039-.218.082-.435.127-.651Zm1.159-3.823.464.186c-.242.6-.46,1.224-.649,1.845l-.478-.145L1,16.395H1q.288-.942.652-1.849Zm1.8-3.565.425.263c-.342.553-.665,1.126-.958,1.706l-.446-.226q.064-.126.129-.251h0l.007-.013h0l.007-.013h0l.014-.027h0l.007-.013h0l.006-.012v0l.006-.012h0l.014-.026v0l.006-.012v0l.006-.011,0,0,.006-.012v0l.006-.01v0l.006-.011,0,0,.006-.01,0,0,.006-.01,0,0,.005-.01,0,0,0-.009,0,0,.005-.01,0,0,.005-.01,0,0,.005-.01,0,0,0-.007,0-.007,0-.007,0,0,.005-.009,0-.005.005-.009,0,0,0-.008,0-.007,0-.006,0-.008,0-.006,0-.006,0-.008,0-.006,0-.006.005-.009,0,0,.006-.011,0,0,.006-.011,0,0,.006-.011,0,0,0-.007,0,0,.006-.011,0,0,.006-.012,0,0,.008-.014h0l.012-.022h0l.007-.013v0l.008-.014h0l.021-.038h0l.008-.015h0l.108-.19h0l.03-.053h0l.009-.015h0l.03-.053h0l.009-.015h0l.022-.037h0l.008-.013v0l.008-.013v0l.013-.022v0l.007-.013,0,0L3.2,11.4l0,0,.006-.01,0,0,0-.006,0-.008,0-.006,0-.005.005-.008,0-.005,0-.008,0-.006,0-.006.005-.008,0-.005.005-.009,0,0,.005-.008,0,0,.005-.008,0,0,.006-.01,0,0,.006-.01,0,0,.006-.011,0,0,.007-.011h0l.007-.012v0l.007-.012v0l.007-.012h0Zm2.389-3.2.374.332c-.432.486-.848,1-1.236,1.517l-.4-.3q.292-.391.6-.77h0l.01-.013h0l.027-.033v0L5.233,8.5l0,0,.009-.01,0,0,.009-.011,0,0,.005-.006h0l.01-.012,0,0,.008-.01,0,0,.007-.009,0,0L5.3,8.413l0,0,0-.006,0,0,.007-.008,0,0,.007-.009,0,0,.007-.009,0,0,.007-.009,0,0,0-.006,0,0,.007-.009,0,0,.007-.009,0,0L5.393,8.3l0,0L5.4,8.293l0,0,0-.006,0,0,.007-.008,0,0,.008-.009,0,0,.008-.01,0,0,.009-.011,0,0,.005-.006,0,0,.009-.011,0,0L5.484,8.2v0l.01-.012h0l.028-.033h0l.011-.013h0q.154-.181.312-.359Zm2.9-2.747.311.391c-.508.4-1.005.836-1.477,1.283l-.344-.363Q7.421,6.17,7.605,6h0l.011-.01h0l.011-.01h0l.011-.01h0l.011-.009h0l.01-.009,0,0,.009-.008,0,0,.009-.008,0,0L7.7,5.92l0,0,.007-.007,0,0L7.72,5.9l0,0,.008-.007,0,0,.008-.007,0,0,.006-.005.006-.005.006,0,.006,0,.006-.006,0,0,.007-.006.005,0L7.8,5.828l.007-.006,0,0,.008-.007,0,0L7.832,5.8l0,0,.007-.006.005,0,.006,0,0,0,.009-.008,0,0,.01-.009,0,0,.011-.01h0l.019-.017,0,0,.011-.009,0,0,.011-.01h0l.011-.01h0l.032-.028h0L8,5.655H8l.012-.01h0l.019-.017h0l.012-.01h0l.011-.01h0l.012-.011h0l.02-.017h0l.011-.01,0,0,.011-.009,0,0,.012-.01h0l.019-.016,0,0,.01-.009,0,0,.01-.009,0,0,.01-.009,0,0L8.2,5.485l0,0,.01-.008,0,0,.008-.007,0,0,.008-.007,0,0,.007-.006.006,0,.005,0,.007-.006.005,0,.006,0,.007-.006.005,0L8.292,5.4l.006,0,0,0,.008-.007,0,0,.008-.007,0,0,.008-.007,0,0,.007-.006,0,0,.008-.006,0,0,.009-.007,0,0,.009-.008,0,0L8.4,5.317l0,0,.009-.008,0,0L8.42,5.3l0,0,.01-.009,0,0,.011-.009h0l.011-.009h0l.011-.009h0l.012-.01h0l.012-.009h0l.012-.009h0Zm3.328-2.21.24.439c-.57.311-1.133.651-1.675,1.011l-.277-.417.209-.137h0l.013-.008h0L10.6,3.7h0l.025-.016h0l.012-.008h0l.011-.007h0l.011-.007h0l.011-.007h0l.011-.007,0,0,.01-.007,0,0,.01-.006,0,0,.01-.006,0,0,.008,0,.006,0,.007,0,0,0,.009-.005.005,0,.008-.005.006,0,.005,0,.008-.005.005,0,.009-.006,0,0,.006,0,.007,0,.007,0,0,0,.01-.006,0,0,.01-.006,0,0,.011-.007,0,0,.021-.013,0,0,.012-.007,0,0,.012-.008h0l.014-.009h0l.022-.014h0l.014-.008h0L11,3.445h0l.037-.023h0l.015-.009h0l.067-.041h0Q11.593,3.085,12.077,2.821Zm3.659-1.6.16.474c-.615.208-1.229.446-1.824.707l-.2-.458h0l.012-.005h0l.012-.005h0l.012-.005h0l.013-.006h0l.014-.006h0l.013-.006h0l.014-.006h0q.757-.328,1.541-.6h0l.015-.005h0l.043-.015h0l.013,0h0l.013,0h0l.013,0h0l.012,0h0l.012,0h0l.011,0h0l.01,0,.014,0,.005,0,.01,0,.005,0,.01,0,.013,0ZM19.617.27l.076.494c-.643.1-1.288.227-1.919.381L17.656.659Q18.233.518,18.82.407h0l.008,0,.008,0,.008,0h.006l.02,0h.008l.008,0,.008,0,.008,0,.008,0,.012,0h.007l.009,0,.008,0,.008,0h.008l.008,0,.01,0,.009,0,.008,0,.009,0,.008,0,.008,0,.017,0,.009,0,.008,0,.009,0,.009,0h0l.027,0,.009,0h0l.053-.009h0C19.319.317,19.468.293,19.617.27ZM23.164,0V.5c-.508,0-1.022.017-1.526.051L21.6.052l.241-.015h.125l.038,0h0q.352-.017.706-.024h.449Z" transform="translate(0 0)" fill="#95a2fe"/></g></g><g transform="translate(299.827 702.406)"><g transform="translate(0 0)"><path d="M20.268,0h.393c.03,0,.059,0,.09,0h.074q.391.011.778.036h.113l.064,0H21.9q.185.015.37.033l-.049.5C21.574.532,20.918.5,20.268.5V0Zm3.975.39.218.045.007,0,.007,0,.008,0,.007,0,.007,0,.007,0,.008,0,.008,0,.007,0,.008,0,.007,0,.008,0q.8.172,1.571.4h0l.007,0,.007,0,.008,0,.008,0,.007,0,.008,0,.008,0,.006,0-.146.478c-.619-.189-1.255-.348-1.891-.475l.1-.49Zm3.82,1.164q.463.193.914.408h0l.014.007h0l.013.006h0l.013.006h0l.013.006h0L29.061,2l.008,0q.4.2.8.408l-.237.44c-.571-.307-1.163-.59-1.76-.839Zm3.516,1.894.129.088h0l.013.009h0l.012.008h0l.011.008h0l.011.008h0l.011.007,0,0,.01.007,0,0,.01.007,0,0,.01.007,0,0,.01.007,0,0,.01.007,0,0,.01.007,0,0,.01.007,0,0,.009.007,0,0,.009.007,0,0,.009.006,0,0,.009.007,0,0,.009.006,0,0,.009.006,0,0,.009.006,0,0,.009.006,0,0,.009.006,0,0,.009.006,0,0L32,3.74l0,0,.009.006,0,0,.009.006,0,0,.009.006,0,0,.009.006,0,0,.009.006,0,0,.009.007,0,0,.009.007,0,0,.01.007,0,0,.01.007,0,0,.01.007,0,0,.01.007,0,0,.01.007,0,0,.011.008,0,0,.011.008,0,0,.011.008h0l.011.008h0l.011.008h0l.011.008h0l.011.008h0l.011.008h0l.011.008h0l.011.008h0l.011.008h0l.011.008h0l.012.009h0l.012.009h0l.012.009h0q.376.279.739.576h0l.012.01h0l.012.01h0l.012.01h0l.011.009,0,0,.01.008,0,0,.009.008,0,0,.009.007,0,0,.007.006,0,0-.319.385c-.5-.413-1.025-.806-1.562-1.168Zm3.077,2.546q.52.524,1,1.086h0l.009.01v0l.008.009,0,0,.007.009,0,0,.007.008,0,0,.007.008,0,0,.006.008,0,0,.006.007,0,0,.006.007,0,.006.005.006.006.007,0,0,.006.008,0,0,.007.008,0,0,.007.008,0,0,.007.008,0,0,.007.008,0,0,.007.009,0,0,.007.009,0,0,.007.009,0,0,.008.009,0,0,.008.01,0,0,.008.01v0l.009.01v0l.009.011h0l.01.012h0l.009.011h0l.009.011h0l.009.011h0l.009.011h0l.009.011h0l.009.011h0l.009.011h0l.009.011h0l.009.011h0l.009.011h0l-.388.316c-.409-.5-.848-.99-1.3-1.45l.355-.352Zm2.522,3.1.041.062h0l0,.006h0l0,.005h0l0,.005h0l0,0v0l0,0v0l0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0v0l0,0v0l0,.005h0l0,.006h0q.451.693.845,1.424h0l0,.007h0l0,.006h0l0,.005v0l0,0v0l0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0v0l0,0v0l0,.005v0l0,.005v0l0,.006h0l.032.059-.442.234c-.3-.572-.637-1.136-1-1.677Zm1.866,3.531c.06.146.117.292.173.44h0l0,.006h0l0,.007h0l0,.007h0l0,.007h0l0,.007h0l0,.006h0l0,.006v0l0,.005v0l0,0v0l0,0v0l0,0v0l0,0v.021l0,0v0l0,0v0l0,0v0l0,0v0l0,0v0l0,0v0l0,0v0l0,0v0l0,0v0l0,0v0l0,.006v0l0,.006v0l0,.005v0l0,.005h0l0,.006h0l0,.006h0l0,.006h0l0,.006h0l0,.007h0l0,.006v0l0,.006v0l0,.006h0l0,.006h0l0,.006h0l0,.006h0l0,.007h0l0,.007h0l0,.007h0l0,.006v0l0,.006h0l0,.007h0l0,.006h0l0,.006h0l0,.007h0l0,.007h0l0,.007h0l0,.006h0l0,.006h0l0,.007h0l0,.007h0l0,.006h0l0,.007h0l0,.007h0l0,.007h0l0,.007h0l0,.007h0l0,.007h0l0,.007h0l0,.007h0l.005.014h0l0,.007h0l0,.006v0l0,.006h0l0,.006h0l0,.006h0l0,.006h0l0,.006h0l0,.006h0l0,.006v0l0,.005v0l0,.006v0l0,.005v0l0,.005v0l0,.005v0l0,.006h0l0,.006h0l0,.005v0l0,0v0l0,0v0l0,0v0l0,0v.035l0,0v0l0,0v.01l0,0v0l0,.005h0l0,.006v0q.124.365.234.737l-.479.142c-.184-.621-.4-1.24-.645-1.839Zm1.133,3.829v.038h0V16.5h0V16.5h0v.007h0q.057.3.1.612h0v.007h0v.007h0l0,.015h0l0,.015h0l0,.015h0l0,.015h0l0,.015h0l0,.015h0l0,.015h0l.018.123h0v.007h0v.007h0v.3l0,.022,0,.013v.289h0v.014h0v.007h0q.022.2.04.4l-.5.045c-.058-.646-.149-1.3-.27-1.931Zm-.141,3.974.5,0c0,.034,0,.069,0,.1v.07h0v.007h0v.007h0v.007h0v.008h0q-.013.764-.083,1.513h0v.015h0v.115l-.006.064h0l-.009.082-.5-.053C39.994,21.728,40.031,21.073,40.036,20.424Zm-.41,3.874.49.1q-.05.242-.106.483h0V24.9h0l0,.007h0v.125l0,.006h0l0,.006h0l0,.006h0l0,.006h0l0,.007h0l0,.007h0l0,.006h0l0,.007h0q-.153.626-.344,1.236l-.477-.149C39.33,25.567,39.495,24.932,39.626,24.3Zm-1.164,3.716.46.2q-.2.461-.415.911h0l0,.006h0l0,.006h0l0,.006h0l0,.006h0l0,.006h0l0,.006h0l0,.006h0l0,.006v0l0,.005v0l0,.005v0l0,.005v0l0,0v0l0,0v0l0,0v0l0,.005v0l0,0,0,0,0,0v0l0,0v0l0,0v0l0,0,0,0,0,0v0l0,0v0l0,0v0l0,0,0,0v0l0,0v0l0,0v0l0,0h0l-.09.178h0l0,.007h0q-.132.258-.271.512l-.438-.241C37.922,29.2,38.208,28.609,38.462,28.014Zm-1.874,3.414.412.283q-.566.826-1.21,1.592l-.383-.322C35.825,32.484,36.222,31.962,36.588,31.428ZM34.082,34.41l.349.358q-.35.342-.716.666h0l-.023.02h0l-.011.01h0l-.011.009h0l-.01.009h0l-.01.009,0,0-.01.008,0,0-.009.008,0,0-.008.007,0,0-.009.008,0,0-.008.007-.006.005-.005,0-.006.006-.005,0-.006.006-.005,0-.007.006,0,0-.007.006,0,0-.007.006,0,0-.007.006-.005,0-.007.006-.005,0-.007.006,0,0-.007.006,0,0-.007.006,0,0-.008.007,0,0-.009.007,0,0-.009.008,0,0-.009.008,0,0-.009.008,0,0-.009.008,0,0-.01.008,0,0-.01.008,0,0-.01.008,0,0-.01.009h0l-.01.009h0l-.01.009h0l-.011.009h0L33.3,35.8h0l-.012.01h0q-.174.146-.35.287l-.313-.39C33.127,35.3,33.618,34.863,34.082,34.41Zm-3.04,2.436.273.419-.183.118h0l-.013.008h0l-.006,0h0l-.006,0h0l-.006,0h0l-.019.012h0l-.006,0h0l-.006,0h0l-.006,0h0l-.005,0-.006,0h0l0,0h0l0,0-.005,0-.005,0,0,0-.008.005-.022.014h0l0,0h0l-.006,0h0l-.006,0h0l-.006,0h0l-.025.016h0l-.006,0h0l-.006,0h0l-.013.008h0l-.026.016h0q-.632.39-1.295.733l-.23-.444C29.932,37.53,30.5,37.2,31.042,36.846Zm-3.457,1.793.185.464q-.57.227-1.157.421h0l-.008,0-.008,0-.007,0-.008,0-.008,0-.008,0-.007,0-.008,0-.008,0-.007,0-.008,0-.008,0-.008,0-.008,0-.008,0-.008,0-.008,0-.007,0-.008,0-.007,0h0c-.193.062-.386.12-.581.177l-.138-.481C26.362,39.091,26.983,38.879,27.584,38.639Zm-3.743,1.076.09.492q-.77.141-1.559.222l-.016,0h-.008l-.016,0H22.3q-.176.018-.353.032l-.041-.5C22.555,39.917,23.206,39.831,23.842,39.715Zm-5.83.195c.642.073,1.3.115,1.946.125l-.008.5h-.126l-.189,0H19.6l-.141-.005h-.11l-.125-.006h-.11l-.1-.006H18.86l-.061,0h-.165l-.06,0H18.4l-.077-.007h-.078l-.015,0-.277-.03ZM14.2,39.09c.616.2,1.25.368,1.883.5l-.105.489q-.7-.152-1.391-.352l-.007,0-.007,0-.007,0-.007,0-.007,0-.007,0-.007,0-.008,0-.007,0-.007,0-.007,0-.007,0-.007,0-.007,0-.007,0h0l-.015,0h0q-.209-.062-.416-.129Zm-3.57-1.555c.566.316,1.153.608,1.747.866l-.2.458q-.524-.228-1.031-.484h0l-.02-.01h0l-.006,0h0l-.006,0h0l-.007,0h0l-.007,0h0l-.006,0h0l-.006,0h0l-.006,0h0l-.013-.007h0l-.006,0h0l-.006,0h0l-.006,0h0l-.006,0-.007,0-.007,0-.007,0h0l-.006,0-.007,0L11,38.3l-.007,0-.007,0-.007,0-.007,0h0l-.012-.006-.007,0h0l-.007,0-.007,0-.013-.007h0l-.014-.007h0l-.02-.01h0l-.007,0h0l-.013-.007h0l-.006,0h0l-.007,0h0l-.013-.007h0l-.007,0h0l-.007,0h0l-.013-.007h0q-.214-.113-.424-.231Zm-3.2-2.227c.493.421,1.012.822,1.543,1.192l-.286.41q-.27-.188-.533-.384h0l-.012-.009h0l-.012-.009h0L8.126,36.5h0l-.011-.008h0L8.1,36.48h0l-.01-.008,0,0-.01-.008,0,0-.01-.007,0,0-.009-.007,0,0-.008-.006,0,0-.008-.006,0,0-.007-.006-.005,0L8,36.406,8,36.4l-.006,0-.006,0-.006,0-.007,0-.006,0-.007-.005-.006,0-.007-.005-.005,0-.008-.006,0,0q-.292-.224-.575-.458h0l-.011-.009h0l-.011-.009h0l-.01-.009h0l-.01-.008,0,0-.01-.008,0,0-.009-.007,0,0-.007-.006-.006-.005-.005,0-.007-.006,0,0L7.246,35.8l0,0-.008-.007,0,0-.009-.007,0,0L7.21,35.77l0,0L7.2,35.76l0,0-.01-.009h0l-.011-.009h0l-.059-.05ZM4.738,32.5c.4.508.832,1,1.281,1.47l-.36.347,0,0-.008-.008,0,0L5.638,34.3l0,0-.008-.008,0,0-.008-.008,0,0-.008-.008,0,0L5.6,34.251l0,0-.008-.009,0,0-.008-.009,0,0-.008-.009,0,0-.008-.009,0,0-.009-.009,0,0-.009-.009,0,0-.009-.009,0,0-.009-.01,0,0L5.5,34.148l0,0-.009-.009,0,0-.009-.009,0,0-.009-.01,0,0-.009-.01h0l-.009-.01,0,0-.009-.01h0l-.01-.01h0l-.009-.01h0l-.009-.01h0l-.01-.01h0l-.01-.011h0l-.01-.011h0L5.359,34h0q-.529-.574-1.014-1.189Zm-2.1-3.281c.293.577.619,1.146.969,1.692l-.421.27q-.084-.131-.166-.263h0L3,30.891H3l-.016-.026h0l-.065-.106h0l-.008-.013h0l-.008-.013h0L2.9,30.718h0l0-.007h0l0-.006h0l-.008-.013h0l-.008-.013h0l-.016-.027h0q-.352-.59-.665-1.2ZM1.225,25.591c.174.624.381,1.246.616,1.85l-.466.181q-.2-.508-.369-1.03h0l0-.007H1l0-.007H1l0-.007H1l0-.007H1l0-.007h0l0-.007h0l0-.006h0l0-.006v0l0-.006h0l0-.006h0l0-.006h0l0-.006v0l0-.006v0l0-.006v0l0-.005v0l0-.005v0l0-.005v0l0-.006v0l0-.006h0l0-.006v0l0-.006v0l0-.005v-.074l0,0v-.032l0,0v0l0-.005v0l0-.006h0l0-.007h0l0-.006h0l0-.007h0l0-.006v0l0-.006v0l0-.007h0l0-.014h0l0-.006h0l0-.007h0l0-.014h0l0-.006h0l0-.007h0q-.071-.232-.136-.467Zm-.67-3.837c.048.647.129,1.3.24,1.935L.3,23.775Q.168,23,.092,22.213h0V22.2h0V22.2h0v-.007h0v-.015h0v-.137h0v-.006h0q-.01-.117-.019-.235ZM.149,17.8l.5.06C.567,18.5.52,19.154.505,19.8l-.5-.012q.023-1.01.144-1.994Zm.871-3.9.475.157c-.2.615-.378,1.247-.519,1.879l-.488-.109Q.707,14.846,1.02,13.9Zm1.623-3.649.434.247c-.321.563-.617,1.149-.88,1.74l-.457-.2v0l0,0v0l0,0v0l0,0v0l0-.006h0l0-.006h0q.232-.519.492-1.022h0l0-.006v0l0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0v0l0,0v0l0-.005h0l0-.006h0l.007-.013h0q.172-.33.356-.653ZM4.952,6.993l.378.328c-.425.49-.83,1.006-1.2,1.534l-.408-.289Q4.3,7.749,4.952,6.993Zm2.9-2.746.307.395c-.512.4-1.01.824-1.48,1.27l-.344-.363q.34-.322.7-.629h0l.012-.01h0l.012-.01h0l.011-.01h0l.011-.01h0l.011-.01h0L7.1,4.86h0l.024-.02h0L7.2,4.779h0l.012-.01h0l.011-.01h0l.011-.009h0l.011-.009h0l.011-.009h0l.011-.009h0l.011-.009h0L7.291,4.7h0L7.3,4.689h0l.011-.009h0l.012-.01h0l.012-.01h0l.1-.079h0l.012-.01h0l.012-.01h0l.012-.01h0l.012-.01h0l.012-.01h0l.012-.01h0Q7.679,4.382,7.852,4.247Zm3.38-2.127.223.447c-.579.289-1.151.61-1.7.956L9.49,3.1l0,0,.007,0,.006,0,.006,0,0,0h0l0,0h0l0,0h0l.005,0h0l.012-.008h0q.415-.259.842-.5h0l.027-.015h0l.006,0h0l.006,0h0l.006,0h0l.006,0h0l.006,0h0l.007,0h0l.007,0h0l.006,0h0l.006,0h0l.006,0h0l.006,0h0l.006,0,.007,0h0l.006,0h0l.006,0h0l.006,0h0l.188-.1h0c.171-.092.343-.18.517-.267ZM14.965.7l.131.483c-.625.169-1.249.371-1.854.6l-.178-.467q.371-.141.749-.268l.007,0,.007,0,.007,0,.007,0,.007,0,.007,0,.007,0,.007,0,.007,0,.007,0,.007,0,.007,0,.007,0,.007,0,.007,0,.007,0h0l.007,0,.007,0Q14.449.841,14.965.7ZM18.9.045l.033.5c-.648.043-1.3.119-1.937.225L16.918.276c.155-.026.312-.05.468-.072h.085Q18.18.094,18.9.045Z" transform="translate(0 0)" fill="#e5c773"/></g></g><g transform="translate(299.932 703.51)"><rect width="39.596" height="39.596" fill="none"/></g></g><g transform="translate(59 619)"><rect width="23" height="23" fill="none"/><path d="M17,11.455v3.818a1.9,1.9,0,0,1-1.889,1.909v.955A2.849,2.849,0,0,1,12.278,21H9.444a.95.95,0,0,1-.944-.955v-.477a.475.475,0,0,1,.472-.477h3.306a.95.95,0,0,0,.944-.955v-.955a.95.95,0,0,1-.944-.955V10.5a.95.95,0,0,1,.944-.955V6.682a4.722,4.722,0,1,0-9.444,0V9.545a.95.95,0,0,1,.944.955v5.727a.95.95,0,0,1-.944.955H1.889A1.9,1.9,0,0,1,0,15.273V11.455A1.9,1.9,0,0,1,1.889,9.545V6.682a6.611,6.611,0,1,1,13.222,0V9.545a1.879,1.879,0,0,1,1.336.559A1.92,1.92,0,0,1,17,11.455Z" transform="translate(3 1)" fill="#fff" opacity="0.8"/></g></g></svg>';

  static String freeDeliveryItem =
      '<svg xmlns="http://www.w3.org/2000/svg" width="71.502" height="62.579" viewBox="0 0 71.502 62.579"><g transform="translate(-34.5 -904)"><g transform="translate(-117.5 213)"><g transform="translate(-323.21 -1507.823)"><g transform="translate(490.744 2201.516)"><rect width="54.906" height="54.906" fill="none"/><g transform="translate(6.488 6.863)"><path d="M366.683,90.772a.642.642,0,0,0-1,.8l.644.806h-4.082a.642.642,0,1,0,0,1.284h4.082l-.644.806a.642.642,0,0,0,1,.8l1.477-1.848a.642.642,0,0,0,0-.8Z" transform="translate(-333.292 -83.309)" fill="#95a2fe"/><path d="M73.731,368.018a.642.642,0,0,0,1-.8l-.644-.806h4.082a.642.642,0,1,0,0-1.284H74.09l.644-.806a.642.642,0,1,0-1-.8l-1.477,1.848a.642.642,0,0,0,0,.8Z" transform="translate(-65.943 -334.301)" fill="#95a2fe"/><path d="M5.089,405.478H18.458a.642.642,0,0,0,0-1.284H5.307V402.62a.642.642,0,0,0-1.1-.451L.186,406.235a.642.642,0,0,0,0,.9L4.209,411.2a.642.642,0,0,0,1.1-.451v-1.574H18.458a.642.642,0,1,0,0-1.284H5.089a1.067,1.067,0,0,0-1.066,1.066v.23l-2.478-2.5,2.478-2.5v.23A1.067,1.067,0,0,0,5.089,405.478Z" transform="translate(-0.001 -370.215)" fill="#fff" opacity="0.7"/><path d="M307.744,4.256,303.722.19a.642.642,0,0,0-1.1.451V2.215h-2.96a.642.642,0,1,0,0,1.284h3.178a1.067,1.067,0,0,0,1.066-1.066V2.2l2.478,2.5-2.478,2.5v-.23a1.067,1.067,0,0,0-1.066-1.066H289.473a.642.642,0,1,0,0,1.284h13.151V8.774a.642.642,0,0,0,1.1.451l4.023-4.066a.642.642,0,0,0,0-.9Z" transform="translate(-266.751 0)" fill="#fff"/><path d="M289.473,27.174H297.1a.642.642,0,0,0,0-1.284h-7.624a.642.642,0,1,0,0,1.284Z" transform="translate(-265.981 -23.674)" fill="#fff"/></g></g><path d="M366.683,90.772a.642.642,0,0,0-1,.8l.644.806h-4.082a.642.642,0,1,0,0,1.284h4.082l-.644.806a.642.642,0,0,0,1,.8l1.477-1.848a.642.642,0,0,0,0-.8Z" transform="translate(113.61 2115.156)" fill="#f2f6f9"/><path d="M369.8,90.919a1.035,1.035,0,1,0-1.617,1.292l1.038,1.3h-6.582a1.035,1.035,0,1,0,0,2.07h6.582l-1.038,1.3a1.035,1.035,0,1,0,1.617,1.292l2.381-2.98a1.035,1.035,0,0,0,0-1.292Z" transform="translate(113.61 2108.293)" fill="#95a2fe"/><path d="M73.731,368.018a.642.642,0,0,0,1-.8l-.644-.806h4.082a.642.642,0,1,0,0-1.284H74.09l.644-.806a.642.642,0,1,0-1-.8l-1.477,1.848a.642.642,0,0,0,0,.8Z" transform="translate(467.898 1893.143)" fill="#95a2fe"/></g><g transform="translate(-6.5)"><g transform="translate(185.836 702.844)"><g transform="translate(0)"><path d="M24.045,0h.566q.721.017,1.432.075L26,.58C25.356.527,24.7.5,24.045.5V0Zm3.982.328q.244.041.486.086h0l.017,0,.01,0h0l.016,0,.017,0,.012,0,.016,0,.015,0,.014,0,.016,0,.014,0,.015,0,.016,0,.014,0,.016,0,.015,0,.014,0,.016,0,.014,0h0l.03.006h0q.59.119,1.17.266l-.123.485c-.63-.16-1.274-.3-1.916-.4l.082-.493Zm3.872.984q.95.328,1.861.731l-.2.457c-.595-.263-1.208-.5-1.822-.716l.163-.473Zm3.654,1.616q.556.3,1.095.636h0l.012.008h0l.012.008h0l.012.007h0L36.7,3.6l0,0,.011.007,0,0,.009.005,0,0,.01.006,0,0,.009.006,0,0,.008,0,.006,0,.006,0,.006,0,.008,0,.006,0,0,0,.01.007,0,0,.012.008h0l.022.014h0l.015.009h0q.2.127.4.258l-.275.417c-.543-.358-1.108-.7-1.678-1.009Zm3.335,2.2.012.01h0l.049.039h0l.037.029h0l.049.039h0l.012.01h0l.036.029h0l.012.009h0l.024.019h0l.011.009h0l.011.009h0l.012.01h0l.011.009h0l.011.009h0l.011.009h0l.011.009h0l.011.009,0,0,.01.008,0,0,.011.009h0l.01.008,0,0,.01.008,0,0,.01.008,0,0,.009.007,0,0,.009.007,0,0,.009.008,0,0,.009.008,0,0,.008.007,0,0,.009.008,0,0,.009.007,0,0,.007.006.005,0,.006.005.005,0,.007.006,0,0,.008.007,0,0,.005,0,.008.006,0,0,.007.006.006.005.005,0,.006,0,.008.006,0,0,.01.008,0,0,.01.008,0,0,.006.005,0,0,.01.008,0,0,.01.009,0,0,.02.016h0l.011.01,0,0,.013.011h0l.02.017h0l.013.011h0l.178.153h0l.082.071h0l.034.03h0l.012.011h0l.033.029h0l.011.01,0,0,.012.01h0l.006.005,0,0L40,6.052l0,0,.008.007,0,0,.005,0,.007.006.005,0,.005,0,.007.006.005,0,.005,0,.007.007,0,0,.008.008,0,0,.008.007,0,0,.008.007,0,0,.01.009,0,0,.01.009h0l.022.02h0l.011.01h0q.127.115.253.232l-.34.366c-.476-.443-.977-.871-1.488-1.272Zm2.927,2.72,0,0,.008.009,0,0,.007.008,0,0,.009.009,0,0,.008.009,0,0,.008.009,0,0,.007.008,0,0,.008.009,0,0,.008.009,0,0,.007.008,0,0,.007.008,0,0,.008.009,0,0,.008.009,0,0L41.951,8l0,0,.008.009,0,0,.008.009,0,0,.008.009,0,0,.007.008,0,0L42,8.054l0,0,.008.009,0,0,.008.009,0,0,.007.008,0,0,.008.009,0,0,.008.009,0,0,.006.007,0,0,.006.007,0,0,.008.009,0,0,.008.009,0,0,.006.007,0,0,.006.007,0,0,.007.008,0,0,.007.007,0,0,.005.006.005.006.006.007,0,0,.007.008,0,0,.005.006.005.006,0,.006,0,.005.006.007,0,0,.007.008,0,0,0,.006.005.006,0,.006,0,0,.007.008,0,0,0,.006.006.007,0,0,.007.008,0,0,0,.006.006.007.005.006,0,0,.008.009,0,0,.009.011,0,0,.017.019,0,0,.01.011v0l.028.033v0l.01.012h0l.029.034h0q.356.421.692.857l-.4.305c-.4-.515-.82-1.02-1.259-1.5Zm2.439,3.165q.258.4.5.81h0l.008.013h0l.015.026v0l.007.012v0l.007.012h0l.007.012v0l.007.011,0,0,.007.011v0l.006.011v0l.006.011,0,0,.006.01,0,0,.006.01,0,0,0,.008,0,0,.005.009,0,0,.005.009,0,.005,0,.007,0,.007,0,.007,0,.006,0,.008,0,.006,0,.006,0,.009,0,0,.006.01,0,0,0,.007h0l.008.013,0,0,.007.012,0,0,.008.014h0l.013.023h0l.009.015h0c.1.178.2.359.3.54l-.441.236c-.306-.573-.64-1.141-.993-1.687Zm1.882,3.524v0l0,.009,0,0,0,.01,0,0,0,.011,0,0,0,.01,0,0,0,.01v0l0,.011,0,0,0,.012v0l0,.01v0l.005.012v0l.005.012v0l.005.012h0l.005.012v0l.005.012v0l.005.012v0l0,.012v0l0,.012,0,0,0,.012v0l0,.011v0l.005.012v0l0,.012v0l0,.011v0l0,.012v0l0,.012v0l0,.011v0l0,.01v0l0,.011,0,0,0,.011v0l0,.01v0l0,.011,0,0,0,.011,0,0,0,.009,0,.006,0,.008,0,.005,0,.01,0,0,0,.01,0,.005,0,.008,0,0,0,.01,0,.005,0,.01,0,.005,0,.007,0,.007,0,.008,0,.006,0,.01,0,.006,0,.006,0,.01,0,.005,0,.008,0,.008,0,.007,0,.006,0,.01,0,0,0,.011,0,.005,0,.007,0,0,0,.011,0,0,0,.012v0l.009.024v0l.005.014v0l.01.025h0l.006.017h0l.016.042h0q.172.445.327.9h0l.014.042v0l0,.014v0l0,.008v0l0,.012,0,0,0,.011,0,.005,0,.007,0,.01,0,.006,0,.008v0l-.474.159c-.206-.616-.442-1.231-.7-1.829Zm1.269,3.789q.236.966.392,1.96l-.494.078c-.1-.643-.23-1.289-.384-1.92Zm.62,3.947c.028.382.047.765.057,1.152v.087h0V24s0,.008,0,.012,0,0,0,0,0,.007,0,.01v0s0,.008,0,.012h0s0,.008,0,.013v.015h0s0,.009,0,.014h0q0,.091,0,.183l-.5,0q0-.11,0-.221c0-.578-.021-1.163-.063-1.737l.5-.036Zm-.535,3.95.5.046q-.087.949-.247,1.876h0v.06l-.008.045-.492-.087C47.345,27.523,47.432,26.871,47.491,26.222Zm-.68,3.853.483.128-.018.067v0l0,.016h0l-.007.026v0l0,.014v0l-.007.025v0l0,.013v0l0,.014v0l0,.008v0l0,.011v.005l0,.011,0,.006,0,.007,0,.007,0,.009,0,.007,0,.008,0,.008,0,.006,0,.01,0,.006,0,.008,0,.006,0,.009v.005l0,.011v0l0,.01,0,.005,0,.01v0l0,.012v0l0,.012v0l0,.011v0l0,.012v0l0,.012v0l0,.011v0l0,.012v0l0,.012v0l0,.012v0l0,.013v0l0,.013v0l-.008.027v0l0,.013v0l0,.014h0l0,.014h0l0,.014v0l0,.014h0l0,.014h0l0,.014h0l0,.014h0l-.009.029v0l0,.014h0l-.009.029h0l0,.014h0l-.009.029h0l0,.014h0l-.013.044h0l-.014.044h0l0,.015h0l-.009.029h0l0,.015h0L47,31.225h0l-.014.044h0l0,.015h0l-.009.029h0l0,.014h0l-.009.029h0l0,.014h0l-.009.028v0l0,.013v0l0,.014h0l0,.014v0l0,.013v0l0,.013h0l0,.013v0l0,.013v0l0,.012v0l0,.011v0l0,.013v0l0,.012v0l0,.01v0l0,.011v0l0,.011v0l0,.01,0,.005,0,.009,0,.005,0,.01,0,.005,0,.009,0,.007,0,.007,0,.006,0,.01,0,.006,0,.007,0,.009,0,.006,0,.01,0,.006,0,.007,0,.007,0,.01,0,0,0,.012v0l0,.008h0l-.006.017v0l0,.014v0q-.049.143-.1.284l-.471-.168C46.451,31.332,46.646,30.7,46.812,30.075ZM45.5,33.76l.455.207q-.414.914-.9,1.785l-.437-.244C44.934,34.939,45.231,34.352,45.5,33.76Zm-1.907,3.416.415.279q-.294.437-.607.861v0l-.009.013h0l-.01.014h0l-.005.007h0l-.01.014v0l-.009.012,0,0-.015.02,0,0-.008.01,0,0-.007.009,0,0,0,.006-.006.008,0,.006,0,0-.006.008,0,0-.006.008,0,.006,0,.005-.006.008,0,.005,0,.006-.005.007,0,.006,0,0-.006.009,0,0-.006.008,0,0,0,.006,0,.006-.005.007,0,0-.007.009,0,0-.006.008,0,0-.006.008,0,0-.007.009,0,0-.007.009,0,0-.007.009,0,0-.007.009,0,0-.007.01,0,0-.007.01,0,0-.007.009,0,0-.008.01,0,0-.008.01,0,0-.008.01h0l-.008.01v0l-.008.011,0,0-.008.01,0,0-.017.022v0l-.009.011v0l-.009.011h0L43,38.845v0l-.009.011v0l-.009.011h0l-.009.011h0l-.009.012h0l-.009.011h0l-.019.024h0l-.009.012h0l-.009.012h0l-.019.024h0l-.009.012h0l-.029.036h0l-.01.012h0l-.019.024-.39-.313C42.839,38.246,43.23,37.715,43.592,37.176ZM41.147,40.23l.363.344q-.11.116-.221.231h0l-.012.012h0l-.018.019v0l-.01.01,0,0-.009.009,0,0-.005.005-.007.007,0,0,0,0-.006.006L41.2,40.9l0,0-.007.007,0,0-.008.008,0,0-.007.007,0,0-.007.007,0,0-.009.009,0,0-.009.009,0,0-.009.009h0l-.01.01h0L41.1,41h0q-.5.5-1.019.967l-.333-.373C40.227,41.161,40.7,40.7,41.147,40.23Zm-2.916,2.61.3.4q-.585.442-1.2.849h0l-.013.008h0l-.025.017h0l-.012.008h0l-.012.008h0l-.012.008h0l-.012.008h0l-.011.008,0,0-.011.007h0l-.01.007,0,0-.011.007,0,0-.01.007,0,0-.009.006,0,0-.009.006,0,0-.01.006,0,0-.009.006,0,0-.006,0-.007,0-.006,0-.006,0-.008.005-.006,0,0,0-.01.006,0,0-.012.008h0l-.022.014h0l-.014.009h0l-.124.079-.267-.422C37.171,43.607,37.712,43.232,38.231,42.839Zm-3.306,2.092.231.443h0l-.008,0-.006,0-.005,0-.009,0-.006,0-.007,0-.008,0-.006,0-.009,0,0,0-.007,0-.006,0-.008,0,0,0-.01.005,0,0-.009,0,0,0-.009,0,0,0-.011.006,0,0-.012.006h0l-.011.006h0l-.012.006h0l-.012.006h0l-.012.006h0l-.012.006h0l-.013.006h0l-.013.007h0l-.028.014h0q-.745.376-1.518.7l-.194-.461C33.752,45.513,34.349,45.233,34.925,44.932Zm-3.607,1.514.154.476q-.443.144-.894.271h0l-.014,0h0l-.028.008h0l-.013,0h0l-.013,0h0l-.012,0h0l-.012,0h0l-.011,0-.014,0h0l-.011,0-.016,0-.012,0-.016,0-.015,0-.014,0-.01,0-.013,0h0l-.013,0h0l-.015,0h0l-.026.007h0c-.24.064-.48.124-.723.181l-.114-.487C30.066,46.823,30.7,46.647,31.318,46.446Zm-3.809.892.073.495c-.195.029-.391.055-.587.079h-.069l-.014,0H26.9q-.646.076-1.3.118l-.032-.5C26.212,47.5,26.867,47.432,27.509,47.338Zm-5.86.133c.645.065,1.3.1,1.955.116l-.009.5q-.249,0-.5-.014h0l-.039,0h-.642l-.029,0h-.01q-.391-.027-.778-.066ZM17.8,46.754c.626.172,1.268.319,1.908.438l-.091.492q-.4-.075-.8-.163h0l-.014,0h0l-.014,0h0l-.013,0-.016,0-.014,0-.015,0-.016,0-.013,0-.016,0-.014,0-.014,0-.016,0-.012,0-.016,0-.014,0h0l-.026-.006h0l-.017,0h0q-.444-.1-.881-.224ZM14.13,45.408c.59.274,1.2.527,1.808.75l-.172.469q-.942-.346-1.847-.766Zm-3.4-1.939c.536.368,1.094.718,1.659,1.04l-.248.434,0,0-.006,0-.009-.005,0,0-.011-.007,0,0-.022-.013h0l-.014-.008h0c-.2-.115-.4-.232-.591-.352h0l-.023-.014h0l-.013-.008,0,0-.012-.007,0,0-.013-.008h0l-.007,0,0,0-.011-.007,0,0-.01-.006,0,0-.006,0-.008-.005-.006,0-.006,0-.007,0-.007,0,0,0-.009-.006,0,0-.01-.006,0,0-.008,0,0,0-.009-.006,0,0-.01-.006,0,0-.011-.007h0l-.01-.007h0l-.011-.007h0l-.012-.007h0l-.025-.016h0l-.013-.008h0q-.362-.23-.716-.473ZM7.7,40.994c.468.451.961.889,1.464,1.3l-.316.387q-.327-.267-.644-.545h0l-.023-.02h0l-.011-.01h0l-.011-.01h0l-.022-.019h0l-.01-.009,0,0-.01-.009,0,0L8.1,42.044h0l-.01-.009,0,0-.009-.008,0,0-.009-.008,0,0L8.055,42l0,0-.009-.008,0,0-.009-.008,0,0-.008-.007,0,0-.007-.006,0,0L8,41.952l0,0-.008-.008,0,0-.007-.006,0,0-.007-.006,0,0L7.95,41.91l0,0L7.938,41.9l0,0-.006-.006-.006-.005-.006-.006,0,0L7.9,41.868l0,0-.006-.005-.006-.005-.006-.005,0,0-.007-.006,0,0-.008-.007,0,0-.005,0-.007-.006,0,0L7.83,41.8l-.007-.006,0,0-.006-.005-.007-.006,0,0-.007-.007,0,0-.005,0-.007-.007,0,0-.005,0-.007-.007,0,0-.008-.007,0,0-.005,0-.007-.006L7.726,41.7l0,0-.008-.008,0,0L7.7,41.683l0,0-.005,0-.007-.006-.005,0,0,0-.007-.007,0,0,0,0-.007-.007,0,0-.006-.006-.006-.006,0,0-.008-.008,0,0,0,0-.007-.007,0,0-.006-.005-.006-.006-.005,0-.006-.006-.006-.005,0,0L7.56,41.55l0,0-.006-.006-.006-.006-.005,0-.006-.006-.006-.006,0,0-.008-.007,0,0L7.5,41.5l0,0-.006-.006,0,0-.008-.008,0,0-.008-.008,0,0-.007-.007,0,0-.008-.008,0,0-.008-.008,0,0-.009-.008,0,0-.008-.007,0,0L7.4,41.4l0,0-.009-.009,0,0-.009-.009,0,0-.008-.008,0,0-.009-.009h0Zm-2.582-2.94c.387.522.8,1.035,1.23,1.523l-.376.33q-.659-.75-1.256-1.555ZM3.058,34.729c.3.579.619,1.153.961,1.705l-.425.263,0,0,0-.008,0,0-.006-.01,0,0-.007-.011,0,0-.007-.012h0l-.008-.012h0l-.008-.012h0q-.171-.278-.335-.562h0L3.2,36.042h0L3.179,36v0l-.007-.012v0l-.007-.012h0l-.006-.011v0l-.006-.011,0,0-.006-.011,0,0-.006-.01,0,0-.006-.01,0,0-.006-.01,0,0-.005-.009,0-.005,0-.007,0-.007,0-.007,0-.006,0-.007,0-.008,0,0-.006-.01,0,0L3.066,35.8v0l0-.007,0,0-.007-.013v0l-.007-.013v0c-.148-.266-.291-.533-.43-.8Zm-1.48-3.621c.195.62.418,1.239.664,1.841l-.463.189h0l-.005-.012v0l0-.011v0l0-.012v0l0-.012v0l0-.011v0l0-.011v0l0-.011,0,0,0-.01,0,0,0-.009,0,0,0-.011,0,0,0-.011,0,0,0-.008,0-.006,0-.009,0-.005,0-.01,0,0,0-.008,0-.006,0-.008,0-.005,0-.01,0-.005,0-.008,0-.006,0-.008,0,0,0-.01,0-.006,0-.008,0-.007,0-.007,0-.008,0-.007,0-.006,0-.009,0-.007,0-.005,0-.01,0-.005,0-.007,0-.009,0-.007,0-.005,0-.01,0-.005,0-.009,0-.006,0-.007,0,0,0-.012,0,0,0-.012v0l0-.007,0,0,0-.011,0,0,0-.012v0L1.566,32.6l0,0,0-.011,0,0,0-.012v0l0-.007,0,0,0-.011,0,0,0-.011,0-.005,0-.007,0,0,0-.012,0,0,0-.012,0,0,0-.007v0l0-.013,0,0,0-.012v0l0-.012,0,0,0-.007,0,0,0-.012,0,0,0-.011,0,0,0-.007,0-.005,0-.01,0-.005,0-.01,0-.006,0-.007,0-.009,0-.006,0-.006,0-.01,0-.006,0-.007,0-.009,0-.007,0-.006,0-.01,0-.006,0-.01,0-.006,0-.007,0-.006,0-.01,0-.005,0-.009,0-.007,0-.006,0-.01,0-.006,0-.007,0-.008,0-.008,0-.005,0-.01,0-.005,0-.009,0-.006,0-.008,0,0,0-.011,0-.005,0-.011,0,0,0-.009v0l0-.012v0l0-.012v0l0-.011v0l0-.012v0l0-.012v0l0-.012v0l0-.012v0l0-.013v0l0-.013h0l-.01-.028h0l0-.014h0l-.01-.029h0q-.1-.289-.191-.581ZM.722,27.291c.089.645.206,1.293.348,1.926l-.488.109q-.064-.288-.122-.578h0L.451,28.7h0l0-.014h0l0-.015h0l0-.014v0l0-.014v0l0-.013v0l0-.013v0l0-.013v0l0-.011v0l0-.012v0l0-.012v0l0-.01v0l0-.012v-.005l0-.01v-.02l0-.011V28.47l0-.008V28.44l0-.01v-.013l0-.011V28.4l0-.013v-.016l0-.013v0l0-.015v0l0-.008v0l0-.015v0l0-.026v0l0-.016v0l-.008-.045h0q-.051-.292-.095-.585h0l-.007-.045v0l0-.016v-.013l0-.015v0l0-.013v-.017l0-.012v-.075l0-.012V27.39l0-.013v0l0-.013h0ZM.009,23.369l.5.014C.5,23.6.5,23.825.5,24.045c0,.431.012.868.035,1.3l-.5.027Q.007,24.836,0,24.3H0v-.087H0c0-.009,0-.017,0-.026V24.1s0,0,0-.005v0s0,0,0-.006,0,0,0,0,0,0,0-.006,0,0,0,0,0,0,0-.007,0,0,0,0v-.01H0v-.01s0,0,0,0,0,0,0-.006,0,0,0,0V24s0,0,0,0,0,0,0-.005V23.93H0V23.7H0v-.1q0-.026,0-.052h0v-.01h0v-.01h0v-.01h0v-.01h0V23.5h0Q.008,23.432.009,23.369ZM.449,19.4l.491.1c-.125.637-.224,1.288-.3,1.935l-.5-.055q.055-.5.131-.995h0l0-.03h0v-.007h0v-.015h0l0-.02v-.08h0v-.036h0v-.009h0v-.027h0l0-.027h0v-.008h0V20.1h0l0-.018h0v-.008h0v-.008h0l.006-.037h0l.008-.046h0v-.009h0l.006-.036h0L.356,19.9h0l.006-.037h0l0-.009h0v-.008h0l0-.009h0l0-.018h0v-.028l0-.009h0v-.124l0-.021h0v-.015h0Q.427,19.508.449,19.4Zm1.093-3.843.468.177c-.23.608-.436,1.234-.614,1.859l-.481-.137.046-.16h0l.008-.026v0l0-.014v0l0-.016h0l0-.008v0l0-.013v0l0-.01,0-.006,0-.007,0-.008,0-.008,0-.006,0-.009,0-.008,0-.006,0-.01,0-.005,0-.008,0-.006,0-.009,0-.006,0-.011,0-.005,0-.009,0-.006,0-.009v0l0-.011v0l0-.012v0l0-.01v0l0-.012v0l0-.012v0l0-.01v0l0-.012v0l0-.012v0l0-.012v0l0-.012v0l0-.013v0l0-.013v0l0-.011v0l0-.012v0l0-.012v0l0-.011v0l0-.012v0l0-.012v0l0-.011v0l0-.012v0l0-.012v0l0-.012v0l0-.012v0l0-.012v0l0-.012v0l0-.011v0l0-.012v0l0-.012v0l0-.01v0l0-.012v0l0-.011v0l0-.01v0l0-.01v0l0-.011v0l0-.01,0-.006,0-.008,0,0,0-.011,0-.005,0-.01,0-.005,0-.009,0,0,0-.011,0-.005,0-.011,0,0,0-.008,0-.005,0-.01,0,0,0-.011,0,0,0-.009,0-.007,0-.009,0,0,0-.011,0-.005,0-.01,0,0,0-.009,0,0,0-.011,0-.005,0-.01,0-.005,0-.008,0-.005,0-.01,0-.005,0-.01,0,0,0-.008,0-.006,0-.009,0-.005,0-.011,0,0,0-.009,0-.005,0-.009,0,0,0-.011,0,0,0-.009,0-.006,0-.009v0l0-.011,0,0,0-.011v0l0-.009v0l0-.011v0l0-.012v0l0-.01v0l0-.011v0l0-.012v0l0-.011v0l0-.011v0l0-.012v0l0-.013h0l0-.013v0l0-.013v0l0-.013h0l.005-.014h0l.005-.014h0l.005-.014h0Q1.484,15.708,1.542,15.555ZM3.26,11.948l.432.252c-.328.562-.636,1.144-.915,1.731l-.451-.215Q2.757,12.81,3.26,11.948ZM5.553,8.676,5.937,9c-.416.5-.816,1.023-1.189,1.556l-.41-.287Q4.913,9.444,5.553,8.676Zm2.8-2.85.326.379c-.492.425-.973.875-1.429,1.339l-.357-.35q.345-.351.7-.688h0l.011-.01h0l.045-.042h0L7.7,6.409h0Q8.022,6.112,8.353,5.826Zm3.232-2.349.259.427c-.555.337-1.1.7-1.63,1.085l-.294-.4.007,0h0l.021-.015h0l.012-.009h0l.012-.009,0,0L10,4.526l0,0,.011-.008,0,0,.012-.009h0l.007,0,0,0,.011-.008,0,0,.01-.007,0,0,.011-.008,0,0,.006,0,0,0,.009-.007,0,0,.009-.007,0,0,.006,0,.008-.006.006,0,0,0,.009-.006,0,0,.008-.005.006,0,.006,0,.008-.006.005,0,.005,0,.008-.006.005,0,.005,0,.008-.006.006,0,.006,0,.007,0,.006,0,.007,0,.007,0,.005,0,.008-.006.005,0,.006,0,.006,0,.007,0,0,0,.009-.006,0,0,.009-.006,0,0,.007,0,.005,0,.008-.006,0,0,.01-.007,0,0,.009-.006,0,0,.008-.005,0,0,.01-.007,0,0,.01-.007,0,0,.009-.006,0,0,.009-.006,0,0,.01-.007,0,0,.01-.007,0,0,.01-.007h0l.011-.007,0,0,.011-.007,0,0,.011-.007h0l.01-.007h0l.011-.008,0,0,.011-.008h0l.024-.016h0L10.6,4.11h0L10.61,4.1h0l.024-.016h0l.012-.008h0l.012-.008h0l.013-.008h0l.012-.008h0L10.7,4.04h0l.025-.017h0l.012-.008h0L10.753,4h0l.026-.017h0l.013-.008h0l.039-.026h0l.052-.034h0Q11.23,3.692,11.585,3.476Zm3.576-1.782.185.465c-.6.24-1.2.509-1.787.8l-.223-.448q.76-.378,1.549-.7h0L14.9,1.8h0l.014-.006h0l.013-.005h0l.012,0h0l.012,0h0l.01,0,0,0,.01,0,.005,0,.01,0,.012,0,.01,0,.005,0,.023-.009h0l.015-.006h0l.094-.037ZM18.984.534l.1.489c-.636.136-1.273.3-1.9.49l-.145-.478c.126-.038.252-.075.379-.112h0l.016,0h0l.016,0,.009,0h0l.012,0L17.5.9l.016,0,.015,0,.013,0,.011,0h.005l.011,0,.013,0h0l.012,0h0l.013,0h0l.012,0h0l.013,0h0l.013,0h0l.029-.008h0l.015,0h0Q18.338.672,18.984.534ZM22.947.025l.022.5c-.651.029-1.307.086-1.951.169L20.955.2q.456-.058.917-.1l.028,0h.063l.029,0H22Q22.47.046,22.947.025Z" transform="translate(-14 -5.273)" fill="#95a2fe"/><path d="M15.559,1A14.559,14.559,0,0,0,5.264,25.853,14.559,14.559,0,1,0,25.853,5.264,14.463,14.463,0,0,0,15.559,1m0-1A15.559,15.559,0,1,1,0,15.559,15.559,15.559,0,0,1,15.559,0Z" transform="translate(-5.513 3.214)" fill="#ffdd7e"/></g></g><g transform="translate(177 704.789)"><rect width="31" height="31" fill="none"/></g></g></g><path d="M99.99,49.974a3.4,3.4,0,0,0,3.12,3.347h.008a3.405,3.405,0,0,0,3.12-3.346l2.137.681a6.653,6.653,0,0,1,2.8,1.869l3.728,3.438-2.671,3.268-1.741-1.6v-2.1a.4.4,0,1,0-.808,0V70.875l-13.129,0V55.53a.4.4,0,1,0-.809,0v2.1L94,59.229,91.33,55.963l3.726-3.437a6.67,6.67,0,0,1,2.8-1.871Zm.8-.051a11.608,11.608,0,0,0,4.639,0,2.334,2.334,0,1,1-4.639,0Zm5.079-.913a.4.4,0,0,0-.213.057,11.359,11.359,0,0,1-5.152-.016.38.38,0,0,0-.151-.027.412.412,0,0,0-.175.043l-2.571.818a7.571,7.571,0,0,0-3.1,2.046l-4.008,3.7a.4.4,0,0,0-.04.554l3.184,3.893a.4.4,0,0,0,.589.043l1.507-1.388V71.276a.4.4,0,0,0,.406.406h13.935a.4.4,0,0,0,.4-.4V58.728l1.508,1.389a.4.4,0,0,0,.589-.043l3.183-3.893a.405.405,0,0,0-.041-.552l-4.006-3.7a7.605,7.605,0,0,0-3.1-2.047l-2.52-.8a.407.407,0,0,0-.226-.075Z" transform="translate(-29.52 873.275)" fill="#fff" fill-rule="evenodd"/></g></svg>';

/* Bottom Tab */

  static String shop =
      '<svg xmlns="http://www.w3.org/2000/svg" width="75" height="32" viewBox="0 0 75 32"><rect width="75" height="32" fill="#323741"/><g transform="translate(26.234 1.706)" opacity="0.2"><path d="M18.77,20.727H17.758v-5.15a2.564,2.564,0,0,1-1.992-.959,2.564,2.564,0,0,1-4,0,2.564,2.564,0,0,1-4,0,2.563,2.563,0,0,1-2,.96v5.15h-1a.283.283,0,0,0,0,.567H18.769a.283.283,0,0,0,0-.567Zm-5.012,0H9.774V16.257h3.984v4.469ZM17.474,7.294H6.049L3.766,13.01a2,2,0,1,0,4,0,2,2,0,1,0,4,0,2,2,0,1,0,4,0,2,2,0,1,0,4,0Z" transform="translate(0 0)" fill="#fff"/></g></svg>';

  static String shopSelected =
      '<svg xmlns="http://www.w3.org/2000/svg" width="75" height="32" viewBox="0 0 75 32"><rect width="75" height="32" fill="#323741"/><g transform="translate(26.234 1.706)"><path d="M18.77,20.727H17.758v-5.15a2.564,2.564,0,0,1-1.992-.959,2.564,2.564,0,0,1-4,0,2.564,2.564,0,0,1-4,0,2.563,2.563,0,0,1-2,.96v5.15h-1a.283.283,0,0,0,0,.567H18.769a.283.283,0,0,0,0-.567Zm-5.012,0H9.774V16.257h3.984v4.469ZM17.474,7.294H6.049L3.766,13.01a2,2,0,1,0,4,0,2,2,0,1,0,4,0,2,2,0,1,0,4,0,2,2,0,1,0,4,0Z" transform="translate(0 0)" fill="#95a2fe"/></g></svg>';

      // ... existing code ...

// Account Management Icons
  static const String bankAccountIcon = '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 2L2 8L12 14L22 8L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2 12L12 18L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2 16L12 22L22 16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';

  static const String businessInfoIcon = '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20 7H4C2.89543 7 2 7.89543 2 9V19C2 20.1046 2.89543 21 4 21H20C21.1046 21 22 20.1046 22 19V9C22 7.89543 21.1046 7 20 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M16 21V5C16 4.46957 15.7893 3.96086 15.4142 3.58579C15.0391 3.21071 14.5304 3 14 3H10C9.46957 3 8.96086 3.21071 8.58579 3.58579C8.21071 3.96086 8 4.46957 8 5V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';

  static const String userProfileIcon = '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 15C15.3137 15 18 12.3137 18 9C18 5.68629 15.3137 3 12 3C8.68629 3 6 5.68629 6 9C6 12.3137 8.68629 15 12 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M3 20.25C3 18.4598 3.71116 16.7429 5.02513 15.4289C6.33909 14.115 8.05644 13.4038 9.84662 13.4038H14.1534C15.9436 13.4038 17.6609 14.115 18.9749 15.4289C20.2888 16.7429 21 18.4598 21 20.25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';

  static const String emailIcon = '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M22 6L12 13L2 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';

  static const String phoneIcon = '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17 2H7C5.89543 2 5 2.89543 5 4V20C5 21.1046 5.89543 22 7 22H17C18.1046 22 19 21.1046 19 20V4C19 2.89543 18.1046 2 17 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M12 18H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <line x1="8" y1="6" x2="16" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
</svg>
''';

  static const String passwordIcon = '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="3" y="11" width="18" height="11" rx="2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <circle cx="12" cy="16" r="1" fill="currentColor"/>
</svg>
''';

  static const String deactivateIcon = '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M15 9L9 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M9 9L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';
// ... existing code ...

// Shop Info Grid SVGs
 // ... existing code ...

// Shop Info Grid SVGs
  static const String svgProducts = '''
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M3.74645 9.77273C3.28644 8.80402 3.28644 7.69598 3.74645 6.72727L5.28596 3.64824C5.74596 2.67953 6.58238 2 7.5 2H16.5C17.4176 2 18.254 2.67953 18.714 3.64824L20.2536 6.72727C20.7136 7.69598 20.7136 8.80402 20.2536 9.77273L12.7536 24H11.2464L3.74645 9.77273Z" stroke="currentColor" stroke-width="1.5"/>
      <path d="M3.5 7H20.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M8 11L8 14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M16 11L16 14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M12 11L12 16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>
  ''';
  static const String svgPending = '''
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M12 8V13L15 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="1.5"/>
    </svg>
  ''';

   static const String svgDraft = '''
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M4 5C4 3.89543 4.89543 3 6 3H14L20 9V19C20 20.1046 19.1046 21 18 21H6C4.89543 21 4 20.1046 4 19V5Z" stroke="currentColor" stroke-width="1.5"/>
      <path d="M14 3V9H20" stroke="currentColor" stroke-width="1.5"/>
      <path d="M8 13H16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M8 17H13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>
  ''';

  static const String svgQtyAlert = '''
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M12 9V14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M12 17.5V18" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M5.31171 10.7746L8.96771 4.45059C10.4807 1.86759 13.5193 1.86759 15.0323 4.45059L18.6883 10.7746C20.2463 13.4446 18.6632 16.7996 15.656 16.7996H8.34399C5.33679 16.7996 3.75371 13.4446 5.31171 10.7746Z" stroke="currentColor" stroke-width="1.5"/>
    </svg>
  ''';

  static const String svgOutOfStock = '''
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M4 7.8C4 6.11984 4 5.27976 4.32698 4.63803C4.6146 4.07354 5.07354 3.6146 5.63803 3.32698C6.27976 3 7.11984 3 8.8 3H15.2C16.8802 3 17.7202 3 18.362 3.32698C18.9265 3.6146 19.3854 4.07354 19.673 4.63803C20 5.27976 20 6.11984 20 7.8V21L17.25 19L14.75 21L12 19L9.25 21L6.75 19L4 21V7.8Z" stroke="currentColor" stroke-width="1.5"/>
      <path d="M9 10L15 10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M12 13L12 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>
  ''';

   static const String iconDeliveryBlue = '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 14H13C14.1 14 15 13.1 15 12V2H6C4.5 2 3.19001 2.82999 2.51001 4.04999" stroke="#2196F3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2 17C2 18.66 3.34 20 5 20H6C6 18.9 6.9 18 8 18C9.1 18 10 18.9 10 20H14C14 18.9 14.9 18 16 18C17.1 18 18 18.9 18 20H19C20.66 20 22 18.66 22 17V12H19C18.45 12 18 11.55 18 11V7C18 6.45 18.45 6 19 6H20.29L18.58 3.84999C18.22 3.31999 17.61 3 16.96 3H15V12C15 13.1 14.1 14 13 14H12" stroke="#2196F3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M8 22C9.10457 22 10 21.1046 10 20C10 18.8954 9.10457 18 8 18C6.89543 18 6 18.8954 6 20C6 21.1046 6.89543 22 8 22Z" stroke="#2196F3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M16 22C17.1046 22 18 21.1046 18 20C18 18.8954 17.1046 18 16 18C14.8954 18 14 18.8954 14 20C14 21.1046 14.8954 22 16 22Z" stroke="#2196F3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M22 12V17H19C17.9 17 17 16.1 17 15V7C17 5.9 17.9 5 19 5H20.29L22 7.71V12Z" stroke="#2196F3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2 8H8" stroke="#2196F3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2 11H6" stroke="#2196F3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2 14H4" stroke="#2196F3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
''';
}
