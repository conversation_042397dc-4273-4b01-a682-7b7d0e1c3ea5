import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/other/shimmer.dart';

class OrdersLoader extends StatelessWidget {
  const OrdersLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: Shimmer.fromColors(
          baseColor: AppColors.headerColorDark.withOpacity(0.5),
          highlightColor: AppColors.colorPrimary.withOpacity(0.5),
          child: Container(
            margin: const EdgeInsets.only(top: 10),
            decoration: BoxDecoration(
              color: Colors.grey,
              borderRadius: BorderRadius.circular(10),
            ),
          )),
    );
  }
}
