import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/controller/shop_info_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_border_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';

class ShopCustomerContact extends GetView<ShopInfoController>
    with InputValidationUtil {
  final Function(int value) tabPosition;
  ShopCustomerContact({Key? key, required this.tabPosition}) : super(key: key);
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  RxString strError = ''.obs;
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    Future.delayed(Duration.zero, () {
      if (controller.requiredPageAutoValidate()) {
        formKey.currentState!.validate();
      }
    });
    return SingleChildScrollView(
      
        child: Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: Column(
        children: [
          const SizedBox(height: 20),
          Center(
            child: SvgPicture.string(SvgStrings.shopCustomerContactTag),
          ),
          const SizedBox(height: 20),
          ElbaaabInputTextField(
            margin: const EdgeInsets.only(
                top: 16, left: kLeftSpace, right: kRightSpace),
            onChanged: (value) => controller.strContactNumber = value,
            initialValue: controller.strContactNumber,
            hint: appLocal.contactFeildHint,
            autoTextDirection: true,
            label: appLocal.contactNumbers,
            validator: (v) => validateContactNumber(
              v,
              errorMessage: appLocal.adminRejectPhoneNumber,
              serverValue:
                  controller.shop?.shopContactDetails?.phoneNumber ?? "",
              isReturend:
                  (controller.history.returnValues?.contains("Phone number") ??
                      false),
            ),
            prefix: SvgPicture.string(SvgStrings.iconCall),
            formatter: [
              FilteringTextInputFormatter.singleLineFormatter,
              LengthLimitingTextInputFormatter(10),
            ],
            inputType: TextInputType.phone,
          ),
          ElbaaabInputTextField(
            margin: const EdgeInsets.only(
                top: 16, left: kLeftSpace, right: kRightSpace),
            onChanged: (value) => controller.strWhatsappNumber = value,
            initialValue: controller.strWhatsappNumber,
            hint: appLocal.contactFeildHint,
            autoTextDirection: true,
            label: appLocal.whatsappNumber,
            validator: (v) => validateContactNumber(
              v,
              errorMessage: appLocal.adminRejectWhatsapppNumber,
              serverValue:
                  controller.shop?.shopContactDetails?.whatsUpPhoneNumber ?? "",
              isReturend: (controller.history.returnValues
                      ?.contains("Whatsup number") ??
                  false),
              isOptionalFeild: !(controller.history.returnValues
                      ?.contains("Whatsup number") ??
                  false),
            ),
            prefix: SvgPicture.string(SvgStrings.iconWhatsapp),
            formatter: [
              FilteringTextInputFormatter.singleLineFormatter,
              LengthLimitingTextInputFormatter(10),
            ],
            inputType: TextInputType.phone,
          ),
          ElbaaabInputTextField(
            margin: const EdgeInsets.only(
                top: 16, left: kLeftSpace, right: kRightSpace),
            onChanged: (value) => controller.strEmail = value,
            initialValue: controller.strEmail,
            hint: appLocal.emailHint,
            prefix: SvgPicture.string(SvgStrings.iconEmailBlue),
            label: appLocal.emailAddress,
            autoTextDirection: true,
            charaterlimit: 50,
            validator: (v) => validateEmail(
              v,
              errorMessage: appLocal.adminRejectContactEmail,
              serverValue: controller.shop?.shopContactDetails?.email ?? "",
              isReturend:
                  (controller.history.returnValues?.contains("Email") ?? false),
              isOptionalFeild:
                  !(controller.history.returnValues?.contains("Email") ??
                      false),
            ),
            formatter: [FilteringTextInputFormatter.singleLineFormatter],
            inputType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 40),
          ElbaabNetworkEroor(strError: strError),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: kLeftSpace, vertical: 20),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: ElbaabBorderButtonWidget(
                      text: appLocal.back,
                      onPress: () => tabPosition(1),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    flex: 1,
                    child: ElbaabGradientButtonWidget(
                      onPress: () {
                        if (formKey.currentState!.validate()) {
                          String contactObject =
                              prefs.getString(shopCustomerContact) ?? "";

                          if (contactObject.isEmpty &&
                              controller.shop == null) {
                            var object = {
                              "phoneNumber": controller.strContactNumber,
                              "whatsUpPhoneNumber":
                                  controller.strWhatsappNumber,
                              "email": controller.strEmail
                            };
                            String contactDetails = jsonEncode(object);
                            prefs.setString("customerContact", contactDetails);
                            tabPosition(3);
                          } else {
                            String contactNumber = controller
                                    .shop?.shopContactDetails?.phoneNumber ??
                                "";
                            String watsappNumber = controller.shop
                                    ?.shopContactDetails?.whatsUpPhoneNumber ??
                                "";
                            String email =
                                controller.shop?.shopContactDetails?.email ??
                                    "";
                            if (contactObject.isNotEmpty) {
                              Map<String, dynamic> contactInfo =
                                  jsonDecode(contactObject);

                              if (controller.history.returnValues
                                      ?.contains("Phone number") ==
                                  false) {
                                contactNumber =
                                    contactInfo["phoneNumber"] ?? "";
                              }
                              if (controller.history.returnValues
                                      ?.contains("Whatsup number") ==
                                  false) {
                                watsappNumber =
                                    contactInfo["whatsUpPhoneNumber"] ?? "";
                              }
                              if (controller.history.returnValues
                                      ?.contains("Email") ==
                                  false) {
                                email = contactInfo["email"] ?? "";
                              }
                            }

                            if ((controller.history.returnValues
                                        ?.contains("Phone number") ??
                                    false) &&
                                contactNumber == controller.strContactNumber) {
                              strError.value =
                                  ("Please Update Your Contact Number");
                            } else if ((controller.history.returnValues
                                        ?.contains("Whatsup number") ??
                                    false) &&
                                watsappNumber == controller.strWhatsappNumber) {
                              strError.value =
                                  ("Please Update Your Whatsapp Number");
                            } else if ((controller.history.returnValues
                                        ?.contains("Email") ??
                                    false) &&
                                email == controller.strEmail) {
                              strError.value =
                                  ("Please Update Your Contact Email");
                            } else {
                              if (contactNumber !=
                                      controller.strContactNumber ||
                                  watsappNumber !=
                                      controller.strWhatsappNumber ||
                                  email != controller.strEmail) {
                                var object = {
                                  "phoneNumber": controller.strContactNumber,
                                  "whatsUpPhoneNumber":
                                      controller.strWhatsappNumber,
                                  "email": controller.strEmail
                                };
                                String contactDetails = jsonEncode(object);
                                prefs.setString(
                                    "customerContact", contactDetails);
                                tabPosition(3);
                              } else {
                                tabPosition(3);
                              }
                            }
                          }
                        }
                      },
                      text: appLocal.next,
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    ));
  }
}
