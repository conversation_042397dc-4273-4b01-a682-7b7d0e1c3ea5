import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class ProfilePageLisTile extends StatelessWidget {
  final Function onTap;
  final String leading;
  final Widget? leadingWidget;
  final Widget? trailingWidget;
  final String title;
  final Color? color;
  final double textSize;
  final double height;

  const ProfilePageLisTile(
      {Key? key,
      required this.onTap,
      required this.leading,
      this.leadingWidget,
      this.trailingWidget,
      this.color,
      this.textSize = 15,
      this.height = 60,
      required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: AppColors.headerColorDark,
      child: Column(
        children: [
          SizedBox(
            height: height,
            child: Center(
              child: ListTile(
                dense: true,
                onTap: () => onTap(),
                leading: leadingWidget ??
                    SvgPicture.string(
                      leading,
                      color: color,
                    ),
                title: Transform.translate(
                  offset: const Offset(-10, 0),
                  child: Text(
                    title,
                    style: FontStyles.fontRegular(fontSize: textSize),
                  ),
                ),
                trailing: trailingWidget ??
                    Icon(
                      Icons.chevron_right,
                      color: Colors.white.withOpacity(0.37),
                    ),
              ),
            ),
          ),
          Container(
            height: 1,
            color: AppColors.backgroundColorDark,
          ),
        ],
      ),
    );
  }
}
