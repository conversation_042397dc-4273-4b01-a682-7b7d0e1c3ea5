import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class ElbaabPasswordTextField extends StatelessWidget {
  final ValueChanged<String> onChanged;
  final String hint;
  final String initialValue;
  final String label;
  final bool obscure;
  final Function obscureState;
  final EdgeInsets? edgeInsets;
  final FormFieldValidator<String>? validator;
  TextEditingController? passwordController;
  ElbaabPasswordTextField(
      {Key? key,
      required this.onChanged,
      required this.hint,
      required this.label,
      required this.obscure,
      required this.obscureState,
      this.validator,
      this.passwordController,
      this.initialValue = "",
      this.edgeInsets})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: edgeInsets ?? EdgeInsets.zero,
      child: TextFormField(
        initialValue: passwordController != null ? null : initialValue,
        controller: passwordController,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          LengthLimitingTextInputFormatter(48)
        ],
        obscureText: obscure,
        onChanged: onChanged,
        validator: validator,
        style: FontStyles.fontRegular(),
        decoration: InputDecoration(
          fillColor: AppColors.feildColorDark,
          filled: true,
          errorMaxLines: 1,
          border: OutlineInputBorder(
            borderRadius: const BorderRadius.all(Radius.circular(10)),
            borderSide: BorderSide(width: 1, color: AppColors.feildBorderColor),
          ),
          focusedBorder: const OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            borderSide: BorderSide(width: 1, color: Colors.white),
          ),
          hintText: hint,
          labelText: label,
          errorStyle: FontStyles.fontRegular(
              color: AppColors.colorDanger, fontSize: 12),
          hintStyle: FontStyles.fontRegular(
              fontSize: 12, color: Colors.white.withOpacity(0.5)),
          labelStyle: const TextStyle(color: Colors.white),
          suffixIcon: IconButton(
            icon: Icon(
              obscure ? Icons.remove_red_eye : Icons.remove_red_eye_outlined,
              color: AppColors.colorPrimary,
              size: 20,
            ),
            onPressed: () => obscureState(),
          ),
        ),
        obscuringCharacter: '•',
        keyboardType: TextInputType.text,
      ),
    );
  }
}
