import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/income_expense/income_expenses_model.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';

class DeliveryChargesFees extends StatelessWidget {
  DeliveryChargesFees({super.key});

  List<SupplierDeliveryFeeChargesType> deliveryChargesFees = Get.arguments[0];

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar:  ElbaabHeader(
        title: appLocal.deliveryChargesFees,
        leadingBack: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16).r,
        child: ListView.builder(
            itemCount: deliveryChargesFees.length,
            itemBuilder: (context, index) {
              return ElbaabFeildContainerWidget(
                borderWidth: 0,
                edgeInsets: const EdgeInsets.only(top: 16).r,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 14, vertical: 16).r,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                            style: FontStyles.fontSemibold(fontSize: 12),
                            children: <TextSpan>[
                               TextSpan(
                                text: "${appLocal.shipmentCode}: ",
                                style: const TextStyle(color: Colors.white),
                              ),
                              TextSpan(
                                text:
                                    (deliveryChargesFees[index].shipmentCode ??
                                            "")
                                        .replaceFirst("SHP", ""),
                                style: TextStyle(color: AppColors.colorPrimary),
                              ),
                            ]),
                      ),
                       SizedBox(height: 6.h),
                      Text(
                        GlobalMethods.convertTimeFormate(
                            deliveryChargesFees[index].date ?? "",
                            format: "EEE dd MMM, yyyy  hh : mm a"),
                        style: FontStyles.fontRegular(
                          fontSize: 10,
                          color: Colors.white.withOpacity(0.5),
                        ),
                      ),
                       SizedBox(height: 6.h),
                      RichText(
                        text: TextSpan(
                            style: FontStyles.fontRegular(fontSize: 12),
                            children: <TextSpan>[
                              TextSpan(
                                text: "${appLocal.type}: ",
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.5),
                                ),
                              ),
                              TextSpan(
                                text: deliveryChargesFees[index].feeType ?? "",
                              ),
                            ]),
                      ),
                       SizedBox(height: 6.h),
                      Row(
                        children: [
                          SvgPicture.string(SvgStrings.iconPriceTagWhite),
                          Text(
                            appLocal.dynamicPrice("${deliveryChargesFees[index].feeAmount ?? 0}"),
                            style: FontStyles.fontSemibold(fontSize: 16),
                          )
                        ],
                      )
                    ],
                  ),
                ),
              );
            }),
      ),
    );
  }
}
