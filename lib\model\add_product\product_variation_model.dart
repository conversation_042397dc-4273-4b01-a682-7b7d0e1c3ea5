import 'view_prduct/product_detail_model.dart';

class ProductVariationModel {
  final String? typename;
  final int? status;
  final String? message;
  final List<Variants>? colorGroupedVariants;
  final List<Variants>? sizeGroupedVariants;
  final List<Variations>? variations;

  ProductVariationModel({
    this.typename,
    this.status,
    this.message,
    this.colorGroupedVariants,
    this.sizeGroupedVariants,
    this.variations,
  });



  ProductVariationModel.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        status = json['status'] as int?,
        message = json['message'] as String?,
        sizeGroupedVariants = (json['sizeGroupedVariants'] as List?)
            ?.map((dynamic e) => Variants.fromJson(e as Map<String, dynamic>))
            .toList(),
        colorGroupedVariants = (json['colorGroupedVariants'] as List?)
            ?.map((dynamic e) => Variants.fromJson(e as Map<String, dynamic>))
            .toList(),
        variations = (json['variations'] as List?)
            ?.map((dynamic e) => Variations.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'status': status,
        'message': message,
        'sizeGroupedVariants':
            sizeGroupedVariants?.map((e) => e.toJson()).toList(),
        'colorGroupedVariants':
            colorGroupedVariants?.map((e) => e.toJson()).toList(),
        'variations': variations?.map((e) => e.toJson()).toList()
      };
}

class Variants {
  final String? typename;
  final String? unit;
  final String? size;
  final String? colorName;
  String? colorIcon;
  final String? colorFamily;
  List<Variations>? variations;
  final String? id;

  Variants({
    this.typename,
    this.id,
    this.unit,
    this.size,
    this.colorName,
    this.colorIcon,
    this.colorFamily,
    this.variations,
  });

  Variants.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        unit = json['unit'] as String?,
        id = json['_id'] as String?,
        size = json['size'] as String?,
        colorName = json['colorName'] as String?,
        colorIcon = json['colorIcon'] as String?,
        colorFamily = json['colorFamily'] as String?,
        variations = (json['variations'] as List?)
            ?.map((dynamic e) => Variations.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'unit': unit,
        'size': size,
        '_id': id,
        'colorName': colorName,
        'colorIcon': colorIcon,
        'colorFamily': colorFamily,
        'variations': variations?.map((e) => e.toJson()).toList()
      };
}

class Variations {
  int? variantQte;
  final String? variantName;
  final String? variantCode;
  dynamic variantPrice;
  String? id;
  String? variantEIN;
  String? variantManufacturerId;
   List<String>? variantImages;
  VariantAttributes? variantAttributes;
  Variations? ar;
  bool? isVariantVisible;
  bool? isVariantModified;
  bool? isUpdated;
  bool? isPriceReturned;
  bool? isQtyReturned;
  bool? isGtinReturned;
  Variations({
    this.variantQte,
    this.variantName,
    this.variantCode,
    this.variantPrice,
    this.id,
    this.ar,
    this.variantEIN,
    this.variantManufacturerId,
    this.variantImages,
    this.variantAttributes,
    this.isVariantVisible,
    this.isVariantModified,
    this.isUpdated,
    this.isPriceReturned,
    this.isQtyReturned,
    this.isGtinReturned,
  });

  Variations.fromJson(Map<String, dynamic> json)
      : variantQte = json['variantQte'] as int?,
        variantName = json['variantName'] as String?,
        id = json['_id'] as String?,
        variantCode = json['variantCode'] as String?,
        variantPrice = json['variantPrice'],
        ar = json['ar'] != null
            ? Variations.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        variantManufacturerId = json['variantManufacturerId'] as String?,
         variantImages = (json['variantImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        variantAttributes =
            (json['variantAttributes'] as Map<String, dynamic>?) != null
                ? VariantAttributes.fromJson(
                    json['variantAttributes'] as Map<String, dynamic>)
                : null,
        isVariantVisible = json['isVariantVisible'] as bool?,
        isUpdated = json['isUpdated'] as bool?,
        isVariantModified = json['isVariantModified'] as bool?;

  Map<String, dynamic> toJson() => {
        'variantQte': variantQte,
        "_id": id,
        'isUpdated': isUpdated,
        'variantName': variantName,
        'variantCode': variantCode,
        'variantManufacturerId': variantManufacturerId,
        'variantPrice': variantPrice,
        'variantImages': variantImages,
        'variantAttributes': variantAttributes?.toJson(),
        'ar': ar?.toJson(),
        'isVariantVisible': isVariantVisible,
        'isVariantModified': isVariantModified
      };
}

class VariantSize {
  final String? unit;
  final String? size;

  VariantSize({
    this.unit,
    this.size,
  });

  VariantSize.fromJson(Map<String, dynamic> json)
      : unit = json['unit'] as String?,
        size = json['size'] as String?;

  Map<String, dynamic> toJson() => {'unit': unit, 'size': size};
}

class VariantColor {
  final dynamic colorName;
  final dynamic colorIcon;
  final dynamic colorFamily;
  final dynamic colorImages;

  VariantColor({
    this.colorName,
    this.colorIcon,
    this.colorFamily,
    this.colorImages,
  });

  VariantColor.fromJson(Map<String, dynamic> json)
      : colorName = json['colorName'],
        colorIcon = json['colorIcon'],
        colorFamily = json['colorFamily'],
        colorImages = json['colorImages'];

  Map<String, dynamic> toJson() => {
        'colorName': colorName,
        'colorIcon': colorIcon,
        'colorFamily': colorFamily,
        'colorImages': colorImages
      };
}

class VariantCustomOptions {
  final String? attributeTitle;
  final String? attributeValue;

  VariantCustomOptions({
    this.attributeTitle,
    this.attributeValue,
  });

  VariantCustomOptions.fromJson(Map<String, dynamic> json)
      : attributeTitle = json['attributeTitle'] as String?,
        attributeValue = json['attributeValue'] as String?;

  Map<String, dynamic> toJson() =>
      {'attributeTitle': attributeTitle, 'attributeValue': attributeValue};
}
