import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';

class PasswordStrength<PERSON>hecker extends StatefulWidget {
  const PasswordStrengthChecker({
    super.key,
    required this.password,
    required this.onStrengthChanged,
  });

  final String password;

  final Function(bool isStrong) onStrengthChanged;

  @override
  State<PasswordStrengthChecker> createState() =>
      _PasswordStrengthCheckerState();
}

class _PasswordStrengthCheckerState extends State<PasswordStrengthChecker> {
  @override
  void didUpdateWidget(covariant PasswordStrengthChecker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.password != oldWidget.password) {
      final isStrong = _validators.entries.every(
        (entry) => entry.key.hasMatch(widget.password),
      );
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => widget.onStrengthChanged(isStrong),
      );
    }
  }

  final Map<RegExp, String> _validators = Get.locale?.languageCode == 'ar'
      ? {
          RegExp(r'[A-Z]'): 'حرف كبير واحد',
          RegExp(r'[!@#\$%^&*(),.?":{}|<>]'): 'رمز خاص واحد',
          RegExp(r'\d'): 'رقم واحد',
          RegExp(r'^.{6,32}$'): '6-32 حرف',
        }
      : {
          RegExp(r'[A-Z]'): 'One uppercase letter',
          RegExp(r'[!@#\$%^&*(),.?":{}|<>]'): 'One special character',
          RegExp(r'\d'): 'One number',
          RegExp(r'^.{6,32}$'): '6-32 characters',
        };

  @override
  Widget build(BuildContext context) {
    final hasValue = widget.password.isNotEmpty;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _validators.entries.map(
        (entry) {
          final hasMatch = entry.key.hasMatch(widget.password);
          final color = hasValue
              ? (hasMatch ? AppColors.colorSecondary : AppColors.colorDanger)
              : null;

          return Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Row(
              children: [
                AnimatedOpacity(
                  opacity: hasMatch ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 500),
                  child: Icon(
                    Icons.check_circle,
                    color: AppColors.colorSecondary,
                  ),
                ),
                SizedBox(width: hasMatch ? 5 : 0),
                Text(
                  entry.value,
                  style: TextStyle(color: color),
                ),
              ],
            ),
          );
        },
      ).toList(),
    );
  }
}
