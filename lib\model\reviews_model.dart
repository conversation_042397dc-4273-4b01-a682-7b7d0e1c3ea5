class ReviewsModel {
  final String? typename;
  final int? status;
  final String? message;
  final String? arMessage;
  final Reviews? reviews;

  ReviewsModel({
    this.typename,
    this.status,
    this.arMessage,
    this.message,
    this.reviews,
  });

  ReviewsModel.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        status = json['status'] as int?,
        message = json['message'] as String?,
        arMessage = json['arMessage'] as String?,
        reviews = (json['reviews'] as Map<String, dynamic>?) != null
            ? Reviews.fromJson(json['reviews'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'status': status,
        'message': message,
        'arMessage': arMessage,
        'reviews': reviews?.toJson()
      };
}

class Reviews {
  final String? typename;
  final int? page;
  final int? totalPages;
  final int? totalItems;
  final bool? hasNextPage;
  final List<Items>? items;

  Reviews({
    this.typename,
    this.page,
    this.totalPages,
    this.totalItems,
    this.hasNextPage,
    this.items,
  });

  Reviews.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        page = json['page'] as int?,
        totalPages = json['totalPages'] as int?,
        totalItems = json['totalItems'] as int?,
        hasNextPage = json['hasNextPage'] as bool?,
        items = (json['items'] as List?)
            ?.map((dynamic e) => Items.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'page': page,
        'totalPages': totalPages,
        'totalItems': totalItems,
        'hasNextPage': hasNextPage,
        'items': items?.map((e) => e.toJson()).toList()
      };
}

class Items {
  final String? typename;
  final List<Review>? reviews;
  final bool? hasMoreReviews;
  final int? totalReviewCount;

  Items({
    this.typename,
    this.reviews,
    this.hasMoreReviews,
    this.totalReviewCount,
  });

  Items.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        reviews = (json['reviews'] as List?)
            ?.map((dynamic e) => Review.fromJson(e as Map<String, dynamic>))
            .toList(),
        hasMoreReviews = json['hasMoreReviews'] as bool?,
        totalReviewCount = json['totalReviewCount'] as int?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'reviews': reviews?.map((e) => e.toJson()).toList(),
        'hasMoreReviews': hasMoreReviews,
        'totalReviewCount': totalReviewCount
      };
}

class Review {
  final String? typename;
  final int? rate;
  final List<String>? reviewImages;
  final String? reviewMessage;
  final String? createdAt;
  final OrderItemId? orderItemId;
  final ClientId? clientId;

  Review({
    this.typename,
    this.rate,
    this.reviewImages,
    this.reviewMessage,
    this.createdAt,
    this.orderItemId,
    this.clientId,
  });

  Review.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        rate = json['rate'] as int?,
      reviewImages = (json['reviewImages'] as List?)?.map((dynamic e) => e as String).toList(),
        reviewMessage = json['reviewMessage'] as String?,
        createdAt = json['createdAt'] as String?,
        orderItemId = (json['orderItemId'] as Map<String, dynamic>?) != null
            ? OrderItemId.fromJson(json['orderItemId'] as Map<String, dynamic>)
            : null,
        clientId = (json['clientId'] as Map<String, dynamic>?) != null
            ? ClientId.fromJson(json['clientId'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'rate': rate,
        'reviewImages': reviewImages,
        'reviewMessage': reviewMessage,
        'createdAt': createdAt,
        'orderItemId': orderItemId?.toJson(),
        'clientId': clientId?.toJson()
      };
}

class OrderItemId {
  final String? typename;
  final String? orderItemCode;
  final List<Item>? items;

  OrderItemId({
    this.typename,
    this.orderItemCode,
    this.items,
  });

  OrderItemId.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        orderItemCode = json['orderItemCode'] as String?,
        items = (json['items'] as List?)
            ?.map((dynamic e) => Item.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'orderItemCode': orderItemCode,
        'items': items?.map((e) => e.toJson()).toList()
      };
}

class Item {
  final String? typename;
  final int? quantity;
  final Product? product;
  final Variant? variant;

  Item({
    this.typename,
    this.quantity,
    this.product,
    this.variant,
  });

  Item.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        quantity = json['quantity'] as int?,
        product = (json['product'] as Map<String, dynamic>?) != null
            ? Product.fromJson(json['product'] as Map<String, dynamic>)
            : null,
        variant = (json['variant'] as Map<String, dynamic>?) != null
            ? Variant.fromJson(json['variant'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'quantity': quantity,
        'product': product?.toJson(),
        'variant': variant?.toJson()
      };
}

class Variant {
  final String? typename;
  final String? variantEIN;
  final List<String>? variantImages;
  final VariantAttributes? variantAttributes;
  final int? variantPrice;
  final ProductId? productId;

  Variant({
    this.typename,
    this.productId,
    this.variantEIN,
    this.variantImages,
    this.variantAttributes,
    this.variantPrice,
  });

  Variant.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        productId = (json['productId'] as Map<String, dynamic>?) != null
            ? ProductId.fromJson(json['productId'] as Map<String, dynamic>)
            : null,
        variantEIN = json['variantEIN'] as String?,
        variantImages = (json['variantImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        variantAttributes =
            (json['variantAttributes'] as Map<String, dynamic>?) != null
                ? VariantAttributes.fromJson(
                    json['variantAttributes'] as Map<String, dynamic>)
                : null,
        variantPrice = json['variantPrice'] as int?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'variantEIN': variantEIN,
        'productId': productId?.toJson(),
        'variantAttributes': variantAttributes?.toJson(),
        'variantImages': variantImages,
        'variantPrice': variantPrice
      };
}

class ProductId {
  final String? id;
  final String? productName;

  ProductId({
    this.id,
    this.productName,
  });

  ProductId.fromJson(Map<String, dynamic> json)
      : id = json['_id'] as String?,
        productName = json['productName'] as String?;

  Map<String, dynamic> toJson() => {'_id': id, 'productName': productName};
}

class VariantAttributes {
  final String? typename;
  final VariantColor? variantColor;
  final VariantSize? variantSize;
  final List<VariantCustomOptions>? variantCustomOptions;

  VariantAttributes({
    this.typename,
    this.variantColor,
    this.variantSize,
    this.variantCustomOptions,
  });

  VariantAttributes.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        variantColor = (json['variantColor'] as Map<String, dynamic>?) != null
            ? VariantColor.fromJson(
                json['variantColor'] as Map<String, dynamic>)
            : null,
        variantSize = (json['variantSize'] as Map<String, dynamic>?) != null
            ? VariantSize.fromJson(json['variantSize'] as Map<String, dynamic>)
            : null,
        variantCustomOptions = (json['variantCustomOptions'] as List?)
            ?.map((dynamic e) =>
                VariantCustomOptions.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'variantColor': variantColor?.toJson(),
        'variantSize': variantSize?.toJson(),
        'variantCustomOptions':
            variantCustomOptions?.map((e) => e.toJson()).toList()
      };
}

class VariantColor {
  final String? typename;
  final String? colorIcon;
  final String? colorFamily;

  VariantColor({
    this.typename,
    this.colorIcon,
    this.colorFamily,
  });

  VariantColor.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        colorIcon = json['colorIcon'] as String?,
        colorFamily = json['colorFamily'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'colorIcon': colorIcon,
        'colorFamily': colorFamily
      };
}

class VariantSize {
  final String? typename;
  final String? unit;
  final String? size;

  VariantSize({
    this.typename,
    this.unit,
    this.size,
  });

  VariantSize.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        unit = json['unit'] as String?,
        size = json['size'] as String?;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'unit': unit, 'size': size};
}

class VariantCustomOptions {
  final String? typename;
  final String? attributeValue;
  final String? attributeTitle;

  VariantCustomOptions({
    this.typename,
    this.attributeValue,
    this.attributeTitle,
  });

  VariantCustomOptions.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        attributeValue = json['attributeValue'] as String?,
        attributeTitle = json['attributeTitle'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'attributeValue': attributeValue,
        'attributeTitle': attributeTitle
      };
}

class Product {
  final String? id;
  final String? productName;
  final String? productEIN;
  final int? productPrice;
  final List<String>? productImages;

  Product({
    this.id,
    this.productName,
    this.productEIN,
    this.productPrice,
    this.productImages,
  });

  Product.fromJson(Map<String, dynamic> json)
      : id = json['_id'] as String?,
        productName = json['productName'] as String?,
        productEIN = json['productEIN'] as String?,
        productPrice = json['productPrice'] as int?,
        productImages = (json['productImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList();

  Map<String, dynamic> toJson() => {
        '_id': id,
        'productName': productName,
        'productEIN': productEIN,
        'productPrice': productPrice,
        'productImages': productImages
      };
}

class ClientId {
  final String? typename;
  final UserId? userId;

  ClientId({
    this.typename,
    this.userId,
  });

  ClientId.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        userId = (json['userId'] as Map<String, dynamic>?) != null
            ? UserId.fromJson(json['userId'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'userId': userId?.toJson()};
}

class UserId {
  final String? typename;
  final String? avatar;
  final String? userName;

  UserId({
    this.typename,
    this.avatar,
    this.userName,
  });

  UserId.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        avatar = json['avatar'] as String?,
        userName = json['userName'] as String?;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'avatar': avatar, 'userName': userName};
}
