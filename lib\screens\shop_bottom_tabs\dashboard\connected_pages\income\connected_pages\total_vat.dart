import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/components/row_title.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';

class TotalVat extends StatelessWidget {
  RxString chartSort = "jul 21 - jul 27".obs;

  TotalVat({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const ElbaabHeader(
        title: EnStrings.totalVat,
        leadingBack: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: () => BottomSheets.dateRangeSelector(context, '')
                  .then((value) => chartSort.value = value),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 13, vertical: 6),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: AppColors.colorPrimary.withOpacity(0.22),
                ),
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Obx(
                      () => Text(
                        chartSort.value,
                        style: FontStyles.fontRegular(
                          fontSize: 12,
                          color: AppColors.colorPrimary,
                        ),
                      ),
                    ),
                    const SizedBox(width: 11),
                    Icon(
                      Icons.arrow_drop_down,
                      color: AppColors.colorPrimary,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            Container(
              height: 141,
              decoration: BoxDecoration(
                color: AppColors.headerColorDark,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Wrap(
                      children: [
                        Text(
                          "15",
                          style: FontStyles.fontMedium(fontSize: 32),
                        ),
                        Text(
                          "  AED",
                          style: FontStyles.fontMedium(fontSize: 12),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      EnStrings.totalAmount,
                      style: FontStyles.fontRegular(
                        color: Colors.white.withOpacity(0.5),
                      ),
                    )
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              EnStrings.vat,
              style: FontStyles.fontSemibold(),
            ),
            const SizedBox(height: 12),
            Container(
              height: 50,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(5),
                  topRight: Radius.circular(5),
                ),
                color: AppColors.colotMidGray,
              ),
              child: const Row(
                children: [
                  Expanded(
                      flex: 1, child: RowTitle(title: EnStrings.orderNumber)),
                  Expanded(flex: 1, child: RowTitle(title: EnStrings.amount)),
                  Expanded(flex: 1, child: RowTitle(title: EnStrings.vat)),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                  itemCount: 5,
                  itemBuilder: (context, index) {
                    return Container(
                      height: 51,
                      decoration: BoxDecoration(
                        color: AppColors.feildColorDark,
                        borderRadius: index < 4
                            ? BorderRadius.zero
                            : const BorderRadius.only(
                                bottomLeft: Radius.circular(5),
                                bottomRight: Radius.circular(5),
                              ),
                      ),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 50,
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Center(
                                    child: Text(
                                      "#33",
                                      style: FontStyles.fontRegular(
                                        fontSize: 10,
                                        color: AppColors.colorPrimary,
                                      ),
                                      textAlign: TextAlign.center,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        ' 450 AED',
                                        style: TextStyle(
                                          fontFamily: 'Poppins',
                                          fontSize: 12,
                                          color: Colors.white.withOpacity(0.8),
                                        ),
                                        maxLines: 1,
                                        textAlign: TextAlign.center,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(left: 25),
                                        child: Text(
                                          'fees',
                                          style: FontStyles.fontMedium(
                                              fontSize: 9,
                                              color:
                                                  AppColors.colorSecondary_Red),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  flex: 1,
                                  child: Center(
                                    child: Text(
                                      "10 AED",
                                      style: FontStyles.fontRegular(
                                        fontSize: 10,
                                        color: Colors.white.withOpacity(0.8),
                                      ),
                                      textAlign: TextAlign.center,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            height: 1,
                            color: Colors.white.withOpacity(0.08),
                            margin: const EdgeInsets.symmetric(horizontal: 10),
                          )
                        ],
                      ),
                    );
                  }),
            )
          ],
        ),
      ),
    );
  }
}
