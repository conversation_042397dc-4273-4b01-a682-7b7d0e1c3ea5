import 'package:flutter/services.dart';

class ArabicInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // Define a regex pattern that allows Arabic characters, numbers, and specific symbols
    final RegExp regex = RegExp(r'^[\u0621-\u064A0-9\s-.]+$ ');

    // Check if the new value matches the regex
    if (regex.hasMatch(newValue.text)) {
      return newValue; // If it matches, return the new value
    }
    return oldValue; // If it doesn't match, return the old value
  }
}
