import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/other/badge_decoration.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';

class ProductInfo extends StatelessWidget {
  final Items item;
  const ProductInfo({Key? key, required this.item}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Container(
      height: 132.h,
      width: 246.w,
      foregroundDecoration:
          item.orderItemProductStatus?.last.status == "Canceled"
              ? BadgeDecoration(
                  badgeColor: AppColors.colorSecondaryYellow,
                  badgeSize: 80,
                  textSpan: TextSpan(
                    text: "Cancelled\n by customer",
                    style: FontStyles.fontMedium(
                      fontSize: 10,
                      height: 1.2,
                      color: AppColors.colotMidBlack,
                    ),
                  ),
                )
              : item.orderItemProductStatus?.last.status == "Rejected"
                  ? BadgeDecoration(
                      badgeColor: AppColors.colorSecondary_Red.withOpacity(0.8),
                      badgeSize: 80,
                      textSpan: TextSpan(
                        text: "Rejected\n by seller",
                        style: FontStyles.fontMedium(fontSize: 10, height: 1.2),
                      ),
                    )
                  : null,
      margin: const EdgeInsets.only(right: 16).r,
      child: Row(
        children: [
          SizedBox(
            height: 132.h,
            width: 90.w,
            child: GlobalMethods.netWorkImage(
              item.product?.productName != null
                  ? (item.product?.productImages[0] ?? "")
                  : item.variant?.variantImages?.first ?? "",
              BorderRadius.circular(5),
              BoxFit.cover,
            ),
          ),
           SizedBox(width: 11.w),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.product?.productName != null
                    ? (item.product?.productName ?? "")
                    : item.variant?.variantName ?? "",
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: FontStyles.fontBold(fontSize: 12),
              ),
              const Spacer(),
              infoTitle(
                item.product?.productName != null
                    ? appLocal.productEin
                    : appLocal.variantEin,
                "#${item.product?.productName != null ? (item.product?.productEIN ?? "") : item.variant?.variantEIN ?? ""}",
              ),
              if (item.variant != null &&
                  (item.variant?.variantAttributes?.variantColor?.colorIcon ??
                          "")
                      .isNotEmpty)
                const Spacer(),
              if (item.variant != null &&
                  (item.variant?.variantAttributes?.variantColor?.colorIcon ??
                          "")
                      .isNotEmpty)
                infoTitle(
                  appLocal.color,
                  '',
                  child: Row(
                    children: [
                      Container(
                        height: 12,
                        width: 12,
                        margin: const EdgeInsets.only(right: 4),
                        child: GlobalMethods.netWorkImage(
                          item.variant?.variantAttributes?.variantColor
                                  ?.colorIcon ??
                              "",
                          BorderRadius.circular(5),
                          BoxFit.cover,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          "${item.variant?.variantAttributes?.variantColor?.colorFamily ?? ""} ${item.variant?.variantAttributes?.variantColor?.colorName ?? ""}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: FontStyles.fontRegular(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.6),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              if (item.variant != null &&
                  (item.variant?.variantAttributes?.variantSize?.size ?? "")
                      .isNotEmpty)
                const Spacer(),
              if (item.variant != null &&
                  (item.variant?.variantAttributes?.variantSize?.size ?? "")
                      .isNotEmpty)
                infoTitle(
                  appLocal.size,
                  item.variant?.variantAttributes?.variantSize?.size ?? "",
                ),
              if (item.variant != null &&
                  (item.variant?.variantAttributes?.variantCustomOptions
                          ?.isNotEmpty ??
                      false))
                const Spacer(),
              if (item.variant != null &&
                  (item.variant?.variantAttributes?.variantCustomOptions
                          ?.isNotEmpty ??
                      false))
                infoTitle(
                  appLocal.options,
                  (item.variant?.variantAttributes?.variantCustomOptions
                          ?.map((e) => e.attributeValue ?? "")
                          .join(", ") ??
                      ""),
                ),

              const Spacer(),
              infoTitle(appLocal.qty, "${item.quantity ?? 0} pieces"),
              const Spacer(),
              infoTitle(appLocal.price,
                  "${(item.product?.productName != null ? (item.product?.productPrice ?? "") : item.variant?.variantPrice ?? "")} ${appLocal.aed}"),
            ],
          ))
        ],
      ),
    );
  }

  infoTitle(String title, String info, {Widget? child}) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Text(
            title,
            style: FontStyles.fontMedium(fontSize: 12),
          ),
        ),
        Expanded(
          flex: 1,
          child: child ??
              Text(
                info,
                maxLines: 1,
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: Colors.white.withOpacity(0.6),
                ),
              ),
        ),
      ],
    );
  }
}
