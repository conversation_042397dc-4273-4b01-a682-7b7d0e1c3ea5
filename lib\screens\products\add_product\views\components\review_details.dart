import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_carousel_feild_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';

class ReviewDetails extends StatelessWidget {
  final AddProductController controller;
  ReviewDetails({Key? key, required this.controller}) : super(key: key);

  late AppLocalizations appLocal;

  @override
  Widget build(BuildContext context) {
    appLocal = AppLocalizations.of(context)!;
    return Padding(
      padding: const EdgeInsets.only(top: 24).r,
      child: ElbaabCarouselFeildWidget(
        aspectRatio: controller.arrAllVariations.isNotEmpty
            ? 4 / 5
            : controller.productSpecificationList.isNotEmpty
                ? 6 / 4
                : (controller.arrAllVariations.isNotEmpty && controller.productSpecificationList.isNotEmpty)?5/5: 7 / 4,
        children: [
          ElbaabFeildContainerWidget(
            borderWidth: 0,
            borderColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.only(
                  left: kLeftSpace, right: kRightSpace, top: 10, bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: Text(
                          appLocal.detailsTitleEnglish,
                          style: FontStyles.fontBold(fontSize: 12),
                        ),
                      ),
                      IconButton(
                        onPressed: () =>
                            controller.changeTab(isNext: false, isJumpPage: 1),
                        icon: Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.06),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: SvgPicture.string(SvgStrings.iconEditGray),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          appLocal.productPrice,
                          style: FontStyles.fontRegular(fontSize: 12),
                        ),
                      ),
                      Expanded(child: getPrice())
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          appLocal.availableQuantity,
                          style: FontStyles.fontRegular(
                            fontSize: 12,
                          ),
                        ),
                      ),
                      Expanded(child: getQty())
                    ],
                  ),
                  if (controller.isHideMinimumQty.value ||
                      (controller.product?.productNotifOnMinQte ?? false))
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(
                            appLocal.miniQTY,
                            style: FontStyles.fontRegular(
                              fontSize: 12,
                            ),
                          ),
                        ),
                        Expanded(child: getMinimumQty())
                      ],
                    ),
                  if (controller.colorList.isNotEmpty ||
                      controller.size.value.sizes.isNotEmpty ||
                      controller.customOptionList.isNotEmpty)
                    const SizedBox(height: 20),
                  if (controller.colorList.isNotEmpty ||
                      controller.size.value.sizes.isNotEmpty ||
                      controller.customOptionList.isNotEmpty)
                    Text(
                      appLocal.options,
                      style: FontStyles.fontBold(fontSize: 12),
                    ),
                  if (controller.colorList.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: 20.h),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              appLocal.color,
                              style: FontStyles.fontRegular(
                                fontSize: 12,
                              ),
                            ),
                          ),
                          Expanded(
                            child: SizedBox(
                              height: 18,
                              child: ListView.builder(
                                  itemCount: controller.colorList.length,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder: (context, index) {
                                    return Container(
                                      height: 18,
                                      width: 18,
                                      margin: const EdgeInsets.only(right: 8),
                                      child: GlobalMethods.netWorkImage(
                                        controller.colorList[index].thumnailUrl,
                                        BorderRadius.circular(16),
                                        BoxFit.cover,
                                      ),
                                    );
                                  }),
                            ),
                          )
                        ],
                      ),
                    ),
                  if (controller.size.value.sizes.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: 10.h),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              appLocal.size,
                              style: FontStyles.fontRegular(fontSize: 12),
                            ),
                          ),
                          Expanded(
                            child: SizedBox(
                              height: 24,
                              child: ListView.builder(
                                  itemCount: controller.size.value.sizes.length,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder: (context, index) {
                                    return Container(
                                      height: 24,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 14),
                                      margin: const EdgeInsets.only(right: 8),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(5),
                                        border: Border.all(
                                            color:
                                                Colors.white.withOpacity(0.3),
                                            width: 1),
                                      ),
                                      child: Center(
                                        child: Text(
                                          controller
                                              .size.value.sizes[index].value,
                                          style: FontStyles.fontRegular(
                                            fontSize: 10,
                                            color:
                                                Colors.white.withOpacity(0.5),
                                          ),
                                        ),
                                      ),
                                    );
                                  }),
                            ),
                          )
                        ],
                      ),
                    ),
                  if (controller.customOptionList.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: 10.h),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              appLocal.customOption,
                              style: FontStyles.fontRegular(fontSize: 12),
                            ),
                          ),
                          Expanded(
                            child: Column(
                              children: [
                                ...List.generate(
                                    controller.customOptionList.length,
                                    (superIndex) {
                                  return SizedBox(
                                    height: 55.h,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          controller
                                              .customOptionList[superIndex]
                                              .title,
                                          style: FontStyles.fontRegular(
                                              fontSize: 10),
                                        ),
                                        SizedBox(height: 8.w),
                                        SizedBox(
                                          height: 24.h,
                                          child: ListView.builder(
                                              itemCount: controller
                                                  .customOptionList[superIndex]
                                                  .valueList
                                                  .length,
                                              scrollDirection: Axis.horizontal,
                                              itemBuilder: (context, index) {
                                                return Container(
                                                  height: 24.h,
                                                  padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 14)
                                                      .r,
                                                  margin: const EdgeInsets.only(
                                                          right: 8)
                                                      .r,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            5),
                                                    border: Border.all(
                                                        color: Colors.white
                                                            .withOpacity(0.3),
                                                        width: 1),
                                                  ),
                                                  child: Center(
                                                    child: Text(
                                                      controller
                                                          .customOptionList[
                                                              superIndex]
                                                          .valueList[index]
                                                          .value,
                                                      style: FontStyles
                                                          .fontRegular(
                                                              fontSize: 10,
                                                              color: Colors
                                                                  .white
                                                                  .withOpacity(
                                                                      0.5)),
                                                    ),
                                                  ),
                                                );
                                              }),
                                        ),
                                      ],
                                    ),
                                  );
                                })
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  if (controller.arrVariations.isNotEmpty)
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                      onTap: () =>
                          controller.changeTab(isNext: false, isJumpPage: 2),
                      leading: Text(
                        appLocal.variation,
                        style: FontStyles.fontBold(
                          fontSize: 12,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      trailing: const Icon(
                        Icons.chevron_right,
                        color: Color.fromRGBO(252, 252, 252, 0.65),
                        size: 24,
                      ),
                    ),
                  if (controller.productSpecificationList.isNotEmpty)
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                      onTap: () =>
                          controller.changeTab(isNext: false, isJumpPage: 3),
                      leading: Text(
                        appLocal.specification,
                        style: FontStyles.fontBold(
                          fontSize: 12,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      trailing: const Icon(
                        Icons.chevron_right,
                        color: Color.fromRGBO(252, 252, 252, 0.65),
                        size: 24,
                      ),
                    ),
                ],
              ),
            ),
          ),
          ElbaabFeildContainerWidget(
            borderWidth: 0,
            borderColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.only(
                  left: kLeftSpace, right: kRightSpace, top: 10, bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: Text(
                          appLocal.detailsTitleArabic,
                          style: FontStyles.fontBold(fontSize: 12),
                        ),
                      ),
                      IconButton(
                        onPressed: () =>
                            controller.changeTab(isNext: false, isJumpPage: 1),
                        icon: Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.06),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: SvgPicture.string(SvgStrings.iconEditGray),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          appLocal.productPrice,
                          style: FontStyles.fontRegular(fontSize: 12),
                        ),
                      ),
                      Expanded(flex: 2, child: getPrice())
                    ],
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          appLocal.availableQuantity,
                          style: FontStyles.fontRegular(
                            fontSize: 12,
                          ),
                        ),
                      ),
                      Expanded(flex: 2, child: getQty())
                    ],
                  ),
                  if (controller.isHideMinimumQty.value ||
                      (controller.product?.productNotifOnMinQte ?? false))
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            appLocal.miniQTY,
                            style: FontStyles.fontRegular(
                              fontSize: 12,
                            ),
                          ),
                        ),
                        Expanded(flex: 2, child: getMinimumQty())
                      ],
                    ),
                  if (controller.colorList.isNotEmpty ||
                      controller.size.value.sizes.isNotEmpty ||
                      controller.customOptionList.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 20),
                      child: Text(
                        appLocal.options,
                        style: FontStyles.fontBold(fontSize: 12),
                      ),
                    ),
                  if (controller.colorList.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Text(
                              appLocal.color,
                              style: FontStyles.fontRegular(
                                fontSize: 12,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: SizedBox(
                              height: 18,
                              child: ListView.builder(
                                  itemCount: controller.colorList.length,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder: (context, index) {
                                    return Container(
                                      height: 18,
                                      width: 18,
                                      margin: const EdgeInsets.only(right: 8),
                                      child: GlobalMethods.netWorkImage(
                                        controller.colorList[index].thumnailUrl,
                                        BorderRadius.circular(16),
                                        BoxFit.cover,
                                      ),
                                    );
                                  }),
                            ),
                          )
                        ],
                      ),
                    ),
                  if (controller.size.value.sizes.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Text(
                              appLocal.size,
                              style: FontStyles.fontRegular(fontSize: 12),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: SizedBox(
                              height: 24,
                              child: ListView.builder(
                                  itemCount: controller.size.value.sizes.length,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder: (context, index) {
                                    return Container(
                                      height: 24,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 14),
                                      margin: const EdgeInsets.only(right: 8),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(5),
                                        border: Border.all(
                                            color:
                                                Colors.white.withOpacity(0.3),
                                            width: 1),
                                      ),
                                      child: Center(
                                        child: Text(
                                          controller
                                              .size.value.sizes[index].value,
                                          style: FontStyles.fontRegular(
                                            fontSize: 10,
                                            color:
                                                Colors.white.withOpacity(0.5),
                                          ),
                                        ),
                                      ),
                                    );
                                  }),
                            ),
                          )
                        ],
                      ),
                    ),
                  if (controller.customOptionList.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: 10.h),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              appLocal.customOption,
                              style: FontStyles.fontRegular(fontSize: 12),
                            ),
                          ),
                          Expanded(
                            child: Column(
                              children: [
                                ...List.generate(
                                    controller.customOptionList.length,
                                    (superIndex) {
                                  return SizedBox(
                                    height: 55.h,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          controller
                                              .customOptionList[superIndex]
                                              .titleAr,
                                          style: FontStyles.fontRegular(
                                              fontSize: 10),
                                        ),
                                        SizedBox(height: 8.w),
                                        SizedBox(
                                          height: 24.h,
                                          child: ListView.builder(
                                              itemCount: controller
                                                  .customOptionList[superIndex]
                                                  .valueList
                                                  .length,
                                              scrollDirection: Axis.horizontal,
                                              itemBuilder: (context, index) {
                                                return Container(
                                                  height: 24.h,
                                                  padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 14)
                                                      .r,
                                                  margin: const EdgeInsets.only(
                                                          right: 8)
                                                      .r,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            5),
                                                    border: Border.all(
                                                        color: Colors.white
                                                            .withOpacity(0.3),
                                                        width: 1),
                                                  ),
                                                  child: Center(
                                                    child: Text(
                                                      controller
                                                          .customOptionList[
                                                              superIndex]
                                                          .valueList[index]
                                                          .valueAr,
                                                      style: FontStyles
                                                          .fontRegular(
                                                              fontSize: 10,
                                                              color: Colors
                                                                  .white
                                                                  .withOpacity(
                                                                      0.5)),
                                                    ),
                                                  ),
                                                );
                                              }),
                                        ),
                                      ],
                                    ),
                                  );
                                })
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  if (controller.arrVariations.isNotEmpty)
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                      onTap: () =>
                          controller.changeTab(isNext: false, isJumpPage: 2),
                      leading: Text(
                        appLocal.variation,
                        style: FontStyles.fontBold(
                          fontSize: 12,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      trailing: const Icon(
                        Icons.chevron_right,
                        color: Color.fromRGBO(252, 252, 252, 0.65),
                        size: 24,
                      ),
                    ),
                  if (controller.productSpecificationList.isNotEmpty)
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                      onTap: () =>
                          controller.changeTab(isNext: false, isJumpPage: 3),
                      leading: Text(
                        appLocal.specification,
                        style: FontStyles.fontBold(
                          fontSize: 12,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      trailing: const Icon(
                        Icons.chevron_right,
                        color: Color.fromRGBO(252, 252, 252, 0.65),
                        size: 24,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getPrice() {
    String details = prefs.getString(productDetails) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (details.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(details);
      cacheValue = appLocal.dynamicPrice(info["price"] ?? "");
      serverValue =
          appLocal.dynamicPrice("${controller.product?.productPrice ?? 0}");
    }
    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: appLocal.dynamicPrice(controller.price),
        errorCacheValue: appLocal.errorCacheValuePrice,
        errorServerValue: appLocal.errorServerValuePrice);
  }

  Widget getQty() {
    String details = prefs.getString(productDetails) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (details.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(details);
      cacheValue = appLocal.dynamicPieceCount(info["qty"] ?? "");
      serverValue = appLocal
          .dynamicPieceCount("${controller.product?.productAvailableQte ?? 0}");
    }
    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: appLocal.dynamicPieceCount(controller.qty),
        errorCacheValue: appLocal.errorCacheValueQty,
        errorServerValue: appLocal.errorServerValueQty);
  }

  Widget getMinimumQty() {
    String details = prefs.getString(productDetails) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (details.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(details);
      bool isHide = info["notify_minimum_qty"] ?? false;
      if (isHide) {
        cacheValue = appLocal.dynamicPieceCount(info["minimum_qty"] ?? "");
      } else {
        cacheValue = appLocal.errorCacheValueMinQty1;
      }
      serverValue = appLocal
          .dynamicPieceCount("${controller.product?.productMinQte ?? 0}");
    }
    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: appLocal.dynamicPieceCount(controller.minQty),
        errorCacheValue: appLocal.errorCacheValueMinQty,
        errorServerValue: appLocal.errorServerValueMinQty);
  }
}
