import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class ElbaabButtonWidget extends StatelessWidget {
  final Function onPress;
  final Color colors;
  final Color? textColor;
  final Icon? icon;
  final String? text;
  final double? height;
  final double textSize;
  final double? borderRadius;
  final EdgeInsets? margin;
  final bool iconOnRight;
  final Widget? widget;
  final TextStyle? textStyle;

  const ElbaabButtonWidget({
    Key? key,
    required this.onPress,
    required this.colors,
    this.text,
    this.textColor,
    this.widget,
    this.textStyle,
    this.height,
    this.borderRadius,
    this.textSize = 14,
    this.margin,
    this.icon,
    this.iconOnRight = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? EdgeInsets.zero,
      child: ConstrainedBox(
        constraints: BoxConstraints.tightFor(height: (height ?? 58)),
        child: ElevatedButton(
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all<Color>(colors),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(borderRadius ??8.0),
              ),
            ),
          ),
          onPressed: () => onPress(),
          child: widget ??
              Center(
                child: icon == null
                    ? Text(
                        text!,
                        textAlign: TextAlign.center,
                        style: textStyle ??
                            FontStyles.fontSemibold(
                              fontSize: textSize,
                              color: textColor ?? Colors.white,
                            ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          if (!iconOnRight) icon ?? const Icon(Icons.wifi_lock),
                          SizedBox(width: iconOnRight ? 0 : 10),
                          Text(
                            text!,
                            style: textStyle ??
                                FontStyles.fontSemibold(
                                  fontSize: textSize,
                                  color: textColor ?? Colors.white,
                                ),
                          ),
                          SizedBox(width: iconOnRight ? 10 : 0),
                          if (iconOnRight) icon ?? const Icon(Icons.wifi_lock),
                        ],
                      ),
              ),
        ),
      ),
    );
  }
}
