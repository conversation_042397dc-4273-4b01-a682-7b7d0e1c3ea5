#!/bin/bash

# macOS DMG Creation Script for Overola Suppliers
# This script creates a DMG installer for the Flutter macOS application

set -e

APP_NAME="Overola Suppliers"
APP_VERSION="1.0.1"
APP_BUNDLE="overolasuppliers.app"
DMG_NAME="OverolaSuppliers-${APP_VERSION}"
BUILD_DIR="../../build/macos/Build/Products/Release"
DIST_DIR="../../dist/macos"

echo "🍎 Creating macOS DMG for ${APP_NAME}..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if the app bundle exists
if [ ! -d "${BUILD_DIR}/${APP_BUNDLE}" ]; then
    print_error "App bundle not found at ${BUILD_DIR}/${APP_BUNDLE}"
    print_error "Please build the macOS app first using: flutter build macos --release"
    exit 1
fi

# Create distribution directory
mkdir -p "${DIST_DIR}"

# Create temporary directory for DMG contents
TEMP_DIR=$(mktemp -d)
print_status "Using temporary directory: ${TEMP_DIR}"

# Copy app bundle to temp directory
print_status "Copying app bundle..."
cp -R "${BUILD_DIR}/${APP_BUNDLE}" "${TEMP_DIR}/"

# Create Applications symlink
print_status "Creating Applications symlink..."
ln -s /Applications "${TEMP_DIR}/Applications"

# Create background image directory (optional)
mkdir -p "${TEMP_DIR}/.background"

# Copy background image if it exists
if [ -f "dmg_background.png" ]; then
    cp "dmg_background.png" "${TEMP_DIR}/.background/"
fi

# Create .DS_Store for custom DMG layout (optional)
if [ -f "DS_Store_template" ]; then
    cp "DS_Store_template" "${TEMP_DIR}/.DS_Store"
fi

# Calculate size needed for DMG
print_status "Calculating DMG size..."
SIZE=$(du -sm "${TEMP_DIR}" | cut -f1)
SIZE=$((SIZE + 50)) # Add 50MB buffer

print_status "Creating DMG with size: ${SIZE}MB"

# Create DMG
hdiutil create -volname "${APP_NAME}" \
    -srcfolder "${TEMP_DIR}" \
    -ov \
    -format UDZO \
    -size ${SIZE}m \
    "${DIST_DIR}/${DMG_NAME}.dmg"

# Clean up temp directory
rm -rf "${TEMP_DIR}"

print_success "DMG created successfully: ${DIST_DIR}/${DMG_NAME}.dmg"

# Verify DMG
print_status "Verifying DMG..."
if hdiutil verify "${DIST_DIR}/${DMG_NAME}.dmg"; then
    print_success "DMG verification passed!"
else
    print_error "DMG verification failed!"
    exit 1
fi

# Get DMG size
DMG_SIZE=$(du -h "${DIST_DIR}/${DMG_NAME}.dmg" | cut -f1)
print_status "Final DMG size: ${DMG_SIZE}"

# Code signing (if developer certificate is available)
if security find-identity -v -p codesigning | grep -q "Developer ID Application"; then
    print_status "Code signing certificate found, signing DMG..."
    codesign --force --verify --verbose --sign "Developer ID Application" "${DIST_DIR}/${DMG_NAME}.dmg"
    print_success "DMG signed successfully!"
else
    print_warning "No code signing certificate found. DMG is not signed."
    print_warning "For distribution outside the App Store, you'll need to sign the DMG."
fi

# Notarization (if Apple ID credentials are configured)
if [ ! -z "$APPLE_ID" ] && [ ! -z "$APPLE_ID_PASSWORD" ]; then
    print_status "Notarizing DMG..."
    xcrun notarytool submit "${DIST_DIR}/${DMG_NAME}.dmg" \
        --apple-id "$APPLE_ID" \
        --password "$APPLE_ID_PASSWORD" \
        --team-id "$TEAM_ID" \
        --wait
    
    if [ $? -eq 0 ]; then
        print_success "DMG notarized successfully!"
        xcrun stapler staple "${DIST_DIR}/${DMG_NAME}.dmg"
        print_success "Notarization ticket stapled to DMG!"
    else
        print_warning "Notarization failed. DMG may not run on other Macs."
    fi
else
    print_warning "Apple ID credentials not configured. Skipping notarization."
    print_warning "Set APPLE_ID, APPLE_ID_PASSWORD, and TEAM_ID environment variables for notarization."
fi

print_success "🎉 macOS DMG creation completed!"
echo ""
echo "DMG Location: ${DIST_DIR}/${DMG_NAME}.dmg"
echo ""
echo "Next steps:"
echo "1. Test the DMG on a clean macOS system"
echo "2. If not signed/notarized, users will need to allow the app in System Preferences"
echo "3. Distribute via your website, Mac App Store, or other channels"
echo ""
echo "For App Store distribution:"
echo "1. Use 'flutter build macos --release --obfuscate --split-debug-info=build/debug-info'"
echo "2. Archive and upload using Xcode or Application Loader"
