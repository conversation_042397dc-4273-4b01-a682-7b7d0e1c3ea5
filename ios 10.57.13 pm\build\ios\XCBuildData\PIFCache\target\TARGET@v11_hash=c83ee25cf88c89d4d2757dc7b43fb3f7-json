{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e32bdfad09d645abc0960da04b370cf4", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b4f84c4610457e33f183f2e0b2e0467d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98576e8d854ba1e2c834ee00bfde5e1def", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98729153e9425d0e75e4e6ec9a87719d64", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98576e8d854ba1e2c834ee00bfde5e1def", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984da21980f9b30565297242e8b6c41733", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982c8d369fce03a31a230cfe006ec6b565", "guid": "bfdfe7dc352907fc980b868725387e98906f56aae61aef067fb721d9800c27e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec2345ac055d28e87fa449c56abd2549", "guid": "bfdfe7dc352907fc980b868725387e987034d4369ba392e1b654d52e935006f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d3872e5c994076950a7872b2f0e40d5", "guid": "bfdfe7dc352907fc980b868725387e985987f1eaf40f94f7f6c7523837693ecb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ba4731a7bd74ebd0ddde6dbf839b5c4", "guid": "bfdfe7dc352907fc980b868725387e9874099c5a2dde85e5a9309d774ca91b73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986522897a534b68e0e85dc44de07897c1", "guid": "bfdfe7dc352907fc980b868725387e985901c7973f3beaaa0e32d5fc4c8460aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834c262ede2750dc79da36c9fb02d7d8d", "guid": "bfdfe7dc352907fc980b868725387e98f8456801aac5d0c6950e4701c997bd27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee0c2175f3d4b9e01a30df883338f2ca", "guid": "bfdfe7dc352907fc980b868725387e98bb84ee02254cf5c60f4954388bb1f5ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cd40e07b7dfd0a7b950a9370aa64ce9", "guid": "bfdfe7dc352907fc980b868725387e982499d60a4a32fcc6879af970f9f20776", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3182026b383dc5748436e1d1bcbeddf", "guid": "bfdfe7dc352907fc980b868725387e989f07401c69c27317eac486243237a66d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b43f2cba3e1792e7d460f96a1b1ac5e5", "guid": "bfdfe7dc352907fc980b868725387e9806d3fb2ef7dc81435004d7970dd863f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982371cc6bcfb6b4f6c952964a687f58dd", "guid": "bfdfe7dc352907fc980b868725387e98aa1d98b708ca06d0a83556d62bac9ba8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e37d7e3efd1a253ff995acb61dabd74", "guid": "bfdfe7dc352907fc980b868725387e985974f55e5faa8e8339ff1cfe437d023f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b7dd280ac7d4d685f9c4498de7bcee", "guid": "bfdfe7dc352907fc980b868725387e982658b82af3114188ad79f7a882afa307", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a98ee467a599ff8d2e20807abd0c5c4", "guid": "bfdfe7dc352907fc980b868725387e981e8ab4eb8bfbf04d57c7cd1b350806c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a8f2d48216f465b543d5e76fb50a482", "guid": "bfdfe7dc352907fc980b868725387e98619ad0372f018de4a8a680956e2b3520", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878575b20d4d5b581fdeee55313c9b4be", "guid": "bfdfe7dc352907fc980b868725387e988652999ad1951b651b303216e3a8b101", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a28192cdd56837806dd8e6715f766518", "guid": "bfdfe7dc352907fc980b868725387e9885d695ccf98ea615cd4df85e10fac01b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcb4295294b159651f6d4b3915448b9c", "guid": "bfdfe7dc352907fc980b868725387e98dc056e8bc66dd34918e674ce494baa88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b2efca60a7ea6be23681943dc84d27", "guid": "bfdfe7dc352907fc980b868725387e987dcb453bea08d28b596940be18a7ea24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a60532ab286393657fbe4ddf05ce9251", "guid": "bfdfe7dc352907fc980b868725387e984a55d594a5d84e37136db3e217bf06c8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b4701e472a3808bf6249761962378fd", "guid": "bfdfe7dc352907fc980b868725387e98a1c64a875d48f87d202618051e59637d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ef75fcd3a048aeea2e925fb4726c2b8", "guid": "bfdfe7dc352907fc980b868725387e983173673c77fb77f4d4a75ee59808895e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9885ff4395e9832a66ff456440c36e3580", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f1d8794e286904f7791e77b4a53794d", "guid": "bfdfe7dc352907fc980b868725387e98889f0f2a606c8d9830385d327a750dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeabd47c9e5588cae593e69f502733b9", "guid": "bfdfe7dc352907fc980b868725387e98ce3d0f65e88d59bd7aafa042263bb1b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e0ba8857f30f3b148925271d10c1b7", "guid": "bfdfe7dc352907fc980b868725387e9819dcf63c1aedec0f5b03c4487c9c85af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842dbb40a3ccf63fd30ac38ece2a33f28", "guid": "bfdfe7dc352907fc980b868725387e987ab8a543a79956357dd4b248b58de109"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98298296600365622f3c545753907e2b85", "guid": "bfdfe7dc352907fc980b868725387e982b35e4f14295c9b9fb3e85287dc19ae9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8bb33d893d4b8134f986692b7e4deb", "guid": "bfdfe7dc352907fc980b868725387e9877cbec2c69cfab8e7e03866c2a30d920"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d7feb74bfc9adbb9c023828161888f", "guid": "bfdfe7dc352907fc980b868725387e98e4b373507096c339dcb2c3a47cf27286"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8ea056a644c9024d4b0458bb6631aa3", "guid": "bfdfe7dc352907fc980b868725387e983ae1344f5de0a04e8db483336c9779ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae10059ce2769a779de78bf91c00590", "guid": "bfdfe7dc352907fc980b868725387e989f5d517640858a7b4090f5fdd2bab5f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806cb9a90cf139431fbec297ddd3a92aa", "guid": "bfdfe7dc352907fc980b868725387e983b2d5664a704bcd0aca7628b7ec13301"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804aff8cdac5deaca03ee196e7f8b66e7", "guid": "bfdfe7dc352907fc980b868725387e98461ecd8d6a7d24a7115e9f38f3193d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988816aa06ff7a02334789f5c3aa3973b2", "guid": "bfdfe7dc352907fc980b868725387e983ac2b44108ff2d9f8790f104d228e12f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ff571f5a0221faa5b1c5d7a175f7fe3", "guid": "bfdfe7dc352907fc980b868725387e989ad8b45136bb13488e940c15ca1f6e19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d22853bd2fd7e7b70546839a0991f002", "guid": "bfdfe7dc352907fc980b868725387e98a3a66c42c8138b30b4c20a1a4f45f9e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ca198b71cc6eda93137116bf63e7c83", "guid": "bfdfe7dc352907fc980b868725387e987badcb8aa6966f9dc91f5a43d06497f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b06ca1bf6442b3db03acfca5a8755506", "guid": "bfdfe7dc352907fc980b868725387e98e995b69e95b66d8b4d98500d956f29a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b23cc29ecfca8097eb88aca5f9a4e6", "guid": "bfdfe7dc352907fc980b868725387e98c49ab42452b41ba3e726e4b78a5accfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f457011f706ed7bd0d3ea81f9a503d2", "guid": "bfdfe7dc352907fc980b868725387e9805992a011a9b5a5be2c68aa0826433c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce4eb90e0d8ea430cc39ecabaa82f86e", "guid": "bfdfe7dc352907fc980b868725387e980ea448a52bef334c2f8a040e87d80478"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea538e5c50b900773261b071fd0b913e", "guid": "bfdfe7dc352907fc980b868725387e98706cece249b367cff8ceddd703b10c1b"}], "guid": "bfdfe7dc352907fc980b868725387e98ad2763217f8f1d6d66ea4394871ea1f5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e986bed3737ffa2a32a26e44850aa3a7b90"}], "guid": "bfdfe7dc352907fc980b868725387e98773d9ecd8cd846fe8d7c269405e480bb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9804f76ec428acc8a8e3e2a726f76a2457", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9852bf41b6a97040a08cad6bd2ca89690c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}