import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';

class CreateShopFirstStep extends StatelessWidget {
  const CreateShopFirstStep({super.key});

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              appLocal.createShopContent,
              textAlign: TextAlign.center,
              style: FontStyles.fontRegular(),
            ),
          ),
          ElbaabGradientButtonWidget(
            edgeInsets:
                const EdgeInsets.symmetric(vertical: 40, horizontal: 16),
            onPress: () => Get.toNamed(RouteNames.createShopScreen,
                arguments: Get.arguments),
            text: appLocal.createShop,
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: SizedBox(
          height: 80,
          child: Center(
            child: InkWell(
              onTap: () => GlobalMethods.logout(),
              child: Text(
                appLocal.logout,
                style: FontStyles.fontRegular(color: AppColors.colorPrimary),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
