import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:sms_autofill/sms_autofill.dart';

class ChangeYourNumber extends StatelessWidget
    with InputValidationUtil, CodeAutoFill
    implements ServerResponse {
  RxInt status = 0.obs;
  RxString otpCode = "".obs, errorText = "".obs;
  late GraphQlInitilize _request;
  String defaultPhoneNumber = prefs.getString(userPhoneNumber) ?? "",
      strPhoneNumber = "";

  ChangeYourNumber({Key? key}) : super(key: key);
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late AppLocalizations appLocal;

  @override
  void codeUpdated() {
    otpCode.value = code!;
    _request.runMutation(
      context: Get.context!,
      query: GraphQlQuries.numberVerify,
      type: "Verify",
      variables: GraphQlVariables.verifyNumber(
          userId: supplierID, otpNumber: otpCode.value),
    );
  }

  @override
  Widget build(BuildContext context) {
    appLocal = AppLocalizations.of(context)!;
    strPhoneNumber = defaultPhoneNumber;
    _request = GraphQlInitilize(this);
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.changeYourMobileNumber,
        leadingBack: true,
      ),
      body: Obx(
        () => status.value == 0
            ? Form(
                key: _formKey,
                child: ElbaaabInputTextField(
                  margin: const EdgeInsets.only(
                      left: kLeftSpace, right: kRightSpace, top: 50),
                  onChanged: (v) => strPhoneNumber = v,
                  charaterlimit: 50,
                  initialValue: defaultPhoneNumber,
                  autoTextDirection: true,
                  hint: "ex: 5xxxxxxxx",
                  prefix: Text(
                    " 971 ",
                    style: FontStyles.fontRegular(),
                  ),
                  label: appLocal.newPhoneNumber,
                  validator: (v) {
                    if (v?.isEmpty ?? false) {
                      return appLocal.phoneNumber;
                    } else if ((v ?? "") == defaultPhoneNumber) {
                      return appLocal.phoneNumberAlreadyInuse;
                    } else {
                      return null;
                    }
                  },
                  inputFormatter: '[+0-9]',
                  inputType: TextInputType.phone,
                ),
              )
            : status.value == 1
                ? enterOtp(context)
                : numberChanged(),
      ),
      bottomNavigationBar: Obx(
        () => SizedBox(
          height: 100,
          child: Center(
            child: ElbaabGradientButtonWidget(
              edgeInsets: const EdgeInsets.symmetric(horizontal: 30),
              onPress: () {
                if (strPhoneNumber.startsWith('0')) {
                  strPhoneNumber = strPhoneNumber.replaceFirst('0', '+9715');
                } else if (strPhoneNumber.startsWith('5')) {
                  strPhoneNumber = strPhoneNumber.replaceFirst('5', '+9715');
                }

                String validatePhone =
                    InputValidationUtil.validatePhone(strPhoneNumber);
                if (status.value == 0) {
                  if (_formKey.currentState!.validate()) {
                    if (validatePhone.isNotEmpty) {
                      listenForCode();
                      _request.runMutation(
                        context: context,
                        query: GraphQlQuries.changePhone,
                        variables: GraphQlVariables.changePhone(
                            userPhoneNumber: strPhoneNumber,
                            userId: supplierID),
                      );
                    } else {
                      errorText.value = validatePhone.toString();
                    }
                  }
                } else if (status.value == 1) {
                  if (otpCode.value.isNotEmpty && otpCode.value.length > 5) {
                    _request.runMutation(
                      context: context,
                      query: GraphQlQuries.numberVerify,
                      type: "Verify",
                      variables: GraphQlVariables.verifyNumber(
                          userId: supplierID, otpNumber: otpCode.value),
                    );
                  } else {
                    errorText.value = appLocal.requiredOtpCode;
                  }
                } else {
                  Get.back();
                }
              },
              text: status.value == 0
                  ? appLocal.update
                  : status.value == 1
                      ? appLocal.next
                      : appLocal.done,
            ),
          ),
        ),
      ),
    );
  }

  Widget enterOtp(BuildContext context) {
    return Container(
      margin:
          const EdgeInsets.only(left: kLeftSpace, right: kRightSpace, top: 100),
      child: Column(
        children: [
          Text(
            appLocal.pleaseEnterDigitalCode,
            style: FontStyles.fontSemibold(
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 29),
          Obx(
            () => TextFieldPinAutoFill(
              decoration: InputDecoration(
                errorText: errorText.value.isNotEmpty ? errorText.value : null,
                errorStyle:
                    FontStyles.fontRegular(color: AppColors.colorDanger),
                fillColor: AppColors.feildColorDark,
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(
                    width: 1,
                    color: AppColors.feildBorderColor,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(
                    width: 1,
                    color: Colors.white,
                  ),
                ),
              ),
              currentCode: otpCode.value,
              onCodeChanged: (code) {
                if (code.length == 6) {
                  otpCode.value = code;
                  FocusScope.of(context).requestFocus(FocusNode());
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget numberChanged() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset("assets/images/numberChanged.svg"),
          Padding(
            padding: const EdgeInsets.all(32),
            child: Text(
              appLocal.mobileNumberChanged,
              textAlign: TextAlign.center,
              style: FontStyles.fontRegular(
                height: 2,
                color: AppColors.colorGray,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  onError(error, String type) {
    errorText.value = appLocal.localeName == "ar"
        ? BaseModel.fromJson(error).arMessage ?? ""
        : BaseModel.fromJson(error).message ?? "";
  }

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (model.status == statusOK && type == 'Verify') {
      status.value = 2;
      errorText.value = '';
    } else if (model.status == statusOK) {
      status.value = 1;
      errorText.value = '';
    }
    if (model.status == status400) {
      errorText.value = appLocal.localeName == "ar"
          ? model.arMessage ?? ""
          : model.message ?? "";
    }
  }
}
