import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/widgets/elbaab_name_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class EmailVerification extends StatefulWidget {
  const EmailVerification({super.key});

  @override
  State<EmailVerification> createState() => _EmailVerificationState();
}

class _EmailVerificationState extends State<EmailVerification>
    implements ServerResponse {
  final RxInt _time = 90.obs;

  bool isForget = false;

  late GraphQlInitilize _request;

  RxString strError = "".obs;

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    Timer.periodic(
      oneSec,
      (Timer timer) {
        if (_time.value == 0) {
          timer.cancel();
        } else {
          _time.value--;
        }
      },
    );
  }

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => {
          if (Get.arguments[1] != null) {isForget = Get.arguments[1]},
          startTimer()
        });
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0).r,
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                 SizedBox(height: 32.h),
                const Center(child: ElbaabNameWidget()),
                 SizedBox(height: 32.h),
                Text(
                  isForget ? appLocal.resetPassword : appLocal.verifyEmailAddress,
                  style: FontStyles.fontSemibold(fontSize: 25),
                ),
                Text(
                  '${appLocal.to} ${isForget ? appLocal.resetYourPassword : appLocal.verifyYourEmail}, ${appLocal.weSentOtp} ${Get.arguments[2]}',
                  style: FontStyles.fontRegular(
                      color: Colors.white.withOpacity(0.55), height: 1.8),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 30),
                  child: PinCodeTextField(
                    appContext: context,
                    length: 6,
                    hintCharacter: "0",
                    hintStyle: FontStyles.fontBold(
                        fontSize: 16, color: Colors.white.withOpacity(0.5)),
                    animationType: AnimationType.slide,
                    pinTheme: PinTheme(
                      shape: PinCodeFieldShape.underline,
                      inactiveColor: const Color.fromRGBO(203, 208, 220, 1),
                      inactiveFillColor: AppColors.backgroundColorDark,
                      selectedFillColor: AppColors.backgroundColorDark,
                      activeColor: AppColors.colorPrimary,
                      disabledColor: Colors.white,
                      selectedColor: AppColors.colorPrimary,
                      activeFillColor: AppColors.backgroundColorDark,
                    ),
                    onCompleted: (v) {
                      String userID = Get.arguments[0];
                      if (isForget) {
                        _request.runMutation(
                          context: context,
                          query: GraphQlQuries.forgetemailVerify,
                          variables: GraphQlVariables.verifyEmail(
                              userId: userID, otpNumber: v),
                        );
                      } else {
                        _request.runMutation(
                          context: context,
                          query: GraphQlQuries.emailVerify,
                          variables: GraphQlVariables.verifyEmail(
                              userId: userID, otpNumber: v),
                        );
                      }
                    },
                    cursorColor: AppColors.colorYellow,
                    animationDuration: const Duration(milliseconds: 300),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {},
                  ),
                ),
                Obx(() => Padding(
                      padding: const EdgeInsets.only(top: 24.0),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: _time.value == 0
                            ? InkWell(
                                onTap: () {
                                  String userID = Get.arguments[0];
                                  if (!isForget) {
                                    _request.runMutation(
                                        context: context,
                                        query: GraphQlQuries.resendOtpEmail,
                                        variables:
                                            GraphQlVariables.resendOtpEmail(
                                          userId: userID,
                                        ),
                                        type: "Resend");
                                  } else {
                                    _request.runMutation(
                                      context: context,
                                      query: GraphQlQuries.forgetPassword,
                                      variables:
                                          GraphQlVariables.forgetpassword(
                                              userEmail: Get.arguments[2]),
                                      type: "Resend",
                                    );
                                  }

                                  _time.value = 90;
                                  startTimer();
                                },
                                child: RichText(
                                  text: TextSpan(
                                      style:
                                          FontStyles.fontRegular(fontSize: 10),
                                      children: <TextSpan>[
                                         TextSpan(
                                          text: appLocal.notReceived,
                                          style: const TextStyle(
                                            color: Colors.white,
                                          ),
                                        ),
                                        TextSpan(
                                          text: appLocal.resendOtp,
                                          style: TextStyle(
                                            decoration:
                                                TextDecoration.underline,
                                            color: AppColors.colorPrimary,
                                          ),
                                        ),
                                      ]),
                                ),
                              )
                            : Wrap(
                                children: [
                                  SizedBox(
                                    width: 28,
                                    child: Text(
                                      '${_time.value}',
                                      style:
                                          FontStyles.fontRegular(fontSize: 17),
                                    ),
                                  ),
                                  Text(
                                    appLocal.secondsRemainToResendOtp,
                                    style: FontStyles.fontRegular(fontSize: 17),
                                  )
                                ],
                              ),
                      ),
                    )),
                ElbaabNetworkEroor(strError: strError)
              ]),
        ),
      ),
      bottomNavigationBar: SizedBox(
        height: 90,
        child: Center(
          child: InkWell(
            onTap: () => GlobalMethods.logout(),
            child: Text(
              appLocal.logout,
              style: FontStyles.fontRegular(color: AppColors.colorPrimary),
            ),
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {
    BaseModel model = BaseModel.fromJson(error);
    strError.value = Get.locale?.languageCode == "en" ? model.message ?? "" : model.arMessage ?? "";
  }

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (type != "Resend") {
      String userID = Get.arguments[0];
      if (model.status == statusOK) {
        if (isForget) {
          Get.toNamed(RouteNames.chanegPasswordScreen,
              arguments: [userID, true, isForget ? Get.arguments[2] : ""]);
        } else {
          Get.toNamed(RouteNames.numberVerificationScreen, arguments: [userID]);
        }
      } else {
        strError.value = Get.locale?.languageCode == "en" ? model.message ?? "" : model.arMessage ?? "";
      }
    }
  }
}
