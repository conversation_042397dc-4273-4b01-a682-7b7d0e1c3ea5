# Flutter Setup Script for Windows (PowerShell)
# This script downloads and installs Flutter SDK and sets up the development environment

Write-Host "🚀 Flutter Desktop Development Setup for Windows" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# Set installation directory
$FlutterDir = "C:\flutter"
$FlutterBin = "$FlutterDir\bin"

# Check if Flutter is already installed
if (Test-Path "$FlutterBin\flutter.bat") {
    Write-Host "[INFO] Flutter is already installed at $FlutterDir" -ForegroundColor Blue
} else {
    Write-Host "[INFO] Creating Flutter installation directory..." -ForegroundColor Blue
    
    # Create directory if it doesn't exist
    if (!(Test-Path $FlutterDir)) {
        New-Item -ItemType Directory -Path $FlutterDir -Force | Out-Null
    }

    # Download Flutter SDK
    Write-Host "[INFO] Downloading Flutter SDK..." -ForegroundColor Blue
    Write-Host "[INFO] This may take several minutes depending on your internet connection..." -ForegroundColor Blue

    $url = "https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.24.3-stable.zip"
    $output = "C:\flutter_sdk.zip"

    try {
        $ProgressPreference = 'SilentlyContinue'
        Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
        Write-Host "[SUCCESS] Flutter SDK downloaded successfully" -ForegroundColor Green
    } catch {
        Write-Host "[ERROR] Failed to download Flutter SDK: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "[ERROR] Please check your internet connection and try again" -ForegroundColor Red
        exit 1
    }

    # Extract Flutter SDK
    Write-Host "[INFO] Extracting Flutter SDK..." -ForegroundColor Blue
    try {
        Expand-Archive -Path $output -DestinationPath "C:\" -Force
        Write-Host "[SUCCESS] Flutter SDK extracted to $FlutterDir" -ForegroundColor Green
    } catch {
        Write-Host "[ERROR] Failed to extract Flutter SDK: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }

    # Clean up downloaded zip file
    Remove-Item $output -ErrorAction SilentlyContinue
}

# Add Flutter to PATH
Write-Host "[INFO] Configuring Flutter PATH..." -ForegroundColor Blue

$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($currentPath -notlike "*$FlutterBin*") {
    Write-Host "[INFO] Adding Flutter to user PATH..." -ForegroundColor Blue
    try {
        [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$FlutterBin", "User")
        Write-Host "[SUCCESS] Flutter added to user PATH" -ForegroundColor Green
        Write-Host "[INFO] Please restart your terminal or IDE to use Flutter" -ForegroundColor Yellow
    } catch {
        Write-Host "[ERROR] Failed to add Flutter to PATH: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "[INFO] Flutter is already in PATH" -ForegroundColor Blue
}

# Update current session PATH
$env:PATH += ";$FlutterBin"

# Enable desktop support
Write-Host "[INFO] Enabling Flutter desktop support..." -ForegroundColor Blue
try {
    & "$FlutterBin\flutter.bat" config --enable-windows-desktop
    & "$FlutterBin\flutter.bat" config --enable-macos-desktop  
    & "$FlutterBin\flutter.bat" config --enable-linux-desktop
    & "$FlutterBin\flutter.bat" config --no-analytics
    Write-Host "[SUCCESS] Desktop support enabled" -ForegroundColor Green
} catch {
    Write-Host "[WARNING] Could not configure Flutter settings" -ForegroundColor Yellow
}

# Run Flutter doctor
Write-Host "[INFO] Running Flutter doctor to check installation..." -ForegroundColor Blue
try {
    & "$FlutterBin\flutter.bat" doctor
} catch {
    Write-Host "[WARNING] Could not run Flutter doctor" -ForegroundColor Yellow
}

# Check for Visual Studio
Write-Host "[INFO] Checking for Visual Studio installation..." -ForegroundColor Blue
$vsInstallations = @(
    "C:\Program Files\Microsoft Visual Studio\2022",
    "C:\Program Files (x86)\Microsoft Visual Studio\2022",
    "C:\Program Files\Microsoft Visual Studio\2019",
    "C:\Program Files (x86)\Microsoft Visual Studio\2019"
)

$vsFound = $false
foreach ($vsPath in $vsInstallations) {
    if (Test-Path $vsPath) {
        Write-Host "[SUCCESS] Visual Studio found at $vsPath" -ForegroundColor Green
        $vsFound = $true
        break
    }
}

if (-not $vsFound) {
    Write-Host "[WARNING] Visual Studio not found" -ForegroundColor Yellow
    Write-Host "[WARNING] You need Visual Studio 2019 or 2022 with C++ tools for Windows development" -ForegroundColor Yellow
    Write-Host "[INFO] Download from: https://visualstudio.microsoft.com/downloads/" -ForegroundColor Blue
}

# Navigate to project directory and get dependencies
Write-Host "[INFO] Setting up project dependencies..." -ForegroundColor Blue
$projectDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $projectDir

try {
    & "$FlutterBin\flutter.bat" pub get
    Write-Host "[SUCCESS] Project dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to install project dependencies" -ForegroundColor Red
    Write-Host "[ERROR] Please run 'flutter pub get' manually after fixing any issues" -ForegroundColor Red
}

Write-Host ""
Write-Host "[SUCCESS] Flutter setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Restart your command prompt or IDE to use Flutter" -ForegroundColor White
Write-Host "2. Run 'flutter doctor' to verify installation" -ForegroundColor White
Write-Host "3. Run 'flutter devices' to see available devices" -ForegroundColor White
Write-Host "4. Run 'flutter run -d windows' to run the app on Windows" -ForegroundColor White
Write-Host ""
Write-Host "For development, you can use:" -ForegroundColor Cyan
Write-Host "- Visual Studio Code with Flutter extension" -ForegroundColor White
Write-Host "- Android Studio with Flutter plugin" -ForegroundColor White
Write-Host "- IntelliJ IDEA with Flutter plugin" -ForegroundColor White
Write-Host ""

# Check if we can run Flutter immediately
Write-Host "[INFO] Testing Flutter installation..." -ForegroundColor Blue
try {
    $flutterVersion = & "$FlutterBin\flutter.bat" --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[SUCCESS] Flutter is working correctly!" -ForegroundColor Green
        Write-Host "Flutter version: $($flutterVersion[0])" -ForegroundColor White
    } else {
        Write-Host "[WARNING] Flutter installation may have issues" -ForegroundColor Yellow
    }
} catch {
    Write-Host "[WARNING] Could not test Flutter installation" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
