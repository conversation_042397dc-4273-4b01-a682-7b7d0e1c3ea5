import 'package:overolasuppliers/model/reviews_model.dart';

class ReviewsByProduct {
  final String? typename;
  final int? status;
  final String? message;
  final Reviews? reviews;

  ReviewsByProduct({
    this.typename,
    this.status,
    this.message,
    this.reviews,
  });

  ReviewsByProduct.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        status = json['status'] as int?,
        message = json['message'] as String?,
        reviews = (json['reviews'] as Map<String, dynamic>?) != null
            ? Reviews.fromJson(json['reviews'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'status': status,
        'message': message,
        'reviews': reviews?.toJson()
      };
}

class Reviews {
  final String? typename;
  final int? page;
  final bool? hasNextPage;
  final int? totalPages;
  final int? totalItems;
  final List<UserReview>? items;

  Reviews({
    this.typename,
    this.page,
    this.hasNextPage,
    this.totalPages,
    this.totalItems,
    this.items,
  });

  Reviews.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        page = json['page'] as int?,
        hasNextPage = json['hasNextPage'] as bool?,
        totalPages = json['totalPages'] as int?,
        totalItems = json['totalItems'] as int?,
        items = (json['items'] as List?)
            ?.map((dynamic e) => UserReview.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'page': page,
        'hasNextPage': hasNextPage,
        'totalPages': totalPages,
        'totalItems': totalItems,
        'items': items?.map((e) => e.toJson()).toList()
      };
}

class UserReview {
  final String? typename;
  final int? rate;
  final List<String>? reviewImages;
  final String? reviewMessage;
  final String? createdAt;
  final ClientId? clientId;

  UserReview(
      {this.typename,
      this.rate,
      this.reviewImages,
      this.reviewMessage,
      this.createdAt,
      this.clientId});

  UserReview.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        rate = json['rate'] as int?,
        reviewImages = (json['reviewImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        reviewMessage = json['reviewMessage'] as String?,
        createdAt = json['createdAt'] as String?,
        clientId = (json['clientId'] as Map<String, dynamic>?) != null
            ? ClientId.fromJson(json['clientId'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'rate': rate,
        'reviewImages': reviewImages,
        'reviewMessage': reviewMessage,
        'createdAt': createdAt,
        'clientId': clientId?.toJson()
      };
}
