import 'dart:convert';
import 'dart:developer';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_share/flutter_share.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/other/shimmer.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/shop/ViewShopModel.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/controller/shop_info_controller.dart';

class ShopInfo extends GetView<ShopInfoController> {
  final Function(int index) tabIndex;
  final Function(bool product)? isProduct;
  final Shop? shop;
  final bool isReview;
  final bool isPreview;
  final TabController? tabController;
  final int selectedTabIndex;

  final RxInt? acceptedProductCount,
      pendingProductCount,
      draftProductCount,
      qtyAlertProductCount,
      outOfStockProductCount,
      viewsCount,
      freeDeliveryProductCount;

  const ShopInfo(
      {Key? key,
      this.tabController,
      required this.tabIndex,
      this.shop,
      this.viewsCount,
      this.acceptedProductCount,
      this.pendingProductCount,
      this.draftProductCount,
      this.qtyAlertProductCount,
      this.outOfStockProductCount,
      this.freeDeliveryProductCount,
      this.selectedTabIndex = 0,
      this.isPreview = false,
      this.isProduct,
      this.isReview = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    String information = prefs.getString(shopInfo) ?? "";
    final appLocal = AppLocalizations.of(context)!;

    // Add skeleton loading state when shop is null and not in review mode
    if (shop == null && !isReview) {
      return Column(
        children: [
          // Banner skeleton
          Shimmer.fromColors(
            baseColor: AppColors.headerColorDark.withOpacity(0.5),
            highlightColor: AppColors.colorPrimary.withOpacity(0.5),
            child: Container(
              height: 183.h,
              width: MediaQuery.of(context).size.width,
              color: Colors.white,
            ),
          ),

          Container(
            color: AppColors.headerColorDark,
            padding: EdgeInsets.all(16.r),
            child: Column(
              children: [
                // Shop info skeleton
                Row(
                  children: [
                    // Logo skeleton
                    Shimmer.fromColors(
                      baseColor: AppColors.headerColorDark.withOpacity(0.5),
                      highlightColor: AppColors.colorPrimary.withOpacity(0.5),
                      child: Container(
                        height: 65.h,
                        width: 65.w,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5),
                        ),
                      ),
                    ),
                    SizedBox(width: 16.w),

                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Name skeleton
                          Shimmer.fromColors(
                            baseColor:
                                AppColors.headerColorDark.withOpacity(0.5),
                            highlightColor:
                                AppColors.colorPrimary.withOpacity(0.5),
                            child: Container(
                              height: 20.h,
                              width: 150.w,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                          SizedBox(height: 8.h),

                          // Followers skeleton
                          Shimmer.fromColors(
                            baseColor:
                                AppColors.headerColorDark.withOpacity(0.5),
                            highlightColor:
                                AppColors.colorPrimary.withOpacity(0.5),
                            child: Container(
                              height: 16.h,
                              width: 100.w,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                          SizedBox(height: 8.h),

                          // Slogan skeleton
                          Shimmer.fromColors(
                            baseColor:
                                AppColors.headerColorDark.withOpacity(0.5),
                            highlightColor:
                                AppColors.colorPrimary.withOpacity(0.5),
                            child: Container(
                              height: 16.h,
                              width: 200.w,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 20.h),

                // About shop button skeleton
                Shimmer.fromColors(
                  baseColor: AppColors.headerColorDark.withOpacity(0.5),
                  highlightColor: AppColors.colorPrimary.withOpacity(0.5),
                  child: Container(
                    height: 45.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),

                SizedBox(height: 20.h),

                // Add product button skeleton
                Shimmer.fromColors(
                  baseColor: AppColors.headerColorDark.withOpacity(0.5),
                  highlightColor: AppColors.colorPrimary.withOpacity(0.5),
                  child: Container(
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),

                SizedBox(height: 20.h),

                // Tab bar skeleton
                Shimmer.fromColors(
                  baseColor: AppColors.headerColorDark.withOpacity(0.5),
                  highlightColor: AppColors.colorPrimary.withOpacity(0.5),
                  child: Container(
                    height: 30.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    // For review mode, wrap content in SingleChildScrollView
    if (isReview) {
      return SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Column(
          children: [
            // Banner with edit buttons
            Stack(
              children: [
                SizedBox(
                  height: 220.h,
                  width: double.infinity,
                  child: GlobalMethods.netWorkImage(
                    controller.bannerUrl,
                    BorderRadius.zero,
                    BoxFit.cover,
                  ),
                ),
                // Edit buttons if needed
              ],
            ),

            // Shop info card
            Container(
              margin: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 8.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.05),
                  ],
                  stops: const [0.1, 0.9],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 15,
                    spreadRadius: -8,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      children: [
                        // Shop Info
                        Padding(
                          padding: EdgeInsets.all(16.w),
                          child: Row(
                            children: [
                              // Shop Logo
                              Container(
                                height: 56.h,
                                width: 56.w,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: GlobalMethods.netWorkImage(
                                    isReview
                                        ? controller.logoUrl
                                        : shop?.shopLogo ?? "",
                                    BorderRadius.zero,
                                    BoxFit.cover,
                                  ),
                                ),
                              ),
                              SizedBox(width: 16.w),
                              // Shop Info
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      isReview
                                          ? controller.storeName
                                          : shop?.shopName ?? "",
                                      style: FontStyles.fontBold(fontSize: 16),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      isReview
                                          ? controller.slogan
                                          : shop?.shopSlogan ?? "",
                                      style: FontStyles.fontMedium(
                                        fontSize: 13,
                                        color: Colors.white.withOpacity(0.7),
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                              // Share Button
                              Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: () async {
                                    String url =
                                        "https://elbaab-user.app.link/a2B98CVM4vb?uri_redirect_mode=shareshop&shop_id=${shop?.id ?? ""}&shop_name=${shop?.shopName ?? ""}";
                                    await FlutterShare.share(
                                      title: shop?.shopName ?? "",
                                      text: "Check my shop",
                                      chooserTitle: shop?.shopName ?? "",
                                      linkUrl: url,
                                    );
                                  },
                                  borderRadius: BorderRadius.circular(8),
                                  child: Container(
                                    padding: EdgeInsets.all(8.w),
                                    decoration: BoxDecoration(
                                      color: AppColors.colorPrimary
                                          .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.share_rounded,
                                      color: AppColors.colorPrimary,
                                      size: 20,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Stats Row with divider
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          decoration: BoxDecoration(
                            border: Border(
                              top: BorderSide(
                                color: Colors.white.withOpacity(0.1),
                              ),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildCompactStatItem(
                                icon: Icons.people_alt_outlined,
                                value: "${shop?.followerCount ?? 0}",
                                label: 'Followers',
                              ),
                              Container(
                                height: 24.h,
                                width: 1,
                                color: Colors.white.withOpacity(0.1),
                              ),
                              _buildCompactStatItem(
                                icon: Icons.remove_red_eye_outlined,
                                value: '${viewsCount?.value ?? 0}',
                                label: 'Views',
                              ),
                              Container(
                                height: 24.h,
                                width: 1,
                                color: Colors.white.withOpacity(0.1),
                              ),
                              _buildCompactStatItem(
                                icon: Icons.star_outline_rounded,
                                value: '${shop?.shopRate ?? 0}',
                                label: 'Rating',
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // Description section
            if (getDescription() != null)
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w),
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: AppColors.headerColorDark,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appLocal.description,
                      style: FontStyles.fontMedium(fontSize: 14),
                    ),
                    SizedBox(height: 8.h),
                    if (getDescription() != null) getDescription()!,
                  ],
                ),
              ),

            // Target Price section
            if (getTargetPrice() != null)
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                width: MediaQuery.of(context).size.width,
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: AppColors.headerColorDark,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appLocal.targetPrice,
                      style: FontStyles.fontMedium(fontSize: 14),
                    ),
                    SizedBox(height: 8.h),
                    if (getTargetPrice() != null) getTargetPrice()!,
                  ],
                ),
              ),

            // Terms section
            if (getTerms() != null)
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w),
                width: MediaQuery.of(context).size.width,
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: AppColors.headerColorDark,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appLocal.termsAndCondition,
                      style: FontStyles.fontMedium(fontSize: 14),
                    ),
                    SizedBox(height: 8.h),
                    if (getTerms() != null) getTerms()!,
                  ],
                ),
              ),

            SizedBox(height: 20.h), // Bottom padding
          ],
        ),
      );
    } else {
      // Return existing CustomScrollView for non-review mode
      return CustomScrollView(
        physics: const NeverScrollableScrollPhysics(),
        slivers: [
          // Animated Sliver App Bar with Shop Banner
          SliverAppBar(
            expandedHeight: 180.h,
            pinned: true,
            backgroundColor: AppColors.headerColorDark,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // Banner Image
                  GlobalMethods.netWorkImage(
                    shop?.shopBanner ?? "",
                    BorderRadius.zero,
                    BoxFit.cover,
                  ),
                  // Gradient Overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.4),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Content Section
          SliverToBoxAdapter(
            child: Column(
              children: [
                // Shop Info Section
                Container(
                  margin: EdgeInsets.fromLTRB(16.w, 10.h, 16.w, 8.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withOpacity(0.2),
                        Colors.white.withOpacity(0.05),
                      ],
                      stops: const [0.1, 0.9],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 15,
                        spreadRadius: -8,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.white.withOpacity(0.2),
                          ),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Column(
                          children: [
                            // Shop Info
                            Padding(
                              padding: EdgeInsets.all(16.w),
                              child: Row(
                                children: [
                                  // Shop Logo
                                  Container(
                                    height: 56.h,
                                    width: 56.w,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.2),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: GlobalMethods.netWorkImage(
                                        shop?.shopLogo ?? "",
                                        BorderRadius.zero,
                                        BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 16.w),
                                  // Shop Info
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          shop?.shopName ?? "",
                                          style:
                                              FontStyles.fontBold(fontSize: 16),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        SizedBox(height: 4.h),
                                        Text(
                                          shop?.shopSlogan ?? "",
                                          style: FontStyles.fontMedium(
                                            fontSize: 13,
                                            color:
                                                Colors.white.withOpacity(0.7),
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        SizedBox(height: 8.h),
                                        // About Shop Button
                                        GestureDetector(
                                          onTap: () {
                                            if (shop == null) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(SnackBar(
                                                content: Text(
                                                  appLocal.waitWhileLoadShop,
                                                  style:
                                                      FontStyles.fontMedium(),
                                                ),
                                                duration:
                                                    const Duration(seconds: 2),
                                                backgroundColor:
                                                    AppColors.headerColorDark,
                                              ));
                                            } else {
                                              Get.toNamed(
                                                  RouteNames.shopAboutScreen,
                                                  arguments: [shop]);
                                            }
                                          },
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                              horizontal: 12.w,
                                              vertical: 6.h,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.white
                                                  .withOpacity(0.05),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                SvgPicture.string(
                                                  SvgStrings.iconAbout,
                                                  color: Colors.white,
                                                  height: 14,
                                                ),
                                                SizedBox(width: 6.w),
                                                Text(
                                                  appLocal.aboutTheShop,
                                                  style: FontStyles.fontMedium(
                                                    fontSize: 12,
                                                    color: Colors.white
                                                        .withOpacity(0.9),
                                                  ),
                                                ),
                                                SizedBox(width: 6.w),
                                                Icon(
                                                  Icons
                                                      .arrow_forward_ios_rounded,
                                                  color: Colors.white
                                                      .withOpacity(0.5),
                                                  size: 13,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // Share Button
                                  Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      onTap: () async {
                                        String url =
                                            "https://elbaab-user.app.link/a2B98CVM4vb?uri_redirect_mode=shareshop&shop_id=${shop?.id ?? ""}&shop_name=${shop?.shopName ?? ""}";
                                        await FlutterShare.share(
                                          title: shop?.shopName ?? "",
                                          text: "Check my shop",
                                          chooserTitle: shop?.shopName ?? "",
                                          linkUrl: url,
                                        );
                                      },
                                      borderRadius: BorderRadius.circular(8),
                                      child: Container(
                                        padding: EdgeInsets.all(8.w),
                                        decoration: BoxDecoration(
                                          color: AppColors.colorPrimary
                                              .withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Icon(
                                          Icons.share_rounded,
                                          color: AppColors.colorPrimary,
                                          size: 20,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Stats Row with divider
                            Container(
                              padding: EdgeInsets.symmetric(vertical: 12.h),
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                    color: Colors.white.withOpacity(0.1),
                                  ),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildCompactStatItem(
                                    icon: Icons.people_alt_outlined,
                                    value: "${shop?.followerCount ?? 0}",
                                    label: 'Followers',
                                  ),
                                  Container(
                                    height: 24.h,
                                    width: 1,
                                    color: Colors.white.withOpacity(0.1),
                                  ),
                                  _buildCompactStatItem(
                                    icon: Icons.remove_red_eye_outlined,
                                    value: '${viewsCount?.value ?? 0}',
                                    label: 'Views',
                                  ),
                                  Container(
                                    height: 24.h,
                                    width: 1,
                                    color: Colors.white.withOpacity(0.1),
                                  ),
                                  _buildCompactStatItem(
                                    icon: Icons.star_outline_rounded,
                                    value: '${shop?.shopRate ?? 0}',
                                    label: 'Rating',
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                // Free Delivery Target Message
                if ((shop?.freeDeliveryTarget ?? false) && !isReview)
                  GestureDetector(
                    onTap: () => BottomSheets.showAlertMessageBottomSheet(
                      appLocal.freeDeliveryTargetMessage,
                      appLocal.freeDeliveryTarget,
                      context,
                    ),
                    child: Container(
                      margin:
                          EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
                      padding: EdgeInsets.symmetric(
                          horizontal: 14.w, vertical: 10.h),
                      decoration: BoxDecoration(
                        color: isPreview
                            ? Colors.white.withOpacity(0.05)
                            : AppColors.colormossGreen.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isPreview
                              ? Colors.white.withOpacity(0.1)
                              : AppColors.colormossGreen.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.local_shipping_outlined,
                            size: 18,
                            color: isPreview
                                ? Colors.white
                                : AppColors.colormossGreen,
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            child: Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: '${appLocal.freeDeliveryTarget} ',
                                    style: FontStyles.fontRegular(
                                      fontSize: 13,
                                      color: isPreview
                                          ? Colors.white
                                          : AppColors.colormossGreen,
                                    ),
                                  ),
                                  TextSpan(
                                    text: (shop?.targetPriceForFdt ?? 0)
                                        .toString(),
                                    style: FontStyles.fontBold(
                                      fontSize: 13,
                                      color: isPreview
                                          ? Colors.white
                                          : AppColors.colormossGreen,
                                    ),
                                  ),
                                  TextSpan(
                                    text: ' ${appLocal.aed}',
                                    style: FontStyles.fontRegular(
                                      fontSize: 13,
                                      color: isPreview
                                          ? Colors.white
                                          : AppColors.colormossGreen,
                                    ),
                                  ),
                                ],
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Icon(
                            Icons.info_outline,
                            color: isPreview
                                ? Colors.white
                                : AppColors.colormossGreen,
                            size: 18,
                          ),
                        ],
                      ),
                    ),
                  ),

                Container(
                  margin: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 8.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withOpacity(0.1),
                        Colors.white.withOpacity(0.05),
                      ],
                      stops: const [0.1, 0.9],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 15,
                        spreadRadius: -8,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Container(
                        padding: EdgeInsets.all(12.r),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.white.withOpacity(0.1),
                              AppColors.headerColorDark.withOpacity(0.2),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border:
                              Border.all(color: Colors.white.withOpacity(0.1)),
                        ),
                        child: GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 3,
                          mainAxisSpacing: 12.h,
                          crossAxisSpacing: 12.w,
                          childAspectRatio: 1.2,
                          children: [
                            _buildGridItem(
                              label: "Products",
                              count: acceptedProductCount?.value ?? 0,
                              onTap: () => tabIndex(0),
                              isSelected: selectedTabIndex == 0,
                              svgString: SvgStrings.svgProducts,
                            ),
                            _buildGridItem(
                              label: appLocal.pending,
                              count: pendingProductCount?.value ?? 0,
                              onTap: () => tabIndex(1),
                              isSelected: selectedTabIndex == 1,
                              svgString: SvgStrings.svgPending,
                            ),
                            _buildGridItem(
                              label: "Draft",
                              count: draftProductCount?.value ?? 0,
                              onTap: () => tabIndex(2),
                              isSelected: selectedTabIndex == 2,
                              svgString: SvgStrings.svgDraft,
                            ),
                            _buildGridItem(
                              label: "Qty Alert",
                              count: qtyAlertProductCount?.value ?? 0,
                              onTap: () => tabIndex(3),
                              isSelected: selectedTabIndex == 3,
                              svgString: SvgStrings.svgQtyAlert,
                            ),
                            _buildGridItem(
                              label: "Out Of Stock",
                              count: outOfStockProductCount?.value ?? 0,
                              onTap: () => tabIndex(4),
                              isSelected: selectedTabIndex == 4,
                              svgString: SvgStrings.svgOutOfStock,
                            ),
                            _buildAddProductItem(
                              appLocal: appLocal,
                              onTap: () =>
                                  Get.toNamed(RouteNames.similarProductsScreen),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // // Add Product Button
                // Container(
                //   margin: EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 8.h),
                //   child: Material(
                //     color: AppColors.colorPrimary,
                //     borderRadius: BorderRadius.circular(12),
                //     child: InkWell(
                //       onTap: () =>
                //           Get.toNamed(RouteNames.similarProductsScreen),
                //       borderRadius: BorderRadius.circular(12),
                //       child: Container(
                //         padding: EdgeInsets.symmetric(vertical: 12.h),
                //         child: Row(
                //           mainAxisAlignment: MainAxisAlignment.center,
                //           children: [
                //             Icon(
                //               Icons.add_circle_outline,
                //               color: Colors.white,
                //               size: 20,
                //             ),
                //             SizedBox(width: 8.w),
                //             Text(
                //               appLocal.addNewProduct,
                //               style: FontStyles.fontMedium(
                //                 fontSize: 14,
                //                 color: Colors.white,
                //               ),
                //             ),
                //           ],
                //         ),
                //       ),
                //     ),
                //   ),
                // ),

                // SizedBox(height: 8.h),

                // // Category Tabs
                // Container(
                //   margin: EdgeInsets.symmetric(horizontal: 16.w),
                //   child: ClipRRect(
                //     borderRadius: BorderRadius.circular(16),
                //     child: BackdropFilter(
                //       filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                //       child: Container(
                //         padding: EdgeInsets.symmetric(
                //             vertical: 3.h, horizontal: 3.w),
                //         decoration: BoxDecoration(
                //           gradient: LinearGradient(
                //             colors: [
                //               Colors.white.withOpacity(0.1),
                //               Colors.white.withOpacity(0.05),
                //             ],
                //           ),
                //           borderRadius: BorderRadius.circular(16),
                //           border:
                //               Border.all(color: Colors.white.withOpacity(0.1)),
                //         ),
                //         child: SizedBox(
                //           height: 40.h,
                //           child: DefaultTabController(
                //             initialIndex: selectedTabIndex,
                //             length: 5,
                //             child: Obx(
                //               () => TabBar(
                //                 controller: tabController,
                //                 indicator: BoxDecoration(
                //                   gradient: LinearGradient(
                //                     begin: Alignment.topLeft,
                //                     end: Alignment.bottomRight,
                //                     colors: [
                //                       AppColors.colorPrimary,
                //                       AppColors.colorPrimary.withOpacity(0.8),
                //                     ],
                //                   ),
                //                   borderRadius: BorderRadius.circular(12),
                //                   boxShadow: [
                //                     BoxShadow(
                //                       color: AppColors.colorPrimary
                //                           .withOpacity(0.3),
                //                       blurRadius: 8,
                //                       offset: const Offset(0, 2),
                //                     ),
                //                   ],
                //                 ),
                //                 indicatorSize: TabBarIndicatorSize.tab,
                //                 indicatorPadding: EdgeInsets.all(4.r),
                //                 labelColor: Colors.white,
                //                 tabAlignment: TabAlignment.start,
                //                 unselectedLabelColor:
                //                     Colors.white.withOpacity(0.5),
                //                 labelStyle:
                //                     FontStyles.fontSemibold(fontSize: 13),
                //                 unselectedLabelStyle:
                //                     FontStyles.fontRegular(fontSize: 13),
                //                 isScrollable: true,
                //                 padding: EdgeInsets.zero,
                //                 labelPadding: EdgeInsets.zero,
                //                 dividerColor: Colors.transparent,
                //                 onTap: (v) => {
                //                   if (selectedTabIndex != v)
                //                     {
                //                       tabIndex(v),
                //                     },
                //                 },
                //                 tabs: [
                //                   _buildNeumorphicTab(
                //                       appLocal
                //                           .productTab("$acceptedProductCount"),
                //                       0),
                //                   _buildNeumorphicTab(
                //                       appLocal
                //                           .pendingTab("$pendingProductCount"),
                //                       1),
                //                   _buildNeumorphicTab(
                //                       appLocal.draftTab("$draftProductCount"),
                //                       2),
                //                   _buildNeumorphicTab(
                //                       appLocal
                //                           .qtyAlertTab("$qtyAlertProductCount"),
                //                       3),
                //                   _buildNeumorphicTab(
                //                       appLocal.outOfStocTab(
                //                           "$outOfStockProductCount"),
                //                       4),
                //                 ],
                //               ),
                //             ),
                //           ),
                //         ),
                //       ),
                //     ),
                //   ),
                // ),

                // Review Description, Target Price, and Terms sections
                // if (isReview && getDescription() != null)
                //   Container(
                //     margin: EdgeInsets.symmetric(horizontal: 16.w),
                //     padding: EdgeInsets.all(12.r),
                //     decoration: BoxDecoration(
                //       color: AppColors.headerColorDark,
                //       borderRadius: BorderRadius.circular(16),
                //       border: Border.all(color: Colors.white.withOpacity(0.1)),
                //     ),
                //     child: Column(
                //       crossAxisAlignment: CrossAxisAlignment.start,
                //       children: [
                //         Text(
                //           appLocal.description,
                //           style: FontStyles.fontMedium(fontSize: 14),
                //         ),
                //         SizedBox(height: 8.h),
                //         if (getDescription() != null) getDescription()!,
                //       ],
                //     ),
                //   ),

                // if (isReview && getTargetPrice() != null)
                //   Container(
                //     margin:
                //         EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                //     padding: EdgeInsets.all(12.r),
                //     decoration: BoxDecoration(
                //       color: AppColors.headerColorDark,
                //       borderRadius: BorderRadius.circular(16),
                //       border: Border.all(color: Colors.white.withOpacity(0.1)),
                //     ),
                //     child: Column(
                //       crossAxisAlignment: CrossAxisAlignment.start,
                //       children: [
                //         Text(
                //           appLocal.targetPrice,
                //           style: FontStyles.fontMedium(fontSize: 14),
                //         ),
                //         SizedBox(height: 8.h),
                //         if (getTargetPrice() != null) getTargetPrice()!,
                //       ],
                //     ),
                //   ),

                // if (isReview && getTerms() != null)
                //   Container(
                //     margin: EdgeInsets.symmetric(horizontal: 16.w),
                //     padding: EdgeInsets.all(12.r),
                //     decoration: BoxDecoration(
                //       color: AppColors.headerColorDark,
                //       borderRadius: BorderRadius.circular(16),
                //       border: Border.all(color: Colors.white.withOpacity(0.1)),
                //     ),
                //     child: Column(
                //       crossAxisAlignment: CrossAxisAlignment.start,
                //       children: [
                //         Text(
                //           appLocal.termsAndCondition,
                //           style: FontStyles.fontMedium(fontSize: 14),
                //         ),
                //         SizedBox(height: 8.h),
                //         if (getTerms() != null) getTerms()!,
                //       ],
                //     ),
                //   ),
              ],
            ),
          ),
        ],
      );
    }
  }

  Widget _buildGridItem({
    required String label,
    required int count,
    required VoidCallback onTap,
    required bool isSelected,
    required String svgString,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 8.w),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.colorPrimary.withOpacity(0.1)
              : Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? AppColors.colorPrimary.withOpacity(0.3)
                : Colors.white.withOpacity(0.1),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.string(
                  svgString,
                  height: 16.h,
                  colorFilter: ColorFilter.mode(
                    isSelected
                        ? AppColors.colorPrimary
                        : Colors.white.withOpacity(0.7),
                    BlendMode.srcIn,
                  ),
                ),
                SizedBox(width: 4.w),
                Text(
                  count.toString(),
                  style: FontStyles.fontBold(
                    fontSize: 18,
                    color: isSelected ? AppColors.colorPrimary : Colors.white,
                  ),
                ),
              ],
            ),
            SizedBox(height: 4.h),
            Text(
              label,
              style: isSelected
                  ? FontStyles.fontSemibold(
                      fontSize: 11,
                      color: AppColors.colorPrimary,
                    )
                  : FontStyles.fontRegular(
                      fontSize: 11,
                      color: Colors.white.withOpacity(0.7),
                    ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddProductItem({
    required AppLocalizations appLocal,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.colorSecondary.withOpacity(0.15),
              AppColors.colorSecondary.withOpacity(0.05),
            ],
            stops: const [0.2, 0.9],
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppColors.colorSecondary.withOpacity(0.2),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.colorSecondary.withOpacity(0.1),
              blurRadius: 12,
              spreadRadius: -4,
            ),
          ],
        ),
        child: Stack(
          children: [
            // Background pattern
            Positioned(
              right: -15,
              top: -15,
              child: Container(
                height: 50,
                width: 50,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.colorSecondary.withOpacity(0.1),
                    width: 2,
                  ),
                ),
              ),
            ),
            // Content
            Positioned(
              left: 10,
              top: 10,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.all(5.r),
                    decoration: BoxDecoration(
                      color: AppColors.colorSecondary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.add_rounded,
                      color: AppColors.colorSecondary,
                      size: 22,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    "Add Product",
                    style: FontStyles.fontMedium(
                      fontSize: 12,
                      color: AppColors.colorSecondary,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget getSlogan() {
    String information = prefs.getString(shopInfo) ?? "";
    if (controller.shop != null) {
      if (information.isNotEmpty) {
        Map<String, dynamic> info = jsonDecode(information);
        String cacheSlogan = info["slogan"] ?? "";
        String slogan = controller.shop?.shopSlogan ?? "";
        if (cacheSlogan.isNotEmpty && slogan != cacheSlogan) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                slogan.isEmpty ? "Slogan Not Provided" : slogan,
                style: FontStyles.fontRegular(
                    fontSize: 15,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                "$cacheSlogan\n",
                style: FontStyles.fontRegular(
                  fontSize: 15,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else if (cacheSlogan.isEmpty && slogan.isNotEmpty) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                slogan,
                style: FontStyles.fontRegular(
                    fontSize: 15,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                "You removed slogan from information",
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else {
          return Text(
            "${controller.shop?.shopSlogan ?? ""}\n\n",
            style: FontStyles.fontRegular(
              fontSize: 15,
              color: Colors.white.withOpacity(0.5),
            ),
          );
        }
      } else {
        return Text(
          "${controller.shop?.shopSlogan ?? ""}\n\n",
          style: FontStyles.fontRegular(
            fontSize: 15,
            color: Colors.white.withOpacity(0.5),
          ),
        );
      }
    } else {
      return Text(
        "${controller.slogan}\n\n",
        style: FontStyles.fontRegular(
          fontSize: 15,
          color: Colors.white.withOpacity(0.5),
        ),
      );
    }
  }

  Widget? getDescription() {
    String information = prefs.getString(shopInfo) ?? "";
    if (controller.shop != null) {
      if (information.isNotEmpty) {
        Map<String, dynamic> info = jsonDecode(information);
        String cacheDescription = info["description"] ?? "";
        String description = controller.shop?.shopDescription ?? "";
        if (cacheDescription.isNotEmpty && description != cacheDescription) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                description.isEmpty ? "Description Not Provided" : description,
                style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                cacheDescription,
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else if (cacheDescription.isEmpty && description.isNotEmpty) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                description,
                style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                "You removed description from information",
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else {
          return Text(
            (controller.shop?.shopDescription ?? "").isEmpty
                ? "Description Not Provided"
                : controller.shop?.shopDescription ?? "",
            style: FontStyles.fontRegular(
              fontSize: 12,
              color: Colors.white.withOpacity(0.5),
            ),
          );
        }
      } else {
        return Text(
          (controller.shop?.shopDescription ?? "").isEmpty
              ? "Description Not Provided"
              : controller.shop?.shopDescription ?? "",
          style: FontStyles.fontRegular(
            fontSize: 12,
            color: Colors.white.withOpacity(0.5),
          ),
        );
      }
    } else {
      if (controller.description.isNotEmpty) {
        return Text(
          controller.description,
          style: FontStyles.fontRegular(
            fontSize: 12,
            color: Colors.white.withOpacity(0.5),
          ),
        );
      } else {
        return null;
      }
    }
  }

  Widget? getTerms() {
    String information = prefs.getString(shopInfo) ?? "";
    log("information: $information");
    if (controller.shop != null) {
      log("controller.shop: ${controller.shop?.shopTermsAndConditions}");
      if (information.isNotEmpty) {
        log("information.isNotEmpty");
        Map<String, dynamic> info = jsonDecode(information);
        String cacheTerms = info["terms"] ?? "";
        String shopTermsAndConditions =
            controller.shop?.shopTermsAndConditions ?? "";
        log("shopTermsAndConditions: $shopTermsAndConditions");
        log("cacheTerms: $cacheTerms");
        if ((shopTermsAndConditions.isNotEmpty || cacheTerms.isNotEmpty) &&
            controller.isRemoveTerms.value) {
          return Text(
            "You removed terms and conditions from information",
            style: FontStyles.fontRegular(
              fontSize: 12,
              color: AppColors.colorDanger,
            ),
          );
        } else {
          if (cacheTerms.isNotEmpty && shopTermsAndConditions != cacheTerms) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  shopTermsAndConditions.isEmpty
                      ? "Terms & Condition Not Provided"
                      : shopTermsAndConditions
                          .substring(shopTermsAndConditions.length - 50),
                  style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
                InkWell(
                  onTap: () => GlobalMethods.launchInWebView(cacheTerms),
                  child: Text(
                    cacheTerms.split("/").last,
                    style: FontStyles.fontRegular(
                      fontSize: 12,
                      decoration: TextDecoration.underline,
                      color: AppColors.colorSecondary,
                    ),
                  ),
                ),
              ],
            );
          } else if (cacheTerms.isEmpty) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (shopTermsAndConditions.isNotEmpty)
                  Text(
                    shopTermsAndConditions.split("/").last,
                    style: FontStyles.fontRegular(
                      fontSize: 12,
                      color: AppColors.colorSecondary_Red,
                      decoration: TextDecoration.lineThrough,
                    ),
                  ),
                Text(
                  shopTermsAndConditions.isNotEmpty
                      ? "You removed terms & condition from information"
                      : "Terms & Condition Not Provided",
                  style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: shopTermsAndConditions.isNotEmpty
                        ? AppColors.colorSecondary
                        : Colors.white.withOpacity(0.5),
                  ),
                ),
              ],
            );
          }
        }
      }
      return InkWell(
        onTap: () => GlobalMethods.launchInWebView(
            controller.shop?.shopTermsAndConditions ?? ""),
        child: Text(
          (controller.shop?.shopTermsAndConditions ?? "").isEmpty
              ? "Terms & Condition Not Provided"
              : (controller.shop?.shopTermsAndConditions ?? "").split("/").last,
          style: FontStyles.fontRegular(
            fontSize: 12,
            decoration: TextDecoration.underline,
            color: Colors.white.withOpacity(0.5),
          ),
        ),
      );
    } else if (controller.termsUrl.isNotEmpty) {
      return InkWell(
        onTap: () => GlobalMethods.launchInWebView(controller.termsUrl),
        child: Text(
          controller.termsUrl.split("/").last,
          style: FontStyles.fontRegular(
            fontSize: 12,
            decoration: TextDecoration.underline,
            color: Colors.white.withOpacity(0.5),
          ),
        ),
      );
    }
    return null;
  }

  Widget? getTargetPrice() {
    String information = prefs.getString(shopInfo) ?? "";
    if (controller.shop != null) {
      if (information.isNotEmpty) {
        Map<String, dynamic> info = jsonDecode(information);
        String cacheTargetPrice = info["targetPrice"] ?? "0";
        bool freedelivery = info["freeDelivery"] ?? false;
        String targetPriceForFdt =
            (controller.shop?.targetPriceForFdt ?? 0).toString();
        bool freeDeliveryTarget = controller.shop?.freeDeliveryTarget ?? false;

        if (!freedelivery && freeDeliveryTarget) {
          return Text(
            "You disabled free delivery from information",
            style: FontStyles.fontRegular(
              fontSize: 12,
              color: AppColors.colorSecondary_Red,
            ),
          );
        } else if (cacheTargetPrice.isNotEmpty &&
            targetPriceForFdt != cacheTargetPrice) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "$targetPriceForFdt AED",
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary_Red,
                  decoration: TextDecoration.lineThrough,
                ),
              ),
              Text(
                "$cacheTargetPrice AED",
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else if (cacheTargetPrice.isEmpty && targetPriceForFdt != "0") {
          return Text(
            "You removed Target Price from information",
            style: FontStyles.fontRegular(
              fontSize: 12,
              color: AppColors.colorSecondary,
            ),
          );
        }
      }
      return !(controller.shop?.freeDeliveryTarget ?? false)
          ? null
          : Text(
              ((controller.shop?.targetPriceForFdt ?? 0).toString()) != "0"
                  ? "Target Price Not Provided"
                  : "${controller.shop?.targetPriceForFdt ?? 0} AED",
              style: FontStyles.fontRegular(
                fontSize: 12,
                color: Colors.white.withOpacity(0.5),
              ),
            );
    } else if (controller.targetPrice.isNotEmpty) {
      return Text(
        "${controller.targetPrice} AED",
        style: FontStyles.fontRegular(
          fontSize: 12,
          color: Colors.white.withOpacity(0.5),
        ),
      );
    }
    return null;
  }

  // Helper method for compact stats
  Widget _buildCompactStatItem({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: AppColors.colorPrimary,
              size: 14,
            ),
            SizedBox(width: 4.w),
            Text(
              value,
              style: FontStyles.fontBold(fontSize: 13),
            ),
          ],
        ),
        SizedBox(height: 2.h),
        Text(
          label,
          style: FontStyles.fontRegular(
            fontSize: 11,
            color: Colors.white.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildVariantSelector({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? AppColors.colorPrimary : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Center(
          child: Text(
            title,
            style: FontStyles.fontMedium(
              fontSize: 14,
              color: isSelected ? Colors.white : Colors.white.withOpacity(0.6),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNeumorphicTab(String text, int index) {
    final isSelected = tabController?.index == index;

    return Tab(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 4.w),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
            color:
                isSelected ? Colors.transparent : Colors.black.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? Colors.white.withOpacity(0.2)
                  : Colors.white.withOpacity(0.05),
            ),
            boxShadow: isSelected
                ? []
                : [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              if (isSelected)
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0.1),
                        Colors.white.withOpacity(0.05),
                      ],
                    ),
                  ),
                ),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 8.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      text,
                      style: TextStyle(
                        fontSize: 13.sp,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w400,
                        letterSpacing: 0.3,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
