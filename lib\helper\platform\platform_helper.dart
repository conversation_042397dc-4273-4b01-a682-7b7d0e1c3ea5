import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';

/// Platform detection and adaptation utilities for cross-platform development
class PlatformHelper {
  // Platform detection
  static bool get isWeb => kIsWeb;
  static bool get isMobile => !kIsWeb && (Platform.isIOS || Platform.isAndroid);
  static bool get isDesktop => !kIsWeb && (Platform.isWindows || Platform.isMacOS || Platform.isLinux);
  static bool get isWindows => !kIsWeb && Platform.isWindows;
  static bool get isMacOS => !kIsWeb && Platform.isMacOS;
  static bool get isLinux => !kIsWeb && Platform.isLinux;
  static bool get isIOS => !kIsWeb && Platform.isIOS;
  static bool get isAndroid => !kIsWeb && Platform.isAndroid;

  // Feature availability checks
  static bool get hasCameraSupport => isMobile;
  static bool get hasLocationSupport => isMobile;
  static bool get hasBiometricSupport => isMobile;
  static bool get hasSMSSupport => isMobile;
  static bool get hasNativeMapSupport => isMobile;
  static bool get hasPhotoGallerySupport => isMobile;
  static bool get hasAppSettingsSupport => isMobile;
  static bool get hasScannerSupport => isMobile;
  static bool get hasNativeShareSupport => isMobile;

  // Desktop-specific features
  static bool get hasWindowManagement => isDesktop;
  static bool get hasFileDropSupport => isDesktop;
  static bool get hasAdvancedFileSelection => isDesktop;

  // UI Adaptations
  static double getResponsiveWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (isDesktop) {
      return screenWidth > 1200 ? 1200 : screenWidth * 0.9;
    }
    return screenWidth;
  }

  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isDesktop) {
      return const EdgeInsets.symmetric(horizontal: 32, vertical: 24);
    }
    return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
  }

  static double getResponsiveFontSize(double baseFontSize) {
    if (isDesktop) {
      return baseFontSize * 1.1; // Slightly larger fonts for desktop
    }
    return baseFontSize;
  }

  // Navigation adaptations
  static bool get shouldUseDrawer => isMobile;
  static bool get shouldUseNavigationRail => isDesktop;
  static bool get shouldUseTabBar => isDesktop;

  // Input adaptations
  static TextInputType getKeyboardType(TextInputType mobileType) {
    if (isDesktop) {
      // Desktop keyboards don't need special input types
      return TextInputType.text;
    }
    return mobileType;
  }

  // Window size constraints for desktop
  static Size get minWindowSize => const Size(800, 600);
  static Size get defaultWindowSize => const Size(1200, 800);
  static Size get maxWindowSize => const Size(1920, 1080);

  // Platform-specific colors and themes
  static Color getPlatformAccentColor() {
    if (isMacOS) {
      return Colors.blue;
    } else if (isWindows) {
      return Colors.blue.shade700;
    } else if (isLinux) {
      return Colors.orange;
    }
    return Colors.blue; // Default for mobile
  }

  // Error handling for unsupported features
  static void showUnsupportedFeatureDialog(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Feature Not Available'),
        content: Text('$feature is not supported on this platform.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Safe feature execution
  static Future<T?> executeIfSupported<T>(
    bool isSupported,
    Future<T> Function() action, {
    T? fallback,
  }) async {
    if (isSupported) {
      try {
        return await action();
      } catch (e) {
        debugPrint('Platform feature execution failed: $e');
        return fallback;
      }
    }
    return fallback;
  }

  // Platform-specific app bar configuration
  static PreferredSizeWidget? getAppBar(BuildContext context, String title) {
    if (isDesktop) {
      return AppBar(
        title: Text(title),
        centerTitle: false,
        elevation: 0,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        foregroundColor: Theme.of(context).textTheme.bodyLarge?.color,
      );
    }
    return AppBar(
      title: Text(title),
      centerTitle: true,
    );
  }

  // Platform-specific scroll behavior
  static ScrollBehavior getScrollBehavior() {
    if (isDesktop) {
      return const MaterialScrollBehavior().copyWith(
        dragDevices: {
          PointerDeviceKind.mouse,
          PointerDeviceKind.touch,
          PointerDeviceKind.stylus,
          PointerDeviceKind.unknown,
        },
      );
    }
    return const MaterialScrollBehavior();
  }
}
