import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/components/user_unauthrized.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/bottom_navigation/elbaab_shop_bottom_tabs.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/shop_dashboard.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/my_orders.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/profile/profile.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/view_shop.dart';
import 'package:provider/provider.dart';

class ShopHome extends StatefulWidget {
  const ShopHome({Key? key}) : super(key: key);

  @override
  State<ShopHome> createState() => _ShopHomeState();
}

class _ShopHomeState extends State<ShopHome> 
    with AutomaticKeepAliveClientMixin<ShopHome> implements ServerResponse {
  late GraphQlInitilize _request;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _request = GraphQlInitilize(this);
    Future.delayed(Duration.zero, () {
      _request.runQuery(
          context: context,
          query: GraphQlQuries.getTotalNotViewedNotifications,
          isRequiredLoader: false);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final tabPosition = Provider.of<UpdatedInfo>(context).getTabPosition();
    if (userAuthToken.isEmpty) {
      return const UserUnauthrized();
    } else {
      return PopScope(
          canPop: false,
          child:  Scaffold(
            body: PageView.builder(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              onPageChanged: (index) {
                //Provider.of<UpdatedInfo>(context, listen: false).setTabPosition(index);
              },
              itemCount: 4,
              itemBuilder: (context, index) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (_pageController.hasClients && tabPosition != index) {
                    _pageController.jumpToPage(tabPosition);
                  }
                });
                
                switch (index) {
                  case 0:
                    return const ShopDashboard();
                  case 1:
                    return const MyOrders();
                  case 2:
                    return const ViewShop();
                  case 3:
                    return const Profile();
                  default:
                    return const ShopDashboard();
                }
              },
            ),
            bottomNavigationBar: const ElbaabShopBottomTab(),
          ));
    }
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    Provider.of<UpdatedInfo>(Get.context!, listen: false)
        .setNotificationCount(response ?? 0);
  }
  
  @override
  bool get wantKeepAlive => true;
}
