import 'package:flutter/material.dart';
import 'package:overolasuppliers/model/shop/ViewShopModel.dart';

class ShopInfoProvider extends ChangeNotifier {
  ViewShopModel _viewShopModel = ViewShopModel();
  ViewShopModel get getShopInfo => _viewShopModel;

  String _shopUserName = "", _shopName = "";
  String get userName => _shopUserName;
  String get shopName => _shopName;

 setShopUserName(String v) {
    _shopUserName = v;
    notifyListeners();
  }

  setShopName(String v) {
    _shopName = v;
    notifyListeners();
  }

setShopInfo(ViewShopModel result) {
    _viewShopModel = result;
    notifyListeners();
  }
}
