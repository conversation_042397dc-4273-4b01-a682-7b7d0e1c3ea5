import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ElbaabNameWidget extends StatelessWidget {
  const ElbaabNameWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final locale = AppLocalizations.of(context)!;
    return Text(
      locale.appName,
      style: FontStyles.fontRegular(fontSize: 70),
    );
  }
}
