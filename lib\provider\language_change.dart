import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/main.dart';

class LanguageChnangeProvider extends ChangeNotifier {
  Locale _locale = const Locale('en');

  Locale get locale => _locale;

  void changeLanguage(BuildContext context, String languageCode) {
    if (languageCode == 'en') {
      _locale = const Locale('en');
    } else if (languageCode == 'ar') {
      _locale = const Locale('ar'); // Example for Arabic
    }
    prefs.setString("languageCode", languageCode);
    Get.updateLocale(_locale);

    notifyListeners();
  }
}
