class ReturnDurationModel {
  final String? typename;
  final List<ReturnDurations>? returnDurations;
  final int? status;
  final String? message;

  ReturnDurationModel({
    this.typename,
    this.returnDurations,
    this.status,
    this.message,
  });

  ReturnDurationModel.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      returnDurations = (json['returnDurations'] as List?)?.map((dynamic e) => ReturnDurations.fromJson(e as Map<String,dynamic>)).toList(),
      status = json['status'] as int?,
      message = json['message'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'returnDurations' : returnDurations?.map((e) => e.toJson()).toList(),
    'status' : status,
    'message' : message
  };
}

class ReturnDurations {
  final String? typename;
  final String? returnDuration;
  final ReturnDurations? returnDurationsAr;

  ReturnDurations({
    this.typename,
    this.returnDuration,
    this.returnDurationsAr
  });

  ReturnDurations.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      returnDuration = json['returnDuration'] as String?,
      returnDurationsAr = (json['ar'] as Map<String,dynamic>?) != null
          ? ReturnDurations.fromJson(json['ar'] as Map<String,dynamic>)
          : null
      ;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'returnDuration' : returnDuration,
    'ar' : returnDurationsAr
  };
}