import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';
// import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class DateRangePicker extends StatelessWidget {
  final String type;

  DateRangePicker({Key? key, required this.type}) : super(key: key);

  String selectedDates = '';
  final RxString _range = ''.obs, strError = ''.obs;

  final List<String> _list = ['Last 7 Days', 'Last 14 Days', 'Last 30 Days'];
  // void _onSelectionChanged(DateRangePickerSelectionChangedArgs args) {
  //   if (args.value is PickerDateRange) {
  //     _range.value =
  //         '${DateFormat('MMM dd').format(args.value.startDate)} - ${DateFormat('MMM dd').format(args.value.endDate ?? args.value.startDate)}';
  //     selectedDates =
  //         '${DateFormat('dd MMM yy').format(args.value.startDate)} - ${DateFormat('dd MMM yy').format(args.value.endDate ?? args.value.startDate)}';
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: type == 'income' ? 500 : 500,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(20),
            topLeft: Radius.circular(20),
          
          ),
          color: AppColors.backgroundColorDark,
        ),
        child: Column(
          children: <Widget>[
            Container(
              margin: const EdgeInsets.all(10),
              width: 90,
              height: 5,
              decoration: BoxDecoration(
                  color: Colors.grey, borderRadius: BorderRadius.circular(5)),
            ),
            SizedBox(
              height: 50,
              child: Stack(
                alignment: Alignment.center,
                children: <Widget>[
                  Obx(
                    () => Text(
                      _range.value,
                      style: FontStyles.fontRegular(),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 15.0),
                    child: GestureDetector(
                      onTap: () {
                        if (selectedDates.isEmpty) {
                          strError.value =
                              ('Please Select Your Own Date Range');
                        } else {
                          Get.back(result: selectedDates);
                        }
                      },
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          EnStrings.update,
                          style: FontStyles.fontMedium(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            ElbaabNetworkEroor(strError: strError),
            if (type != 'income')
              SizedBox(
                height: 50,
                child: Stack(
                  alignment: Alignment.centerLeft,
                  children: <Widget>[
                    Positioned(
                      left: 0,
                      right: 0,
                      top: 0,
                      child: Container(
                        height: 1,
                        color: Colors.grey,
                      ),
                    ),
                    Positioned(
                      left: 0,
                      right: 0,
                      top: 9,
                      bottom: 9,
                      child: ListView.builder(
                          itemCount: _list.length,
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (context, index) {
                            return Container(
                              height: 31,
                              margin: const EdgeInsets.only(left: 10),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.7),
                                borderRadius: BorderRadius.circular(7),
                              ),
                              child: Padding(
                                padding:
                                    const EdgeInsets.only(left: 10, right: 10),
                                child: Center(
                                  child: Text(
                                    _list[index],
                                    style: FontStyles.fontRegular(fontSize: 11),
                                  ),
                                ),
                              ),
                            );
                          }),
                    ),
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: Container(
                        height: 1,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            // SfDateRangePicker(
            //   onSelectionChanged: _onSelectionChanged,
            //   headerHeight: 50,
            //   rangeSelectionColor: AppColors.colorPrimary.withOpacity(0.7),
            //   todayHighlightColor: AppColors.colorPrimary,
            //   endRangeSelectionColor: Colors.amber,
            //   startRangeSelectionColor: Colors.amber,
            //   headerStyle:
            //       const DateRangePickerHeaderStyle(textAlign: TextAlign.center),
            //   selectionMode: DateRangePickerSelectionMode.extendableRange,
            //   initialSelectedRange: PickerDateRange(
            //       DateTime.now().subtract(const Duration(days: 4)),
            //       DateTime.now().add(const Duration(days: 3))),
            // )
            // DateRangePickerWidget(
            //   initialDateRange: DateRange(
            //       DateTime.now().subtract(const Duration(days: 4)),
            //       DateTime.now()),
            //   // theme: CalendarTheme(
            //   //     selectedColor: AppColors.colorPrimary,
            //   //     inRangeColor: AppColors.colorPrimary.withOpacity(0.7),
            //   //     inRangeTextStyle: FontStyles.fontSemibold(fontSize: 20),
            //   //     selectedTextStyle: FontStyles.fontSemibold(fontSize: 20),
            //   //     todayTextStyle: FontStyles.fontSemibold(fontSize: 20),
            //   //     defaultTextStyle: FontStyles.fontMedium(fontSize: 20),
            //   //     disabledTextStyle: FontStyles.fontMedium(fontSize: 20),
            //   //     radius: 5,
            //   //     tileSize: 20),
            //   doubleMonth: false,
            //   initialDisplayedDate: DateTime.now(),
            //   onDateRangeChanged: print,
            // ),
            CalendarDatePicker2(
              config: CalendarDatePicker2Config(
                selectedDayHighlightColor: AppColors.colorPrimary,
                weekdayLabels: [
                  'Sun',
                  'Mon',
                  'Tue',
                  'Wed',
                  'Thu',
                  'Fri',
                  'Sat'
                ],
                weekdayLabelTextStyle: FontStyles.fontSemibold(),
                firstDayOfWeek: 1,
                controlsHeight: 50,
                controlsTextStyle: FontStyles.fontSemibold(),
                dayTextStyle: FontStyles.fontRegular(),
                selectableDayPredicate: (day) => !day
                    .difference(
                        DateTime.now().subtract(const Duration(days: 3)))
                    .isNegative,
              ),
              value: [DateTime.now()],
              onValueChanged: (dates) => {
                print("dates $dates")
                // setState(() => _singleDatePickerValueWithDefaultValue = dates)
              },
            ),
          ],
        ));
  }
}
