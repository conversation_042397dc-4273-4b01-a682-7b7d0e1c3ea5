import 'package:flutter/material.dart';
import '../../helper/platform/platform_helper.dart';

/// Responsive layout widget that adapts to different screen sizes and platforms
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? web;

  const ResponsiveLayout({
    Key? key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.web,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (PlatformHelper.isWeb && web != null) {
      return web!;
    }

    if (PlatformHelper.isDesktop && desktop != null) {
      return desktop!;
    }

    if (screenWidth >= 768 && tablet != null) {
      return tablet!;
    }

    return mobile;
  }
}

/// Responsive container that adapts its constraints based on platform
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final EdgeInsets? padding;
  final bool centerContent;

  const ResponsiveContainer({
    Key? key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.centerContent = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final responsiveWidth = PlatformHelper.getResponsiveWidth(context);
    final responsivePadding = padding ?? PlatformHelper.getResponsivePadding(context);

    Widget content = Container(
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? responsiveWidth,
      ),
      padding: responsivePadding,
      child: child,
    );

    if (centerContent && PlatformHelper.isDesktop) {
      content = Center(child: content);
    }

    return content;
  }
}

/// Adaptive navigation widget that switches between drawer and navigation rail
class AdaptiveNavigation extends StatelessWidget {
  final List<NavigationItem> items;
  final int selectedIndex;
  final ValueChanged<int> onItemSelected;
  final Widget body;

  const AdaptiveNavigation({
    Key? key,
    required this.items,
    required this.selectedIndex,
    required this.onItemSelected,
    required this.body,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (PlatformHelper.shouldUseNavigationRail) {
      return Row(
        children: [
          NavigationRail(
            selectedIndex: selectedIndex,
            onDestinationSelected: onItemSelected,
            labelType: NavigationRailLabelType.all,
            destinations: items
                .map((item) => NavigationRailDestination(
                      icon: item.icon,
                      selectedIcon: item.selectedIcon ?? item.icon,
                      label: Text(item.label),
                    ))
                .toList(),
          ),
          const VerticalDivider(thickness: 1, width: 1),
          Expanded(child: body),
        ],
      );
    }

    return Scaffold(
      body: body,
      drawer: PlatformHelper.shouldUseDrawer
          ? Drawer(
              child: ListView(
                children: [
                  const DrawerHeader(
                    child: Text('Navigation'),
                  ),
                  ...items.asMap().entries.map((entry) {
                    final index = entry.key;
                    final item = entry.value;
                    return ListTile(
                      leading: item.icon,
                      title: Text(item.label),
                      selected: selectedIndex == index,
                      onTap: () {
                        onItemSelected(index);
                        Navigator.of(context).pop();
                      },
                    );
                  }).toList(),
                ],
              ),
            )
          : null,
      bottomNavigationBar: !PlatformHelper.shouldUseDrawer &&
              !PlatformHelper.shouldUseNavigationRail
          ? BottomNavigationBar(
              currentIndex: selectedIndex,
              onTap: onItemSelected,
              type: BottomNavigationBarType.fixed,
              items: items
                  .map((item) => BottomNavigationBarItem(
                        icon: item.icon,
                        activeIcon: item.selectedIcon ?? item.icon,
                        label: item.label,
                      ))
                  .toList(),
            )
          : null,
    );
  }
}

/// Navigation item data class
class NavigationItem {
  final Widget icon;
  final Widget? selectedIcon;
  final String label;

  const NavigationItem({
    required this.icon,
    this.selectedIcon,
    required this.label,
  });
}

/// Responsive grid that adapts column count based on screen size
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;

  const ResponsiveGrid({
    Key? key,
    required this.children,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 8.0,
    this.mainAxisSpacing = 8.0,
  }) : super(key: key);

  int _getColumnCount(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (PlatformHelper.isDesktop) {
      if (screenWidth > 1200) return 4;
      if (screenWidth > 900) return 3;
      return 2;
    }
    
    if (screenWidth > 600) return 2;
    return 1;
  }

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: _getColumnCount(context),
      childAspectRatio: childAspectRatio,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      children: children,
    );
  }
}

/// Responsive text that adapts font size based on platform
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    Key? key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final baseStyle = style ?? Theme.of(context).textTheme.bodyMedium!;
    final responsiveFontSize = PlatformHelper.getResponsiveFontSize(
      baseStyle.fontSize ?? 14.0,
    );

    return Text(
      text,
      style: baseStyle.copyWith(fontSize: responsiveFontSize),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}
