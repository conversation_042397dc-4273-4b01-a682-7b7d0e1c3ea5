import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class RowTitle extends StatelessWidget {
  final String title;

  const RowTitle({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        title,
        style: FontStyles.fontBold(fontSize: 10),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
        maxLines: 2,
      ),
    );
  }
}
