class EnStrings {
  static const String appName = 'Elbaab';
  static const String your_country = 'Your Country';
  static const String your_language = 'Your Language';
  static const String login = 'Login';
  static const String verifyMobileNumber = 'Verify mobile number';
  static const String signup = 'Signup';
  static const String back = 'Back';
  static const String restrictOption =
      'Adding more option for accepted and matched product is not allowed';
  static const String reset = 'Reset';
  static const String rememberMe = 'Remember Me';
  static const String clearCache = 'Clear Cache';
  static const String pushNotification = 'Manage Notificatios';
  static const String reciveOrderEmails = 'Recive Order Email';
  static const String biometricAccess = 'Biometric Access';
  static const String verifyMobileNumberContent =
      'To verify your mobile number, we\'ve sent a One Time Password (OTP) to your phone';
  static const String addPhotos = 'Add Photos';
  static const String addOption = 'Add Option';
  static const String addedColor = 'ADDED COLORS';
  static const String brand = 'Brand';
  static const String completeSetup = 'Complete Setup';
  static const String updateInfo = 'Update Info';
  static const String addSize = 'Add Size';
  static const String uploadColorPhoto = 'UPLOAD COLOR PHOTO';
  static const String category = 'Category';
  static const String custom = 'Custom';
  static const String viewColors = 'View Colors';
  static const String productname = 'Product Name';
  static const String addColor = 'Add Color';
  static const String categories = 'Categories';
  static const String variation = 'Variations';
  static const String subCategory = 'Sub Category';
  static const String acceptReturn = 'Accept return';
  static const String accept = 'Accept';
  static const String type = 'Type';
  static const String customOption = 'Custom Option';
  static const String addcustomOption = 'Add Custom Option';
  static const String reject = 'Reject';
  static const String addStory = 'Add Story';
  static const String addPrice = 'Add Price';
  static const String add = 'Add';
  static const String addYourBrandName = 'Add Your Brand Name';
  static const String maxBoxDimension = "Maximum Box Dimensions";
  static const String genrateVariations = 'Genrate Variations';
  static const String notificationOnMinQtyIsReached =
      'Notification on Min. Qty is reached';
  static const String availableQuantity = 'Available Quantity';
  static const String aed = 'AED';
  static const String size = 'Sizes';
  static const String returnCondition = 'Return Condition';
  static const String productPolicy = 'Product Policy';
  static const String viewListofBrand = 'View List of brand';
  static const String chooseBrand = 'Choose Brand';
  static const String recentlySelected = 'Recently Selected';
  static const String selectCategory = 'Select Category';
  static const String updateRefundStatus = 'Update refund status';
  static const String ok = 'OK';
  static const String polices = 'Polices';
  static const String width = 'Width';
  static const String length = 'Length';
  static const String weight = 'Weight';
  static const String height = 'Height';
  static const String requiredRefregiration = 'Required refregiration';
  static const String specification = 'Specification';
  static const String itemPerOrder = 'Items per Order';
  static const String newSpecification = 'New Specification';
  static const String oldSpecification = 'Old Specification';
  static const String driverName = 'Driver Name';
  static const String deliveryCompany = 'Delivery Company';
  static const String officeAddress = 'Office Address';
  static const String confirmAll = 'CONFIRM SHIPMENT';
  static const String send = 'Send';
  static const String returnDuration = 'Return Duration';
  static const String warrantyDuration = 'Warranty Duration';
  static const String keyword = 'Keywords';
  static const String buyerDetails = 'Buyer Details';
  static const String deliveryDetails = 'Delivery Details';
  static const String ordered = 'Ordered';
  static const String delivered = 'Delivered';
  static const String confirmed = 'Confirmed';
  static const String confirm = 'Confirm';
  static const String rejected = 'Rejected';
  static const String cancelled = 'Cancelled';
  static const String shipmentInformation = 'Shipment Information';
  static const String shipmentFits = 'Package Type';
  static const String returned = 'Return';
  static const String shipment = 'Shipment';
  static const String paymentMethod = "Payment Method";
  static const String returnedConfirmed = 'Returned\n Confirmed';
  static const String cancelledByCustomer = 'Cancelled\n by customer';
  static const String shipped = 'Shipped';
  static const String orderDetail = 'Order Detail';
  static const String transferRequest = 'Transfer Request';
  static const String youAreGoingToSend = 'You are going to send';
  static const String vat = 'Vat';
  static const String myShop = 'My shop';
  static const String orderNumber = 'Order Number';
  static const String totalAmount = 'Total Amount';
  static const String done = 'Done';
  static const String transactionID = 'Transaction ID';
  static const String withdrawalID = 'WithDrawal ID';
  static const String orderID = 'Order ID';
  static const String amount = 'Amount';
  static const String more = 'more';
  static const String sales = 'Sales';
  static const String status = 'Status';
  static const String transferredAmount = 'Transferred Amount';
  static const String topSellingItems = 'Top Selling Items';
  static const String initiateTransferRequest = 'Initiate Transfer Request';
  static const String totalVat = 'Total Vat';
  static const String totalRevenue = 'Total revenue';
  static const String productforFreeDelivery = 'Product for free delivery';
  static const String update = 'Update';
  static const String verify = 'Verify';
  static const String submit = 'Submit';
  static const String next = 'Next';
  static const String pendingAmount = 'Pending Amount';
  static const String availableAmount = 'Available Amount';
  static const String price = 'Price';
  static const String miniQTY = 'Minimum Quantity';
  static const String deliveryChargesFees = 'Delivery Charges Fees';
  static const String reply = 'Reply';
  static const String edit = 'Edit';
  static const String remove = 'Remove';
  static const String delete = 'Delete';
  static const String details = 'Details';
  static const String resetPasswordContent =
      'Enter your email to reset password';
  static const String tryAnotherWay = 'Or you can try another way';
  static const String welcome = 'Welcome,';
  static const String addMobileNumber = 'Add Mobile Number';

  static const String enterAMobileNumberToSafeguardYourAccount =
      'Enter a mobile number to safeguard your account.';
  static const String resetPassword = 'Reset Password';
  static const String welcomeAgain = 'Welcome again ,';
  static const String welcomeBack = 'Welcome back!';
  static const String cancel = 'Cancel';
  static const String bankAccount = 'Bank Account';
  static const String activity = 'ACTIVITY';
  static const String creditCard = 'Credit Card';
  static const String setting = 'SETTING';
  static const String yes = 'Yes';
  static const String no = 'No';
  static const String removeTermsConditionMessage =
      "Are you Sure You Want To Remove Your Terms And Condition";
  static const String myInformation = 'MY INFORMATION';
  static const String save = 'Save';
  static const String order = 'Orders';
  static const String howCanWeHelpYou = 'How can we help you?';
  static const String callUs = 'Call US';
  static const String supportEmail = '<EMAIL>';
  static const String whatsapp = 'WHATSAPP';
  static const String support = 'Support';
  static const String minimumQtyAlert = 'Minimum Qty Alert';
  static const String minimumQty = 'Minimum QTY';
  static const String freeDeliveryTarget = 'Free Delivery Target';
  static const String alert = 'Alert';
  static const String freeDeliveryItems = 'Free Delivery Items';
  static const String sold = 'Sold';
  static const String services = 'Services';
  static const String review = 'Review';
  static const String incomeAndExpenses = 'Income & Expenses';
  static const String addProduct = 'Add Product';
  static const String myTotalIncome = 'MY TOTAL INCOME';
  static const String matchThisProduct = 'Match this product';
  static const String createNewProduct = 'Create New Product';
  static const String similarProduct = 'Match Product';
  static const String myAccounts = 'My Accounts';
  static const String sellerDashboard = 'Seller Dashboard';
  static const String profile = 'Profile';
  static const String product = 'Product';
  static const String updateNow = 'Update Now';
  static const String publishNow = 'Publish Now';
  static const String saveForLater = 'Save As Draft';
  static const String shop = 'Shop';
  static const String addNewBranch = 'Add new branch';
  static const String addNewColor = 'Add New Colour';
  static const String openGoogleMap = 'OPEN GOOGLE MAP';
  static const String description = 'Description';
  static const String shopDescription =
      'Nulla eleifend pulvinar purus, molestie euismod odio imperdiet ac. Ut sit amet erat nec nibh rhoncus varius in non lorem. Donec interdum, lectus in convallis pulvinar, enim elit porta sapien, vel finibus erat felis sed neque.';
  static const String contactNumbers = 'Contact Numbers';
  static const String contactEmails = 'Contact Emails';
  static const String addAnotherBranch = 'Add another Branch';
  static const String forgetPaswoord = 'Forgot password?';
  static const String termsAndCondition = 'Terms And Condition';
  static const String yourName = 'Your name';
  static const String newName = 'New name';
  static const String dashboard = 'Dashboard';
  static const String deleteThisColor = 'Delete This Color';
  static const String deleteThisProduct = 'Delete This Product';
  static const String valid = 'VALID';
  static const String addAnotherContactNumber = 'Add another contact number';
  static const String addAnotherContactEmail = 'Add another contact email';
  static const String addLocationMap = 'Add Location Map';
  static const String addLocationUsingMap = 'add Location using map';
  static const String targetPrice = 'Target Price';
  static const String passwordHint = '••••••••';
  static const String email = 'Email';
  static const String newEmail = 'New Email';
  static const String newPhoneNumber = 'New Phone Number';
  static const String addInfoAboutYourShop = 'Add Info About Your Shop';
  static const String createShop = 'Create Shop';
  static const String createShopContent =
      'Now you can create your own shop and add your products';
  static const String continu = 'Continue';
  static const String city = 'City';
  static const String notification = 'Notification';
  static const String reviews = 'Reviews';
  static const String qty = 'Qty';
  static const String color = 'Colors';
  static const String pickUp = 'Pick-up';
  static const String estimatedDelivery = 'Estimated delivery';
  static const String phone = 'Phone';
  static const String productId = 'Product ID';
  static const String productEin = 'Product EIN';
  static const String variantEin = 'Variant EIN';
  static const String securityAndMangments = 'Accounts And Mangments';
  static const String appSettings = 'App Settings';
  static const String deactivateTheAccount = 'Deactivate The Account';
  static const String changePassword = 'Change Password';
  static const String changeYourEmail = 'Change Your Email';
  static const String changeYourUserName = 'Change Your User Name';
  static const String changeYourMobileNumber = 'Change Your Mobile Number';
  static const String editAccount = 'Edit Account';
  static const String whyThisInformationRequired =
      'Why this information is required?';
  static const String addNewProduct = 'Add New Product';
  static const String logout = 'Logout';
  static const String myOrders = 'My Orders';
  static const String myProfile = 'My profile';
  static const String faq = 'Faq';
  static const String faqs = 'FAQS';
  static const String date = 'Date';
  static const String updatedDate = 'Updated On';
  static const String uploadShopBanner = 'Upload shop banner';
  static const String logo = 'Logo';
  static const String iban = 'IBAN';
  static const String message = 'Message';
  static const String returnedFeilds = 'Returned Feilds';
  static const String bankName = 'Bank Name';
  static const String accountHolderName = 'Account holder name';
  static const String bankAccountInfo = 'Bank account information';
  static const String information = 'Information';
  static const String adminNotes = 'Admin Notes';
  static const String trashProducts = 'Deleted Products';
  static const String customerService = 'Customer Service';
  static const String privacyAndPolicy = 'Privacy And Policy';
  static const String logoutMessage = 'Are you sure you want to logout';
  static const String returnRequest = 'Requests';
  static const String productReviews = 'Product Reviews';
  static const String aboutTheShop = 'About The Shop';
  static const String manageBranches = 'Manage Branches';
  static const String updateBussinessInfo = 'Update Business Info';
  static const String addBusinessInformation = 'Add Business Information';
  static const String updateBusinessInformation = 'Update Business Information';
  static const String loginDescription = 'we are happy to see you again';
  static const String signupDescription =
      'We are thrilled to have you on board again';
  static const String password = 'Password';
  static const String resendOtp = 'Resend OTP';
  static const String accountNumber = 'Account Number';
  static const String returnedDate = 'Returned Date';
  static const String notReceived = 'Not Received? ';
  static const String createYourAccount = 'Create Your Account';
  static const String alreadyHaveAnAccount = 'Already have an account? ';
  static const String emailHint = 'ex : <EMAIL>';
  static const String createAnAccount = 'Create An Account';
  static const String nameHint = "ex : Khalid Moh";
}
