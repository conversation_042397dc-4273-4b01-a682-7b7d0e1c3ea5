import 'dart:convert';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:objectid/objectid.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_streams.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/add_product/categories_model.dart';
import 'package:overolasuppliers/model/add_product/product_variation_model.dart';
import 'package:overolasuppliers/model/add_product/return_duration_model.dart';
import 'package:overolasuppliers/model/add_product/warranty_duration_model.dart';
import 'package:overolasuppliers/screens/products/add_product/model/product_detail_model.dart';
import 'package:overolasuppliers/screens/products/add_product/views/add_colors.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:translator/translator.dart';

import '../../../../model/add_product/shipments_fits_model.dart';
import '../../../../model/add_product/view_prduct/product_detail_model.dart';

class AddProductController extends GetxController implements ServerResponse {
  // General product information
  final RxString gtinNumber = ''.obs;
  final RxString category = ''.obs;
  final RxString categoryAr = ''.obs;
  final RxString subCategory = ''.obs;
  final RxString subCategoryAr = ''.obs;
  final RxString manufacture = ''.obs;
  final RxString manufactureAr = ''.obs;
  final RxString categoryId = ''.obs;
  final RxString subCategoryId = ''.obs;
  final RxString brandFieldError = ''.obs;
  final RxString isRequiredUpdate = ''.obs;
  final RxString brandId = ''.obs;

  String productId = '';
  String oldProductId = '';
  String productKeywords = '';
  String keywords = '';

  List<Brands> brandsList = [];
  final RxList<Brands> filteredBrands = <Brands>[].obs;

  // Color, size, and variations
  final List<ColorFamilies> colorFamilyList = [];
  final List<String> nonRemovableImages = [];
  final Rx<SizeModel> size = SizeModel('', [], '').obs;
  final RxList<Variants> arrVariations = <Variants>[].obs;
  final RxList<Variants> arrAllVariations = <Variants>[].obs;
  final RxList<CustomOption> customOptionList = <CustomOption>[].obs;
  final RxList<ProductSpecificationModel> productSpecificationList =
      <ProductSpecificationModel>[].obs;
  final RxList<String> sliderImages = <String>[].obs;
  final List<String> uploadedImages = [];
  final List<String> returnOptionsTitles = [];

  final RxList<ColorModel> colorList = <ColorModel>[].obs;
  final onVariantUpdate = false.obs;


  RxBool get onVariantFeildsUpdate => onVariantUpdate;

  // Quantity, price, and shipping
  String minQty = '';
  String qty = '';
  String price = '';
  String itemsPerOrder = '';
  String shipmentFitsId = '';
  int specEditIndex = -1;

  final RxInt tabPosition = 0.obs;
  final Rx<SizeCategories> selectedSize = SizeCategories().obs;

  // Product matching and status
  bool isMatchProduct = false;
  bool isUpdateVariations = false;
  bool isApprovedProduct = false;
  bool isSimilarProduct = false;
  bool isRematched = false;
  bool isRequiredRematch = false;
  bool variationViewLoading = true;
  bool isDraftProduct = false;

  // Return and warranty policies
  final RxString returnPolicy = ''.obs;
  final RxString returnPolicyAr = ''.obs;
  final RxString warrantyPolicy = ''.obs;
  final RxString warrantyPolicyAr = ''.obs;
  final RxString returnType = ''.obs;
  RxInt selectedShipmentFits = 0.obs;

  String customWarrantyPolicy = "";
  String customWarrantyPolicyAr = "";
  // String productPolicy = '';
  // String productPolicyAr = '';
  String customReturnPolicy = '';
  String customReturnPolicyAr = '';

  final RxBool acceptReturn = false.obs;
  final RxBool freeDelivery = false.obs;
  final RxBool haveImages = false.obs;
  final RxBool isHideMinimumQty = false.obs;
  final RxBool isHideStepper = false.obs;

  final List<WarrantyDurations> warrantyDurationList = [];
  final List<ReturnDurations> returnDurationList = [];

  // Additional product options
  final RxList<SizeCategories> sizeFits = <SizeCategories>[].obs;
  List<ProductCustomOptions> productCustomOptions = [];
  ValidationHistory? validationHistory;
  Product? product;

  // Form and scroll controllers
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final GlobalKey<FormState> policyFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> shipmentFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> variationFormKey = GlobalKey<FormState>();

  late final ScrollController stepperHideController = ScrollController();
  AutoScrollController autoScrollController = AutoScrollController(
      viewportBoundaryGetter: () => const Rect.fromLTRB(25, 0, 25, 0),
      axis: Axis.horizontal);
  final PageController pageController = PageController();
  final translator = GoogleTranslator();

  late GraphQlInitilize _request;
  late AppLocalizations appLocalizations;

  var txtProductName = TextEditingController();
  var txtProductNameAr = TextEditingController();
  var txtProductDiscription = TextEditingController();
  var txtProductDiscriptionAr = TextEditingController();
  var txtProductPolicy = TextEditingController();
  var txtProductPolicyAr = TextEditingController();

  // Add this new field
  final Rx<CategoriesModel?> cachedCategories = Rx<CategoriesModel?>(null);
  
  // Add this new method
  Future<void> loadCategories() async {
    if (cachedCategories.value != null) return; // Use cached data if available
    
    final stream = GraphqlStreams();
    stream.getCategoryList();
    
    // Listen to stream once and cache the result
    stream.categoryController.stream.listen((data) {
      cachedCategories.value = data;
      stream.categoryController.close();
    });
  }

  @override
  void onInit() {
    super.onInit();
    appLocalizations = AppLocalizations.of(Get.context!)!;
    _request = GraphQlInitilize(this);
    _request.runQuery(
        context: Get.context!,
        isRequiredLoader: false,
        query: GraphQlQuries.getAllSizeCategories);
    stepperHideController.addListener(() {
      if (stepperHideController.position.userScrollDirection ==
          ScrollDirection.reverse) {
        if (!isHideStepper.value) {
          isHideStepper.value = true;
        }
      } else {
        if (stepperHideController.position.userScrollDirection ==
            ScrollDirection.forward) {
          if (isHideStepper.value) {
            isHideStepper.value = false;
          }
        }
      }
    });
    if (Get.arguments != null) {
      product = Get.arguments[0];
      productCustomOptions =
          product?.productOptions?.productCustomOptions ?? [];
      List<dynamic> arr = Get.arguments;
      if (arr.length > 1) {
        isMatchProduct = Get.arguments[1] ?? false;
        isSimilarProduct = Get.arguments[1] ?? false;
        isDraftProduct = Get.arguments[2] ?? false;
      }
      if (arr.length > 3) {
        isRematched = arr[3];
        oldProductId = arr[4];
      }
      if (!isMatchProduct) {
        isMatchProduct = product?.isMatched ?? false;
      }
      validationHistory = product!.validationHistory?.last;
      if (product!.validationHistory?.isNotEmpty ?? false) {
        int acceptIndex = (product!.validationHistory ?? [])
            .indexWhere((element) => element.status == "Accepted");
        if (acceptIndex != -1) {
          isApprovedProduct = true;
        }
        if (acceptIndex == -1 && isMatchProduct && !isRematched) {
          isRequiredRematch = true;
        }
      }
    }

    setProductInfo(product);
    Future.delayed(Durations.short1, () {
      setProductDeatils(product);
      setShipmentInfo(product);
      setPoliciesInfo(product);
    });
  }
  

  @override
  void dispose() {
    super.dispose();
    stepperHideController.dispose();
  }

  setProductInfo(Product? product) {
    String info = prefs.getString(productInformation) ?? "";
    if (info.isNotEmpty && product == null) {
      Map<String, dynamic> productInfo = jsonDecode(info);
      txtProductName.text = productInfo['name'];
      txtProductNameAr.text = productInfo['nameAr'];
      txtProductDiscription.text = productInfo['description'];
      txtProductDiscriptionAr.text = productInfo['descriptionAr'];
      keywords = productInfo['keywords'];
      gtinNumber.value = productInfo['prductGtin'];
      productId = productInfo['prductGtin'];
      categoryAr.value = productInfo['categoryAr'];
      category.value = productInfo['category'];
      subCategoryAr.value = productInfo['subCategoryAr'];
      subCategory.value = productInfo['subCategory'];
      manufacture.value = productInfo['brand'];
      manufactureAr.value = productInfo['brandAr'];
      categoryId.value = productInfo['categoryId'];
      subCategoryId.value = productInfo['subCategoryId'];
      brandId.value = productInfo['brandId'];
      List<dynamic> brandList = productInfo['brandList'];
      if (brandList.isNotEmpty) {
        for (var spec in brandList) {
          brandsList.add(Brands.fromJson(spec));
        }
      }
    } else if (product != null) {
      keywords = (product.productKeyWords ?? [])
          .where((keyword) => keyword.isNotEmpty)
          .join(",");

      productKeywords = keywords;
      txtProductName.text = product.productName ?? "";
      txtProductNameAr.text = product.ar?.productName ?? "";
      txtProductDiscription.text = product.productDescription ?? "";
      txtProductDiscriptionAr.text = product.ar?.productDescription ?? "";
      //   productName = product.productName ?? "";
      // productNameAr = product.ar?.productName ?? "";
      // productNameEnTranslation.value = product.productName ?? "";
      // productNameArTranslation.value = product.ar?.productName ?? "";
      // productDescriptionEnTranslation.value = product.productDescription ?? "";
      // productDescriptionArTranslation.value = product.ar?.productDescription ?? "";
      // productDescriptionAr = product.ar?.productDescription ?? "";
      //   productDescription = product.productDescription ?? "";
      gtinNumber.value = product.productManufacturerId ?? "";
      productId = product.productManufacturerId ?? "";
      category.value = product.productCategory?.categoryName ?? "";
      categoryAr.value = product.productCategory?.ar?.categoryName ?? "";
      subCategory.value = product.productSubCategory?.subCategoryName ?? "";
      subCategoryAr.value =
          product.productSubCategory?.ar?.subCategoryName ?? "";
      manufacture.value = product.productBrand?.brandName ?? "";
      manufactureAr.value = product.productBrand?.ar?.brandName ?? "";
      categoryId.value = product.productCategory?.id ?? "";
      subCategoryId.value = product.productSubCategory?.id ?? "";
      brandId.value = product.productBrand?.id ?? "";
    }
  }

  setProductDeatils(Product? product) {
    String details = prefs.getString(productDetails) ?? "";
    if (details.isNotEmpty && product == null) {
      Map<String, dynamic> productDeatils = jsonDecode(details);
      List<dynamic> arrImages = productDeatils['images'];
      sliderImages.clear();
      for (var element in arrImages) {
        sliderImages.add(element);
      }
      price = productDeatils['price'];
      qty = productDeatils['qty'];
      minQty = productDeatils['minimum_qty'];
      isHideMinimumQty.value = productDeatils['notify_minimum_qty'];

      List<dynamic> arrSpecifications = productDeatils['specs'];
      List<dynamic> arrSize = productDeatils['sizes'];
      List<dynamic> arrCustumOption = productDeatils['custom_options'];
      List<dynamic> arrColors = productDeatils['colors'];
      Map<String, dynamic> arrvariations = productDeatils['variations'];
      if (arrSpecifications.isNotEmpty) {
        for (var spec in arrSpecifications) {
          productSpecificationList.add(ProductSpecificationModel(
              spec['specsTitle'],
              spec['specsValue'],
              "",
              spec['specsTitleAr'],
              spec['specsValueAr']));
        }
      }
      if (arrSize.isNotEmpty) {
        List<dynamic> sizes = arrSize[0]['sizeValues'];
        List<SizeValueModel> sizeValues = [];
        for (var element in sizes) {
          sizeValues
              .add(SizeValueModel(element["value"], element["isValueHidden"]));
        }
        size.value = SizeModel(
            arrSize[0]['sizeUnit'], sizeValues, arrSize[0]['sizeUnitAr']);
      }
      if (arrColors.isNotEmpty) {
        for (var color in arrColors) {
          List<dynamic> images = color['colorImages'];
          colorList.add(ColorModel(
            color['colorName'],
            color['colorNameAr'],
            color['colorFamily'],
            color['colorFamilyAr'],
            color['colorIcon'],
            images.map((e) => e.toString()).toList(),
            color['isHidden'],
          ));
        }
      }
      if (arrCustumOption.isNotEmpty) {
        for (var option in arrCustumOption) {
          List<dynamic> options = option['optionValues'];
          List<CustomOptionValues> optionsValues = [];
          for (var element in options) {
            optionsValues.add(
                CustomOptionValues(element["value"], element["valueAr"], element["isValueHidden"]));
          }
          customOptionList
              .add(CustomOption(option['optionTitle'],option['optionTitleAr'], "", "", optionsValues, ""));
        }
      }
      if (arrvariations.isNotEmpty) {
        var options = arrvariations["options"];
        if (!options) {
          var variants = arrvariations["variations"];
          for (var variant in variants) {
            String obj = jsonEncode(variant);
            Variants variants = Variants.fromJson(jsonDecode(obj));
            arrVariations.add(variants);
          }
        }
      }
    } else if (product != null) {
      sliderImages.clear;
      sliderImages.addAll(product.productImages ?? []);
      if (!isSimilarProduct) {
        price = (product.productPrice ?? 0).toString();
        qty = (product.productAvailableQte ?? 0).toString();
      }

      minQty = isMatchProduct
          ? ""
          : (product.productMinQte) == 0
              ? ""
              : (product.productMinQte).toString();
      isHideMinimumQty.value =
          isMatchProduct ? false : product.productNotifOnMinQte ?? false;
      if (product.productSpecs?.isNotEmpty ?? false) {
        for (ProductSpecs spec in product.productSpecs ?? []) {
          ProductSpecs specAr = product.ar?.productSpecs
                  ?.firstWhere((element) => element.specsId == spec.specsId) ??
              ProductSpecs();
          productSpecificationList.add(ProductSpecificationModel(
            spec.specsTitle ?? "",
            spec.specsValue ?? "",
            spec.specsId ?? "",
            specAr.specsTitle ?? "",
            specAr.specsValue ?? "",
          ));
        }
      }

      if (product.productOptions?.productSizes?.isNotEmpty ?? false) {
        List<SizeValueModel> arrSizes = [];
        for (OptionValues element
            in product.productOptions?.productSizes?[0].sizeValues ?? []) {
          arrSizes.add(SizeValueModel(
              element.value ?? "", element.isValueHidden ?? false));
        }
        size.value = SizeModel(
            product.productOptions?.productSizes?[0].sizeUnit ?? "",
            arrSizes,
            "");
      }
      if (product.productOptions?.productColors?.isNotEmpty ?? false) {
        for (ProductColors color
            in product.productOptions?.productColors ?? []) {
          ProductColors arColor = product.ar?.productOptions?.productColors
                  ?.firstWhere((element) => element.id == color.id) ??
              ProductColors();
          colorList.add(ColorModel(
            color.colorName ?? "",
            arColor.colorName ?? "",
            color.colorFamily ?? "",
            arColor.colorFamily ?? "",
            color.colorIcon ?? "",
            color.colorImages ?? [],
            color.isHidden ?? false,
            id: color.id ?? "",
          ));
        }
      }
      if (product.productOptions?.productCustomOptions?.isNotEmpty ?? false) {
        for (ProductCustomOptions option
            in product.productOptions?.productCustomOptions ?? []) {
              ProductCustomOptions arOption = product.ar?.productOptions?.productCustomOptions
                      ?.firstWhere((element) => element.optionId == option.optionId) ??
                  ProductCustomOptions();
          List<CustomOptionValues> values = [];
          for (var i = 0; i < (option.optionValues ?? []).length; i++) {
            OptionValues optionEn = option.optionValues?[i] ?? OptionValues();
            OptionValues optionAr = arOption.optionValues?[i] ?? OptionValues();
 values.add(CustomOptionValues(
                optionEn.value ?? "",optionAr.value ?? "", optionEn.isValueHidden ?? false));
          }
          
          customOptionList.add(CustomOption(
              option.optionTitle ?? "",arOption.optionTitle ?? "" ,"","", values, option.optionId ?? ""));
        }
      }
      if (product.productVariants?.isNotEmpty ?? false) {
        for (var i = 0; i < (product.productVariants ?? []).length; i++) {
          int index = i;
          if (validationHistory?.returnValues?.contains(
                  "${index + 1}${GlobalMethods.ordinal(index + 1)} Variant GTIN") ??
              false) {
            product.productVariants?[i].isGtinReturned = true;
          } else {
            product.productVariants?[i].isGtinReturned = false;
          }
          if (validationHistory?.returnValues?.contains(
                  "${index + 1}${GlobalMethods.ordinal(index + 1)} variant price") ??
              false) {
            product.productVariants?[i].isPriceReturned = true;
          } else {
            product.productVariants?[i].isPriceReturned = false;
          }
          if (validationHistory?.returnValues?.contains(
                  "${index + 1}${GlobalMethods.ordinal(index + 1)} variant quantity") ??
              false) {
            product.productVariants?[i].isQtyReturned = true;
          } else {
            product.productVariants?[i].isQtyReturned = false;
          }
        }
        if (colorList.isNotEmpty) {
          for (ColorModel color in colorList) {
            List<Variations> newOptions = [];
            for (var i = 0; i < (product.productVariants ?? []).length; i++) {
              ProductVariants variation = product.productVariants![i];
              if ((color.id ==
                      variation.variantAttributes?.variantColor?.colorId) ||
                  color.colorFamily ==
                          variation
                              .variantAttributes?.variantColor?.colorFamily &&
                      color.colorName.trimRight() ==
                          variation.variantAttributes?.variantColor?.colorName
                              ?.trimRight()) {
                newOptions.add((Variations(
                    id: variation.id ?? "",
                    variantName: variation.variantName,
                    variantCode: variation.variantCode,
                    variantPrice: isSimilarProduct ? 0 : variation.variantPrice,
                    variantQte: isSimilarProduct ? 0 : variation.variantQte,
                    variantEIN: variation.variantEIN,
                    variantManufacturerId: variation.variantManufacturerId,
                    variantImages: variation.variantImages,
                    variantAttributes: variation.variantAttributes,
                    isVariantModified: false,
                    isPriceReturned: variation.isPriceReturned,
                    isQtyReturned: variation.isQtyReturned,
                    isGtinReturned: variation.isGtinReturned,
                    isVariantVisible: variation.isVariantVisible)));
              }
            }
            arrVariations.add(Variants(
                colorName: color.colorName,
                colorFamily: color.colorFamily,
                colorIcon: color.thumnailUrl,
                variations: newOptions));
          }
        } else if (size.value.sizes.isNotEmpty) {
          print("filter  sizes ${jsonEncode(product.validationHistory)}");
          for (SizeValueModel sizeType in size.value.sizes) {
            List<Variations> newOptions = [];
            for (ProductVariants variation in (product.productVariants ?? [])) {
              if (sizeType.value ==
                  (variation.variantAttributes?.variantSize?.size ?? "")) {
                newOptions.add((Variations(
                    id: variation.id ?? "",
                    variantName: variation.variantName,
                    variantCode: variation.variantCode,
                    variantPrice: (isMatchProduct &&
                            ((product.validationHistory?.length ?? 0) == 1))
                        ? 0
                        : variation.variantPrice,
                    variantQte: (isMatchProduct &&
                            ((product.validationHistory?.length ?? 0) == 1))
                        ? 0
                        : variation.variantQte,
                    variantEIN: variation.variantEIN,
                    variantManufacturerId: variation.variantManufacturerId,
                    variantImages: variation.variantImages,
                    variantAttributes: variation.variantAttributes,
                    isVariantModified: false,
                    isPriceReturned: variation.isPriceReturned,
                    isQtyReturned: variation.isQtyReturned,
                    isGtinReturned: variation.isGtinReturned,
                    isVariantVisible: variation.isVariantVisible)));
              }
            }
            arrVariations.add(Variants(
                size: sizeType.value,
                unit: size.value.sizeUnit,
                colorName: "",
                colorFamily: "",
                colorIcon: "",
                variations: newOptions));
          }
        } else if (customOptionList.isNotEmpty) {
          List<Variations> newOptions = [];
          for (ProductVariants variation in (product.productVariants ?? [])) {
            newOptions.add((Variations(
                id: variation.id ?? "",
                variantName: variation.variantName,
                variantCode: variation.variantCode,
                variantPrice: isSimilarProduct ? 0 : variation.variantPrice,
                variantQte: isSimilarProduct ? 0 : variation.variantQte,
                variantEIN: variation.variantEIN,
                variantManufacturerId: variation.variantManufacturerId,
                variantImages: variation.variantImages,
                variantAttributes: variation.variantAttributes,
                isVariantModified: false,
                isPriceReturned: variation.isPriceReturned,
                isQtyReturned: variation.isQtyReturned,
                isGtinReturned: variation.isGtinReturned,
                isVariantVisible: variation.isVariantVisible)));
          }
          arrVariations.add(Variants(
              colorName: "",
              colorFamily: "",
              colorIcon: "",
              variations: newOptions));
        }
      }
    }
    arrAllVariations.addAll(arrVariations);
    for (var element in arrAllVariations) {
      log("variations ${jsonEncode(element)}");
    }
    // log("variations ${arrVariations}");
  }

  setShipmentInfo(Product? product) {
    String shipment = prefs.getString(productShipment) ?? "";
    if (shipment.isNotEmpty && product == null) {
      Map<String, dynamic> productShipment = jsonDecode(shipment);
      shipmentFitsId = productShipment['shipmentFitsID'];
      itemsPerOrder = productShipment['itemsPerOrder'];
      selectedSize.value =
          SizeCategories.fromJson(jsonDecode(productShipment["selectedSize"]));
    } else if (product != null) {
      itemsPerOrder = isSimilarProduct ? "" : "${product.itemsPerOrder ?? 0}";
      selectedSize.value = product.productSizeCategory!;
      shipmentFitsId = selectedSize.value.id ?? "";
    }
  }

  setPoliciesInfo(Product? product) {
    String policies = prefs.getString(productPolicies) ?? "";

    if (policies.isNotEmpty && product == null) {
      Map<String, dynamic> productPolicies = jsonDecode(policies);
      String returnDuration = productPolicies['return_duration'];
      String warrantyDuration = productPolicies['warranty_duration'];
      if (returnDuration == "Other") {
        customReturnPolicy = returnDuration;
      } else {
        returnPolicy.value = returnDuration;
      }

      if (warrantyDuration == "Other") {
        customWarrantyPolicy = warrantyDuration;
        customWarrantyPolicyAr = productPolicies['warranty_duration_ar'];
      } else {
        warrantyPolicy.value = warrantyDuration;
        warrantyPolicyAr.value = productPolicies['warranty_duration_ar'];
      }
      acceptReturn.value = productPolicies['accept_return'];
      txtProductPolicy.text = productPolicies['policy'];
      txtProductPolicyAr.text = productPolicies['policyAr'];
      returnType.value = productPolicies['return_type'];
      freeDelivery.value = productPolicies['is_free_delivery'];
    } else if (product != null && !isSimilarProduct) {
      String returnDuration =
          product.productPolicies?.productReturnDuration ?? "";
      String warrantyDuration =
          product.productPolicies?.productWarrantyDuration ?? "";
      if (returnDuration != "No Return Duration") {
        if (returnDuration == "Other") {
          customReturnPolicy = returnDuration;
        } else {
          returnPolicy.value = returnDuration;
        }
      }
      if (warrantyDuration == "Other") {
        customWarrantyPolicy = warrantyDuration;
      } else {
        warrantyPolicy.value = warrantyDuration;
      }
      warrantyPolicyAr.value = product.ar?.productPolicies?.productWarrantyDuration ?? "";
      acceptReturn.value =
          product.productPolicies?.productAcceptReturn ?? false;
      freeDelivery.value = product.isFreeDeliveryItem ?? false;
      txtProductPolicy.text = product.productPolicies?.productReturnPolicy ?? "";
      txtProductPolicyAr.text = product.ar?.productPolicies?.productReturnPolicy ?? "";

      returnType.value =
          product.productPolicies?.productNotFreeReturn ?? false ? "1" : "0";
    }
  }

  void genrateVariations(BuildContext context, GraphQlInitilize request) {
    final sizeValues = size.value.sizes
        .map((sizeValue) => {
              'value': sizeValue.value,
              'isValueHidden': sizeValue.isValueHidden,
            })
        .toList();
    final sizes = sizeValues.isNotEmpty
        ? [
            {'sizeUnit': size.value.sizeUnit, 'sizeValues': sizeValues}
          ]
        : [];

    final sizesAr = sizeValues.isNotEmpty
        ? [
            {'sizeUnit': size.value.sizeUnitAr, 'sizeValues': sizeValues}
          ]
        : [];

    final customOptions = customOptionList
        .map((option) => {
              'optionTitle': option.title,
              'optionValues': option.valueList
                  .map((optionValue) => {
                        'value': optionValue.value,
                        'isValueHidden': optionValue.isValueHidden,
                      })
                  .toList(),
            })
        .toList();
          final customOptionsAr = customOptionList
        .map((option) => {
              'optionTitle': option.titleAr,
              'optionValues': option.valueList
                  .map((optionValue) => {
                        'value': optionValue.valueAr,
                        'isValueHidden': optionValue.isValueHidden,
                      })
                  .toList(),
            })
        .toList();

    List<Map<String, dynamic>> colorsList = [];
    List<Map<String, dynamic>> colorsListAr = [];
    for (ColorModel color in colorList) {
      String colorId = color.id;
      if (colorId.isEmpty) {
        int indexof = colorList.indexOf(color);
        colorId = "${ObjectId()}";
        ColorModel updateColor = colorList[indexof];
        updateColor.id = colorId;
        colorList[indexof] = updateColor;
      }
      colorsList.add({
        'colorId': colorId,
        'isHidden': color.isHidden,
        'colorImages': color.imagesUrl,
        'colorFamily': color.colorFamily,
        'colorName': color.colorName,
        'colorIcon': color.thumnailUrl,
      });
      colorsListAr.add({
        'colorId': colorId,
        'isHidden': color.isHidden,
        'colorImages': color.imagesUrl,
        'colorFamily': color.colorFamilyAr,
        'colorName': color.colorNameAr,
        'colorIcon': color.thumnailUrl,
      });
    }

    var ar = {
      "productName": txtProductNameAr.text,
      'productSizes': sizesAr,
      'productColors': colorsListAr,
      'productCustomOptions': customOptionsAr,
    };

    final variables = {
      'productName': txtProductName.text,
      'productPrice': double.parse(price.isNotEmpty ? price : "0"),
      'productAvailableQte': qty.isNotEmpty ? int.parse(qty) : 0,
      'productImages': sliderImages,
      'productColors': colorsList,
      'productSizes': sizes,
      'productCustomOptions': customOptions,
      'ar': ar,
    };
    if (product != null) {
      isUpdateVariations = true;
    } else {
      if (colorsList.isEmpty && sizes.isEmpty && customOptions.isEmpty) {
        arrVariations.clear();
        return;
      }
    }

    if (colorsList.isEmpty && sizes.isEmpty && customOptions.isEmpty) {
      return;
    }

    log("genrateVariation ${jsonEncode(variables)}");

    request.runMutation(
      context: context,
      query: GraphQlQuries.genrateVariation,
      variables: variables,
      type: "VARIATIONS",
    );
  }

  bool validateProductInformationPage() {
    if (!formKey.currentState!.validate()) return false;

    final productIdValue = productId.isEmpty ? gtinNumber.value : productId;

    if (product != null) {
      final requiredUpdate = validateInfoPageFields();
      prefs.setString(lastProductEditId, product?.id ?? '');
      gtinNumber.value = productId;
      return requiredUpdate
          ? addProductInfoCache(
              txtProductName.text,
              txtProductNameAr.text,
              txtProductDiscription.text,
              txtProductDiscriptionAr.text,
              productIdValue,
              categoryId.value,
              subCategoryId.value,
              brandId.value,
              category.value,
              categoryAr.value,
              subCategory.value,
              subCategoryAr.value,
              manufacture.value,
              manufactureAr.value,
              keywords)
          : true;
    } else {
      return addProductInfoCache(
          txtProductName.text,
          txtProductNameAr.text,
          txtProductDiscription.text,
          txtProductDiscriptionAr.text,
          productIdValue,
          categoryId.value,
          subCategoryId.value,
          brandId.value,
          category.value,
          categoryAr.value,
          subCategory.value,
          subCategoryAr.value,
          manufacture.value,
          manufactureAr.value,
          keywords);
    }
  }

  bool validateInfoPageFields() {
    return [
      txtProductName.text != product?.productName,
      txtProductNameAr.text != product?.ar?.productName,
      txtProductDiscription.text != product?.productDescription,
      txtProductDiscriptionAr.text != product?.ar?.productDescription,
      productId != product?.productManufacturerId,
      category.value != product?.productCategory?.categoryName,
      subCategory.value != product?.productSubCategory?.subCategoryName,
      manufacture.value != product?.productBrand?.brandName,
      keywords != productKeywords,
    ].any((element) => element);
  }

  bool addProductInfoCache(
      String name,
      String nameAr,
      String description,
      String descriptionAr,
      String prductGtin,
      String categoryId,
      String subCategoryId,
      String brandId,
      String category,
      String categoryAr,
      String subCategory,
      String subCategoryAr,
      String brand,
      String brandAr,
      String keywords) {
    final brandList = brandsList
        .map((brand) => {
              'brandName': brand.brandName,
              'brandIcon': brand.brandIcon,
              'id': brand.id,
              'ar': brand.arBrands
            })
        .toList();

    Map<String, dynamic> info = {
      'name': name,
      'nameAr': nameAr,
      'description': description,
      'descriptionAr': descriptionAr,
      'prductGtin': prductGtin,
      "categoryId": categoryId,
      "subCategoryId": subCategoryId,
      "brandId": brandId,
      "category": category,
      "subCategory": subCategory,
      "brand": brand,
      "categoryAr": categoryAr,
      "subCategoryAr": subCategoryAr,
      "brandAr": brandAr,
      "keywords": keywords,
      "brandList": brandList,
    };
    String information = jsonEncode(info);
    log("set info $information");

    prefs.setString(productInformation, information);
    return true;
  }

  bool validateProductShipment() {
    if (shipmentFormKey.currentState!.validate()) {
      if (product != null && !isSimilarProduct) {
        if ((validationHistory?.returnValues?.contains("Size Category") ??
                false) &&
            shipmentFitsId == (product?.productSizeCategory?.id ?? "")) {
          isRequiredUpdate.value = "true";
          return false;
        } else {
          bool requiredUpdate = validateShipmentFeilds();
          isRequiredUpdate.value = "";
          if (requiredUpdate) {
            return setProductShipmentcacheInfo(shipmentFitsID: shipmentFitsId);
          } else {
            return true;
          }
        }
      } else {
        return setProductShipmentcacheInfo(shipmentFitsID: shipmentFitsId);
      }
    } else {
      return false;
    }
  }

  bool validateProductPolicies() {
    if (policyFormKey.currentState!.validate()) {
      if (product != null) {
        if ((validationHistory?.returnValues
                    ?.contains("Does The Product Accepts Return") ??
                false) &&
            acceptReturn.value ==
                (product?.productPolicies?.productAcceptReturn ?? false)) {
          isRequiredUpdate.value = "true";
          return false;
        } else if ((validationHistory?.returnValues
                    ?.contains("Return Duration") ??
                false) &&
            (returnPolicy.value == 'Other'
                    ? customReturnPolicy
                    : returnPolicy.value) ==
                (product?.productPolicies?.productReturnDuration ?? '') &&
            acceptReturn.value) {
          isRequiredUpdate.value = "true";
          return false;
        } else if ((validationHistory?.returnValues
                    ?.contains("Warranty Duration") ??
                false) &&
            (warrantyPolicy.value == 'Other'
                    ? customWarrantyPolicy
                    : warrantyPolicy.value) ==
                (product?.productPolicies?.productWarrantyDuration ?? '')) {
          isRequiredUpdate.value = "true";
          return false;
        } else if ((validationHistory?.returnValues?.contains("Policies") ??
                false) &&
            txtProductPolicy.text ==
                (product?.productPolicies?.productReturnPolicy ?? '')) {
          isRequiredUpdate.value = "true";
          return false;
        } else {
          bool requiredUpdate = validateProductPoliciesFeilds();
          isRequiredUpdate.value = "";
          if (requiredUpdate) {
            return setProductPoliciesCacheInfo(
                acceptReturn: acceptReturn.value,
                returnType: returnType.value,
                returnDuration: acceptReturn.value == false
                    ? ""
                    : returnPolicy.value == 'Other'
                        ? customReturnPolicy
                        : returnPolicy.value,
                warrantyDuration: warrantyPolicy.value == 'Other'
                    ? customWarrantyPolicy
                    : warrantyPolicy.value,
                warrantyDurationAr: warrantyPolicy.value == 'Other'
                    ? customWarrantyPolicyAr
                    : warrantyPolicyAr.value,
                policyAr: txtProductPolicyAr.text,
                policy: txtProductPolicy.text);
          } else {
            prefs.setString(productPolicies, "");
            return true;
          }
        }
      } else {
        return setProductPoliciesCacheInfo(
            acceptReturn: acceptReturn.value,
            returnType: returnType.value,
            returnDuration: acceptReturn.value == false
                ? ""
                : returnPolicy.value == 'Other'
                    ? customReturnPolicy
                    : returnPolicy.value,
            warrantyDuration: warrantyPolicy.value == 'Other'
                ? customWarrantyPolicy
                : warrantyPolicy.value,
            warrantyDurationAr: warrantyPolicy.value == 'Other'
                ? customWarrantyPolicyAr
                : warrantyPolicyAr.value,
            policyAr: txtProductPolicyAr.text,
            policy: txtProductPolicy.text);
      }
    } else {
      return false;
    }
  }

  bool validateProductPoliciesFeilds() {
    bool requiredUpdate = false;

    if (acceptReturn.value !=
        (product?.productPolicies?.productAcceptReturn ?? false)) {
      requiredUpdate = true;
    } else if ((returnPolicy.value == 'Other'
            ? customReturnPolicy
            : returnPolicy.value) !=
        product?.productPolicies?.productReturnDuration) {
      requiredUpdate = true;
    } else if ((warrantyPolicy.value == 'Other'
            ? customWarrantyPolicy
            : warrantyPolicy.value) !=
        product?.productPolicies?.productWarrantyDuration) {
      requiredUpdate = true;
    } else if (txtProductPolicy.text != product?.productPolicies?.productReturnPolicy) {
      requiredUpdate = true;
    } else if (txtProductPolicyAr.text != product?.ar?.productPolicies?.productReturnPolicy) {
      requiredUpdate = true;
    } else if (freeDelivery.value != (product?.isFreeDeliveryItem ?? false)) {
      requiredUpdate = true;
    }
    return requiredUpdate;
  }

  setProductPoliciesCacheInfo(
      {required bool acceptReturn,
      required String returnType,
      required String returnDuration,
      required String warrantyDuration,
      required String warrantyDurationAr,
      required String policy,
      required String policyAr}) {
    Map<String, dynamic> info = {
      'return_type': returnType,
      'return_duration': returnDuration,
      'warranty_duration': warrantyDuration,
      'warranty_duration_ar': warrantyDurationAr,
      "policy": policy,
      "policyar": policyAr,
      "is_free_delivery": freeDelivery.value,
      "accept_return": acceptReturn,
    };

    String policis = jsonEncode(info);
    prefs.setString(productPolicies, policis);
    return true;
  }

  bool validateShipmentFeilds() {
    bool requiredUpdate = false;
    if (shipmentFitsId != (product?.productSizeCategory?.id ?? "")) {
      requiredUpdate = true;
    }
    if (itemsPerOrder != ("${product?.itemsPerOrder ?? 0}")) {
      requiredUpdate = true;
    }
    return requiredUpdate;
  }

  setProductShipmentcacheInfo({required String shipmentFitsID}) {
    String selectedSizes = jsonEncode(selectedSize);
    Map<String, dynamic> info = {
      'shipmentFitsID': shipmentFitsID,
      "itemsPerOrder": itemsPerOrder,
      "selectedSize": selectedSizes
    };
    String shipment = jsonEncode(info);
    prefs.setString(productShipment, shipment);
    return true;
  }

  bool validateProductSpecificatios() {
    if(specEditIndex != -1){
      isRequiredUpdate.value = appLocalizations.aleartCompeleteEditSpec;
      return false;
    }
    if (product != null) {
      if (product?.productSpecs?.isNotEmpty ?? false) {
        for (var i = 0; i < productSpecificationList.length; i++) {
          if ((validationHistory?.returnValues
                      ?.contains("Specification ${i + 1}") ??
                  false) &&
              productSpecificationList[i].title ==
                  (product?.productSpecs?[i].specsTitle ?? '')) {
            isRequiredUpdate.value = appLocalizations.requiredUpdateOnSpec;
            return false;
          }
        }
      }

      bool requiredUpdate = validateSpecs();
      isRequiredUpdate.value = "";
      haveImages.value = false;
      if (requiredUpdate) {
        List<String> images = [];
        for (String element in sliderImages) {
          if (product != null) {
            if (!product!.productImages!.contains(element)) {
              images.add(element);
            }
          } else {
            images.add(element);
          }
        }
        return setProductOptionscacheInfo(images: images);
      } else {
        return true;
      }
    } else {
      isRequiredUpdate.value = "";
      haveImages.value = false;
      List<String> images = [];
      for (var element in sliderImages) {
        images.add(element);
      }
      return setProductOptionscacheInfo(images: images);
    }
  }

  bool validateSpecs() {
    bool requiredUpdate = false;

    if (productSpecificationList.length !=
        (product?.productSpecs?.length ?? 0)) {
      return true;
    }
    if (productSpecificationList.isNotEmpty) {
      if (product?.productSpecs?.isNotEmpty ?? false) {
        for (var i = 0; i < productSpecificationList.length; i++) {
          if (productSpecificationList[i].title !=
                  product?.productSpecs?[i].specsTitle ||
              productSpecificationList[i].value !=
                  product?.productSpecs?[i].specsValue) {
            requiredUpdate = true;
            break;
          }
        }
      } else {
        requiredUpdate = true;
      }
    }
    return requiredUpdate;
  }

  bool validateProductPriceVariations({bool isNext = false}) {
    if (!isNext) {
      return false;
    }
    if (arrVariations.isEmpty) {
      variationViewLoading = false;
    }
    if (arrVariations.isNotEmpty &&
        colorList.isEmpty &&
        size.value.sizes.isEmpty &&
        customOptionList.isEmpty) {
      variationViewLoading = false;
    }
    if (tabPosition.value == 2 &&
        arrVariations.isNotEmpty &&
        variationViewLoading) {
      return false;
    }

    if (formKey.currentState!.validate() &&
        (variationFormKey.currentState != null
            ? variationFormKey.currentState!.validate()
            : true)) {
      if (product != null) {
        bool requiredUpdate = validatePriceAndVariations();
        isRequiredUpdate.value = "";
        haveImages.value = false;
        if (requiredUpdate) {
          List<String> images = [];
          for (String element in sliderImages) {
            if (product != null) {
              if (!product!.productImages!.contains(element)) {
                images.add(element);
              }
            } else {
              images.add(element);
            }
          }
          return setProductOptionscacheInfo(images: images);
        } else {
          return true;
        }
      } else {
        isRequiredUpdate.value = "";
        haveImages.value = false;
        List<String> images = [];
        for (var element in sliderImages) {
          images.add(element);
        }
        return setProductOptionscacheInfo(images: images);
      }
    } else if (validationHistory != null) {
      for (var element in arrVariations) {
        int indexOf = (element.variations ?? []).indexWhere((variant) =>
            variant.isGtinReturned == true ||
            variant.isPriceReturned == true ||
            variant.isQtyReturned == true);
        if (indexOf >= 0) {
          isRequiredUpdate.value = "Please check return variants";
          return false;
        } else {
          isRequiredUpdate.value = "";
          return false;
        }
      }
      isRequiredUpdate.value = "";
      return false;
    } else {
      if (arrVariations.isNotEmpty) {
        bool isUpdateVariations = false;
        for (var superIndex = 0;
            superIndex < arrVariations.length;
            superIndex++) {
          for (var index = 0;
              index < (arrVariations[superIndex].variations ?? []).length;
              index++) {
            String variantPrice =
                "${arrVariations[superIndex].variations![index].variantPrice ?? 0}";
            if (variantPrice.isEmpty || variantPrice == "0") {
              isUpdateVariations = true;
              arrVariations[superIndex].variations![index].variantPrice =
                  double.parse(price);
            }
            if (arrVariations[superIndex].variations![index].variantQte == 0) {
              isUpdateVariations = true;
              arrVariations[superIndex].variations![index].variantQte =
                  int.parse(qty);
            }
          }
        }
        onVariantUpdate.value = isUpdateVariations;
      }
      return false;
    }
  }

  bool validatePriceAndVariations() {
    bool requiredUpdate = false;
    if (price != "${product?.productPrice}") {
      requiredUpdate = true;
    } else if (qty != (product?.productAvailableQte ?? 0).toString()) {
      requiredUpdate = true;
    } else if (minQty !=
        ((product?.productMinQte ?? 0).toString() == "0"
                ? ""
                : product?.productMinQte ?? 0)
            .toString()) {
      requiredUpdate = true;
    }
    if (arrVariations.length != arrAllVariations.length) {
      return true;
    }
    if (arrVariations.isNotEmpty) {
      for (Variants variants in arrVariations) {
        int index = arrAllVariations.indexWhere((allvariant) =>
            allvariant.variations?.length == variants.variations?.length);
        if (index == -1) {
          requiredUpdate = true;
          break;
        } else if (variants.variations?.isNotEmpty ?? false) {
          for (Variations element in variants.variations ?? []) {
            if (element.isUpdated ?? false) {
              requiredUpdate = true;
              break;
            }
          }
        }
      }
    }
    return requiredUpdate;
  }

  bool validateProductOptions() {
    if (!variationViewLoading) {
      variationViewLoading = true;
    }
    if (colorList.isEmpty && sliderImages.isEmpty) {
      haveImages.value = true;
      return false;
    }
    if (product != null) {
      if (sliderImages.isNotEmpty) {
        for (var i = 0; i < sliderImages.length; i++) {
          if ((validationHistory?.returnValues?.contains("Image ${i + 1}") ??
                  false) &&
              sliderImages[i] == (product?.productImages?[i] ?? '')) {
            isRequiredUpdate.value = "Update Return Data";

            return false;
          }
        }
      }
      if (product?.productOptions?.productSizes?.isNotEmpty ?? false) {
        if (size.value.sizes.isNotEmpty) {
          if ((validationHistory?.returnValues?.contains("Size unit 1") ??
              false)) {
            String sizeUnit =
                product?.productOptions?.productSizes?[0].sizeUnit ?? '';
            if (sizeUnit == size.value.sizeUnit) {
              isRequiredUpdate.value = appLocalizations.updateReturnSizeAlert;
              return false;
            }
          }
        }
      }
      if (product?.productOptions?.productColors?.isNotEmpty ?? false) {
        for (ColorModel color in colorList) {
          String colorId = color.id;
          if (colorId.isNotEmpty) {
            int indexWhere = (product?.productOptions?.productColors ?? [])
                .indexWhere((element) => element.id == colorId);
            if (indexWhere != -1) {
              ProductColors? productColors =
                  product?.productOptions?.productColors?[indexWhere];
              if ((validationHistory?.returnValues
                          ?.contains("Color Name ${indexWhere + 1}") ??
                      false) &&
                  color.colorName == productColors?.colorName) {
                isRequiredUpdate.value = appLocalizations.haventUpdateColor;
                return false;
              }
              if ((validationHistory?.returnValues?.contains(
                          "Color family ${indexWhere + 1} ${color.colorFamily}") ??
                      false) &&
                  color.colorFamily == productColors?.colorFamily) {
                isRequiredUpdate.value = appLocalizations.haventUpdateColor;
                return false;
              }
              if ((validationHistory?.returnValues?.any((element) =>
                          element.contains("Color Icon ${indexWhere + 1}")) ??
                      false) &&
                  color.thumnailUrl == productColors?.colorIcon) {
                isRequiredUpdate.value = appLocalizations.haventUpdateColor;
                return false;
              }
              for (var j = 0;
                  j < (productColors?.colorImages?.length ?? 0);
                  j++) {
                if (((validationHistory?.returnValues?.contains(
                                "${product?.productOptions?.productColors?[indexWhere].colorFamily}${product?.productOptions?.productColors?[indexWhere].colorName} image ${j + 1}") ??
                            false) ||
                        (validationHistory?.returnValues?.contains(
                                "${product?.productOptions?.productColors?[indexWhere].colorName} image ${j + 1}") ??
                            false)) &&
                    color.imagesUrl.contains(product?.productOptions
                        ?.productColors?[indexWhere].colorImages?[j])) {
                  isRequiredUpdate.value = appLocalizations.haventUpdateColor;
                  return false;
                }
              }
            }
          }
        }
      }
      if (product?.productOptions?.productCustomOptions?.isNotEmpty ?? false) {
        for (var options in customOptionList) {
          if (options.optionId.isNotEmpty) {
            int optionIndex = (product?.productOptions?.productCustomOptions ??
                    [])
                .indexWhere((element) => element.optionId == options.optionId);
            if (optionIndex != -1) {
              ProductCustomOptions? customOptions =
                  product?.productOptions?.productCustomOptions?[optionIndex];
              // if ((validationHistory?.returnValues?.contains(
              //         "${optionIndex + 1}${GlobalMethods.ordinal(optionIndex + 1)} custom option title") ??
              //     false)) {
               if ((validationHistory?.returnValues?.any((element) => element.contains("${optionIndex + 1}${GlobalMethods.ordinal(optionIndex + 1)} custom option title")) ??
                  false)) {
                String optionTitle = customOptions?.optionTitle ?? '';
              

                if (optionTitle == options.title) {
                  isRequiredUpdate.value =
                      appLocalizations.alertMessageUpdateCCustomOption;
                  return false;
                }
              }
            }
          }
        }
      }

      bool requiredUpdate = validateOptionsFeilds();

      isRequiredUpdate.value = "";
      haveImages.value = false;
      if (requiredUpdate) {
        List<String> images = [];
        for (String element in sliderImages) {
          if (product != null) {
            if (!product!.productImages!.contains(element)) {
              images.add(element);
            }
          } else {
            images.add(element);
          }
        }
        return setProductOptionscacheInfo(images: images);
      } else {
        return true;
      }
    } else {
      isRequiredUpdate.value = "";
      haveImages.value = false;
      List<String> images = [];
      for (var element in sliderImages) {
        images.add(element);
      }
      return setProductOptionscacheInfo(images: images);
    }
  }

  setProductOptionscacheInfo({required List<String> images}) {
    List<Map<String, dynamic>> arrSpecifications = [];
    List<Map<String, dynamic>> arrSizes = [];
    List<Map<String, dynamic>> arrCustumOption = [];
    Map<String, dynamic> variations = {};
    List<Map<String, dynamic>> arrColors = [];
    if (productSpecificationList.isNotEmpty) {
      for (var spec in productSpecificationList) {
        arrSpecifications.add({
          'specsTitle': spec.title,
          'specsValue': spec.value,
          'specsTitleAr': spec.titleAr,
          'specsValueAr': spec.valueAr,
        });
      }
    }
    if (size.value.sizes.isNotEmpty) {
      List<Map<String, dynamic>> sizeValues = [];
      for (SizeValueModel element in size.value.sizes) {
        sizeValues.add({
          'value': element.value,
          'isValueHidden': element.isValueHidden,
        });
      }

      arrSizes.add({
        'sizeUnit': size.value.sizeUnit,
        'sizeUnitAr': size.value.sizeUnitAr,
        'sizeValues': sizeValues,
      });
    }

    if (customOptionList.isNotEmpty) {
      for (var option in customOptionList) {
        List<Map<String, dynamic>> optionValues = [];
        for (CustomOptionValues element in option.valueList) {
          optionValues.add({
            'value': element.value,
            'valueAr': element.valueAr,
            'isValueHidden': element.isValueHidden,
          });
        }
        arrCustumOption.add({
          'optionTitle': option.title,
          'optionTitleAr': option.titleAr,
          'optionValues': optionValues,
        });
      }
    }
    if (arrVariations.isNotEmpty) {
      variations.addAll({
        'options': false,
        'variations': arrVariations,
      });
    }
    if (colorList.isNotEmpty) {
      for (var color in colorList) {
        arrColors.add({
          'colorImages': color.imagesUrl,
          'colorFamily': color.colorFamily,
          'colorFamilyAr': color.colorFamilyAr,
          'colorName': color.colorName,
          'colorNameAr': color.colorNameAr,
          'colorIcon': color.thumnailUrl,
          'isHidden': color.isHidden,
        });
      }
    }
    Map<String, dynamic> info = {
      'images': images,
      'price': price,
      'qty': qty,
      "notify_minimum_qty": isHideMinimumQty.value,
      "minimum_qty": minQty,
      "specs": arrSpecifications,
      "sizes": arrSizes,
      "custom_options": arrCustumOption,
      "variations": variations,
      "colors": arrColors,
    };
    String details = jsonEncode(info);
    log("saving details into cache $details");

    prefs.setString(productDetails, details);
    return true;
  }

  bool hasValidListData({
    required List arrVariations,
    required List<ColorModel> colorList,
    required SizeModel size,
    required List<CustomOption> customOptionList,
  }) {
    final hasArrVariationsData = arrVariations.isNotEmpty;
    final hasOtherListData = colorList.isNotEmpty ||
        size.sizes.isNotEmpty ||
        customOptionList.isNotEmpty;
    if (hasArrVariationsData && !hasOtherListData) {
      arrVariations.clear();
    }
    return hasArrVariationsData != hasOtherListData;
  }

  bool _checkSizeVariations(Variants variant, List<SizeValueModel> sizes) {
    if (arrVariations.length != sizes.length) {
      log("position 8");
      return true;
    } else {
      var contain = sizes.where((element) => element.value == variant.size);
      if (contain.isEmpty) {
        log("position 9");
        return true;
      } else {
        return false;
      }
    }
  }

  bool _checkSizeVariationsForColor(
      SizeValueModel size, List<Variants> variations, SizeModel sizeModel) {
    bool requiredUpdate = false;
    for (var variant in variations) {
      for (Variations element in variant.variations ?? []) {
        if (sizeModel.sizes.indexWhere((size) =>
                size.value == element.variantAttributes?.variantSize?.size) ==
            -1) {
          requiredUpdate = true;
          log("position 20");
          break;
        }
      }
      if (requiredUpdate) {
        break;
      }
      int indexwhere = (variant.variations ?? []).indexWhere((element) =>
          element.variantAttributes?.variantSize?.size == size.value);
      if (indexwhere == -1) {
        requiredUpdate = true;
        log("position 11");
        break;
      }
    }
    return requiredUpdate;
  }

  CustomOption _createOption(String title, String value) {
    return CustomOption(title,"", "","", [CustomOptionValues(value,"", false)], "");
  }

  void _addValueToOption(CustomOption option, String value) {
    int index =
        option.valueList.indexWhere((element) => element.value == value);
    if (index == -1) {
      option.valueList.add(CustomOptionValues(value,"", false));
    }
  }

  bool _optionsMatch(CustomOption option, CustomOption customOption) {
    if (option.valueList.length != customOption.valueList.length) return false;

    if (!listEquals(
        customOption.valueList.map((e) => e.value.trimRight()).toList(),
        option.valueList.map((e) => e.value.trimRight()).toList())) {
      return false;
    }
    for (var value in customOption.valueList) {
      if (!option.valueList.any((element) => element.value == value.value)) {
        return false;
      }
    }
    return true;
  }

  List<ProductColors>? serverProductColors;
  ProductSizes? serverProductSizes;
  List<ProductCustomOptions>? serverProductCustomOptions;

  void updateServerData() {
    if (serverProductColors != null ||
        (product?.productOptions?.productColors?.isEmpty ?? false)) {
      final updatedColorsList = colorList
          .map((element) => ProductColors(
                id: element.id,
                isHidden: element.isHidden,
                colorName: element.colorName,
                colorFamily: element.colorFamily,
                colorIcon: element.thumnailUrl,
                colorImages: element.imagesUrl,
              ))
          .toList();
      print("updatedColorsList $updatedColorsList");
      serverProductColors = [...updatedColorsList]; // update the local copy
    }

    if (serverProductSizes != null ||
        (product?.productOptions?.productSizes?.isEmpty ?? false)) {
      final updatedSizes = size.value.sizes
          .map((element) => OptionValues(
                value: element.value,
                isValueHidden: element.isValueHidden,
              ))
          .toList();
      serverProductSizes = updatedSizes.isNotEmpty
          ? ProductSizes(
              sizeUnit: size.value.sizeUnit, sizeValues: updatedSizes)
          : null;
    }

    if (serverProductCustomOptions != null ||
        (product?.productOptions?.productCustomOptions?.isEmpty ?? false)) {
      final updatedCustomList = customOptionList
          .map((option) => ProductCustomOptions(
                optionTitle: option.title,
                optionId: option.optionId,
                optionValues: option.valueList
                    .map((element) => OptionValues(
                          value: element.value,
                          isValueHidden: element.isValueHidden,
                        ))
                    .toList(),
              ))
          .toList();
      serverProductCustomOptions = [
        ...updatedCustomList
      ]; // update the local copy

      log(jsonEncode(serverProductCustomOptions));
    }
  }

  bool validateVariationsOptions() {
    bool requiredUpdate = false;

    bool hasValidList = hasValidListData(
        arrVariations: arrVariations,
        colorList: colorList,
        size: size.value,
        customOptionList: customOptionList);
    if (hasValidList) {
      updateServerData();
      return hasValidList;
    } else {
      if (product != null) {
        final productOptions = product?.productOptions;
        if (productOptions != null) {
          if ((productOptions.productColors?.isNotEmpty ?? false) &&
              serverProductColors == null) {
            serverProductColors = productOptions.productColors;
          }
          if ((productOptions.productSizes?.isNotEmpty ?? false) &&
              serverProductSizes == null) {
            serverProductSizes = productOptions.productSizes?.first;
          }
          if ((productOptions.productCustomOptions?.isNotEmpty ?? false) &&
              serverProductCustomOptions == null) {
            serverProductCustomOptions = productOptions.productCustomOptions;
          }
        }
      }
      if (colorList.isNotEmpty) {
        if (product != null &&
            serverProductColors?.length != colorList.length) {
          updateServerData();
          requiredUpdate = true;
          log("position 4");
        } else {
          bool hasVariationMismatch = !arrVariations.any((element) => colorList
              .any((color) => (element.colorFamily == color.colorFamily)));
          bool hasLengthMismatch = arrVariations.length != colorList.length;
          bool hasServerColorMismatch =
              serverProductColors?.isNotEmpty ?? false;

          if (product == null && arrVariations.isNotEmpty) {
            for (ColorModel color in colorList) {
              var variant = arrVariations.firstWhereOrNull((element) =>
                  element.colorFamily == color.colorFamily &&
                  element.colorName == color.colorName);
              if (variant != null) {
                for (Variations element in variant.variations ?? []) {
                  if (element.variantAttributes?.variantColor?.colorIcon !=
                      color.thumnailUrl) {
                    requiredUpdate = true;
                    log("position 7.04");
                  } else if (color.imagesUrl.asMap().entries.every((entry) =>
                      entry.value !=
                      element.variantAttributes?.variantColor
                          ?.colorImages?[entry.key])) {
                    requiredUpdate = true;
                    log("position 7.05");
                  }
                }
              }
            }
          }

          if (hasVariationMismatch) {
            requiredUpdate = true;
            log("position 5");
          } else if (hasLengthMismatch) {
            requiredUpdate = true;
            log("position 5.1");
          } else if (hasServerColorMismatch) {
            for (ColorModel color in colorList) {
              if (color.id.isNotEmpty) {
                if (serverProductColors != null &&
                    (serverProductColors?.isNotEmpty ?? false)) {
                  try {
                    final serverColor = serverProductColors
                        ?.firstWhere((element) => element.id == color.id);
                    if (serverColor != null &&
                        serverColor.isHidden != color.isHidden) {
                      updateServerData();
                      requiredUpdate = true;
                      log("position 5.2");
                    }
                  } catch (e) {
                    if (kDebugMode) {
                      print('serverColor Error: $e');
                    }
                  }
                }
              } else {
                if (!(serverProductColors?.any((element) =>
                        element.colorFamily == color.colorFamily &&
                        element.colorName == color.colorName &&
                        (element.id?.isEmpty ?? false)) ??
                    false)) {
                  updateServerData();
                  log("position 5.3");
                  requiredUpdate = true;
                }
              }
            }
          } else if (product == null) {
            for (ColorModel color in colorList) {
              int indexWhere = arrVariations.indexWhere((element) =>
                  element.colorFamily == color.colorFamily &&
                  element.colorName == color.colorName);
              if (indexWhere != -1) {
                if (color.id !=
                    arrVariations[indexWhere]
                        .variations
                        ?.first
                        .variantAttributes
                        ?.variantColor
                        ?.colorId) {
                  requiredUpdate = true;
                  log("position 7.4");
                }
              }
            }
          }
        }
      } else if (serverProductColors?.isNotEmpty ?? false) {
        updateServerData();
        requiredUpdate = true;
        log("position 6");
      } else if (arrVariations.isNotEmpty &&
          (arrVariations.first.variations?.any((variant) =>
                  variant.variantAttributes?.variantColor?.colorFamily !=
                  null) ??
              false)) {
        requiredUpdate = true;
        log("position 7");
      }

      if (size.value.sizes.isNotEmpty) {
        if (colorList.isEmpty) {
          for (SizeValueModel sizes in size.value.sizes) {
            if (sizes.isReAddedValue && !sizes.isReGenratedVariant) {
              int indexWhere = size.value.sizes
                  .indexWhere((element) => element.value == sizes.value);
              if (indexWhere != -1) {
                size.value.sizes[indexWhere].isReGenratedVariant = true;
              }
              requiredUpdate = true;
              updateServerData();
              log("position 10.10");
              break; // break out of the loop if _checkSizeVariationsForColor returns true
            }
          }
          for (var variant in arrVariations) {
            if (variant
                    .variations?.first.variantAttributes?.variantSize?.unit ==
                size.value.sizeUnit) {
              if (_checkSizeVariations(variant, size.value.sizes)) {
                updateServerData();
                requiredUpdate = true;
                break; // break out of the loop if _checkSizeVariations returns true
              }
            } else {
              updateServerData();

              requiredUpdate = true;
              log("position 10");
              break;
            }
          }
        } else {
          for (SizeValueModel sizes in size.value.sizes) {
            if (_checkSizeVariationsForColor(
                sizes, arrVariations, size.value)) {
              requiredUpdate = true;
              updateServerData();
              log("position 10.2");
              break; // break out of the loop if _checkSizeVariationsForColor returns true
            }
          }
        }
        if (serverProductSizes != null && !requiredUpdate) {
          if (serverProductSizes?.sizeUnit == size.value.sizeUnit) {
            for (SizeValueModel size in size.value.sizes) {
              final serverSize = serverProductSizes?.sizeValues
                  ?.firstWhere((element) => element.value == size.value);
              if (serverSize != null &&
                  serverSize.isValueHidden != size.isValueHidden) {
                updateServerData();
                requiredUpdate = true;
                log("position 10.4");
              }
            }
          } else {
            updateServerData();
            requiredUpdate = true;
            log("position 10.3");
          }
        }
      } else {
        if (arrVariations.isNotEmpty) {
          for (Variations variant in arrVariations.first.variations ?? []) {
            if (variant.variantAttributes?.variantSize != null) {
              updateServerData();
              requiredUpdate = true;
              log("position 19");
              break;
            }
          }
        }
      }
      if (customOptionList.isNotEmpty) {
        List<CustomOption> variantOptions = [];
        bool hasCustomOptions = (arrVariations.first.variations ?? []).any(
            (variant) =>
                variant.variantAttributes?.variantCustomOptions?.isNotEmpty ??
                false);
        if (serverProductCustomOptions?.isNotEmpty ?? false) {
          if (!listEquals(
              (serverProductCustomOptions ?? [])
                  .map((e) => e.optionTitle?.trimRight())
                  .toList(),
              customOptionList.map((e) => e.title.trimRight()).toList())) {
            updateServerData();
            requiredUpdate = true;
            log("position 12.13");
          } else if (!listEquals(
              (serverProductCustomOptions ?? [])
                  .map((e) => e.optionId?.trimRight())
                  .toList(),
              customOptionList.map((e) => e.optionId.trimRight()).toList())) {
            updateServerData();
            requiredUpdate = true;
            log("position 12.14");
          } else {
            for (CustomOption customOption in customOptionList) {
              int index = (product?.productOptions?.productCustomOptions ?? [])
                  .indexWhere(
                      (element) => element.optionTitle == customOption.title);
              if (index != -1) {
                List<OptionValues> optionsValues =
                    serverProductCustomOptions?[index].optionValues ?? [];

                if (!listEquals(
                    customOption.valueList
                        .map((e) => e.value.trimRight())
                        .toList(),
                    optionsValues.map((e) => e.value?.trimRight()).toList())) {
                  updateServerData();
                  requiredUpdate = true;
                  log("position 12.12");
                }
              }
            }
          }
        }
        if (!hasCustomOptions) {
          updateServerData();
          requiredUpdate = true;
          log("position 12");
        } else {
          for (Variations variant in arrVariations.first.variations ?? []) {
            for (var customOption
                in variant.variantAttributes?.variantCustomOptions ?? []) {
              int index = variantOptions.indexWhere(
                  (option) => option.title == customOption.attributeTitle);
              if (index != -1) {
                _addValueToOption(
                    variantOptions[index], customOption.attributeValue);
              } else {
                variantOptions.add(_createOption(
                    customOption.attributeTitle, customOption.attributeValue));
              }
            }
          }

          if (variantOptions.length != customOptionList.length) {
            updateServerData();
            requiredUpdate = true;
            log("position 13");
          } else {
            for (var customOption in customOptionList) {
              int index = variantOptions
                  .indexWhere((option) => option.title == customOption.title);
              if (index != -1) {
                if (!_optionsMatch(variantOptions[index], customOption)) {
                  requiredUpdate = true;
                  log("position 14");
                  break;
                }
              } else {
                requiredUpdate = true;
                log("position 16");
                break;
              }
            }
          }
        }
        if (serverProductCustomOptions != null && !requiredUpdate) {
          for (CustomOption option in customOptionList) {
            final serverOptions = serverProductCustomOptions?.firstWhereOrNull(
                (element) => element.optionTitle == option.title);
            if (serverOptions != null) {
              for (var optionsValue in option.valueList) {
                final serverOptionValue = serverOptions.optionValues
                    ?.firstWhere(
                        (element) => element.value == optionsValue.value);
                if (serverOptionValue != null &&
                    serverOptionValue.isValueHidden !=
                        optionsValue.isValueHidden) {
                  updateServerData();
                  requiredUpdate = true;
                  log("position 16.4");
                  break;
                }
              }
            }
          }
        }
      } else {
        if (product != null) {
          if (product?.productOptions?.productCustomOptions?.isNotEmpty ??
              false) {
            requiredUpdate = true;
            updateServerData();
            log("position 17");
          } else {
            if (arrVariations.isNotEmpty) {
              bool hasVariantCustomOptions =
                  (arrVariations.first.variations ?? []).any((variant) =>
                      variant.variantAttributes?.variantCustomOptions
                          ?.isNotEmpty ??
                      false);
              if (hasVariantCustomOptions) {
                requiredUpdate = true;
                updateServerData();
                log("position 18");
              }
            }
          }
        }
      }
    }

    return requiredUpdate;
  }

  bool validateOptionsFeilds() {
    bool requiredUpdate = false;
    if (sliderImages.length != product?.productImages?.length) {
      print("1");
      requiredUpdate = true;
    }
    if (sliderImages.isNotEmpty) {
      if ((sliderImages.length) != (product?.productImages?.length ?? 0)) {
        print("2");
        return true;
      } else {
        for (var i = 0; i < sliderImages.length; i++) {
          if (sliderImages[i] != product?.productImages?[i]) {
            requiredUpdate = true;
            print("3");
            break;
          }
        }
      }
    }
    if ((colorList.length) !=
        (product?.productOptions?.productColors?.length ?? 0)) {
      requiredUpdate = true;
      print("4");
      return requiredUpdate;
    }
    if (colorList.isNotEmpty) {
      for (var i = 0; i < (colorList.length); i++) {
        if (colorList[i].colorName !=
            product?.productOptions?.productColors?[i].colorName) {
          requiredUpdate = true;
          print("5");
          break;
        }

        if (colorList[i].colorFamily !=
            product?.productOptions?.productColors?[i].colorFamily) {
          requiredUpdate = true;
          print("6");
          break;
        }
        if (colorList[i].isHidden !=
            product?.productOptions?.productColors?[i].isHidden) {
          requiredUpdate = true;
          print("7");
          break;
        }
        if (colorList[i].imagesUrl.length !=
            product?.productOptions?.productColors?[i].colorImages?.length) {
          requiredUpdate = true;
          print("17");
          break;
        }
        for (var j = 0; j < colorList[i].imagesUrl.length; j++) {
          if (colorList[i].imagesUrl[j] !=
              product?.productOptions?.productColors?[i].colorImages?[j]) {
            requiredUpdate = true;
            print("18");
            break;
          }
        }
      }
    }

    if (size.value.sizes.isNotEmpty) {
      if (product?.productOptions?.productSizes?.isNotEmpty ?? false) {
        if (size.value.sizeUnit !=
            product?.productOptions?.productSizes?[0].sizeUnit) {
          print("8");
          return true;
        }
        if (size.value.sizes.length !=
            product?.productOptions?.productSizes?[0].sizeValues!.length) {
          print("9");
          return true;
        }

        for (var i = 0; i < size.value.sizes.length; i++) {
          if (size.value.sizes[i].value !=
                  product
                      ?.productOptions?.productSizes?[0].sizeValues?[i].value ||
              size.value.sizes[i].isValueHidden !=
                  product?.productOptions?.productSizes?[0].sizeValues?[i]
                      .isValueHidden) {
            requiredUpdate = true;
            print("10");
            break;
          }
        }
      } else {
        print("11");
        requiredUpdate = true;
      }
    } else if (size.value.sizes.isEmpty &&
        (product != null &&
            (product?.productOptions?.productSizes?.isNotEmpty ?? false))) {
      print("11.1");
      return true;
    }
    if (customOptionList.length !=
        (product?.productOptions?.productCustomOptions?.length ?? 0)) {
      print("12");
      return true;
    }
    if (customOptionList.isNotEmpty) {
      if (product?.productOptions?.productCustomOptions?.isNotEmpty ?? false) {
        for (var i = 0; i < customOptionList.length; i++) {
          if (customOptionList[i].valueList.length !=
              productCustomOptions[i].optionValues?.length) {
            print("13");
            requiredUpdate = true;
            break;
          } else if (customOptionList[i].title !=
              product?.productOptions?.productCustomOptions?[i].optionTitle) {
            print("14");
            requiredUpdate = true;
            break;
          }
          for (var j = 0; j < customOptionList[i].valueList.length; j++) {
            if (customOptionList[i].valueList[j].value !=
                    product?.productOptions?.productCustomOptions?[i]
                        .optionValues?[j].value ||
                (customOptionList[i].valueList[j].isValueHidden !=
                    product?.productOptions?.productCustomOptions?[i]
                        .optionValues?[j].isValueHidden)) {
              print("15");
              requiredUpdate = true;
              break;
            }
          }
        }
      } else {
        print("16");
        requiredUpdate = true;
      }
    }

    return requiredUpdate;
  }

  bool filterVeriations(ProductVariationModel productVariationsModel) {
    List<Variants> variations = arrVariations;
    List<Variants> newVariations = [];
    bool isValid =
        (productVariationsModel.colorGroupedVariants ?? []).isEmpty &&
            (productVariationsModel.sizeGroupedVariants ?? []).isEmpty &&
            (productVariationsModel.variations ?? []).isEmpty;
    if (isValid) {
      arrVariations.clear();
      return false;
    }

    if (colorList.isNotEmpty) {
      if (variations.isNotEmpty) {
        for (Variants variant
            in productVariationsModel.colorGroupedVariants ?? []) {
          List<Variations> newOptions = [];
          for (Variations variation in variant.variations ?? []) {
            int index = variations.indexWhere((element) =>
                element.variations?.first.variantAttributes?.variantColor
                    ?.colorId ==
                (variation.variantAttributes?.variantColor?.colorId));
            if (index != -1) {
              int subIndex =
                  (variations[index].variations ?? []).indexWhere((element) {
                return (element.variantCode ?? "")
                    .contains((variation.variantCode ?? ""));
              });
              if (subIndex != -1) {
                dynamic variantprice =
                    variations[index].variations?[subIndex].variantPrice ?? 0;

                String gtinCode = variations[index]
                        .variations?[subIndex]
                        .variantManufacturerId ??
                    "";
                String id = variations[index].variations?[subIndex].id ?? "";
                int variantquantity =
                    variations[index].variations?[subIndex].variantQte ?? 0;
                bool isVariantVisible =
                    variations[index].variations?[subIndex].isVariantVisible ??
                        false;

                bool isGtinReturned =
                    variations[index].variations?[subIndex].isGtinReturned ??
                        false;

                bool isPriceReturned =
                    variations[index].variations?[subIndex].isPriceReturned ??
                        false;

                bool isQtyReturned =
                    variations[index].variations?[subIndex].isQtyReturned ??
                        false;
                String variantEin =
                    variations[index].variations?[subIndex].variantEIN ?? "";
                bool isUpdated =
                    variations[index].variations?[subIndex].isUpdated ?? false;
                if (isVariantVisible != variation.isVariantVisible) {
                  isUpdated = true;
                  isVariantVisible = variation.isVariantVisible ?? false;
                }
                ProductVariants? oldVariation;
                if (product != null) {
                  for (ProductVariants element
                      in product!.productVariants ?? []) {
                    if (element.variantEIN == variantEin) {
                      oldVariation = element;
                      break;
                    }
                  }
                }
                if (oldVariation != null) {
                  bool variantVisibilty =
                      oldVariation.isVariantVisible ?? false;
                  if (variantVisibilty == variation.isVariantVisible) {
                    if (!isUpdated) {
                      isUpdated = false;
                    }
                  }
                }

                for (SizeValueModel sizeValue in size.value.sizes) {
                  if (sizeValue.isReAddedValue) {
                    if (variation.variantAttributes?.variantSize?.size ==
                        sizeValue.value) {
                      variantprice = int.parse(price);
                      variantquantity = int.parse(qty);
                      gtinCode = "";
                    }
                  }
                }

                if ((product?.productOptions?.productCustomOptions ?? [])
                        .isNotEmpty &&
                    (!listEquals(
                        (product?.productOptions?.productCustomOptions ?? [])
                            .map((e) => e.optionId?.trimRight())
                            .toList(),
                        customOptionList
                            .map((e) => e.optionId.trimRight())
                            .toList()))) {
                  variantprice = int.parse(price);
                  variantquantity = int.parse(qty);
                  gtinCode = "";
                } else {
                  for (CustomOption options in customOptionList) {
                    for (CustomOptionValues value in options.valueList) {
                      if (value.isReAddedValue) {
                        if (variation.variantAttributes?.variantCustomOptions
                                ?.any((element) =>
                                    element.attributeTitle == options.title &&
                                    element.attributeValue == value.value) ??
                            false) {
                          variantprice = int.parse(price);
                          variantquantity = int.parse(qty);
                          gtinCode = "";
                        }
                      }
                    }
                  }
                }

                for (CustomOption options in customOptionList) {
                  for (CustomOptionValues value in options.valueList) {
                    if (value.isReAddedValue) {
                      if (variation.variantAttributes?.variantCustomOptions
                              ?.any((element) =>
                                  element.attributeTitle == options.title &&
                                  element.attributeValue == value.value) ??
                          false) {
                        variantprice = int.parse(price);
                        variantquantity = int.parse(qty);
                        gtinCode = "";
                      }
                    }
                  }
                }

                newOptions.add(Variations(
                    id: id,
                    variantName: variation.variantName,
                    variantCode: variation.variantCode,
                    variantPrice: variantprice,
                    variantQte: variantquantity,
                    variantEIN: variantEin,
                    variantManufacturerId: gtinCode,
                    variantImages: variation.variantImages,
                    variantAttributes: variation.variantAttributes,
                    isVariantModified: variation.isVariantModified,
                    isUpdated: isUpdated,
                    isQtyReturned: isQtyReturned,
                    isGtinReturned: isGtinReturned,
                    isPriceReturned: isPriceReturned,
                    isVariantVisible: isVariantVisible));
              } else {
                newOptions.add(variation);
              }
            } else {
              newOptions.add(variation);
            }
          }
          newVariations.add(Variants(
              colorName: variant.colorName,
              colorFamily: variant.colorFamily,
              colorIcon: variant.colorIcon,
              variations: newOptions));
        }

        arrVariations.clear();
        arrVariations.addAll(newVariations);
      } else {
        arrVariations.clear();
        for (Variants variant
            in productVariationsModel.colorGroupedVariants ?? []) {
          arrVariations.add(variant);
        }
      }
    } else if (size.value.sizeUnit.isNotEmpty) {
      if (productVariationsModel.sizeGroupedVariants != null) {
        if (variations.isNotEmpty) {
          for (Variants variant
              in productVariationsModel.sizeGroupedVariants ?? []) {
            List<Variations> newOptions = [];
            for (Variations variation in variant.variations ?? []) {
              int index = variations.indexWhere((element) =>
                  element.size ==
                  variation.variantAttributes?.variantSize?.size);
              if (index != -1) {
                int subIndex = (variations[index].variations ?? []).indexWhere(
                    (element) => element.variantCode == variation.variantCode);
                if (subIndex != -1) {
                  dynamic variantPrice =
                      variations[index].variations?[subIndex].variantPrice ?? 0;
                  String gtinCode = variations[index]
                          .variations?[subIndex]
                          .variantManufacturerId ??
                      "";
                  int variantquantity =
                      variations[index].variations?[subIndex].variantQte ?? 0;
                  bool isVariantVisible = variations[index]
                          .variations?[subIndex]
                          .isVariantVisible ??
                      false;
                  String id = variations[index].variations?[subIndex].id ?? "";

                  String variantEin =
                      variations[index].variations?[subIndex].variantEIN ?? "";

                  bool isGtinReturned =
                      variations[index].variations?[subIndex].isGtinReturned ??
                          false;

                  bool isPriceReturned =
                      variations[index].variations?[subIndex].isPriceReturned ??
                          false;

                  bool isQtyReturned =
                      variations[index].variations?[subIndex].isQtyReturned ??
                          false;
                  bool isUpdated =
                      variations[index].variations?[subIndex].isUpdated ??
                          false;
                  if (isVariantVisible != variation.isVariantVisible) {
                    isUpdated = true;
                    isVariantVisible = variation.isVariantVisible ?? false;
                  }
                  ProductVariants? oldVariation;
                  if (product != null) {
                    for (ProductVariants element
                        in product!.productVariants ?? []) {
                      if (element.variantEIN == variantEin) {
                        oldVariation = element;
                        break;
                      }
                    }
                  }
                  if (oldVariation != null) {
                    bool variantVisibilty =
                        oldVariation.isVariantVisible ?? false;
                    if (variantVisibilty == variation.isVariantVisible) {
                      isUpdated = false;
                    }
                  }

                  for (SizeValueModel sizeValue in size.value.sizes) {
                    if (sizeValue.isReAddedValue) {
                      if (variation.variantAttributes?.variantSize?.size ==
                          sizeValue.value) {
                        variantPrice = int.parse(price);
                        variantquantity = int.parse(qty);
                        gtinCode = "";
                      }
                    }
                  }
                  if ((product?.productOptions?.productCustomOptions ?? [])
                          .isNotEmpty &&
                      (!listEquals(
                          (product?.productOptions?.productCustomOptions ?? [])
                              .map((e) => e.optionId?.trimRight())
                              .toList(),
                          customOptionList
                              .map((e) => e.optionId.trimRight())
                              .toList()))) {
                    variantPrice = int.parse(price);
                    variantquantity = int.parse(qty);
                    gtinCode = "";
                  } else {
                    for (CustomOption options in customOptionList) {
                      for (CustomOptionValues value in options.valueList) {
                        if (value.isReAddedValue) {
                          if (variation.variantAttributes?.variantCustomOptions
                                  ?.any((element) =>
                                      element.attributeTitle == options.title &&
                                      element.attributeValue == value.value) ??
                              false) {
                            variantPrice = int.parse(price);
                            variantquantity = int.parse(qty);
                            gtinCode = "";
                          }
                        }
                      }
                    }
                  }

                  newOptions.add(Variations(
                      id: id,
                      variantName: variation.variantName,
                      variantCode: variation.variantCode,
                      variantPrice: variantPrice,
                      variantQte: variantquantity,
                      variantEIN: variantEin,
                      variantManufacturerId: gtinCode,
                      variantImages: variation.variantImages,
                      variantAttributes: variation.variantAttributes,
                      isVariantModified: variation.isVariantModified,
                      isUpdated: isUpdated,
                      isQtyReturned: isQtyReturned,
                      isGtinReturned: isGtinReturned,
                      isPriceReturned: isPriceReturned,
                      isVariantVisible: isVariantVisible));
                } else {
                  newOptions.add(variation);
                }
              } else {
                newOptions.add(variation);
              }
            }

            newVariations.add(Variants(
                typename: variant.typename,
                unit: variant.size,
                size: variant.size,
                variations: newOptions));
          }
          arrVariations.clear();
          arrVariations.addAll(newVariations);
        } else {
          arrVariations.clear();
          for (Variants variant
              in productVariationsModel.sizeGroupedVariants ?? []) {
            arrVariations.add(variant);
          }
        }
      }
    } else if (customOptionList.isNotEmpty) {
      if (productVariationsModel.variations != null) {
        List<Variations> newOptions = [];
        if (variations.isNotEmpty) {
          List<Variations> oldOption = arrVariations.first.variations ?? [];
          for (Variations variant in productVariationsModel.variations ?? []) {
            int index = oldOption.indexWhere(
                (element) => element.variantCode == variant.variantCode);
            if (index != -1) {
              dynamic variantPrice = oldOption[index].variantPrice ?? 0;
              String gtinCode = oldOption[index].variantManufacturerId ?? "";
              String id = oldOption[index].id ?? "";
              int variantQuantity = oldOption[index].variantQte ?? 0;
              String variantEin = oldOption[index].variantEIN ?? "";
              bool isVariantVisible =
                  oldOption[index].isVariantVisible ?? false;
              bool isGtinReturned = oldOption[index].isGtinReturned ?? false;

              bool isPriceReturned = oldOption[index].isPriceReturned ?? false;

              bool isQtyReturned = oldOption[index].isQtyReturned ?? false;

              bool isUpdated = oldOption[index].isUpdated ?? false;
              if (isVariantVisible != variant.isVariantVisible) {
                isUpdated = true;
                isVariantVisible = variant.isVariantVisible ?? false;
              }
              ProductVariants? oldVariation;
              if (product != null) {
                for (ProductVariants element
                    in product!.productVariants ?? []) {
                  if (element.variantEIN == variantEin) {
                    oldVariation = element;
                    break;
                  }
                }
              }
              if (oldVariation != null) {
                bool variantVisibilty = oldVariation.isVariantVisible ?? false;
                if (variantVisibilty == variant.isVariantVisible) {
                  isUpdated = false;
                }
              }

              if ((product?.productOptions?.productCustomOptions ?? [])
                      .isNotEmpty &&
                  (!listEquals(
                      (product?.productOptions?.productCustomOptions ?? [])
                          .map((e) => e.optionId?.trimRight())
                          .toList(),
                      customOptionList
                          .map((e) => e.optionId.trimRight())
                          .toList()))) {
                variantPrice = int.parse(price);
                variantQuantity = int.parse(qty);
                gtinCode = "";
              } else {
                for (CustomOption options in customOptionList) {
                  for (CustomOptionValues value in options.valueList) {
                    if (value.isReAddedValue) {
                      if (variant.variantAttributes?.variantCustomOptions?.any(
                              (element) =>
                                  element.attributeTitle == options.title &&
                                  element.attributeValue == value.value) ??
                          false) {
                        variantPrice = int.parse(price);
                        variantQuantity = int.parse(qty);
                        gtinCode = "";
                      }
                    }
                  }
                }
              }

              newOptions.add(Variations(
                  id: id,
                  variantName: variant.variantName,
                  variantCode: variant.variantCode,
                  variantPrice: variantPrice,
                  variantQte: variantQuantity,
                  variantEIN: variantEin,
                  variantManufacturerId: gtinCode,
                  variantImages: variant.variantImages,
                  variantAttributes: variant.variantAttributes,
                  isGtinReturned: isGtinReturned,
                  isPriceReturned: isPriceReturned,
                  isQtyReturned: isQtyReturned,
                  isVariantModified: variant.isVariantModified,
                  isUpdated: isUpdated,
                  isVariantVisible: isVariantVisible));
            } else {
              newOptions.add(variant);
            }
          }
        } else {
          for (Variations variant in productVariationsModel.variations ?? []) {
            newOptions.add(variant);
          }
        }
        arrVariations.clear();
        arrVariations.add(Variants(variations: newOptions));
      }
    } else {
      arrVariations.clear();
    }
    return true;
  }

  Widget validateWidget(
      {required String cacheValue,
      required String serverValue,
      required String feildValue,
      required String errorCacheValue,
      required String errorServerValue}) {
    if (product != null && !isSimilarProduct) {
      if (cacheValue.isNotEmpty) {
        if (cacheValue.isNotEmpty && serverValue != cacheValue) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                serverValue.isEmpty ? errorServerValue : serverValue,
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
                style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                "$cacheValue\n",
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else if (cacheValue.isEmpty) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                serverValue,
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
                style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: AppColors.colorSecondary_Red,
                    decoration: TextDecoration.lineThrough),
              ),
              Text(
                errorCacheValue,
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
                style: FontStyles.fontRegular(
                  fontSize: 12,
                  color: AppColors.colorSecondary,
                ),
              ),
            ],
          );
        } else {
          return Text(
            feildValue,
            maxLines: 4,
            overflow: TextOverflow.ellipsis,
            style: FontStyles.fontRegular(
              fontSize: 12,
              color: Colors.white.withOpacity(0.5),
            ),
          );
        }
      } else {
        return Text(
          feildValue,
          maxLines: 4,
          overflow: TextOverflow.ellipsis,
          style: FontStyles.fontRegular(
            fontSize: 12,
            color: Colors.white.withOpacity(0.5),
          ),
        );
      }
    } else {
      return Text(
        feildValue,
        maxLines: 4,
        overflow: TextOverflow.ellipsis,
        style: FontStyles.fontRegular(
          fontSize: 12,
          color: Colors.white.withOpacity(0.5),
        ),
      );
    }
  }

  uploadMatchProduct(BuildContext context,
      {required bool isSaveForLater,
      required bool publishNow,
      required GraphQlInitilize request}) {

    List<Map<String, dynamic>> productColors = [];
    List<Map<String, dynamic>> productColorsAr = [];
    List<Map<String, dynamic>> productSizes = [];
    List<Map<String, dynamic>> productSizesAr = [];
    List<Map<String, dynamic>> productCustomOptions = [];
    List<Map<String, dynamic>> productCustomOptionsAr = [];
    List<Map<String, dynamic>> productVariations = [];
    List<Map<String, dynamic>> arrSpecifications = [];
    List<Map<String, dynamic>> arrSpecificationsAr = [];
    List<Map<String, dynamic>> sizeValues = [];
    if (productSpecificationList.isNotEmpty) {
      for (var spec in productSpecificationList) {
        arrSpecifications.add({
          'specsTitle': spec.title,
          'specsValue': spec.value,
          'specsId': spec.specsId
        });
        arrSpecificationsAr.add({
          'specsTitle': spec.titleAr,
          'specsValue': spec.valueAr,
          'specsId': spec.specsId
        });
      }
    }
    if (size.value.sizes.isNotEmpty) {
      for (SizeValueModel sizeValue in size.value.sizes) {
        sizeValues.add({
          'value': sizeValue.value,
          'isValueHidden': sizeValue.isValueHidden
        });
      }
      productSizes.add({
        'sizeUnit': size.value.sizeUnit,
        'sizeValues': sizeValues,
      });

      productSizesAr.add({
        'sizeUnit': size.value.sizeUnitAr,
        'sizeValues': sizeValues,
      });
    }
    if (customOptionList.isNotEmpty) {
      for (var option in customOptionList) {
        List<Map<String, dynamic>> optionValues = [];
          List<Map<String, dynamic>> optionValuesAr = [];
        for (CustomOptionValues optionsValues in option.valueList) {
          optionValues.add({
            'value': optionsValues.value,
            'isValueHidden': optionsValues.isValueHidden
          });
           optionValuesAr.add({
            'value': optionsValues.valueAr,
            'isValueHidden': optionsValues.isValueHidden
          });
        }
        productCustomOptions.add({
          'optionTitle': option.title,
          "optionId": option.optionId,
          'optionValues': optionValues,
        });
        productCustomOptionsAr.add({
          'optionTitle': option.titleAr,
          "optionId": option.optionId,
          'optionValues': optionValuesAr,
        });
      }
    }
    if (colorList.isNotEmpty) {
      for (ColorModel color in colorList) {
        String id = color.id;
        if (id.isEmpty) {
          int variationIndex = arrVariations
              .indexWhere((element) => element.colorIcon == color.thumnailUrl);
          if (variationIndex != -1) {
            int index = (arrVariations[variationIndex].variations ?? [])
                .indexWhere((element) =>
                    element.variantAttributes?.variantColor?.colorIcon ==
                    color.thumnailUrl);
            if (index != -1) {
              id = arrVariations[variationIndex]
                      .variations?[index]
                      .variantAttributes
                      ?.variantColor
                      ?.colorId ??
                  "";
            }
          }
        }

        productColors.add({
          "colorId": id,
          "isHidden": color.isHidden,
          'colorImages': color.imagesUrl,
          'colorFamily': color.colorFamily,
          'colorName': color.colorName,
          'colorIcon': color.thumnailUrl,
        });
           productColorsAr.add({
          "isHidden": color.isHidden,
          'colorImages': color.imagesUrl,
          'colorFamily': color.colorFamilyAr,
          'colorName': color.colorNameAr,
          'colorIcon': color.thumnailUrl,
          "colorId": id,
        });
      }
    }
    if (arrVariations.isNotEmpty) {
      for (Variants variation in arrVariations) {
        for (Variations element in variation.variations ?? []) {
          productVariations.add({
            'variantName': element.variantName ?? "",
            'variantCode': element.variantCode ?? "",
            'variantPrice': double.parse("${element.variantPrice}"),
            'variantQte': element.variantQte ?? 0,
            'variantEIN': element.variantEIN ?? "",
            'variantImages': element.variantImages ?? [],
            'variantAttributes': element.variantAttributes ?? {},
            'isVariantVisible': element.isVariantVisible ?? false,
            'variantManufacturerId': element.variantManufacturerId ?? "",
             'ar': {
                  'variantName': element.ar?.variantName ?? "",
                  'variantAttributes': element.ar?.variantAttributes ?? {},
                }
          });
        }
      }
    }
   var   productAr = {
      "productName": txtProductNameAr.text,
      "productDescription": txtProductDiscriptionAr.text,
      "productColors": productColorsAr,
      "productSizes": productSizesAr,
      "productCustomOptions": productCustomOptionsAr,
      "productSpecs": arrSpecificationsAr,
      "productReturnDuration": returnPolicyAr.value,
      "productWarrantyDuration": warrantyPolicyAr.value,
      "productReturnPolicy": txtProductPolicyAr.text
    };
    request.runMutation(
        context: context,
        query: GraphQlQuries.uploadMatchedProduct,
        variables: GraphQlVariables.addMatchProduct(
          productAr: productAr,
            productCatalogId: product?.id ?? "",
            productPrice: double.parse(price),
            productNotifOnMinQte: isHideMinimumQty.value,
            productAcceptReturn: acceptReturn.value,
            productReturnPolicy: txtProductPolicy.text,
            productFreeReturn: acceptReturn.isFalse
                ? false
                : returnType.value == "0"
                    ? true
                    : false,
            productNotFreeReturn: acceptReturn.isFalse
                ? false
                : returnType.value == "1"
                    ? true
                    : false,
            productReturnDuration: acceptReturn.isFalse
                ? ""
                : returnPolicy.value == 'Other'
                    ? customReturnPolicy
                    : returnPolicy.value,
            productWarrantyDuration: warrantyPolicy.value == 'Other'
                ? customWarrantyPolicy
                : warrantyPolicy.value,
            publishProduct: publishNow,
            saveProductForLater: isSaveForLater,
            productAvailableQte: int.parse(qty),
            productMinQte: isHideMinimumQty.value ? int.parse(minQty) : 0,
            productColors: productColors,
            productSizes: productSizes,
            productCustomOptions: productCustomOptions,
            productVariations: productVariations,
            isFreeDeliveryItem: freeDelivery.value,
            itemsPerOrder: int.parse(itemsPerOrder),
            productName: txtProductName.text,
            productDescription: txtProductDiscription.text,
            productManufacturerId: productId,
            productCategory: categoryId.value,
            productSubCategory: subCategoryId.value,
            productBrand:
                brandId.value.isEmpty ? manufacture.value : brandId.value,
            productKeyWords: keywords.split(","),
            oldProductId: oldProductId,
            productImages: productColors.isNotEmpty ? [] : sliderImages,
            isRematched: isRematched,
            productSizeCategory: shipmentFitsId,
            productSpecs: arrSpecifications));
  }

  List<Map<String, dynamic>> checkReaddedValue(
      List<Map<String, dynamic>> variations) {
    // Create a set to store the re-added values
    var reAddedValues = <Map<String, dynamic>>[];

    for (SizeValueModel sizeValue in size.value.sizes) {
      if (sizeValue.isReAddedValue) {
        final matchingVariations = variations.where((element) {
          VariantAttributes attributes = element["variantAttributes"];
          return attributes.variantSize?.size == sizeValue.value;
        });

        // filter by already updated

        List<Map<String, dynamic>> list = matchingVariations.toList();
        list.removeWhere((map) => map["isAdded"] == true);
        list.removeWhere((map) => map["isDeleted"] == true);

        // Add the matching variations to the set
        reAddedValues.addAll(list);
      }
    }

    // Iterate over the custom options
    for (final option in customOptionList) {
      // Check if the option has re-added values
      print("check 1");
      if (option.optionId.isEmpty &&
          (product?.productOptions?.productCustomOptions ?? []).isNotEmpty) {
        int index = (product?.productOptions?.productCustomOptions ?? [])
            .indexWhere((element) =>
                element.optionTitle == option.title &&
                element.optionValues?.length == option.valueList.length &&
                (element.optionValues ?? []).asMap().entries.every((element) =>
                    element.value.value ==
                    option.valueList[element.key].value));
        if (index != -1) {
          // Find the matching variations
          final matchingVariations = variations.where((element) {
            VariantAttributes attributes = element["variantAttributes"];
            return attributes.variantCustomOptions?.any(
                    (element) => element.attributeTitle == option.title) ??
                false;
          });

          // filter by already updated

          List<Map<String, dynamic>> list = matchingVariations.toList();
          list.removeWhere((map) => map["isAdded"] == true);
          list.removeWhere((map) => map["isDeleted"] == true);

          // Add the matching variations to the set
          reAddedValues.addAll(list);
        }
      }
      if (option.valueList.any((element) => element.isReAddedValue)) {
        // Iterate over the re-added values
        for (final optionsValue
            in option.valueList.where((element) => element.isReAddedValue)) {
          // Find the matching variations
          final matchingVariations = variations.where((element) {
            VariantAttributes attributes = element["variantAttributes"];
            return attributes.variantCustomOptions?.any((element) =>
                    element.attributeTitle == option.title &&
                    element.attributeValue == optionsValue.value) ??
                false;
          });

          // filter by already updated

          List<Map<String, dynamic>> list = matchingVariations.toList();
          list.removeWhere((map) => map["isAdded"] == true);
          list.removeWhere((map) => map["isDeleted"] == true);

          // Add the matching variations to the set
          reAddedValues.addAll(list);
        }
      }
      // check if id is match but option title is not same on productCustomOptions

      int idIndex = (product?.productOptions?.productCustomOptions ?? [])
          .indexWhere((element) => element.optionId == option.optionId);
      if (idIndex != -1 &&
          option.title !=
              product?.productOptions?.productCustomOptions?[idIndex]
                  .optionTitle) {
        final matchingVariations = variations.where((element) {
          VariantAttributes attributes = element["variantAttributes"];
          return attributes.variantCustomOptions
                  ?.any((element) => element.attributeTitle == option.title) ??
              false;
        });

        List<Map<String, dynamic>> list = matchingVariations.toList();
        list.removeWhere((map) => map["isAdded"] == true);
        list.removeWhere((map) => map["isDeleted"] == true);

        reAddedValues.addAll(list);
      }
    }

    // Update the variations list
    if (reAddedValues.isNotEmpty) {
      reAddedValues = reAddedValues.toSet().toList();

      for (int i = 0; i < reAddedValues.length; i++) {
        for (int j = i + 1; j < reAddedValues.length; j++) {
          if (reAddedValues[i]['_id'] == reAddedValues[j]['_id']) {
            variations.removeAt(j);
            j--;
          }
        }
      }
      for (final reAddedValue in reAddedValues) {
        final indexOf = variations.indexOf(reAddedValue);
        if (indexOf != -1) {
          variations[indexOf]["isAdded"] = true;
          variations[indexOf]["isUpdated"] = false;
          variations.add({
            ...variations[indexOf],
            'isDeleted': true,
            'isAdded': false,
            'isUpdated': false,
          });
        }
      }
    }

    return variations;
  }

  uploadProduct(BuildContext context,
      {required bool isSaveForLater,
      required bool publishNow,
      required GraphQlInitilize request}) {
    List<Map<String, dynamic>> productColors = [];
    List<Map<String, dynamic>> productColorsAr = [];
    List<Map<String, dynamic>> productSizes = [];
    List<Map<String, dynamic>> productSizesAr = [];
    List<Map<String, dynamic>> productCustomOptions = [];
    List<Map<String, dynamic>> productCustomOptionsAr = [];
    List<Map<String, dynamic>> productVariations = [];
    List<Map<String, dynamic>> arrSpecifications = [];
    List<Map<String, dynamic>> arrSpecificationsAr = [];
    List<Map<String, dynamic>> sizeValues = [];
    Map<String, dynamic> productAr = {};

    if (productSpecificationList.isNotEmpty) {
      for (var spec in productSpecificationList) {
        arrSpecifications.add({
          'specsTitle': spec.title,
          'specsValue': spec.value,
          'specsId': spec.specsId
        });
        arrSpecificationsAr.add({
          'specsTitle': spec.titleAr,
          'specsValue': spec.valueAr,
          'specsId': spec.specsId
        });
      }
    }
    if (size.value.sizes.isNotEmpty) {
      for (SizeValueModel sizeValue in size.value.sizes) {
        sizeValues.add({
          'value': sizeValue.value,
          'isValueHidden': sizeValue.isValueHidden
        });
      }
      productSizes.add({
        'sizeUnit': size.value.sizeUnit,
        'sizeValues': sizeValues,
      });
      productSizesAr.add({
        'sizeUnit': size.value.sizeUnitAr,
        'sizeValues': sizeValues,
      });
    }
    if (customOptionList.isNotEmpty) {
      for (var option in customOptionList) {
        List<Map<String, dynamic>> optionValues = [];
        List<Map<String, dynamic>> optionValuesAr = [];
        for (CustomOptionValues optionsValues in option.valueList) {
          optionValues.add({
            'value': optionsValues.value,
            'isValueHidden': optionsValues.isValueHidden
          });
           optionValuesAr.add({
            'value': optionsValues.valueAr,
            'isValueHidden': optionsValues.isValueHidden
          });
        }
        productCustomOptions.add({
          'optionTitle': option.title,
          "optionId": option.optionId,
          'optionValues': optionValues,
        });
        productCustomOptionsAr.add({
          'optionTitle': option.titleAr,
          "optionId": option.optionId,
          'optionValues': optionValuesAr,
        });
      }
    }

    if (colorList.isNotEmpty) {
      for (ColorModel color in colorList) {
        String colorId = "${ObjectId()}";
        productColors.add({
          "colorId": (color.id.isEmpty) ? colorId : color.id,
          "isHidden": color.isHidden,
          'colorImages': color.imagesUrl,
          'colorFamily': color.colorFamily,
          'colorName': color.colorName,
          'colorIcon': color.thumnailUrl,
        });
        productColorsAr.add({
          "isHidden": color.isHidden,
          'colorImages': color.imagesUrl,
          'colorFamily': color.colorFamilyAr,
          'colorName': color.colorNameAr,
          'colorIcon': color.thumnailUrl,
          "colorId": (color.id.isEmpty) ? colorId : color.id,
        });
      }
    }

    if (colorList.isEmpty &&
        size.value.sizes.isEmpty &&
        customOptionList.isEmpty &&
        arrVariations.isNotEmpty) {
      arrVariations.clear();
    }

    if (arrVariations.isNotEmpty) {
      for (Variants variation in arrVariations) {
        for (Variations element in variation.variations ?? []) {
          if (product != null) {
            if ((element.id ?? "").isEmpty) {
              productVariations.add({
                'variantName': element.variantName ?? "",
                'variantCode': element.variantCode ?? "",
                'variantPrice': double.parse("${element.variantPrice}"),
                'variantQte': element.variantQte ?? 0,
                'variantEIN': element.variantEIN ?? "",
                'variantImages': element.variantImages ?? [],
                'variantAttributes': element.variantAttributes ?? {},
                'isVariantVisible': element.isVariantVisible ?? false,
                'variantManufacturerId': element.variantManufacturerId ?? "",
                'productId': product?.id ?? "",
                'isDeleted': false,
                '_id': "",
                'isUpdated': false,
                'isAdded': true,
                'ar': {
                  'variantName': element.ar?.variantName ?? "",
                  'variantAttributes': element.ar?.variantAttributes ?? {},
                }
              });
            } else {
              productVariations.add({
                'variantName': element.variantName ?? "",
                'variantCode': element.variantCode ?? "",
                'variantPrice': double.parse("${element.variantPrice}"),
                'variantQte': element.variantQte ?? 0,
                'variantImages': element.variantImages ?? [],
                'variantAttributes': element.variantAttributes ?? {},
                'isVariantVisible': element.isVariantVisible ?? false,
                'variantManufacturerId': element.variantManufacturerId ?? "",
                'productId': product?.id ?? "",
                'variantEIN': element.variantEIN ?? "",
                'isDeleted': false,
                '_id': element.id ?? "",
                'isUpdated': element.isUpdated ?? false,
                'isAdded': false,
                'ar': {
                  'variantName': element.ar?.variantName ?? "",
                  'variantAttributes': element.ar?.variantAttributes ?? {},
                }
              });
            }
          } else {
            productVariations.add({
              'variantName': element.variantName ?? "",
              'variantCode': element.variantCode ?? "",
              'variantPrice': double.parse("${element.variantPrice}"),
              'variantQte': element.variantQte ?? 0,
              'variantImages': element.variantImages ?? [],
              'variantAttributes': element.variantAttributes ?? {},
              'isVariantVisible': element.isVariantVisible ?? false,
              'variantManufacturerId': element.variantManufacturerId ?? "",
              'ar': {
                'variantName': element.ar?.variantName ?? "",
                'variantAttributes': element.ar?.variantAttributes ?? {},
              }
            });
          }
        }
      }
      if (isUpdateVariations) {
        for (Variants variation in arrAllVariations) {
          for (Variations element in variation.variations ?? []) {
            if (arrVariations.indexWhere((element) =>
                    (element.id?.isNotEmpty ?? false) &&
                    variation.id == element.id) !=
                -1) {
              productVariations.add({
                'variantName': element.variantName ?? "",
                'variantCode': element.variantCode ?? "",
                'variantPrice': double.parse("${element.variantPrice}"),
                'variantQte': element.variantQte ?? 0,
                'variantImages': element.variantImages ?? [],
                'variantAttributes': element.variantAttributes ?? {},
                'isVariantVisible': element.isVariantVisible ?? false,
                'variantManufacturerId': element.variantManufacturerId ?? "",
                'productId': product?.id ?? "",
                'variantEIN': element.variantEIN ?? "",
                '_id': element.id ?? "",
                'ar': {
                  'variantName': element.ar?.variantName ?? "",
                  'variantAttributes': element.ar?.variantAttributes ?? {},
                },
                'isDeleted': true,
                'isUpdated': false,
                'isAdded': false,
              });
            } else if (arrVariations.indexWhere((element) =>
                    (variation.id?.isNotEmpty ?? false) &&
                    variation.id != element.id) ==
                -1) {
              if (productVariations
                      .indexWhere((vari) => vari["_id"] == element.id) ==
                  -1) {
                productVariations.add({
                  'variantName': element.variantName ?? "",
                  'variantCode': element.variantCode ?? "",
                  'variantPrice': double.parse("${element.variantPrice}"),
                  'variantQte': element.variantQte ?? 0,
                  'variantImages': element.variantImages ?? [],
                  'variantAttributes': element.variantAttributes ?? {},
                  'isVariantVisible': element.isVariantVisible ?? false,
                  'variantManufacturerId': element.variantManufacturerId ?? "",
                  'productId': product?.id ?? "",
                  'variantEIN': element.variantEIN ?? "",
                  '_id': element.id ?? "",
                  'isDeleted': true,
                  'isUpdated': false,
                  'isAdded': false,
                  'ar': {
                    'variantName': element.ar?.variantName ?? "",
                    'variantAttributes': element.ar?.variantAttributes ?? {},
                  }
                });
              }
            }
          }
        }
      }
    }

    if (product != null &&
        (product?.productVariants?.isNotEmpty ?? false) &&
        arrVariations.isEmpty) {
      for (Variants variation in arrAllVariations) {
        for (Variations element in variation.variations ?? []) {
          productVariations.add({
            'variantName': element.variantName ?? "",
            'variantCode': element.variantCode ?? "",
            'variantPrice': double.parse("${element.variantPrice}"),
            'variantQte': element.variantQte ?? 0,
            'variantImages': element.variantImages ?? [],
            'variantAttributes': element.variantAttributes ?? {},
            'isVariantVisible': element.isVariantVisible ?? false,
            'variantManufacturerId': element.variantManufacturerId ?? "",
            'productId': product?.id ?? "",
            '_id': element.id ?? "",
            'ar': {
              'variantName': element.ar?.variantName ?? "",
              'variantAttributes': element.ar?.variantAttributes ?? {},
            },
            'isDeleted': true,
            'isUpdated': false,
            'isAdded': false,
          });
        }
      }
    }
    if (productVariations.isNotEmpty && product != null) {
      for (var variation in productVariations) {
        String variantCode = variation["variantCode"] ?? "";
        if (variantCode.isNotEmpty) {
          int indexWhere = (product?.productVariants ?? [])
              .indexWhere((element) => element.variantCode == variantCode);
          variation['validationHistory'] = indexWhere != -1
              ? (product?.productVariants?[indexWhere].validationHistory ?? [])
                  .map((e) => e.toJson())
                  .toList()
              : [];
        } else {
          variation['validationHistory'] = [];
        }
      }
    }

    productAr = {
      "productName": txtProductNameAr.text,
      "productDescription": txtProductDiscriptionAr.text,
      "productColors": productColorsAr,
      "productSizes": productSizesAr,
      "productCustomOptions": productCustomOptionsAr,
      "productSpecs": arrSpecificationsAr,
      "productReturnDuration": returnPolicyAr.value,
      "productWarrantyDuration": warrantyPolicyAr.value,
      "productReturnPolicy": txtProductPolicyAr.text
    };

    productVariations = checkReaddedValue(productVariations);

    // print("productVariations ${productVariations.length}");
    // log("productVariations ${jsonEncode(productVariations)}");
    // log("product ${jsonEncode(productAr)}");
    // print("update product");
    // return;
    
    request.runMutation(
      context: context,
      type: product != null
          ? isDraftProduct
              ? "updatedraftproduct"
              : "updateProduct"
          : "",
      query: product != null
          ? GraphQlQuries.updateProductInfo
          : GraphQlQuries.createProduct,
      variables: GraphQlVariables.createProduct(
        id: product != null ? product?.id ?? '' : "",
        productName: txtProductName.text,
        productDescription: txtProductDiscription.text,
        productManufacturerId: productId,
        productCategory: categoryId.value,
        productSubCategory: subCategoryId.value,
        productBrand: brandId.value.isEmpty ? manufacture.value : brandId.value,
        productKeyWords: keywords.split(","),
        productPrice: double.parse(price),
        isFreeDeliveryItem: freeDelivery.value,
        productAvailableQte: int.parse(qty),
        productNotifOnMinQte: isHideMinimumQty.value,
        productMinQte:
            isHideMinimumQty.value && minQty.isNotEmpty ? int.parse(minQty) : 0,
        productImages: productColors.isNotEmpty ? [] : sliderImages,
        productColors: productColors,
        productSizes: productSizes,
        productCustomOptions: productCustomOptions,
        productVariations: productVariations,
        productSpecs: arrSpecifications,
        productAcceptReturn: acceptReturn.value,
        productFreeReturn: acceptReturn.isFalse
            ? false
            : returnType.value == "0"
                ? true
                : false,
        productNotFreeReturn: acceptReturn.isFalse
            ? false
            : returnType.value == "1"
                ? true
                : false,
        productReturnDuration: acceptReturn.isFalse
            ? ""
            : returnPolicy.value == 'Other'
                ? customReturnPolicy
                : returnPolicy.value,
        productWarrantyDuration: warrantyPolicy.value == 'Other'
            ? customWarrantyPolicy
            : warrantyPolicy.value,
        productReturnPolicy: txtProductPolicy.text,
        productSizeCategory: shipmentFitsId,
        itemsPerOrder: int.parse(itemsPerOrder),
        publishProduct: publishNow,
        saveProductForLater: isSaveForLater,
        ar: productAr,
      ),
    );
  }

  publishSavedForlaterProduct(
      BuildContext context, String id, GraphQlInitilize request) {
    request.runMutation(
      context: context,
      query: GraphQlQuries.publishSavedForlaterProduct,
      variables: GraphQlVariables.publishSavedForlaterProduct(productId: id),
    );
  }

  changeTab({required bool isNext, int isJumpPage = -1}) async {
    if (isJumpPage < 0) {
      if (tabPosition.value == 6 && isNext) {
        return;
      } else if (tabPosition.value == 0 && !isNext) {
        return;
      }
      if (tabPosition.value == 0 && !validateProductInformationPage()) {
        return;
      }
      if (tabPosition.value == 1 && !validateProductOptions() && isNext) {
        return;
      }
      if (tabPosition.value == 2 &&
          !validateProductPriceVariations(isNext: isNext) &&
          isNext) {
        return;
      }
      if (tabPosition.value == 3 && !validateProductSpecificatios() && isNext) {
        return;
      }
      if (tabPosition.value == 4 && !validateProductShipment() && isNext) {
        return;
      }
      if (tabPosition.value == 5 && !validateProductPolicies() && isNext) {
        return;
      }
      if (isNext) {
        tabPosition.value++;
      } else {
        isRequiredUpdate.value = "";
        tabPosition.value--;
      }
    } else {
      tabPosition.value = isJumpPage;
    }
    isHideStepper.value = false;
    pageController.animateToPage(tabPosition.value,
        duration: const Duration(milliseconds: 200), curve: Curves.easeIn);
    await autoScrollController.scrollToIndex(tabPosition.value,
        preferPosition: AutoScrollPosition.begin);
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    ShipmentFitsModel shipmentFitsModel = ShipmentFitsModel.fromJson(response);
    if (shipmentFitsModel.status == statusOK) {
      sizeFits.clear();
      sizeFits.addAll(shipmentFitsModel.sizeCategories ?? []);
      if (shipmentFitsId.isEmpty && sizeFits.isNotEmpty) {
        shipmentFitsId = sizeFits.first.id ?? "";
        selectedSize.value = sizeFits.first;
      } else {
        int index =
            sizeFits.indexWhere((element) => element.id == shipmentFitsId);
        if (index != -1) {
          selectedShipmentFits.value = index;
          if (selectedSize.value.dimensions == null) {
            selectedSize.value = sizeFits[index];
          }
        }
      }
    }
  }
}
