import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/components/branch_number_verification.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../controller/shop_info_controller.dart';

class Branches extends StatefulWidget {
  final int index;
  final ShopInfoController controller;

  const Branches({Key? key, required this.index, required this.controller})
      : super(key: key);

  @override
  State<Branches> createState() => _BranchesState();
}

class _BranchesState extends State<Branches>
    with InputValidationUtil
    implements ServerResponse {
  Completer<GoogleMapController> mapController = Completer();
  CameraPosition cameraPosition = const CameraPosition(
    target: LatLng(25.1972, 55.2744),
    zoom: 17,
  );
  Map<MarkerId, Marker> markers = <MarkerId, Marker>{};
  FocusNode focusNode = FocusNode();
  String phoneNumber = '';
  RxString errorText = ''.obs;
  late GraphQlInitilize _request;

  ShopInfoController get controller => widget.controller;
  int get index => widget.index;

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    if (controller.filterBranch[index].latLng != null) {
      cameraPosition = CameraPosition(
        target: controller.filterBranch[index].latLng!,
        zoom: 17,
      );

      MarkerId markerId = const MarkerId("a");
      Marker marker = Marker(
          markerId: markerId,
          draggable: false,
          position: controller.filterBranch[index].latLng!,
          icon:
              BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure));
      markers[markerId] = marker;
    }
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Column(
      children: [
        Obx(
          () => ElbaaabInputTextField(
            margin: EdgeInsets.only(
                top: index == 0 ? 10 : 50,
                left: kLeftSpace,
                right: kRightSpace),
            onChanged: (value) {},
            suffix: Icon(
              Icons.arrow_drop_down_outlined,
              color: Colors.white.withOpacity(0.4),
            ),
            initialValue: controller.filterBranch[index].city.value,
            hint: 'ex : Dubai',
            errorText: (controller.history.returnValues?.contains(
                        "${index + 1}${GlobalMethods.ordinal(index + 1)} Pickup City") ??
                    false)
                ? "${appLocal.adminReturned} ${index + 1}${GlobalMethods.ordinal(index + 1)} Pickup City"
                : null,
            autoTextDirection: true,
            onTap: () {
              if (controller.cityList.isNotEmpty) {
                BottomSheets.showListPicker(context, controller.cityList)
                    .then((value) => {
                          controller.filterBranch[index].city.value =
                              controller.cityList[value],
                          if (controller.shop != null)
                            {controller.branches[index].isUpdate = true}
                        });
              } else {
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                  content: Text(
                    appLocal.waitFechingCities,
                    style: FontStyles.fontMedium(),
                  ),
                  duration: const Duration(seconds: 2),
                  backgroundColor: AppColors.headerColorDark,
                ));
              }
            },
            label: appLocal.city,
            validator: validateFieldEmpty,
          ),
        ),
        ElbaaabInputTextField(
          focusNode: focusNode,
          autoTextDirection: true,
          margin: const EdgeInsets.only(
              top: 24, left: kLeftSpace, right: kRightSpace),
          onChanged: (v) => {
            controller.filterBranch[index].address = v,
            if (controller.shop != null)
              {controller.branches[index].isUpdate = true}
          },
          initialValue: controller.filterBranch[index].address,
          charaterlimit: 300,
          hint: appLocal.pickupFeildHint,
          label: appLocal.pickupFeildlabel,
          errorText: (controller.history.returnValues?.contains(
                      "${index + 1}${GlobalMethods.ordinal(index + 1)} Pickup Address") ??
                  false)
              ? "${appLocal.adminReturned} ${index + 1}${GlobalMethods.ordinal(index + 1)} ${appLocal.pickupFeildAdminReject}"
              : null,
          validator: (v) => validateFieldEmpty(v, focusNode: focusNode),
          formatter: [FilteringTextInputFormatter.singleLineFormatter],
          inputType: TextInputType.text,
        ),
        Obx(
          () => ElbaaabInputTextField(
            margin: const EdgeInsets.only(
                top: 24, left: kLeftSpace, right: kRightSpace),
            hint: appLocal.contactFeildHint,
            label: appLocal.contactFeildlabel,
            charaterlimit: 14,
            readOnly: false,
            validator: (v) => validateFieldEmpty(
              v,
              errorMessage:
                  "${appLocal.adminReturned} ${index + 1}${GlobalMethods.ordinal(index + 1)} ${appLocal.pickupContactNumber}",
              serverValue: (controller.history.returnValues?.contains(
                          "${index + 1}${GlobalMethods.ordinal(index + 1)} Pickup address contact number") ??
                      false)
                  ? (controller.shop?.shopPickupAddresses?[index]
                          .pickUpContactMobileNumber?.number ??
                      "")
                  : "",
              isReturend: (controller.history.returnValues?.contains(
                      "${index + 1}${GlobalMethods.ordinal(index + 1)} Pickup address contact number") ??
                  false),
            ),
            autoTextDirection: true,
            suffix: (controller
                    .filterBranch[index].contactNumber.number.value.isEmpty)
                ? SizedBox(
                    width: 42,
                    height: 42,
                    child: IconButton(
                      onPressed: () {
                        if (phoneNumber.isEmpty) {
                          errorText.value = appLocal.pleaseEnterBranchNumber;
                          return;
                        }
                        String validatePhone =
                            InputValidationUtil.validatePhone(validateNumber());
                        if (validatePhone != appLocal.valid) {
                          errorText.value = validatePhone;
                          return;
                        }
                        int indexWhere = controller.branches.indexWhere(
                            (element) =>
                                element.contactNumber.number.value ==
                                validateNumber());
                        if (indexWhere != -1) {
                          errorText.value = appLocal.numberAlreadyUse;
                          return;
                        } else {
                          errorText.value = "";
                        }
                        if (validatePhone.isNotEmpty) {
                          _request.runMutation(
                            context: context,
                            query: GraphQlQuries.sendPickupAdrNumberOtp,
                            variables: GraphQlVariables.sendPickupAdrNumberOtp(
                                phoneNumber: validateNumber()),
                          );
                        } else {
                          errorText.value = validatePhone.toString();
                        }
                      },
                      icon: Icon(
                        Icons.send,
                        color: AppColors.colorPrimary,
                      ),
                    ),
                  )
                : Container(
                    height: 42,
                    width: 80,
                    alignment: Alignment.center,
                    child: Text(
                      (controller.filterBranch[index].contactNumber.number.value
                              .isEmpty)
                          ? ""
                          : appLocal.verified,
                      style: FontStyles.fontMedium(
                        fontSize: 12,
                        color: AppColors.colorSecondary,
                      ),
                    ),
                  ),
            prefix: Text(
              " 971 ",
              style: FontStyles.fontMedium(
                fontSize: 12,
                color: AppColors.colorSecondary,
              ),
            ),
            errorText: errorText.value.isNotEmpty ? errorText.value : null,
            onChanged: (v) => phoneNumber = v,
            initialValue:
                controller.filterBranch[index].contactNumber.number.value,
            formatter: [
              FilteringTextInputFormatter.singleLineFormatter,
            ],
            inputType: TextInputType.phone,
          ),
        ),
        ElbaaabInputTextField(
          margin: const EdgeInsets.only(
              top: 24, left: kLeftSpace, right: kRightSpace),
          hint: appLocal.landNumberFeildHint,
          label: appLocal.landNumberFeildlabel,
          charaterlimit: 9,
          autoTextDirection: true,
          validator: (v) => validateFieldEmpty(
            v,
            errorMessage: appLocal.landNumberFeildAdminReject,
            serverValue: (controller.history.returnValues?.contains(
                        "${index + 1}${GlobalMethods.ordinal(index + 1)} Pickup address land number") ??
                    false)
                ? (controller.shop?.shopPickupAddresses?[index]
                        .pickUpContactLandNumber?.number ??
                    "")
                : "",
            isReturend: (controller.history.returnValues?.contains(
                    "${index + 1}${GlobalMethods.ordinal(index + 1)} Pickup address land number") ??
                false),
            isOptionalFeild: !(controller.history.returnValues?.contains(
                    "${index + 1}${GlobalMethods.ordinal(index + 1)} Pickup address land number") ??
                false),
          ),
          onChanged: (v) => {
            controller.filterBranch[index].landNumber.number.value = v,
            if (controller.shop != null)
              {controller.branches[index].isUpdate = true}
          },
          initialValue: controller.filterBranch[index].landNumber.number.value,
          formatter: [
            FilteringTextInputFormatter.singleLineFormatter,
            LengthLimitingTextInputFormatter(9),
          ],
          inputType: TextInputType.phone,
        ),
        if (controller.filterBranch[index].latLng == null)
          Padding(
            padding: const EdgeInsets.only(
                top: 20, left: kLeftSpace, right: kRightSpace),
            child: InkWell(
              onTap: () => _branchMapLatLongInfo(),
              child: Obx(
                () => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 139,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: controller.isRequiredLocation.value
                              ? AppColors.colorDanger
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          SizedBox(
                            height: 139,
                            width: MediaQuery.of(context).size.width,
                            child: Image.asset(
                              'assets/images/google_map.png',
                              fit: BoxFit.fill,
                            ),
                          ),
                          Wrap(
                            children: <Widget>[
                              Icon(
                                Icons.add_circle,
                                color: AppColors.headerColorDark,
                                size: 20,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                appLocal.openGoogleMap,
                                style: FontStyles.fontBold(
                                  fontSize: 12,
                                  color: AppColors.headerColorDark,
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                    if (controller.isRequiredLocation.value)
                      Padding(
                        padding: const EdgeInsets.all(5.0),
                        child: Text(
                          appLocal.pleaseAddLocation,
                          style: FontStyles.fontRegular(
                            fontSize: 12,
                            color: AppColors.colorDanger,
                          ),
                        ),
                      )
                  ],
                ),
              ),
            ),
          ),
        if (controller.filterBranch[index].latLng != null)
          Container(
            margin: const EdgeInsets.only(
                top: 20, left: kLeftSpace, right: kRightSpace),
            height: controller.filterBranch[index].latLng == null ? 0 : 139,
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: GoogleMap(
                    onMapCreated: (cont) => mapController.complete(cont),
                    onTap: (v) => _branchMapLatLongInfo(),
                    initialCameraPosition: cameraPosition,
                    mapType: MapType.terrain,
                    myLocationButtonEnabled: false,
                    rotateGesturesEnabled: true,
                    zoomControlsEnabled: true,
                    compassEnabled: true,
                    scrollGesturesEnabled: true,
                    markers: Set<Marker>.of(markers.values),
                  ),
                ),
                if (controller.history.returnValues?.contains(
                        "${index + 1}${GlobalMethods.ordinal(index + 1)} Pickup location") ??
                    false)
                  InkWell(
                    onTap: () => _branchMapLatLongInfo(),
                    child: Container(
                      height: 139,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border:
                            Border.all(color: AppColors.colorDanger, width: 2),
                        color: Colors.black.withOpacity(0.5),
                      ),
                      child: Center(
                        child: Text(
                          "${appLocal.adminReturned} ${index + 1}${GlobalMethods.ordinal(index + 1)} ${appLocal.pickupLocation}",
                          style: FontStyles.fontMedium(),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
      ],
    );
  }

  String validateNumber() {
    if (phoneNumber.startsWith('0')) {
      phoneNumber = phoneNumber.replaceFirst('0', '+9715');
    } else if (phoneNumber.startsWith('5')) {
      phoneNumber = phoneNumber.replaceFirst('5', '+9715');
    }
    return phoneNumber;
  }

  Future<void> _goToPosition(LatLng latLng) async {
    final GoogleMapController controller = await mapController.future;
    setState(() {});
    var newPosition = CameraPosition(target: latLng, zoom: 17);
    CameraUpdate update = CameraUpdate.newCameraPosition(newPosition);
    controller.moveCamera(update);
    MarkerId markerId = const MarkerId("a");
    Marker marker = Marker(
        markerId: markerId,
        draggable: false,
        position: latLng,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure));
    markers[markerId] = marker;
    setState(() {});
  }

  Future<void> _branchMapLatLongInfo() async {
    final result = await Get.toNamed(RouteNames.openGoogleMapScreen,
        arguments: [controller.filterBranch[index].latLng]);
    if (result != '1') {
      setState(() {});
      var latilong = '$result'.split(',');
      controller.filterBranch[index].latLng = LatLng(
          double.parse(latilong[0].replaceAll('LatLng(', '')),
          double.parse(latilong[1].replaceAll(')', '')));
      _goToPosition(controller.filterBranch[index].latLng!);

      if (controller.shop != null) {
        if (controller.branches[index].id.isNotEmpty) {
          controller.branches[index].isUpdate = true;
        }
      }
    }
  }

  @override
  onError(error, String type) {
    // TODO: implement onError
    throw UnimplementedError();
  }

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (model.status == statusOK) {
      errorText.value = '';
       showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (context) => BranchNumberVerification(
                                phoneNumber: validateNumber(),
                                onVerificationSuccess: () {
                                  // Update the contact number verification status
                                  controller.filterBranch[index].contactNumber.number.value = validateNumber();
                                  controller.filterBranch[index].contactNumber.isVerified = true;
                                  if (controller.shop != null) {
                                    controller.branches[index].isUpdate = true;
                                  }
                                  Navigator.pop(context); // Close dialog
                                },
                              ),
                            );
    }
  }
}

class PickupAddressModel {
  RxString city = 'ex : Dubai'.obs;
  String address;
  String id;
  ContactNumber contactNumber;
  ContactNumber landNumber;
  String createdAt;
  String updatedAt;
  LatLng? latLng;
  bool isUpdate;
  bool isDelete;
  bool isAdd;
  bool isUsedInOrder;

  PickupAddressModel(
      this.city,
      this.address,
      this.id,
      this.contactNumber,
      this.landNumber,
      this.createdAt,
      this.updatedAt,
      this.latLng,
      this.isUpdate,
      this.isDelete,
      this.isUsedInOrder,
      this.isAdd);
}

class ContactNumber {
  RxString number;
  bool isVerified;

  ContactNumber({required this.number, required this.isVerified});
}
