import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/provider/security_session_provider.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class OtpDialog extends StatefulWidget {
  const OtpDialog({super.key, required this.validOtp});
  final Function validOtp;

  @override
  _OtpDialogState createState() => _OtpDialogState();
}

class _OtpDialogState extends State<OtpDialog> implements ServerResponse {
  final TextEditingController _otpController1 = TextEditingController();
  final TextEditingController _otpController2 = TextEditingController();
  final TextEditingController _otpController3 = TextEditingController();
  final TextEditingController _otpController4 = TextEditingController();
  final TextEditingController _otpController5 = TextEditingController();
  final TextEditingController _otpController6 = TextEditingController();
  late GraphQlInitilize _request;
  RxString strError = "".obs;
  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;

    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Dialog(
          insetPadding: EdgeInsets.zero,
          clipBehavior: Clip.antiAliasWithSaveLayer,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 3,
          backgroundColor: Colors.transparent,
          child: contentBox(context, appLocal),
        ),
      ),
    );
  }

  contentBox(context, AppLocalizations appLocal) {
    return Stack(
      children: <Widget>[
        Container(
          width: double.infinity,
          padding: const EdgeInsets.only(
            left: 24,
            top: 50,
            right: 24,
            bottom: 20,
          ),
          margin: const EdgeInsets.only(top: 45, left: 16, right: 16),
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            color: AppColors.backgroundColorDark,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                offset: const Offset(0, 10),
                blurRadius: 20,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                appLocal.verificationCode,
                style: FontStyles.fontMedium(fontSize: 24),
              ),
              const SizedBox(height: 8),
              Text(
                appLocal.settingOtpDailogMessage,
                textAlign: TextAlign.center,
                style: FontStyles.fontRegular(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    prefs.getString(localAuthEmail) ?? "",
                    style: FontStyles.fontMedium(fontSize: 12),
                  ),
                  const SizedBox(width: 10),
                  TextButton(
                    onPressed: () => _request.runQuery(
                      context: context,
                      query: GraphQlQuries.resendSettingsOtp,
                    ),
                    child: Text(
                      appLocal.resendOtp,
                      style: FontStyles.fontMedium(
                        fontSize: 13,
                        color: AppColors.colorPrimary,
                      ),
                    ),
                  )
                ],
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(
                  6,
                  (index) => SizedBox(
                    height: 50,
                    width: 40,
                    child: TextFormField(
                      controller: [
                        _otpController1,
                        _otpController2,
                        _otpController3,
                        _otpController4,
                        _otpController5,
                        _otpController6,
                      ][index],
                      onChanged: (v) {
                        if (v.length == 1) {
                          FocusScope.of(context).nextFocus();
                        } else if (v.isEmpty) {
                          FocusScope.of(context).previousFocus();
                        }
                      },
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      style: FontStyles.fontMedium(fontSize: 20),
                      decoration: InputDecoration(
                        hintText: "0",
                        fillColor: AppColors.headerColorDark,
                        filled: true,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: AppColors.colorPrimary,
                            width: 2,
                          ),
                        ),
                      ),
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(1),
                        FilteringTextInputFormatter.digitsOnly
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              ElbaabNetworkEroor(strError: strError),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                height: 45,
                child: ElbaabGradientButtonWidget(
                  onPress: () {
                    String otp = _otpController1.text +
                        _otpController2.text +
                        _otpController3.text +
                        _otpController4.text +
                        _otpController5.text +
                        _otpController6.text;
                    if (otp.length == 6) {
                      strError.value = "";
                      _request.runMutation(
                        context: context,
                        query: GraphQlQuries.verifySettingOtp,
                        type: "verify",
                        variables:
                            GraphQlVariables.verifySettingOtp(otpNumber: otp),
                      );
                    } else {
                      strError.value = "Enter Complete OTP";
                    }
                  },
                  text: appLocal.verifyOtp,
                ),
              )
            ],
          ),
        ),
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.colorPrimary,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.colorPrimary.withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: const Icon(
                Icons.lock_rounded,
                color: Colors.white,
                size: 40,
              ),
            ),
          ),
        ),
        Positioned(
          top: 45,
          right: 8,
          child: IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(
              Icons.close_rounded,
              size: 28,
            ),
          ),
        ),
      ],
    );
  }

  @override
  onError(error, String type) {
    strError.value = BaseModel.fromJson(error).message ?? "";
  }

  @override
  onSucess(response, String type) {
    if (type == "verify") {
      BaseModel model = BaseModel.fromJson(response);
      if (model.status == statusOK) {
        Provider.of<SecuritySessionProvider>(context, listen: false)
            .startSession();
        widget.validOtp;

        Get.back();
      }
    }
  }
}
