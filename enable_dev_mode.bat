@echo off
echo Enabling Windows Developer Mode...
echo.

REM Enable Developer Mode via Registry
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\AppModelUnlock" /v AllowDevelopmentWithoutDevLicense /t REG_DWORD /d 1 /f

if %errorlevel% equ 0 (
    echo [SUCCESS] Developer Mode enabled via registry
) else (
    echo [WARNING] Could not modify registry (admin required)
    echo Please run as administrator or enable manually
)

echo.
echo Opening Windows Settings to verify Developer Mode...
start ms-settings:developers

echo.
echo Please verify in Settings that Developer Mode is enabled:
echo 1. In the Settings window, go to "For developers"
echo 2. Turn ON "Developer Mode"
echo 3. Accept any prompts that appear
echo.
pause
