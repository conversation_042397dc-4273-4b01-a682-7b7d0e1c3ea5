import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/enums/enums.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';

class OrderProductRow extends StatelessWidget implements ServerResponse {
  final OrderType orderType;
  final Items item;
  final String orderItemId;
  final dynamic shipmentCost;
  final Function(String id) onRejectCall;
  RxBool isRejected = false.obs;
  late GraphQlInitilize _request;
  OrderProductRow(
      {Key? key,
      required this.orderType,
      required this.shipmentCost,
      required this.item,
      this.orderItemId = "",
      required this.onRejectCall})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    print("item ${item.variant?.variantAttributes?.variantCustomOptions}");
    final appLocal = AppLocalizations.of(context)!;
    _request = GraphQlInitilize(this);
    Future.delayed(
        Duration.zero,
        () => isRejected.value =
            item.orderItemProductStatus?.last.status == "Rejected");

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 3, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.colorGray.withOpacity(0.5),
            AppColors.colorGray.withOpacity(0.3),
            AppColors.colorGray.withOpacity(0.1),
            AppColors.feildBorderColorDark.withOpacity(0.05),
            AppColors.feildBorderColorDark.withOpacity(0.01),
            AppColors.feildBorderColorDark.withOpacity(0.005),
          ],
        ),
        border: Border.all(
          color: Colors.white.withOpacity(0.15),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Column(
        children: [
          // Status Strip
          if (item.orderItemProductStatus?.last.status == "Canceled" ||
              item.orderItemProductStatus?.last.status == "Rejected" ||
              isRejected.value)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: item.orderItemProductStatus?.last.status == "Canceled"
                    ? AppColors.colorSecondaryYellow
                    : AppColors.colorSecondary_Red,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    item.orderItemProductStatus?.last.status == "Canceled"
                        ? Icons.warning_rounded
                        : Icons.cancel_rounded,
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    item.orderItemProductStatus?.last.status ?? "",
                    textAlign: TextAlign.center,
                    style: FontStyles.fontMedium(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

             Container(
                      width: double.infinity,
                      height: 180,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: GlobalMethods.netWorkImage(
                        item.product?.productName != null
                            ? (item.product?.productImages[0] ?? "")
                            : item.variant?.variantImages?.first ?? "",
                       const BorderRadius.only(topLeft: Radius.circular(16),topRight: Radius.circular(16)),
                        BoxFit.cover,
                      ),
                    ),

          Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
           
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.1),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                          _buildVariantDetail(
                          item.product?.productName != null ? "Product Name" : "Variant Name",
                          "${item.product?.productName ?? item.variant?.variantName ?? ""}",
                          Icons.label_outline,
                        ),
                        if (item.variant?.variantAttributes?.variantColor !=
                            null)
                          _buildVariantDetail(
                            "Color",
                            "${item.variant?.variantAttributes?.variantColor?.colorFamily ?? ""} ${(item.variant?.variantAttributes?.variantColor?.colorName ?? "").isNotEmpty ? "( ${item.variant?.variantAttributes?.variantColor?.colorName} )" : ""}",
                            Icons.color_lens_outlined,
                          ),
                        if (item.variant?.variantAttributes?.variantSize !=
                            null)
                          _buildVariantDetail(
                            "Size",
                            item.variant?.variantAttributes?.variantSize
                                    ?.size ??
                                "",
                            Icons.straighten_rounded,
                          ),
                        if (item.variant?.variantAttributes
                                ?.variantCustomOptions?.isNotEmpty ??
                            false)
                          _buildVariantDetail(
                            "Custom Option",
                            item.variant?.variantAttributes
                                    ?.variantCustomOptions
                                    ?.map((e) =>
                                        "${e.attributeTitle} ${e.attributeValue}")
                                    .join(", ") ??
                                "",
                            Icons.tune_rounded,
                          ),
                        _buildVariantDetail(
                          (item.product?.productEIN ?? "").isEmpty
                              ? "Variant Ein"
                              : "Product Ein",
                          "#${item.product?.productEIN ?? item.variant?.variantEIN ?? ""}",
                          Icons.qr_code_rounded,
                        ),
                        _buildVariantDetail(
                        appLocal.brand,
                        item.product?.productName != null
                            ? (item.product?.productBrand?.brandName ?? "")
                            : item.variant?.productId?.productBrand
                                    ?.brandName ??
                                "",
                        Icons.business_center_outlined,
                      ),
                      _buildVariantDetail(
                        appLocal.category,
                        item.product?.productName != null
                            ? (item.product?.productCategory?.categoryName ??
                                "")
                            : item.variant?.productId?.productCategory
                                    ?.categoryName ??
                                "",
                        Icons.category_outlined,
                      ),
                        _buildVariantDetail(
                          "Quantity",
                          "${item.quantity ?? 0}",
                          Icons.shopping_bag_outlined,
                        ),
                        _buildVariantDetail(
                          "Total Price",
                          "${item.product?.productPrice ?? item.variant?.variantPrice ?? ""} AED",
                          Icons.price_change_outlined,
                        ),
                      ],
                    ),
                  ),
                
                // Action Button
                SizedBox(height: 20.h),
                Obx(
                  () => isRejected.value
                      ? const SizedBox.shrink()
                      : ElbaabButtonWidget(
                          onPress: () {
                            Alerts.alertView(
                              context: context,
                              title: appLocal.alert,
                              content:
                                  "Are You Sure You Want To Reject This Item",
                              defaultActionText: appLocal.yes,
                              action: () => {
                                Get.back(),
                                _request.runMutation(
                                  context: context,
                                  query: GraphQlQuries.cancelItemInOrderItem,
                                  type: item.id ?? "",
                                  variables:
                                      GraphQlVariables.cancelItemInOrderItem(
                                    orderItemId: orderItemId,
                                    itemId: item.id ?? "",
                                    notes: "",
                                  ),
                                )
                              },
                              cancelAction: () => Get.back(),
                              cancelActionText: appLocal.no,
                            );
                          },
                          height: 50,
                          colors: AppColors.colorSecondary_Red,
                          text: appLocal.reject,
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVariantDetail(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.white.withOpacity(0.7),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              "$label: ",
              style: FontStyles.fontRegular(
                fontSize: 13,

                color: Colors.white.withOpacity(0.7),
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: FontStyles.fontMedium(
                fontSize: 13,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 18,
            color: Colors.white.withOpacity(0.6),
          ),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: FontStyles.fontRegular(
                  fontSize: 11,
                  color: Colors.white.withOpacity(0.6),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: FontStyles.fontMedium(fontSize: 13),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (model.status == statusOK) {
      isRejected.value = true;
      onRejectCall(type);
    }
  }
}
