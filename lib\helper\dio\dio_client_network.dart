import 'package:dio/dio.dart';
import 'package:overolasuppliers/helper/strings/http_constants.dart';

class DioClientNetwork {
  late Dio dio;
  String? authToken;
  initializeDioClientNetwork() {
    dio = Dio();
    dio.options = setBaseOptions();
    dio.options.headers['content-Type'] = 'multipart/form-data';
    dio.interceptors.add(
      LogInterceptor(
          request: true, responseBody: true, requestBody: true, error: true),
    );
    dio.interceptors.add(
      InterceptorsWrapper(
          onRequest: requestInterceptor, onError: errorInterceptor),
    );
  }

  BaseOptions setBaseOptions() {
    return BaseOptions(
        connectTimeout: const Duration(seconds: 10),
        baseUrl: HTTPConstants.baseUrl);
  }

  void requestInterceptor(
      RequestOptions options, RequestInterceptorHandler handler) async {
    handler.next(options);
  }

  dynamic errorInterceptor(dioError, ErrorInterceptorHandler handler) async {
    handler.next(dioError);
  }
}
