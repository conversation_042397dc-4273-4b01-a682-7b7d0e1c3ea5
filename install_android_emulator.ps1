# Android Studio and Emulator Installation Script
# This script helps install Android Studio and set up Android emulators for Flutter

Write-Host "🤖 Installing Android Studio and Setting Up Emulators" -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan

# Check system requirements
Write-Host "[INFO] Checking system requirements..." -ForegroundColor Blue

$ram = Get-WmiObject -Class Win32_ComputerSystem | Select-Object -ExpandProperty TotalPhysicalMemory
$ramGB = [math]::Round($ram / 1GB, 2)
$freeSpace = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'" | Select-Object -ExpandProperty FreeSpace
$freeSpaceGB = [math]::Round($freeSpace / 1GB, 2)

Write-Host "System RAM: $ramGB GB" -ForegroundColor White
Write-Host "Free Disk Space (C:): $freeSpaceGB GB" -ForegroundColor White

if ($ramGB -lt 8) {
    Write-Host "[WARNING] Android emulator works best with 8GB+ RAM. You have $ramGB GB" -ForegroundColor Yellow
}

if ($freeSpaceGB -lt 10) {
    Write-Host "[WARNING] Need at least 10GB free space. You have $freeSpaceGB GB" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📋 Installation Steps:" -ForegroundColor Cyan
Write-Host "1. Download Android Studio" -ForegroundColor White
Write-Host "2. Install with recommended settings" -ForegroundColor White
Write-Host "3. Set up Android SDK" -ForegroundColor White
Write-Host "4. Create Android Virtual Device (AVD)" -ForegroundColor White
Write-Host "5. Configure Flutter to use Android SDK" -ForegroundColor White
Write-Host ""

$proceed = Read-Host "Do you want to proceed with Android Studio installation? (y/n)"

if ($proceed -eq "y" -or $proceed -eq "Y") {
    Write-Host ""
    Write-Host "🚀 Starting Android Studio installation process..." -ForegroundColor Green
    
    # Step 1: Download Android Studio
    Write-Host ""
    Write-Host "Step 1: Downloading Android Studio..." -ForegroundColor Yellow
    Write-Host "Opening Android Studio download page..." -ForegroundColor Blue
    Start-Process "https://developer.android.com/studio"
    
    Write-Host ""
    Write-Host "📥 Manual Download Instructions:" -ForegroundColor Cyan
    Write-Host "1. Download 'Android Studio Hedgehog' for Windows" -ForegroundColor White
    Write-Host "2. Choose the .exe installer (recommended)" -ForegroundColor White
    Write-Host "3. Save to Downloads folder" -ForegroundColor White
    Write-Host ""
    
    Read-Host "Press Enter after downloading Android Studio installer..."
    
    # Step 2: Installation guidance
    Write-Host ""
    Write-Host "Step 2: Installing Android Studio..." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔧 Installation Settings (IMPORTANT):" -ForegroundColor Cyan
    Write-Host "✅ Android SDK" -ForegroundColor Green
    Write-Host "✅ Android SDK Platform-Tools" -ForegroundColor Green
    Write-Host "✅ Android Emulator" -ForegroundColor Green
    Write-Host "✅ Intel x86 Emulator Accelerator (HAXM)" -ForegroundColor Green
    Write-Host "✅ Android Virtual Device" -ForegroundColor Green
    Write-Host ""
    Write-Host "📍 Recommended Installation Path:" -ForegroundColor Yellow
    Write-Host "C:\Program Files\Android\Android Studio" -ForegroundColor White
    Write-Host ""
    Write-Host "📍 Android SDK Path:" -ForegroundColor Yellow
    Write-Host "C:\Users\<USER>\AppData\Local\Android\Sdk" -ForegroundColor White
    Write-Host ""
    
    # Try to launch installer if it exists in Downloads
    $downloadsPath = "$env:USERPROFILE\Downloads"
    $installerFiles = Get-ChildItem -Path $downloadsPath -Filter "*android-studio*" -File | Sort-Object LastWriteTime -Descending
    
    if ($installerFiles.Count -gt 0) {
        $latestInstaller = $installerFiles[0]
        Write-Host "Found installer: $($latestInstaller.Name)" -ForegroundColor Green
        $runInstaller = Read-Host "Do you want to run the installer now? (y/n)"
        
        if ($runInstaller -eq "y" -or $runInstaller -eq "Y") {
            Write-Host "Launching Android Studio installer..." -ForegroundColor Blue
            Start-Process -FilePath $latestInstaller.FullName -Wait
        }
    } else {
        Write-Host "Please run the Android Studio installer manually from your Downloads folder" -ForegroundColor Yellow
    }
    
    Read-Host "Press Enter after Android Studio installation is complete..."
    
    # Step 3: First-time setup
    Write-Host ""
    Write-Host "Step 3: Android Studio First-Time Setup..." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🎯 Setup Wizard Instructions:" -ForegroundColor Cyan
    Write-Host "1. Choose 'Standard' installation type" -ForegroundColor White
    Write-Host "2. Accept license agreements" -ForegroundColor White
    Write-Host "3. Let it download SDK components (this may take 10-30 minutes)" -ForegroundColor White
    Write-Host "4. Wait for 'Finish' button to appear" -ForegroundColor White
    Write-Host ""
    
    Read-Host "Press Enter after completing the setup wizard..."
    
    # Step 4: Create AVD
    Write-Host ""
    Write-Host "Step 4: Creating Android Virtual Device (AVD)..." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📱 AVD Creation Steps:" -ForegroundColor Cyan
    Write-Host "1. In Android Studio, click 'More Actions' > 'Virtual Device Manager'" -ForegroundColor White
    Write-Host "   OR go to Tools > AVD Manager" -ForegroundColor White
    Write-Host "2. Click 'Create Virtual Device'" -ForegroundColor White
    Write-Host "3. Select 'Phone' category" -ForegroundColor White
    Write-Host "4. Choose 'Pixel 7' (recommended) or 'Pixel 4'" -ForegroundColor White
    Write-Host "5. Click 'Next'" -ForegroundColor White
    Write-Host "6. Select system image:" -ForegroundColor White
    Write-Host "   - API Level 34 (Android 14) - Recommended" -ForegroundColor Green
    Write-Host "   - Choose x86_64 architecture" -ForegroundColor White
    Write-Host "   - Click 'Download' if not already downloaded" -ForegroundColor White
    Write-Host "7. Click 'Next' then 'Finish'" -ForegroundColor White
    Write-Host ""
    
    Read-Host "Press Enter after creating your first AVD..."
    
    # Step 5: Configure Flutter
    Write-Host ""
    Write-Host "Step 5: Configuring Flutter for Android..." -ForegroundColor Yellow
    
    # Set Android SDK path for Flutter
    $androidSdkPath = "$env:USERPROFILE\AppData\Local\Android\Sdk"
    if (Test-Path $androidSdkPath) {
        Write-Host "Found Android SDK at: $androidSdkPath" -ForegroundColor Green
        
        Write-Host "Configuring Flutter to use Android SDK..." -ForegroundColor Blue
        try {
            & "C:\Users\<USER>\flutter\bin\flutter.bat" config --android-sdk $androidSdkPath
            Write-Host "✅ Flutter configured for Android SDK" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ Could not configure Flutter automatically" -ForegroundColor Yellow
            Write-Host "Manual command: flutter config --android-sdk $androidSdkPath" -ForegroundColor White
        }
    } else {
        Write-Host "⚠️ Android SDK not found at expected location" -ForegroundColor Yellow
        Write-Host "You may need to set it manually with:" -ForegroundColor White
        Write-Host "flutter config --android-sdk <path-to-android-sdk>" -ForegroundColor White
    }
    
    # Accept Android licenses
    Write-Host ""
    Write-Host "Accepting Android licenses..." -ForegroundColor Blue
    try {
        $sdkManagerPath = "$androidSdkPath\cmdline-tools\latest\bin\sdkmanager.bat"
        if (Test-Path $sdkManagerPath) {
            & $sdkManagerPath --licenses
        } else {
            Write-Host "⚠️ SDK Manager not found. You may need to accept licenses manually" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ Could not accept licenses automatically" -ForegroundColor Yellow
    }
    
    # Step 6: Test setup
    Write-Host ""
    Write-Host "Step 6: Testing Android Setup..." -ForegroundColor Yellow
    
    Write-Host "Running Flutter doctor..." -ForegroundColor Blue
    try {
        & "C:\Users\<USER>\flutter\bin\flutter.bat" doctor
    } catch {
        Write-Host "⚠️ Could not run Flutter doctor" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "Checking available devices..." -ForegroundColor Blue
    try {
        & "C:\Users\<USER>\flutter\bin\flutter.bat" devices
    } catch {
        Write-Host "⚠️ Could not check devices" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "Checking available emulators..." -ForegroundColor Blue
    try {
        & "C:\Users\<USER>\flutter\bin\flutter.bat" emulators
    } catch {
        Write-Host "⚠️ Could not check emulators" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "🎉 Android Studio and Emulator Setup Complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Start your AVD from Android Studio (AVD Manager > Play button)" -ForegroundColor White
    Write-Host "2. Wait for emulator to fully boot (may take 2-5 minutes first time)" -ForegroundColor White
    Write-Host "3. Run: flutter devices (should show your emulator)" -ForegroundColor White
    Write-Host "4. Run: flutter run (to launch your app on emulator)" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 Quick Test Commands:" -ForegroundColor Cyan
    Write-Host "flutter emulators --launch <emulator_name>" -ForegroundColor White
    Write-Host "flutter run -d <device_id>" -ForegroundColor White
    Write-Host ""
    
} else {
    Write-Host ""
    Write-Host "Installation cancelled. You can run this script again anytime." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📚 Troubleshooting Tips:" -ForegroundColor Cyan
Write-Host "- If emulator is slow: Enable Hardware Acceleration in BIOS" -ForegroundColor White
Write-Host "- If Flutter doesn't see emulator: Restart command prompt" -ForegroundColor White
Write-Host "- If build fails: Run 'flutter clean' then 'flutter pub get'" -ForegroundColor White
Write-Host ""

Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
