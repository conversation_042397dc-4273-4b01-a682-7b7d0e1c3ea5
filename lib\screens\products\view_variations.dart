import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/product_variation_model.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ViewVariations extends StatelessWidget {
  final List<Variants> arrVariations = Get.arguments[0];

  ViewVariations({super.key});
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.variation,
        leadingBack: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: ListView.builder(
            itemCount: arrVariations.length,
            itemBuilder: (context, superIndex) {
              Variants variations = arrVariations[superIndex];
              return Container(
                height: 280.h,
                margin: EdgeInsets.only(top: 8.h),
                child: ListView.builder(
                    itemCount: arrVariations[superIndex].variations?.length,
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) {
                      Variations variation = variations.variations![index];
                      return Container(
                        height: 300.h,
                        width: 271.w,
                        decoration: BoxDecoration(
                          color: AppColors.headerColorDark,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        margin: EdgeInsets.only(right: 15.r),
                        child: Stack(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(10.0).r,
                              child: Column(
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        width: 70.h,
                                        height: 70.w,
                                        child: GlobalMethods.netWorkImage(
                                          variation.variantImages?.first ?? "",
                                          BorderRadius.circular(5),
                                          BoxFit.cover,
                                        ),
                                      ),
                                      SizedBox(width: 10.w),
                                      Expanded(
                                        child: SizedBox(
                                            height: 120.h,
                                            child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  if ((variation.variantEIN ??
                                                          "")
                                                      .isNotEmpty)
                                                    RichText(
                                                      text: TextSpan(
                                                          style: FontStyles
                                                              .fontRegular(
                                                                  fontSize: 12),
                                                          children: [
                                                            const TextSpan(
                                                                text:
                                                                    'Variant EIN : '),
                                                            TextSpan(
                                                              text: variation
                                                                      .variantEIN ??
                                                                  "",
                                                              style: TextStyle(
                                                                color: Colors
                                                                    .white
                                                                    .withOpacity(
                                                                        0.5),
                                                              ),
                                                            ),
                                                          ]),
                                                    ),
                                                  if ((variations.colorFamily ??
                                                          "")
                                                      .isNotEmpty)
                                                    RichText(
                                                      text: TextSpan(
                                                          style: FontStyles
                                                              .fontRegular(
                                                                  fontSize: 12),
                                                          children: [
                                                             TextSpan(
                                                                text:
                                                                    '${appLocal.color} : '),
                                                            TextSpan(
                                                              text: appLocal.localeName == "ar"?variation.ar?.variantAttributes?.variantColor?.colorFamily: variations
                                                                  .colorFamily,
                                                              style: TextStyle(
                                                                color: Colors
                                                                    .white
                                                                    .withOpacity(
                                                                        0.5),
                                                              ),
                                                            ),
                                                          ]),
                                                    ),
                                                  if ((variations.colorName ??
                                                          "")
                                                      .isNotEmpty)
                                                    RichText(
                                                      text: TextSpan(
                                                          style: FontStyles
                                                              .fontRegular(
                                                                  fontSize: 12),
                                                          children: [
                                                             TextSpan(
                                                                text:
                                                                    '${appLocal.colorName} : '),
                                                            TextSpan(
                                                              text: appLocal.localeName == "ar"?variation.ar?.variantAttributes?.variantColor?.colorName:variations
                                                                  .colorName,
                                                              style: TextStyle(
                                                                color: Colors
                                                                    .white
                                                                    .withOpacity(
                                                                        0.5),
                                                              ),
                                                            ),
                                                          ]),
                                                    ),
                                                  if (variation
                                                          .variantAttributes
                                                          ?.variantSize
                                                          ?.size
                                                          ?.isNotEmpty ??
                                                      false)
                                                    RichText(
                                                      text: TextSpan(
                                                          style: FontStyles
                                                              .fontRegular(
                                                                  fontSize: 12),
                                                          children: [
                                                            TextSpan(
                                                              text: appLocal
                                                                  .variantSize(
                                                                appLocal.localeName ==
                                                                        "en"
                                                                    ? variation
                                                                            .variantAttributes
                                                                            ?.variantSize
                                                                            ?.unit ??
                                                                        ""
                                                                    : variation.ar?.variantAttributes?.variantSize?.unit ?? "",
                                                              ),
                                                            ),
                                                            // 'Size ( ${variation.variantAttributes?.variantSize?.unit ?? ""} ) : '),
                                                            TextSpan(
                                                              text: variation
                                                                      .variantAttributes
                                                                      ?.variantSize
                                                                      ?.size ??
                                                                  "",
                                                              style: TextStyle(
                                                                color: Colors
                                                                    .white
                                                                    .withOpacity(
                                                                        0.5),
                                                              ),
                                                            ),
                                                          ]),
                                                    ),
                                                  if (variation
                                                          .variantAttributes
                                                          ?.variantCustomOptions !=
                                                      null)
                                                    Expanded(
                                                      child: ListView.builder(
                                                          itemCount: variation
                                                              .variantAttributes
                                                              ?.variantCustomOptions
                                                              ?.length,
                                                          shrinkWrap: true,
                                                          physics:
                                                              const NeverScrollableScrollPhysics(),
                                                          itemBuilder:
                                                              (context, index) {
                                                            return RichText(
                                                              text: TextSpan(
                                                                  style: FontStyles
                                                                      .fontRegular(
                                                                          fontSize:
                                                                              12),
                                                                  children: [
                                                                    TextSpan(
                                                                        text:
                                                                            '${variation.variantAttributes?.variantCustomOptions?[index].attributeTitle} : '),
                                                                    TextSpan(
                                                                      text: variation
                                                                          .variantAttributes
                                                                          ?.variantCustomOptions?[
                                                                              index]
                                                                          .attributeValue,
                                                                      style:
                                                                          TextStyle(
                                                                        color: Colors
                                                                            .white
                                                                            .withOpacity(0.5),
                                                                      ),
                                                                    ),
                                                                  ]),
                                                            );
                                                          }),
                                                    )
                                                ])),
                                      )
                                    ],
                                  ),
                                  const Spacer(),
                                  Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: <Widget>[
                                        Expanded(
                                          flex: 3,
                                          child: ElbaaabInputTextField(
                                            onChanged: (v) {},
                                            onTap: () {},
                                            error: false,
                                            textDirection: appLocal.localeName == "ar"?TextDirection.rtl:TextDirection.ltr,
                                            hint: 'ex : 200',
                                            suffix: Container(
                                              width: 60,
                                              height: 60,
                                              decoration: BoxDecoration(
                                                color: AppColors.colorPrimary,
                                                borderRadius:appLocal.localeName == "ar"?const BorderRadius.only(
                                                  topLeft: Radius.circular(10),
                                                  bottomLeft:
                                                      Radius.circular(10),
                                                ):
                                                    const BorderRadius.only(
                                                  topRight: Radius.circular(10),
                                                  bottomRight:
                                                      Radius.circular(10),
                                                ),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  appLocal.aed,
                                                  style: FontStyles.fontRegular(
                                                      fontSize: 12),
                                                ),
                                              ),
                                            ),
                                            initialValue:
                                                arrVariations[superIndex]
                                                    .variations![index]
                                                    .variantPrice,
                                            label: appLocal.price,
                                            inputFormatter: '[0-9]',
                                            inputType: TextInputType.number,
                                          ),
                                        ),
                                        const SizedBox(width: 6),
                                        Expanded(
                                          flex: 2,
                                          child: ElbaaabInputTextField(
                                            onChanged: (v) {},
                                            onTap: () {},
                                            error: false,
                                            textDirection: appLocal.localeName == "ar"?TextDirection.rtl:TextDirection.ltr,
                                            hint: 'ex: 12',
                                            initialValue:
                                                arrVariations[superIndex]
                                                    .variations![index]
                                                    .variantQte
                                                    .toString(),
                                            label: appLocal.qty,
                                            inputFormatter: '[0-9]',
                                            inputType: TextInputType.number,
                                          ),
                                        ),
                                      ]),
                                  const Spacer(),
                                  SizedBox(
                                    height: 60,
                                    child: ElbaaabInputTextField(
                                      onChanged: (v) {},
                                      onTap: () {},
                                            textDirection: appLocal.localeName == "ar"?TextDirection.rtl:TextDirection.ltr,
                                      error: false,
                                      hint: 'ex: 154218413548641',
                                      initialValue: arrVariations[superIndex]
                                          .variations![index]
                                          .variantManufacturerId,
                                      label: 'GTIN Code',
                                      inputType: TextInputType.text,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (variation.isVariantVisible == false)
                              Container(
                                height: 300.h,
                                width: 271.w,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.black.withOpacity(0.6),
                                ),
                                child: const Icon(
                                  Icons.visibility_off,
                                  color: Colors.white,
                                  size: 50,
                                ),
                              ),
                          ],
                        ),
                      );
                    }),
              );
            }),
      ),
    );
  }
}
