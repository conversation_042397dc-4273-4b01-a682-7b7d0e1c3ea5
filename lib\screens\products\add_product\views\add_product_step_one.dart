import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/other/scan_barcode.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/categories_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_carousel_feild_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:translator/translator.dart';

class AddProductStepOne extends GetView<AddProductController>
    with InputValidationUtil
    implements ServerResponse {
  late GraphQlInitilize _request;

  AddProductStepOne({super.key});
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    _request = GraphQlInitilize(this);

    return SingleChildScrollView(
      controller: controller.stepperHideController,
      physics: const ClampingScrollPhysics(),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Form(
          key: controller.formKey,
          child: Column(
            children: <Widget>[
              ElbaabCarouselFeildWidget(
                children: [
                  Center(
                    child: ElbaaabInputTextField(
                      initialValue: controller.txtProductName.text,
                      onChanged: (v) {},
                      inputFormatter: "[a-zA-Z 0-9-.]",
                      inputType: TextInputType.text,
                      hint: appLocal.productNameFeildHint,
                      label: appLocal.productNameFeildLableEnglish,
                      charaterlimit: 300,
                      editingController: controller.txtProductName,
                      onFieldSubmitted: (value) {
                        if (appLocal.localeName == 'en') {
                          controller.txtProductNameAr.clear();
                           GoogleTranslator()
                              .translate(value, to: 'ar')
                              .then((result) {
                            controller.txtProductNameAr.text = result.text;
                          });
                        }
                      },
                      validator: (v) => validateFieldEmpty(
                        v,
                        errorMessage: appLocal.productNameFeildReturnMessage,
                        serverValue: controller.product?.productName ?? "",
                        isReturend: (controller.validationHistory?.returnValues
                                ?.contains("Name") ??
                            false),
                      ),
                      prefix: Text(
                        '*',
                        style: TextStyle(
                          color: AppColors.colorDanger,
                          fontSize: 20,
                        ),
                      ),
                    ),
                  ),
                  Center(
                    child: ElbaaabInputTextField(
                      onChanged: (v) {},
                      inputFormatter: "[0-9-. ا-ي]",
                      initialValue: controller.txtProductNameAr.text,
                      editingController: controller.txtProductNameAr,
                      inputType: TextInputType.text,
                      textDirection: TextDirection.rtl,
                      onFieldSubmitted: (value) {
                        if (appLocal.localeName == 'ar') {
                          controller.txtProductName.clear();
                          GoogleTranslator()
                              .translate(value, to: 'en')
                              .then((result) {
                            controller.txtProductName.text = result.text;
                          });
                        }
                      },
                      hint: appLocal.productNameFeildHint,
                      label: appLocal.productNameFeildLableArbic,
                      charaterlimit: 300,
                      validator: (v) => validateFieldEmpty(v,
                          errorMessage: appLocal.productNameFeildReturnMessage,
                          serverValue:
                              controller.product?.ar?.productName ?? "",
                          isReturend: (controller
                                  .validationHistory?.returnValues
                                  ?.contains("Arabic Name") ??
                              false)),
                      prefix: Text(
                        '*',
                        style: GoogleFonts.montserrat(
                          color: AppColors.colorDanger,
                          fontSize: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 16).r,
                child: ElbaabCarouselFeildWidget(
                  aspectRatio: 9 / 5.5,
                  children: [
                    Center(
                      child: ElbaaabInputTextField(
                        onChanged: (value) {},
                        initialValue: controller.txtProductDiscription.text,
                        editingController: controller.txtProductDiscription,
                        inputType: TextInputType.text,
                        validator: (v) => validateFieldEmpty(
                          v,
                          errorMessage:
                              appLocal.productDescriptionFeildReturnMessage,
                          serverValue:
                              controller.product?.productDescription ?? "",
                          isReturend: (controller
                                  .validationHistory?.returnValues
                                  ?.contains("Description") ??
                              false),
                        ),
                        requiredCounter: true,
                        charaterlimit: 1500,
                        label: appLocal.productDescriptionFeildLableEnglish,
                        onFieldSubmitted: (value) {
                          if (appLocal.localeName == 'en') {
                            controller.txtProductDiscriptionAr.clear();
                            GoogleTranslator()
                                .translate(value, to: 'ar')
                                .then((result) {
                              controller.txtProductDiscriptionAr.text =
                                  result.text;
                            });
                          }
                        },
                        hint: appLocal.productDescriptionFeildHint,
                        prefix: Text(
                          '*',
                          style: TextStyle(
                            color: AppColors.colorDanger,
                            fontSize: 20,
                          ),
                        ),
                        height: 170,
                      ),
                    ),
                    Center(
                      child: ElbaaabInputTextField(
                        onChanged: (value) {},
                        initialValue: controller.txtProductDiscriptionAr.text,
                        inputType: TextInputType.text,
                        textDirection: TextDirection.rtl,
                        editingController: controller.txtProductDiscriptionAr,
                        validator: (v) => validateFieldEmpty(
                          v,
                          errorMessage:
                              appLocal.productDescriptionFeildReturnMessage,
                          serverValue:
                              controller.product?.productDescription ?? "",
                          isReturend: (controller
                                  .validationHistory?.returnValues
                                  ?.contains("Arabic Description") ??
                              false),
                        ),
                        requiredCounter: true,
                        charaterlimit: 1500,
                        label: appLocal.productDescriptionFeildLableArbic,
                        onFieldSubmitted: (value) {
                          if (appLocal.localeName == 'ar') {
                            controller.txtProductDiscription.clear();
                           GoogleTranslator()
                                .translate(value, to: 'en')
                                .then((result) {
                              controller.txtProductDiscription.text =
                                  result.text;
                            });
                          }
                        },
                        hint: appLocal.productDescriptionFeildHint,
                        prefix: Text(
                          '*',
                          style: TextStyle(
                            color: AppColors.colorDanger,
                            fontSize: 20,
                          ),
                        ),
                        height: 170,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    Obx(
                      () => ElbaaabInputTextField(
                          margin: const EdgeInsets.only(top: 24),
                          onChanged: (value) => controller.productId = value,
                          initialValue: controller.gtinNumber.value,
                          inputFormatter: "[a-zA-Z 0-9]",
                          inputType: TextInputType.text,
                          label: appLocal.gtinFeildLabel,
                          hint: appLocal.gtinFeildHint,
                          charaterlimit: 50,
                          validator: (v) => validateFieldEmpty(
                                v,
                                errorMessage: ((v?.isEmpty ?? false) &&
                                        (controller
                                                .product
                                                ?.productManufacturerId
                                                ?.isNotEmpty ??
                                            false))
                                    ? appLocal.fieldRequired
                                    : appLocal.gtinFeildReturnMessage,
                                serverValue:
                                    controller.product?.productManufacturerId ??
                                        "",
                                isReturend: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Manufacturer ID") ??
                                    false),
                                isOptionalFeild: (controller.product
                                            ?.productManufacturerId?.isEmpty ??
                                        true) &&
                                    !(controller.validationHistory?.returnValues
                                            ?.contains("Manufacturer ID") ??
                                        false),
                              ),
                          suffix: SvgPicture.asset(
                            'assets/images/scanner.svg',
                            color: AppColors.colorPrimary,
                          ),
                          textDirection: appLocal.localeName == "en"
                              ? TextDirection.ltr
                              : TextDirection.rtl,
                          suffixClick: () async {
                            FocusManager.instance.primaryFocus?.unfocus();
                            final String barcode = await Get.to(ScanBarCode(),
                                fullscreenDialog: true);
                            if (barcode != "-1") {
                              controller.gtinNumber.value = barcode;
                              controller.productId = barcode;
                            }
                          }),
                    ),
                    Obx(
                      () => ElbaaabInputTextField(
                        margin: const EdgeInsets.only(top: 24),
                        onChanged: (value) {},
                        initialValue: controller.category.value.isEmpty
                            ? ''
                            : '${appLocal.localeName == "en" ? controller.category.value : controller.categoryAr.value} -> ${appLocal.localeName == "en" ? controller.subCategory.value : controller.subCategoryAr.value}',
                        label: appLocal.category,
                        prefix: Text(
                          '*',
                          style: TextStyle(
                            color: AppColors.colorDanger,
                            fontSize: 20,
                          ),
                        ),
                        textDirection: appLocal.localeName == "en"
                            ? TextDirection.ltr
                            : TextDirection.rtl,
                        validator: (v) => validateFieldEmpty(
                          v,
                          errorMessage: (controller
                                      .validationHistory?.returnValues
                                      ?.contains("Category") ??
                                  false)
                              ? appLocal.adminRejectThisCategory
                              : (controller.validationHistory?.returnValues
                                          ?.contains("SubCategory") ??
                                      false)
                                  ? appLocal.adminRejectThisSubCategory
                                  : appLocal.pleaseSelectCategorySubCategory,
                          serverValue:
                              "${controller.product?.productCategory?.categoryName ?? ""} -> ${controller.product?.productSubCategory?.subCategoryName ?? ""}",
                          isReturend: ((controller
                                      .validationHistory?.returnValues
                                      ?.contains("Category") ??
                                  false) ||
                              (controller.validationHistory?.returnValues
                                      ?.contains("SubCategory") ??
                                  false)),
                        ),
                        hint: 'ex : fashion -> man',
                        suffix: const Icon(
                          Icons.chevron_right,
                          color: Colors.white,
                          size: 17,
                        ),
                        onTap: () async {
                          FocusManager.instance.primaryFocus?.unfocus();
                          final result = await Get.toNamed(
                              RouteNames.selectCategoryScreen);
                          if (result != '1') {
                            var jsonResult = jsonDecode(result);
                            if (controller.category.value != jsonResult[0] ||
                                controller.subCategory.value != jsonResult[1]) {
                              controller.manufacture.value = '';
                              controller.brandId.value = '';
                            }
                            controller.category.value = jsonResult[0];
                            controller.categoryAr.value = jsonResult[7];
                            controller.subCategoryAr.value = jsonResult[8];
                            controller.subCategory.value = jsonResult[1];
                            controller.categoryId.value = jsonResult[5];
                            controller.subCategoryId.value = jsonResult[6];
                            List<Brands> brands = [];
                            for (var brand in jsonResult[4]) {
                              brands.add(Brands.fromJson(brand));
                            }
                            controller.brandsList = brands;
                          }
                        },
                      ),
                    ),
                    Obx(
                      () => ElbaaabInputTextField(
                        margin: const EdgeInsets.only(top: 24),
                        onChanged: (value) {},
                        initialValue: appLocal.localeName == "en"
                            ? controller.manufacture.value
                            : controller.manufactureAr.value,
                        label: appLocal.brand,
                        hint: '',
                        textDirection: appLocal.localeName == "en"
                            ? TextDirection.ltr
                            : TextDirection.rtl,
                        validator: (v) => validateFieldEmpty(
                          v,
                          errorMessage: (controller
                                      .validationHistory?.returnValues
                                      ?.contains("Brand") ??
                                  false)
                              ? appLocal.adminRejectThisBrand
                              : appLocal.pleaseSelectBrandOrType,
                          serverValue:
                              controller.product?.productBrand?.brandName ?? "",
                          isReturend: (controller
                                  .validationHistory?.returnValues
                                  ?.contains("Brand") ??
                              false),
                        ),
                        prefix: Text(
                          '*',
                          style: TextStyle(
                            color: AppColors.colorDanger,
                            fontSize: 20,
                          ),
                        ),
                        suffix: const Icon(
                          Icons.chevron_right,
                          color: Colors.white,
                          size: 17,
                        ),
                        onTap: () async {
                          FocusManager.instance.primaryFocus?.unfocus();
                          if (controller.brandsList.isEmpty &&
                              controller.product != null) {
                            _request.runQuery(
                                context: context,
                                query: GraphQlQuries.getBrands,
                                variables: {
                                  "categoryId": controller.categoryId.value
                                });
                          } else {
                            if (controller.categoryId.value.isEmpty) {
                              controller.brandFieldError.value =
                                  appLocal.selectCategoryFirst;
                            } else {
                              controller.brandFieldError.value = "";
                              final result = await Get.toNamed(
                                  RouteNames.selectManufactureScreen);
                              if (result != null && result != '1') {
                                var jsonResult = jsonDecode(result);
                                controller.manufacture.value = jsonResult[0];
                                controller.manufactureAr.value = jsonResult[1];
                                controller.brandId.value = jsonResult[2];
                              }
                            }
                          }
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                    Row(
                      children: [
                        Text(
                          appLocal.keyword,
                          style: FontStyles.fontRegular(),
                        ),
                        const SizedBox(width: 5),
                        InkWell(
                          onTap: () => BottomSheets.showAlertMessageBottomSheet(
                              appLocal.addKeywordMessage,
                              appLocal.keyword,
                              context),
                          child: Icon(
                            Icons.info,
                            color: AppColors.colorPrimary,
                          ),
                        ),
                      ],
                    ),
                    ElbaaabInputTextField(
                      margin: const EdgeInsets.only(top: 24),
                      onChanged: (v) => controller.keywords = v,
                      initialValue: controller.keywords,
                      label: appLocal.keyword,
                      hint: 'ex : t-shirt , kids',
                      charaterlimit: 300,
                      textDirection: appLocal.localeName == "en"
                          ? TextDirection.ltr
                          : TextDirection.rtl,
                      requiredCounter: true,
                      validator: (v) => validateFieldEmpty(
                        v,
                        errorMessage: "Admin rejected these keywords",
                        serverValue: controller.productKeywords,
                        isReturend: (controller.validationHistory?.returnValues
                                ?.contains("Keywords") ??
                            false),
                        isOptionalFeild: !(controller
                                .validationHistory?.returnValues
                                ?.contains("Keywords") ??
                            false),
                      ),
                      inputType: TextInputType.text,
                      inputFormatter: '[a-z A-Z,0-9|/ ا-ي]',
                      isClearable: true,
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (response["status"] == statusOK) {
      if ((response["brands"] as List).isEmpty) {
        selectBrand();
      } else {
        for (var element in response["brands"]) {
          controller.brandsList.add(Brands.fromJson(element));
        }
      }
    }
  }

  selectBrand() async {
    final result = await Get.toNamed(RouteNames.selectManufactureScreen);
    if (result != '1') {
      var jsonResult = jsonDecode(result);
      controller.manufacture.value = jsonResult[0];
      controller.brandId.value = jsonResult[1];
    }
  }
}
