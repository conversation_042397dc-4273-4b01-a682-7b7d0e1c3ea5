class AlertMessagesConstant {
  static const String freeDeliveryTarget =
      "If customer reaches this target amount or above, you will deliver the product for free";
  static const String locationRequired =
      "Your location is required so shipment company can pick up your item";
  static const String whyBankInfo = "To start receiving online payments";
  static const String pendingAmount =
      'Pending amount will be available after 30 days from Customer receiving Date';
  static const String messageSent = 'Your message has been sent successfully';
  static const String addKeywordMessage =
      'To improve your chance of selling add keywords and use comma between them';
  static const String availableQtyMessage =
      'This information will shown only for you so, you can monitor your stock';
  static const String minimumQtyAlertMessage =
      'you will receive a notification when the number of item reaches to minimum quantity and will be displayed in the user app as (Remaining)';
  static const String requiredBanner = "Shop Banner Image Is Required";
  static const String requiredLogo = "Shop Logo Image Is Required";
  static const String requiredTargetPrice = "You Enable Free Delivery Target But You Didn't Enter Price";
  static const String requiredTermsAndConditions = "Upload Your Shop Terms And Conditions";
  static const String requiredCity = "Upload Your Shop Terms And Conditions";
  static const String requiredMapLocation = "Select Pin Location Using Map";
  static const String requiredNumberForBranch = "Atleast One Number Is Required For Branch";
  static const String requiredEmailrForBranch = "Atleast One Email Is Required For Branch";
  static const String requiredOtpCode = "Enter Valid Otp";
  static const String pleaseAcceptTermsandConditions = "Please Accept Terms and Conditions";
  static const String limitReached = "You have reached the limit";
  static const String infoForDeliveryCompany = "This Info Is Important For Delivery Company,you can enter the approximate values";
}
