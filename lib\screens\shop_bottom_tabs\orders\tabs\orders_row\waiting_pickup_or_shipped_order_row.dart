import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/enums/enums.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/helper/url_loader.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/product_info.dart';

class WaitingPickOrShippedOrderRow extends StatelessWidget {
  final bool isShipped;
  final OrderItems orderItems;
  final Function(bool status) onUpdate;

  const WaitingPickOrShippedOrderRow({
    Key? key,
    required this.isShipped,
    required this.orderItems,
    required this.onUpdate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: AppColors.headerColorDark,
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () async {
          final result = await Get.toNamed(
            RouteNames.orderDetailScreen,
            arguments: [
              isShipped ? OrderType.shippedOrder : OrderType.confirmedOrder,
              orderItems
            ],
          );
          if (result) {
            onUpdate(result);
          }
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        orderItems.orderItemCode ?? "",
                        style: FontStyles.fontMedium(fontSize: 16),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "${appLocal.orderedIn} ${GlobalMethods.checkOrderToday(DateTime.parse(orderItems.createdAt ?? ""), "dd MMM, yyyy hh:mm a")}",
                        style: FontStyles.fontRegular(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isShipped
                          ? AppColors.colorTangerine
                          : AppColors.colorSecondary,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      isShipped ? appLocal.shipped : appLocal.confirmed,
                      style: FontStyles.fontMedium(fontSize: 12),
                    ),
                  ),
                ],
              ),
               SizedBox(height: 16.h),

            

              // Products List
              SizedBox(
                height: 132.h,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: orderItems.items?.length ?? 0,
                  itemBuilder: (context, index) => Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ProductInfo(item: (orderItems.items ?? [])[index]),
                  ),
                ),
              ),
               SizedBox(height: 16.h),

              // Footer
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      SvgPicture.string(SvgStrings.iconPriceTag),
                       SizedBox(width: 8.w),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: "Total: ",
                              style: FontStyles.fontSemibold(fontSize: 14),
                            ),
                            TextSpan(
                              text: "${orderItems.finalCost ?? 0}",
                              style: FontStyles.fontBold(fontSize: 18),
                            ),
                            TextSpan(
                              text: " ${appLocal.aed}",
                              style: FontStyles.fontSemibold(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8).r,
                    decoration: BoxDecoration(
                      color: AppColors.colorPrimary.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      appLocal.details,
                      style: FontStyles.fontMedium(
                        fontSize: 14,
                        color: AppColors.colorPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentButton({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.white.withOpacity(0.2)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.string(icon),
              const SizedBox(width: 8),
              Text(
                label,
                style: FontStyles.fontRegular(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 13,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleDocumentTap(String url) {
    if (Platform.isIOS) {
      Get.to(
        () => UrlLoader(url: url),
        fullscreenDialog: true,
        transition: Transition.circularReveal,
      );
    } else {
      GlobalMethods.launchInWebView(url);
    }
  }
}
