import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class Alerts {
  static Future<Future> alertView({
    required BuildContext context,
    String title = EnStrings.alert,
    required String content,
    String? cancelActionText,
    String defaultActionText = EnStrings.yes,
    required Function action,
    Function? cancelAction,
  }) async {
    final appLocal = AppLocalizations.of(context)!;
    title = appLocal.alert;
    defaultActionText = appLocal.yes;
    if (!Platform.isIOS) {
      return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: <Widget>[
            if (cancelActionText != null)
              TextButton(
                child: Text(cancelActionText),
                onPressed: () => cancelAction!(),
              ),
            TextButton(
              child: Text(defaultActionText),
              onPressed: () => action(),
            ),
          ],
        ),
      );
    }
    return showCupertinoDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => CupertinoAlertDialog(
        title: Text(title),
        content: Text(content),
        actions: <Widget>[
          if (cancelActionText != null)
            CupertinoDialogAction(
              child: Text(cancelActionText),
              onPressed: () => cancelAction!(),
            ),
          CupertinoDialogAction(
            child: Text(defaultActionText),
            onPressed: () => action(),
          ),
        ],
      ),
    );
  }

  static Future<Future> alertwithFeild({
    required BuildContext context,
    required String title,
    required String content,
    required String label,
    required String hint,
    String? cancelActionText,
    required ValueChanged<String> onChanged,
    required List<TextInputFormatter> formatter,
    required TextInputType inputType,
    required String defaultActionText,
    required Function action,
  }) async {
    if (!Platform.isIOS) {
      return showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(title),
          content: ElbaaabInputTextField(
            margin: const EdgeInsets.all(10),
            onChanged: onChanged,
            hint: hint,
            inputType: inputType,
            label: label,
            formatter: formatter,
          ),
          actions: <Widget>[
            if (cancelActionText != null)
              TextButton(
                child: Text(cancelActionText),
                onPressed: () => Navigator.of(context).pop(false),
              ),
            TextButton(
              child: Text(defaultActionText),
              onPressed: () => action(),
            ),
          ],
        ),
      );
    }
    return showCupertinoDialog(
      context: context,
      builder: (context) => Scaffold(
        backgroundColor: Colors.black.withOpacity(0.2),
        body: CupertinoAlertDialog(
          title: Text(title),
          content: Column(
            children: [
              Text(content),
              ElbaaabInputTextField(
                margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                onChanged: onChanged,
                hint: hint,
                inputType: inputType,
                label: label,
                formatter: formatter,
              ),
            ],
          ),
          actions: <Widget>[
            if (cancelActionText != null)
              CupertinoDialogAction(
                child: Text(cancelActionText),
                onPressed: () => Navigator.of(context).pop(false),
              ),
            CupertinoDialogAction(
              child: Text(defaultActionText),
              onPressed: () => action(),
            ),
          ],
        ),
      ),
    );
  }

  static showCustomSnackbar({
    required BuildContext context,
    required String contentText,
    required void Function() afterExecuteMethod,
    int second = 4,
  }) {
    bool isExecute = true;
    final snackbar = SnackBar(
      content: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            constraints: const BoxConstraints(maxHeight: 22.0),
            child: TweenAnimationBuilder(
              tween: Tween<double>(begin: 0, end: second * 1000.toDouble()),
              duration: Duration(seconds: second),
              builder: (context, double value, child) {
                return Stack(
                  fit: StackFit.loose,
                  alignment: Alignment.center,
                  children: [
                    SizedBox(
                      height: 20.0,
                      width: 20.0,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.0,
                        value: value / (second * 1000),
                        color: Colors.grey[850],
                        backgroundColor: Colors.white,
                      ),
                    ),
                    Center(
                      child: Text(
                        (second - (value / 1000)).toInt().toString(),
                        style: const TextStyle(color: Colors.white),
                        textScaleFactor: 0.85,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          const SizedBox(width: 12.0),
          Expanded(
            child: Text(
              contentText,
              style: FontStyles.fontRegular(fontSize: 12),
            ),
          ),
          InkWell(
            splashColor: Colors.white,
            onTap: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
              isExecute = !isExecute;
              return;
            },
            child: Container(
              color: Colors.grey[850],
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(width: 4.0),
                  Icon(
                    Icons.undo_rounded,
                    color: AppColors.colorPrimary,
                  ),
                  const SizedBox(width: 8.0),
                  Text(
                    "Undo",
                    style: FontStyles.fontMedium(),
                    textScaleFactor: 1.1,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      backgroundColor: Colors.grey[850],
      duration: Duration(seconds: second),
      behavior: SnackBarBehavior.floating,
      margin: const EdgeInsets.all(6.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4.0),
      ),
    );

    ScaffoldMessenger.of(context)
      ..hideCurrentSnackBar()
      ..showSnackBar(snackbar);

    Timer(Duration(seconds: second), () {
      if (isExecute) afterExecuteMethod();
    });
  }
}
