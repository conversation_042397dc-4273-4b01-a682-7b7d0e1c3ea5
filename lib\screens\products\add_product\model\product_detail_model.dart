class ColorModel {
   String id,colorName, colorNameAr, colorFamily, colorFamilyAr, thumnailUrl;
  final List<String> imagesUrl;
   bool isHidden;

  ColorModel(this.colorName, this.colorNameAr, this.colorFamily, this.colorFamilyAr, this.thumnailUrl, this.imagesUrl, this.isHidden,
      {this.id = ""});


    Map<String, dynamic> toJson() => {
      'colorName': colorName,
      'colorNameAr': colorNameAr,
      'colorFamily': colorFamily,
      'colorFamilyAr': colorFamilyAr,
      'thumnailUrl': thumnailUrl,
      'imagesUrl': imagesUrl,
      'isHidden': isHidden,
    };


}

class SizeModel {
  final String sizeUnit;
  final String sizeUnitAr;
  final List<SizeValueModel> sizes;

  SizeModel(this.sizeUnit, this.sizes, this.sizeUnitAr);
}


class SizeValueModel {
  final String value;
  final bool isValueHidden;
  
    bool isReAddedValue;
    bool isReGenratedVariant;

  SizeValueModel(this.value, this.isValueHidden,{this.isReAddedValue = false, this.isReGenratedVariant = true});
}

class CustomOption {
  String title,titleAr, value,valueAr, optionId;
  List<CustomOptionValues> valueList;

  CustomOption(this.title,this.titleAr, this.value,this.valueAr, this.valueList, this.optionId);
}

class CustomOptionValues {
  final String value;
  final String valueAr;
   bool isValueHidden;
   final bool isReAddedValue;

  CustomOptionValues(this.value,this.valueAr, this.isValueHidden,{this.isReAddedValue = false});
}

class ProductSpecificationModel {
  final String title, value, titleAr,valueAr, specsId;

  ProductSpecificationModel(this.title, this.value, this.specsId, this.titleAr, this.valueAr);

  Map<String, dynamic> toJson() => {
    'title': title,
    'value': value,
    'titleAr': titleAr,
    'valueAr': valueAr,
  };
}
