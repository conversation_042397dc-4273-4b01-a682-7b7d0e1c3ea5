class SignUpModel {
  SignUpModel({
    required this.typename,
    this.user,
    required this.status,
    required this.message,
  });
  late final String typename;
  User? user;
  late final int status;
  late final String message;


  SignUpModel.fromJson(Map<String, dynamic> json) {
    typename = json['__typename'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    status = json['status'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['__typename'] = typename;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    data['status'] = status;
    data['message'] = message;
    return data;
  }
}

class User {
   String? typename;
  String? id;
  bool? isEmailVerified;
  bool? isPhoneVerified;

  User({
    this.typename,
    this.id,
    this.isEmailVerified,
    this.isPhoneVerified,
  });
  User.from<PERSON><PERSON>(Map<String, dynamic> json) {
    typename = json['__typename']?.toString();
    id = json['_id']?.toString();
    isEmailVerified = json['isEmailVerified'];
    isPhoneVerified = json['isPhoneVerified'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['__typename'] = typename;
    data['_id'] = id;
    data['isEmailVerified'] = isEmailVerified;
    data['isPhoneVerified'] = isPhoneVerified;
    return data;
  }
}
