import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/model/shop/ViewShopModel.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/branches.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/controller/shop_info_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_border_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_dotted_border_container.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';

class ShopPickupAdresses extends GetView<ShopInfoController>
    with InputValidationUtil
    implements ServerResponse {
  final Function(int value) tabPosition;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  ShopPickupAdresses({Key? key, required this.tabPosition}) : super(key: key);
  late GraphQlInitilize _grapghRequest;
  RxString strError = ''.obs;
  getCities(BuildContext context) {
    Future.delayed(Duration.zero, () {
      _grapghRequest = GraphQlInitilize(this);
      if (controller.cityList.isEmpty) {
        _grapghRequest.runQueryWithCache(
            isHideSnackBar: false,
            isRequiredLoader: false,
            context: context,
            query: GraphQlQuries.getAllCities);
      }
    });
  }

  filterPickupAdddresses() {
    controller.filterBranch.clear();
    for (PickupAddressModel obj in controller.branches) {
      if (!obj.isDelete) {
        controller.filterBranch.add(obj);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    getCities(context);
    filterPickupAdddresses();
    
    return Scaffold(
      body: SingleChildScrollView(
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(appLocal),
              _buildBranchesList(appLocal),
              _buildAddBranchButton(appLocal),
              ElbaabNetworkEroor(strError: strError),
              _buildNavigationButtons(appLocal),
              const SizedBox(height: 60),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(AppLocalizations appLocal) {
    return Column(
      children: [
        SizedBox(height: 20.h),
        Center(
          child: Column(
            children: [
              SvgPicture.string(SvgStrings.shopPickupTag),
              SizedBox(height: 10.h),
              Text(
                appLocal.pickupLocations,
                style: FontStyles.fontBold(fontSize: 18),
              ),
            ],
          ),
        ),
        SizedBox(height: 20.h),
      ],
    );
  }

  Widget _buildBranchesList(AppLocalizations appLocal) {
    return Obx(() => ListView.separated(
      itemCount: controller.filterBranch.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      separatorBuilder: (context, index) => SizedBox(height: 16.h),
      itemBuilder: (context, index) => _buildBranchCard(appLocal, index),
    ));
  }

  Widget _buildBranchCard(AppLocalizations appLocal, int index) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Stack(
        children: [
          Branches(index: index, controller: controller),
          if (index > 0) _buildDeleteButton(appLocal, index),
        ],
      ),
    );
  }

  Widget _buildDeleteButton(AppLocalizations appLocal, int index) {
    return Positioned(
      right: 16,
      top: 15,
      child: Row(
        children: [
          Text(
            "${appLocal.pickupFeildAdminReject} ${index + 1}",
            style: FontStyles.fontMedium(),
          ),
          _buildDeleteIconButton(appLocal, index),
        ],
      ),
    );
  }

  Widget _buildDeleteIconButton(AppLocalizations appLocal, int index) {
    return InkWell(
      onTap: () {
        if (controller
            .branches[index].isUsedInOrder) {
          BottomSheets.showAlertMessageBottomSheet(
            appLocal.branchAlreadyAssign,
            appLocal.alert,
            Get.context!,
            buttonText: appLocal.ok,
          );
        } else {
          Alerts.alertView(
              context:  Get.context!,
              title: appLocal.alert,
              content:
                  appLocal.aleartOnDeleteAddress,
              defaultActionText: appLocal.yes,
              cancelActionText: appLocal.no,
              cancelAction: () => Get.back(),
              action: () {
                Get.back();
                Alerts.showCustomSnackbar(
                    context: Get.context!,
                    contentText: appLocal.removingCityBranch(controller.branches[index].city.value),
                  
                    afterExecuteMethod: () {
                      if (controller.shop == null) {
                        controller.branches
                            .removeAt(index);
                      } else {
                        if (controller.branches.last
                            .id.isEmpty) {
                          controller.branches
                              .removeAt(index);
                        } else {
                          controller.branches[index]
                              .isDelete = true;
                        }
                      }

                      controller.filterBranch
                          .removeAt(index);
                    });
              });
        }
      },
      child: Container(
        margin: const EdgeInsets.only(right: 5),
        width: 25,
        height: 25,
        child: Icon(
          Icons.cancel,
          color: AppColors.colorDanger,
        ),
      ),
    );
  }

  Widget _buildAddBranchButton(AppLocalizations appLocal) {
    return ElbaabDottedBorderContainer(
      padding: const EdgeInsets.all(24).r,
      onTap: () {
        if (controller.filterBranch.isNotEmpty) {
          if (formKey.currentState!.validate()) {
            var branch = controller.filterBranch.last;
            if (branch.latLng == null) {
              controller.isRequiredLocation.value = true;
            } else {
              controller.branches.add(
                PickupAddressModel(
                  "".obs,
                  "",
                  "",
                  ContactNumber(number: "".obs, isVerified: false),
                  ContactNumber(number: "".obs, isVerified: false),
                  "",
                  "",
                  null,
                  false,
                  false,
                  false,
                  true,
                ),
              );
            }
          }
        }
        filterPickupAdddresses();
      },
      title: appLocal.addAnotherAddress,
    );
  }

  Widget _buildNavigationButtons(AppLocalizations appLocal) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: kLeftSpace, vertical: 20),
      child: Row(
        children: [
          Expanded(
            child: ElbaabBorderButtonWidget(
              text: appLocal.back,
              onPress: () => tabPosition(0),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            child: ElbaabGradientButtonWidget(
              text: appLocal.next,
              onPress: () => _handleNextButton(appLocal),
            ),
          ),
        ],
      ),
    );
  }

  void _handleNextButton(AppLocalizations appLocal) {
    if (formKey.currentState!.validate()) {
      var branch = controller.filterBranch.last;
      if (branch.latLng == null) {
        controller.isRequiredLocation.value = true;
      } else {
        if (controller.shop != null) {
          List<Map<String, dynamic>> pickupAddress = [];
          int indexWhere = controller.branches.indexWhere(
              (element) =>
                  element.isUpdate == true ||
                  element.isDelete == true ||
                  element.isAdd == true);
          if (validateFeilds()) {
            if (indexWhere >= 0) {
              for (PickupAddressModel element
                  in controller.branches) {
                if (element.address.isNotEmpty) {
                  pickupAddress.add({
                    "_id": element.id,
                    "isUpdated": element.isDelete
                        ? false
                        : element.isUpdate,
                    "isAdded": element.isAdd,
                    "isDeleted": element.isDelete,
                    "isUsedInOrder": element.isUsedInOrder,
                    "pickUpAddress": element.address,
                    "pickUpCity": element.city.value,
                    "pickUpContactMobileNumber": {
                      "number": element
                          .contactNumber.number.value,
                      "isVerified":
                          element.contactNumber.isVerified
                    },
                    "pickUpContactLandNumber": {
                      "number":
                          element.landNumber.number.value,
                      "isVerified":
                          element.landNumber.isVerified
                    },
                    "coordinates": [
                      element.latLng?.latitude,
                      element.latLng?.longitude
                    ],
                  });
                }
              }
              String address = jsonEncode({
                "pickupAdr": pickupAddress,
              });
              prefs.setString("pickupAddress", address);
            }
            tabPosition(2);
          }
        } else {
          List<Map<String, dynamic>> pickupAddress = [];
          for (var element in controller.branches) {
            pickupAddress.add({
              "_id": "",
              "isUpdated": false,
              "isAdded": false,
              "isDeleted": false,
              "isUsedInOrder": false,
              "pickUpAddress": element.address,
              "pickUpCity": element.city.value,
              "pickUpContactMobileNumber": {
                "number":
                    element.contactNumber.number.value,
                "isVerified":
                    element.contactNumber.isVerified
              },
              "pickUpContactLandNumber": {
                "number": element.landNumber.number.value,
                "isVerified": element.landNumber.isVerified
              },
              "coordinates": [
                element.latLng?.latitude,
                element.latLng?.longitude
              ],
            });
          }
          String address =
              jsonEncode({"pickupAdr": pickupAddress});
          prefs.setString("pickupAddress", address);
          tabPosition(2);
        }
      }
    }
  }

  validateFeilds() {
    if (controller.shop?.shopPickupAddresses?.isNotEmpty ?? false) {
      for (int i = 0; i < controller.filterBranch.length; i++) {
        if ((controller.history.returnValues?.contains(
                    "${i + 1}${GlobalMethods.ordinal(i + 1)} Pickup City") ??
                false) &&
            controller.shop?.shopPickupAddresses?[i].pickUpCity ==
                controller.filterBranch[i].city.value) {
          strError.value =
              ("Please Update ${i + 1}${GlobalMethods.ordinal(i + 1)} Pickup City");
          i = controller.filterBranch.length;
        } else if ((controller.history.returnValues?.contains(
                    "${i + 1}${GlobalMethods.ordinal(i + 1)} Pickup Address") ??
                false) &&
            controller.shop?.shopPickupAddresses?[i].pickUpAddress ==
                controller.filterBranch[i].address) {
          strError.value =
              ("Please Update ${i + 1}${GlobalMethods.ordinal(i + 1)} Pickup Address");
          i = controller.filterBranch.length;
        } else if ((controller.history.returnValues?.contains(
                    "${i + 1}${GlobalMethods.ordinal(i + 1)} Pickup address contact number") ??
                false) &&
            controller.shop?.shopPickupAddresses?[i].pickUpContactMobileNumber?.number ==
                controller.filterBranch[i].contactNumber.number.value) {
          strError.value =
              ("Please Update ${i + 1}${GlobalMethods.ordinal(i + 1)} Pickup Contact Number");
          i = controller.filterBranch.length;
        } else if ((controller.history.returnValues?.contains(
                    "${i + 1}${GlobalMethods.ordinal(i + 1)} Pickup location") ??
                false) &&
            (controller.shop?.shopPickupAddresses?[i].pickUpMapLocation?.coordinates ?? [])
                    .first ==
                (controller.filterBranch[i].latLng?.latitude ?? 0.0)) {
          strError.value =
              ("Please Update ${i + 1}${GlobalMethods.ordinal(i + 1)} Pickup Location");
          i = controller.filterBranch.length;
        } else if ((i + 1) == controller.filterBranch.length) {
          return true;
        }
      }
    } else {
      return true;
    }
  }

  @override
  onError(error, String type) {
    BaseModel model = BaseModel.fromJson(error);
    strError.value = model.message ?? "";
  }

  @override
  onSucess(response, String type) {
    if (type == "ADDRESS") {
      ViewShopModel model = ViewShopModel.fromJson(response);
      if (model.status == statusOK) {
        tabPosition(2);
      }
    } else {
      controller.cityList.clear();
      for (var city in response["cities"]) {
        controller.cityList.add(city["cityName"]);
        if(city["ar"] == null){

        controller.cityListAr.add(city["cityName"]);
        }else{
        controller.cityListAr.add(city["ar"]["cityName"]);

        }
      }
    }
  }
}
