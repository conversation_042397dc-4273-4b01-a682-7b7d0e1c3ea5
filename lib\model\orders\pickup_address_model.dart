class AllPickupAddressModel {
  final String? typename;
  final int? status;
  final String? message;
  final List<PickUpAddresses>? pickUpAddresses;

  AllPickupAddressModel({
    this.typename,
    this.status,
    this.message,
    this.pickUpAddresses,
  });

  AllPickupAddressModel.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      status = json['status'] as int?,
      message = json['message'] as String?,
      pickUpAddresses = (json['pickUpAddresses'] as List?)?.map((dynamic e) => PickUpAddresses.fromJson(e as Map<String,dynamic>)).toList();

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'status' : status,
    'message' : message,
    'pickUpAddresses' : pickUpAddresses?.map((e) => e.toJson()).toList()
  };
}

class PickUpAddresses {
  final String? typename;
  final String? id;
  final String? pickUpCity;
  final String? pickUpAddress;

  PickUpAddresses({
    this.typename,
    this.id,
    this.pickUpCity,
    this.pickUpAddress,
  });

  PickUpAddresses.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      id = json['_id'] as String?,
      pickUpCity = json['pickUpCity'] as String?,
      pickUpAddress = json['pickUpAddress'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    '_id' : id,
    'pickUpCity' : pickUpCity,
    'pickUpAddress' : pickUpAddress
  };
}