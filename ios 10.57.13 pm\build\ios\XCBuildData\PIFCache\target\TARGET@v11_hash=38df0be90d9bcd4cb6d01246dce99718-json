{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b7f99340c93bf4ca9a82754662c9c8a0", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e19228be12252edf38dd6ca51b6df219", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c5a173ef682d97f0584bd773dbfb7f9f", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98641391e496a4a37f3ef5b39b0f291421", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c5a173ef682d97f0584bd773dbfb7f9f", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cab47b6636e5c33377ce749e179bf40d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98da803358ab8a553343136120080d5b7b", "guid": "bfdfe7dc352907fc980b868725387e98a3aecb2067b7ea581e8574efa04b567d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faecb9190282a2d0f6cd1edea8020293", "guid": "bfdfe7dc352907fc980b868725387e98ca5aae6bb06ee1fa2c61cadee9179d13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce8f4c37251864763a10602b09132c85", "guid": "bfdfe7dc352907fc980b868725387e9849e5d1e5f086b31345a2271979af829c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873d6cd5ea40d9d2441294627dda6ff25", "guid": "bfdfe7dc352907fc980b868725387e98c8117d5259ba2d833aa77b55e0629a69", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cad76be4f94604e9ac6c5b406a97482", "guid": "bfdfe7dc352907fc980b868725387e98148958ee6cc3b932ee6e5984841c3b6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832ed8d4d162ead3521f826e253819a9f", "guid": "bfdfe7dc352907fc980b868725387e9822ca6085734242156a2156e489c2d9d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98570714bf402c26f0a6b68410475feb8d", "guid": "bfdfe7dc352907fc980b868725387e9805095e1c78cbc4d0c108bfcbab5be396", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a9e9fcb06cd32c3239fe15852355fa7", "guid": "bfdfe7dc352907fc980b868725387e980ebb02f1f2e4ce853aaaf202a97e37e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b0000b25e6c663c9dc10e527a3edad", "guid": "bfdfe7dc352907fc980b868725387e98a7868bbb446b33ba81b29c4692b60130", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98077481e00a3a77b7120344981f1d68ae", "guid": "bfdfe7dc352907fc980b868725387e98f4f5efa54904f7ef1257e6c4e99d0b0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fecaa8c1d3728abd859713abab180008", "guid": "bfdfe7dc352907fc980b868725387e98201969cda593f96062491e39d26f967c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c0dd3d530d329dee8c839f11675079e", "guid": "bfdfe7dc352907fc980b868725387e981e2698a3379974d0bec5bf6ba3fd3ba6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ad2dc82ab8ed911bd330f7f2eae217f7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f6cb714f2b8f2b3e39a77b40a02454b6", "guid": "bfdfe7dc352907fc980b868725387e98f725d4e8e04b01e297b4f883c6405f63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815904a3a5d65cbb745349f119cd11176", "guid": "bfdfe7dc352907fc980b868725387e98822bf6f0ad108ef3b31b216612febc6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a4e068673da6522dd05f6b9ecc55430", "guid": "bfdfe7dc352907fc980b868725387e981ac78f58810a9ea025aac7235a1b6e4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a274c96c6bb860e2d0e1e21c7629edc1", "guid": "bfdfe7dc352907fc980b868725387e9898ca26f39193cd147d880b89af3c1748"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ccdc8ffac16cd6c358b88bfa6dcec79", "guid": "bfdfe7dc352907fc980b868725387e98175c5911f1ecafa8c146623ca52797dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b7748e19b1b33e69542c32377ecf54f", "guid": "bfdfe7dc352907fc980b868725387e98c8d8dca40bc50b0d3e06dbba81945820"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e2d2b84a503936230ffea26d7126a66", "guid": "bfdfe7dc352907fc980b868725387e9873adeedd6697988ca43493b92f4dfa28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a065fd7280c8d4842cb92f01b3893126", "guid": "bfdfe7dc352907fc980b868725387e980c07715cb6dba3d426fcfe4ec4992c32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e54bf33490fe14df60b0ce5d47c707d5", "guid": "bfdfe7dc352907fc980b868725387e98728384efd9122084f7442c8c0def9852"}], "guid": "bfdfe7dc352907fc980b868725387e98da8caa0468a6494c539bcc9a3f56496f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e980d562db8451c55dccc31ac9670df4bb5"}], "guid": "bfdfe7dc352907fc980b868725387e989c121c0173be5443faa5b2120d803293", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c44defa1c51e6619c9bc20d8804a8b84", "targetReference": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321"}], "guid": "bfdfe7dc352907fc980b868725387e984c8ab30283e75361b6a65cb914f55e53", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321", "name": "sqflite-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e", "name": "sqflite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9892137925c03f59a4fb600ced1a959f92", "name": "sqflite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}