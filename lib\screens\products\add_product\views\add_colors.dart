import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/dio/api_requests.dart';
import 'package:overolasuppliers/helper/enums/enums.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/media_picker.dart';
import 'package:overolasuppliers/helper/multi_image_picker/multi_image_crop.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/product_variation_model.dart';
import 'package:overolasuppliers/model/add_product/view_prduct/product_detail_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/screens/products/add_product/model/product_detail_model.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_carousel_feild_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_label_container.dart';
import 'package:translator/translator.dart';

import '../../../../model/upload_image_model.dart';

class AddColors extends StatefulWidget {
  const AddColors({super.key});

  @override
  State<AddColors> createState() => _AddColorsState();
}

class _AddColorsState extends State<AddColors>
    with InputValidationUtil
    implements ServerResponse {
  TextEditingController txtColorName = TextEditingController();
  TextEditingController txtColorNameAr = TextEditingController();
  final controller = Get.find<AddProductController>();
  List<int> indexList = [];
  RxList<String> arrImages = <String>[].obs,
      arrReturnImageUrls = <String>[].obs;

  RxString colorFamilyError = ''.obs,
      noImagesError = ''.obs,
      thumnailUrl = ''.obs,
      returnColorName = "".obs,
      returnColorFamily = "".obs,
      returnColorIcon = "".obs,
      colorNameError = ''.obs,
      translationError = ''.obs,
      colorFamily = ''.obs,
      colorFamilyAr = ''.obs;

  int updateIndex = -1;

  late ApiRequest _request;
  late ColorModel colorModel;
  late GraphQlInitilize _grapghRequest;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      _grapghRequest = GraphQlInitilize(this);
      if (controller.colorFamilyList.isEmpty) {
        _grapghRequest.runQueryWithCache(
          context: context,
          query: GraphQlQuries.getAllColors,
          type: "getAllColors",
        );
      }

      if (controller.product?.productOptions?.productColors?.isNotEmpty ??
          false) {
        for (var element in controller.validationHistory?.returnValues ?? []) {
          if (element.contains("image")) {
            int i = int.parse(element.replaceAll(RegExp(r'[^0-9]'), ''));
            String image = element.replaceAll(RegExp(r"[0-9]+"), "");
            int indexWhere =
                (controller.product?.productOptions?.productColors ?? [])
                    .indexWhere((element) =>
                        element.colorName?.trim() ==
                            image.replaceAll("image", "").trim() ||
                        element.colorFamily ==
                            image.replaceAll("image", "").removeAllWhitespace);
            if (indexWhere != -1) {
              arrReturnImageUrls.add(controller.product?.productOptions
                      ?.productColors?[indexWhere].colorImages?[i - 1] ??
                  "");
            }
          }
          if (element.contains("Color family")) {
            returnColorFamily.value =
                returnValue(element, filterType: "family");
          }
          if (element.contains("Color Name")) {
            returnColorName.value = returnValue(element, filterType: "name");
            if (returnColorName.isNotEmpty) {
              controller.formKey.currentState!.validate();
            }
          }
          if (element.contains("Color Icon")) {
            returnColorIcon.value = returnValue(element, filterType: "icon");
          }
        }
      }
    });

    sortImages();
  }

  String returnValue(String element, {required String filterType}) {
    int i = int.parse(element.replaceAll(RegExp(r'[^0-9]'), ''));
    var color =
        (controller.product?.productOptions?.productColors ?? [])[i - 1];
    try {
      if (color.id == colorModel.id) {
        switch (filterType) {
          case "name":
            return color.colorName ?? "";
          case "family":
            return color.colorFamily ?? "";
          default:
            return color.colorIcon ?? "";
        }
      } else {
        return "";
      }
    } catch (e) {
      return "";
    }
  }

  sortImages() async {
    arrImages.clear();
    if (Get.arguments != null) {
      colorModel = Get.arguments[0];
      updateIndex = Get.arguments[1];
      thumnailUrl.value = colorModel.thumnailUrl;
      for (var element in colorModel.imagesUrl) {
        arrImages.add(element);
      }
      colorFamily.value = colorModel.colorFamily;
      colorFamilyAr.value = colorModel.colorFamilyAr;
      txtColorName.text = colorModel.colorName;
      txtColorNameAr.text = colorModel.colorNameAr;
    } else {
      for (var i = 0; i < controller.sliderImages.length; i++) {
        String file = controller.sliderImages[i];
        final index = controller.colorList
            .indexWhere((element) => element.imagesUrl.contains(file));

        if (index == -1) {
          if (file.isNotEmpty) {
            arrImages.add(file);
          }
        }
        if (controller.validationHistory?.returnValues
                ?.any((element) => element.contains("Image")) ??
            false) {
          if ((controller.validationHistory?.returnValues
                  ?.contains("Image ${i + 1}") ??
              false)) {
            arrReturnImageUrls.add(file);
          }
        }
      }
      if (arrImages.isNotEmpty) {
        thumnailUrl.value = arrImages.first;
      }
    }
  }

  bool requriedFeildUpdate() {
    bool readOnly = false;
    if (updateIndex != -1) {
      ColorModel colorModel = Get.arguments[0];
      if (controller.isApprovedProduct) {
        for (ProductColors product
            in controller.product?.productOptions?.productColors ?? []) {
          if (product.colorFamily == colorModel.colorFamily &&
              product.colorName == txtColorName.text) {
            readOnly = true;
            break;
          }
        }
      } else if (controller.isMatchProduct) {
        for (ProductColors product
            in controller.product?.productOptions?.productColors ?? []) {
          if (product.colorFamily == colorModel.colorFamily &&
              product.colorName == txtColorName.text) {
            readOnly = true;
            break;
          }
        }
      }
    }
    return readOnly;
  }

  Widget deleteColor(AppLocalizations appLocal) {
    return IconButton(
      onPressed: () => Alerts.alertView(
          context: context,
          title: appLocal.alert,
          defaultActionText: appLocal.yes,
          content: appLocal.alertMessageForRemoveColor,
          cancelActionText: appLocal.no,
          cancelAction: () => Get.back(),
          action: () {
            List<String> images = [];
            for (var i = 0;
                i < controller.colorList[updateIndex].imagesUrl.length;
                i++) {
              if (!controller.nonRemovableImages
                  .contains(controller.colorList[updateIndex].imagesUrl[i])) {
                images.add(controller.colorList[updateIndex].imagesUrl[i]);
                controller.sliderImages
                    .remove(controller.colorList[updateIndex].imagesUrl[i]);
              }
            }

            controller.colorList.removeAt(updateIndex);
            Get.back();
            Get.back();
          }),
      icon: Icon(
        Icons.delete,
        color: AppColors.colorDanger,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    _request = ApiRequest(this);
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.addColor,
        leadingBack: true,
        trailingWidget: updateIndex != -1
            ? ((controller.isApprovedProduct || controller.isMatchProduct) &&
                    controller.colorList[updateIndex].id.isEmpty)
                ? deleteColor(appLocal)
                : (controller.colorList[updateIndex].id.isEmpty ||
                        (controller.colorList[updateIndex].id.isNotEmpty &&
                            (controller.validationHistory?.status ==
                                    "Returned" &&
                                !(controller.product?.validationHistory?.any(
                                        (element) =>
                                            element.status == "Accepted") ??
                                    false))))
                    ? deleteColor(appLocal)
                    : (controller.product == null || controller.isDraftProduct)
                        ? deleteColor(appLocal)
                        : null
            : null,
      ),
      body: SingleChildScrollView(
        child: Form(
          key: controller.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.all(16.0).r,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(
                      () => RichText(
                        text: TextSpan(children: <TextSpan>[
                          TextSpan(
                            text: appLocal.uploadColorPhoto,
                            style: FontStyles.fontMedium(fontSize: 12),
                          ),
                          TextSpan(
                            text: '  ${appLocal.addedImages(arrImages.length)}',
                            style: FontStyles.fontRegular(
                              fontSize: 10,
                              color: Colors.white.withOpacity(0.38),
                            ),
                          ),
                        ]),
                      ),
                    ),
                    SizedBox(height: 11.h),
                    SizedBox(
                      height: 93.h,
                      child: Row(
                        children: [
                          Obx(
                            () => arrImages.length < 5
                                ? Align(
                                    alignment: Alignment.topLeft,
                                    child: GestureDetector(
                                      onTap: () async {
                                        int count = 5 - arrImages.length;
                                        if (count > 0) {
                                          multiImagePicker(context, count)
                                              .then((value) {
                                            if (value != null) {
                                              noImagesError.value = "";
                                              uploadimage(value);
                                            }
                                          });
                                        }
                                      },
                                      child: Obx(
                                        () => Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              height: 70.h,
                                              width: 70.w,
                                              decoration: BoxDecoration(
                                                  color:
                                                      AppColors.headerColorDark,
                                                  borderRadius:
                                                      BorderRadius.circular(5),
                                                  border: Border.all(
                                                    color: noImagesError
                                                            .value.isNotEmpty
                                                        ? AppColors.colorDanger
                                                        : Colors.transparent,
                                                    width: 1,
                                                  )),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(9.0).r,
                                                child: Column(
                                                  children: <Widget>[
                                                    SvgPicture.string(
                                                        SvgStrings.iconCamera),
                                                    Text(
                                                      appLocal.add,
                                                      style:
                                                          FontStyles.fontMedium(
                                                              fontSize: 12),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                            if (noImagesError.value.isNotEmpty)
                                              Text(
                                                noImagesError.value,
                                                style: FontStyles.fontRegular(
                                                    fontSize: 11,
                                                    color:
                                                        AppColors.colorDanger),
                                              ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  )
                                : Container(),
                          ),
                          SizedBox(width: 6.w),
                          Expanded(
                            child: Obx(
                              () => ListView.builder(
                                  itemCount: arrImages.length,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder: (context, index) {
                                    if (index == 0) {
                                      indexList.clear();
                                    }
                                    final containindex = indexList.indexWhere(
                                        (element) => element == index);
                                    if (indexList.contains(index)) {
                                      indexList.removeAt(index);
                                    } else {
                                      indexList.add(index);
                                    }
                                    return Obx(
                                      () => Container(
                                        width: 70.w,
                                        height: 93.h,
                                        margin:
                                            const EdgeInsets.only(right: 10).r,
                                        child: Stack(
                                          alignment: Alignment.center,
                                          children: <Widget>[
                                            Positioned(
                                              top: 0,
                                              left: 0,
                                              right: 0,
                                              height: 70.h,
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(5),
                                                  border: Border.all(
                                                    color: containindex >= 0
                                                        ? AppColors
                                                            .colorSecondary
                                                        : Colors.transparent,
                                                    width: 3.5,
                                                  ),
                                                ),
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            5),
                                                    border: Border.all(
                                                      color: arrReturnImageUrls
                                                              .contains(
                                                                  arrImages[
                                                                      index])
                                                          ? AppColors
                                                              .colorDanger
                                                          : Colors.transparent,
                                                      width: 2,
                                                    ),
                                                  ),
                                                  child: GlobalMethods
                                                      .netWorkImage(
                                                          arrImages[index],
                                                          BorderRadius.circular(
                                                              5),
                                                          BoxFit.cover),
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              bottom: 0,
                                              child: InkWell(
                                                onTap: () => editPhoto(
                                                    context,
                                                    index,
                                                    controller,
                                                    appLocal),
                                                child: SvgPicture.string(
                                                    SvgStrings.iconEdit),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  }),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Obx(
                      () => thumnailUrl.isNotEmpty
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                SizedBox(height: 10.h),
                                Text(
                                  appLocal.colorIcon,
                                  style: FontStyles.fontMedium(fontSize: 12),
                                ),
                                SizedBox(height: 15.h),
                                InkWell(
                                  onTap: () => getIcon(url: thumnailUrl.value),
                                  child: Wrap(
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: (returnColorIcon.value ==
                                                    thumnailUrl.value)
                                                ? AppColors.colorDanger
                                                : Colors.transparent,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(15),
                                        ),
                                        width: 30.w,
                                        height: 30.h,
                                        child: GlobalMethods.netWorkImage(
                                          thumnailUrl.value,
                                          BorderRadius.circular(15),
                                          BoxFit.fill,
                                        ),
                                      ),
                                      SizedBox(width: 10.w),
                                      SvgPicture.string(SvgStrings.iconEdit),
                                    ],
                                  ),
                                ),
                              ],
                            )
                          : Container(),
                    ),
                    SizedBox(height: 20.h),
                    Obx(
                      () => ElbaabLabelContainer(
                        onTap: () => requriedFeildUpdate()
                            ? {}
                            : BottomSheets.showListPicker(
                                    context,
                                    appLocal.localeName == "en"
                                        ? controller.colorFamilyList
                                            .map((e) => e.colorFamilyName ?? "")
                                            .toList()
                                        : controller.colorFamilyList
                                            .map((e) =>
                                                e.colorFamilyNameAr ?? "")
                                            .toList())
                                .then(
                                (index) => {
                                  colorFamily.value = controller
                                          .colorFamilyList[index]
                                          .colorFamilyName ??
                                      "",
                                  colorFamilyAr.value = controller
                                          .colorFamilyList[index]
                                          .colorFamilyNameAr ??
                                      "",
                                },
                              ),
                        label: appLocal.selectColorFamily,
                        borderColor: (colorFamilyError.value.isNotEmpty ||
                                (returnColorFamily.isNotEmpty &&
                                    returnColorFamily.value ==
                                        colorFamily.value))
                            ? AppColors.colorDanger
                            : null,
                        errorText: colorFamilyError.value.isNotEmpty
                            ? colorFamilyError.value
                            : (returnColorFamily.isNotEmpty &&
                                    returnColorFamily.value ==
                                        colorFamily.value)
                                ? appLocal.colorFamilyFeildReturnMessage
                                : null,
                        leading: Text(
                          appLocal.localeName == "en"
                              ? colorFamily.value.isEmpty
                                  ? appLocal.colorFamilyFeildHint
                                  : colorFamily.value
                              : colorFamilyAr.value.isEmpty
                                  ? appLocal.colorFamilyFeildHint
                                  : colorFamilyAr.value,
                          style: colorFamily.value.isEmpty
                              ? FontStyles.fontLight(
                                  fontStyle: FontStyle.italic,
                                  color: Colors.white.withOpacity(0.5))
                              : FontStyles.fontRegular(),
                        ),
                        trailing: const Icon(
                          Icons.arrow_drop_down_outlined,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              ElbaabCarouselFeildWidget(children: [
                Center(
                  child: Obx(
                    () => ElbaaabInputTextField(
                      initialValue:
                          updateIndex == -1 ? "" : colorModel.colorName,
                      editingController: txtColorName,
                      onChanged: (v) {},
                      inputFormatter: '[a-z&A-Z ]',
                      label: appLocal.colorNameEnglish,
                      errorText: colorNameError.value.isNotEmpty
                          ? colorNameError.value
                          : null,
                      charaterlimit: 30,
                      validator: (v) => validateFieldEmpty(
                        v,
                        errorMessage: appLocal.adminRejectColorName,
                        serverValue: returnColorName.value,
                        isReturend: returnColorName.value.isNotEmpty,
                        isOptionalFeild: returnColorName.isEmpty,
                      ),
                      onFieldSubmitted: (value) {
                        if (appLocal.localeName == 'en') {
                          txtColorNameAr.clear();
                          GoogleTranslator()
                              .translate(value, to: 'ar')
                              .then((result) {
                            translationError.value =
                                appLocal.readySpecTranslation;

                            txtColorNameAr.text = result.text;
                            Future.delayed(Durations.extralong4,
                                () => translationError.value = "");
                          });
                        }
                      },
                      textDirection: appLocal.localeName == "en"
                          ? TextDirection.ltr
                          : TextDirection.rtl,
                      readOnly: requriedFeildUpdate(),
                      // onChanged: (v) => colorName = v,
                      hint: appLocal.colorNameFeildHint,
                      inputType: TextInputType.text,
                    ),
                  ),
                ),
                Center(
                  child: Obx(
                    () => ElbaaabInputTextField(
                      initialValue:
                          updateIndex == -1 ? "" : colorModel.colorNameAr,
                      editingController: txtColorNameAr,
                      onChanged: (v) {},
                      inputFormatter: null,
                      label: appLocal.colorNameArabic,
                      errorText: colorNameError.value.isNotEmpty
                          ? colorNameError.value
                          : null,
                      charaterlimit: 30,
                      validator: (v) => validateFieldEmpty(
                        v,
                        errorMessage: appLocal.adminRejectColorName,
                        serverValue: returnColorName.value,
                        isReturend: returnColorName.value.isNotEmpty,
                        isOptionalFeild: returnColorName.isEmpty,
                      ),
                      onFieldSubmitted: (value) {
                        if (appLocal.localeName == 'ar') {
                          txtColorName.clear();
                          GoogleTranslator()
                              .translate(value, to: 'en')
                              .then((result) {

                            translationError.value =
                                appLocal.readySpecTranslation;
                            txtColorName.text = result.text;
                            Future.delayed(Durations.extralong4,
                                () => translationError.value = "");
                          });
                        }
                      },
                      textDirection: TextDirection.rtl,
                      readOnly: requriedFeildUpdate(),
                      // onChanged: (v) => colorName = v,
                      hint: appLocal.colorNameFeildHint,
                      inputType: TextInputType.text,
                    ),
                  ),
                ),
              ]),

              Obx(() => Center(
                child: Text(translationError.value,
                    style: FontStyles.fontRegular(color: AppColors.colorDanger)),
              )),

              Padding(
                padding: const EdgeInsets.all(16.0).r,
                child: ElbaabButtonWidget(
                  onPress: () {
                    if (returnColorName.isNotEmpty &&
                        (returnColorName.value == txtColorName.text ||
                            returnColorName.value == txtColorNameAr.text)) {
                      return;
                    } else if (returnColorFamily.isNotEmpty &&
                        returnColorFamily.value == colorFamily.value) {
                      return;
                    } else if (returnColorIcon.isNotEmpty &&
                        returnColorIcon.value == thumnailUrl.value) {
                      return;
                    } else if (arrImages
                        .any((item) => arrReturnImageUrls.contains(item))) {
                      return;
                    }
                    if (appLocal.localeName == "en") {
                      if (txtColorName.text.isNotEmpty &&
                          txtColorNameAr.text.isEmpty) {

                    translationError.value = appLocal.aleartFillSpecFeild;
                        return;
                      }
                    } else {
                      if (txtColorNameAr.text.isNotEmpty &&
                          txtColorName.text.isEmpty) {
                        translationError.value = appLocal.aleartFillSpecFeild;
                        return;
                      }

                    }
                    if (indexList.isEmpty || arrImages.isEmpty) {
                      noImagesError.value = appLocal.pleaseUploadColorImage;
                      return;
                    }
                    if (colorFamily.isEmpty) {
                      colorFamilyError.value = appLocal.pleaseSelectColorFamily;
                    } else if (indexList.isNotEmpty) {
                      List<String> images = [];
                      for (var index in indexList) {
                        images.add(arrImages[index]);
                      }

                      int colorFamilyIndex = controller.colorList.indexWhere(
                          (element) =>
                              element.colorFamily == colorFamily.value &&
                              element.colorName.trimRight() ==
                                  txtColorName.text.trimRight());

                      if (colorFamilyIndex == -1) {
                        if (updateIndex != -1) {
                          controller.colorList[updateIndex] = ColorModel(
                              txtColorName.text,
                              txtColorNameAr.text,
                              colorFamily.value,
                              colorFamilyAr.value,
                              thumnailUrl.value,
                              images,
                              false);
                        } else {
                          controller.colorList.add(
                            ColorModel(
                                txtColorName.text,
                                txtColorNameAr.text,
                                colorFamily.value,
                                colorFamilyAr.value,
                                thumnailUrl.value,
                                images,
                                false),
                          );
                        }

                        Get.back();
                      } else {
                        int index = controller.colorList.indexWhere((element) =>
                            (element.colorName.isNotEmpty &&
                                element.colorName == txtColorName.text));
                        if (txtColorName.text.isEmpty) {
                          if (updateIndex != -1 &&
                              updateIndex != colorFamilyIndex) {
                            colorNameError.value = appLocal.addNewColorName;
                            return;
                          } else if (colorFamilyIndex != -1 &&
                              updateIndex != colorFamilyIndex) {
                            bool iscontain = controller.colorList.any(
                                (element) =>
                                    element.colorFamily == colorFamily.value &&
                                    element.colorName.isEmpty);
                            if (iscontain) {
                              colorNameError.value = appLocal.addNewColorName;
                              return;
                            }
                          } else if ((controller.colorList[colorFamilyIndex]
                                  .colorName.isEmpty) &&
                              updateIndex != colorFamilyIndex) {
                            colorNameError.value = appLocal.addNewColorName;
                            return;
                          }
                        }
                        if (index != -1 && updateIndex != index) {
                          colorNameError.value = appLocal.colorNameAlreadyExist;
                        } else {
                          if (txtColorName.text.isEmpty) {
                            if (updateIndex != -1) {
                              txtColorName.text =
                                  controller.colorList[updateIndex].colorName;
                            }
                          }
                          if (updateIndex != -1) {
                            controller.colorList[updateIndex] = ColorModel(
                                txtColorName.text.trimRight(),
                                txtColorNameAr.text.trim(),
                                colorFamily.value,
                                colorFamilyAr.value,
                                thumnailUrl.value,
                                images,
                                false,
                                id: controller.colorList[updateIndex].id);
                          } else {
                            controller.colorList.add(ColorModel(
                                txtColorName.text.trimRight(),
                                txtColorNameAr.text.trim(),
                                colorFamily.value,
                                colorFamilyAr.value,
                                thumnailUrl.value,
                                images,
                                false));
                          }
                          Get.back();
                        }
                      }
                    }
                    if (controller.arrVariations.isNotEmpty &&
                        controller.colorList.length ==
                            controller.arrVariations.length) {
                      for (var i = 0; i < controller.colorList.length; i++) {
                        String colorFamily =
                            controller.colorList[i].colorFamily;
                        String colorName = controller.colorList[i].colorName;
                        int index = controller.arrVariations.indexWhere(
                            (element) =>
                                element.colorFamily == colorFamily &&
                                element.colorName == colorName);
                        if (index != -1) {
                          if (index != i) {
                            Variants variants = controller.arrVariations[index];
                            controller.arrVariations.removeAt(index);
                            controller.arrVariations.insert(i, variants);
                          }
                        }
                      }
                    }
                  },
                  colors: AppColors.colorSecondaryYellow,
                  margin: const EdgeInsets.only(top: 24),
                  textColor: Colors.black,
                  height: 42,
                  text: appLocal.done,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  uploadimage(List<File> images, {int? index}) {
    List<String> arrParams = [];
    for (var i = 0; i < images.length; i++) {
      arrParams.add("images$i");
    }
    _request.uploadImage(files: images, parameters: arrParams);
  }

  editPhoto(BuildContext context, int index, AddProductController controller,
      AppLocalizations appLocalizations) {
    BottomSheets.editPhoto(context).then((value) async {
      switch (value) {
        case UpdateMedia.delete:
          Alerts.alertView(
            context: context,
            content: appLocalizations.deleteImageAlertMesssgae,
            action: () {
              controller.sliderImages.remove(arrImages[index]);
              arrImages.removeAt(index);
              if (index == 0 && arrImages.isNotEmpty) {
                thumnailUrl.value = arrImages.first;
              } else if (index == 0 && arrImages.isEmpty) {
                thumnailUrl.value = "";
              }
              try {
                if (colorModel.id.isNotEmpty) {
                  for (Variants variants in controller.arrVariations) {
                    if (variants.colorFamily == colorModel.colorFamily &&
                        variants.colorName == colorModel.colorName) {
                      for (Variations element in variants.variations ?? []) {
                        element.isUpdated = true;
                        if (arrImages.isNotEmpty) {
                          element.variantImages?[index] = arrImages.first;
                          if (index == 0) {
                            element.variantAttributes?.variantColor?.colorIcon =
                                arrImages.first;
                            variants.colorIcon = arrImages.first;
                            element.variantImages = arrImages;
                          }
                        }
                      }
                    }
                  }
                }
              } catch (e) {}
              Get.back();
            },
            cancelAction: () => Get.back(),
            cancelActionText: appLocalizations.no,
          );

          break;
        case UpdateMedia.replace:
          multiImagePicker(context, 1).then((value) {
            if (value != null) {
              _request.uploadImage(
                  files: [value[0]],
                  parameters: ["images$index"],
                  type: "$index");
            }
          });
          break;
      }
    });
  }

  Future getIcon({required String url}) async {
    if (returnColorIcon.value == url) {
      File file = await DefaultCacheManager().getSingleFile(url);
      MultiImageCrop.startCropping(
          context: Get.context!,
          aspectRatio: 0,
          requiredCirsleCroper: true,
          files: [file],
          callBack: (List<File> images) {
            if (images.isNotEmpty) {
              _request.uploadImage(
                  files: [File(images.first.path)],
                  parameters: ["thumbnail"],
                  type: "thumbnail");
            }
          });
    } else {
      File file = await DefaultCacheManager().getSingleFile(url);
      MultiImageCrop.startCropping(
          context: Get.context!,
          aspectRatio: 0,
          requiredCirsleCroper: true,
          files: [file],
          callBack: (List<File> images) {
            if (images.isNotEmpty) {
              _request.uploadImage(
                  files: [File(images.first.path)],
                  parameters: ["thumbnail"],
                  type: "thumbnail");
            }
          });
    }
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == 'getAllColors') {
      controller.colorFamilyList.clear();
      for (var colorObj in response["colorFamilies"]) {
        controller.colorFamilyList.add(ColorFamilies(
            colorFamilyName: colorObj["colorFamilyName"],
            colorFamilyNameAr: colorObj["ar"]["colorFamilyName"]));
      }
    } else if (type == "thumbnail") {
      UploadImageModel model = UploadImageModel.fromJson(response);
      if (model.status == statusOK) {
        thumnailUrl.value = model.fileList?[0].fileUrls?.first ?? "";
        controller.uploadedImages.add(model.fileList?[0].fileUrls?.first ?? "");
      }
      if (Get.arguments != null) {
        ColorModel colorModel = Get.arguments[0];
        for (Variants variants in controller.arrVariations) {
          if (variants.colorFamily == colorModel.colorFamily &&
              variants.colorName == colorModel.colorName) {
            for (Variations element in variants.variations ?? []) {
              element.isUpdated = true;
              element.variantAttributes?.variantColor?.colorIcon =
                  thumnailUrl.value;
              variants.colorIcon = thumnailUrl.value;
            }
          }
        }
      }
    } else {
      UploadImageModel model = UploadImageModel.fromJson(response);
      if (model.status == statusOK) {
        model.fileList?.forEach((element) {
          controller.uploadedImages.addAll(element.fileUrls ?? []);
        });

        for (FileList element in model.fileList ?? []) {
          if (thumnailUrl.value.isEmpty) {
            thumnailUrl.value = element.fileUrls?.first ?? "";
          }

          if (type.isNotEmpty) {
            int replaceIndex = int.parse(type);
            if (replaceIndex == 0) {
              thumnailUrl.value = element.fileUrls?.first ?? "";
            }
            int indexOf =
                controller.sliderImages.indexOf(arrImages[replaceIndex]);
            if (indexOf != -1) {
              controller.sliderImages[indexOf] = element.fileUrls?.first ?? "";
            }
            arrImages[replaceIndex] = element.fileUrls?.first ?? "";
          } else {
            controller.sliderImages.add(element.fileUrls?.first ?? "");
            arrImages.add(element.fileUrls?.first ?? "");
          }

          if (Get.arguments != null) {
            ColorModel colorModel = Get.arguments[0];
            for (Variants variants in controller.arrVariations) {
              if (variants.colorFamily == colorModel.colorFamily &&
                  variants.colorName == colorModel.colorName) {
                for (Variations variantions in variants.variations ?? []) {
                  variantions.isUpdated = true;
                  if (type.isNotEmpty) {
                    int index = int.parse(type);
                    variantions.variantImages?[index] =
                        (model.fileList ?? []).first.fileUrls?.first ?? "";
                    if (index == 0) {
                      variantions.variantAttributes?.variantColor?.colorIcon =
                          (model.fileList ?? []).first.fileUrls?.first ?? "";
                      variants.colorIcon =
                          (model.fileList ?? []).first.fileUrls?.first ?? "";
                      variantions.variantImages = arrImages;
                    }
                  } else {
                    variantions.variantImages = arrImages;
                    variantions.variantAttributes?.variantColor?.colorIcon =
                        arrImages.first;
                    variants.colorIcon = arrImages.first;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

class ColorFamilies {
  final String? colorFamilyName;
  final String? colorFamilyNameAr;

  ColorFamilies(
      {required this.colorFamilyName, required this.colorFamilyNameAr});
}
