#!/bin/bash
set -e

# Update system packages quietly
sudo apt-get update -qq

# Install required dependencies for Flutter quietly
sudo apt-get install -y -qq curl git unzip xz-utils zip libglu1-mesa

# Download and install Flutter SDK
cd $HOME
if [ ! -d "flutter" ]; then
    echo "Downloading Flutter SDK..."
    wget -q -O flutter.tar.xz https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.24.3-stable.tar.xz
    tar xf flutter.tar.xz > /dev/null 2>&1
    rm flutter.tar.xz
fi

# Add Flutter to PATH in .profile
echo 'export PATH="$HOME/flutter/bin:$PATH"' >> $HOME/.profile
export PATH="$HOME/flutter/bin:$PATH"

# Configure Flutter
flutter config --no-analytics > /dev/null 2>&1

# Navigate to project directory
cd /mnt/persist/workspace

# Fix the intl dependency conflict by updating pubspec.yaml
sed -i 's/intl: \^0\.18\.1/intl: ^0.19.0/' pubspec.yaml

# Get Flutter dependencies
echo "Getting Flutter dependencies..."
flutter pub get

# Create a corrected unit test file
cat > test/widget_test.dart << 'EOF'
// Basic unit tests for the Elbaab Suppliers app.

import 'package:flutter_test/flutter_test.dart';

// Simple utility functions to test
class AppUtils {
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  static String formatPrice(double price) {
    return '\$${price.toStringAsFixed(2)}';
  }
  
  static bool isValidPhoneNumber(String phone) {
    // More restrictive phone validation - must be at least 7 digits
    return RegExp(r'^\+?[1-9]\d{6,14}$').hasMatch(phone);
  }
  
  static String capitalizeFirstLetter(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }
}

void main() {
  group('App Utility Tests', () {
    test('Email validation works correctly', () {
      expect(AppUtils.isValidEmail('<EMAIL>'), isTrue);
      expect(AppUtils.isValidEmail('<EMAIL>'), isTrue);
      expect(AppUtils.isValidEmail('invalid-email'), isFalse);
      expect(AppUtils.isValidEmail(''), isFalse);
      expect(AppUtils.isValidEmail('@domain.com'), isFalse);
    });

    test('Price formatting works correctly', () {
      expect(AppUtils.formatPrice(10.0), equals('\$10.00'));
      expect(AppUtils.formatPrice(99.99), equals('\$99.99'));
      expect(AppUtils.formatPrice(0.5), equals('\$0.50'));
      expect(AppUtils.formatPrice(1000.0), equals('\$1000.00'));
    });

    test('Phone number validation works correctly', () {
      expect(AppUtils.isValidPhoneNumber('+1234567890'), isTrue);
      expect(AppUtils.isValidPhoneNumber('1234567890'), isTrue);
      expect(AppUtils.isValidPhoneNumber('+44123456789'), isTrue);
      expect(AppUtils.isValidPhoneNumber('123'), isFalse);
      expect(AppUtils.isValidPhoneNumber(''), isFalse);
      expect(AppUtils.isValidPhoneNumber('abc123'), isFalse);
      expect(AppUtils.isValidPhoneNumber('12345'), isFalse); // Too short
    });

    test('Text capitalization works correctly', () {
      expect(AppUtils.capitalizeFirstLetter('hello'), equals('Hello'));
      expect(AppUtils.capitalizeFirstLetter('WORLD'), equals('World'));
      expect(AppUtils.capitalizeFirstLetter('tEST'), equals('Test'));
      expect(AppUtils.capitalizeFirstLetter(''), equals(''));
      expect(AppUtils.capitalizeFirstLetter('a'), equals('A'));
    });
  });

  group('Basic Dart Tests', () {
    test('List operations work correctly', () {
      final list = <String>['apple', 'banana', 'cherry'];
      expect(list.length, equals(3));
      expect(list.contains('banana'), isTrue);
      expect(list.contains('orange'), isFalse);
      
      list.add('orange');
      expect(list.length, equals(4));
      expect(list.last, equals('orange'));
    });

    test('Map operations work correctly', () {
      final map = <String, int>{'a': 1, 'b': 2, 'c': 3};
      expect(map.length, equals(3));
      expect(map['b'], equals(2));
      expect(map.containsKey('a'), isTrue);
      expect(map.containsKey('d'), isFalse);
      
      map['d'] = 4;
      expect(map.length, equals(4));
      expect(map['d'], equals(4));
    });

    test('String operations work correctly', () {
      const text = 'Hello World';
      expect(text.toLowerCase(), equals('hello world'));
      expect(text.toUpperCase(), equals('HELLO WORLD'));
      expect(text.contains('World'), isTrue);
      expect(text.startsWith('Hello'), isTrue);
      expect(text.endsWith('World'), isTrue);
      expect(text.split(' '), equals(['Hello', 'World']));
    });

    test('Number operations work correctly', () {
      expect(10 + 5, equals(15));
      expect(10 - 5, equals(5));
      expect(10 * 5, equals(50));
      expect(10 / 5, equals(2.0));
      expect(10 % 3, equals(1));
    });

    test('Boolean operations work correctly', () {
      expect(true && true, isTrue);
      expect(true && false, isFalse);
      expect(true || false, isTrue);
      expect(false || false, isFalse);
      expect(!true, isFalse);
      expect(!false, isTrue);
    });
  });
}
EOF

echo "Setup completed successfully!"