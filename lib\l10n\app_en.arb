{"changeLanguage": "Change Language", "customerService": "Customer Service", "deleteProduct": "Deleted Products", "privacyPolicy": "Privacy And Policy", "accountAndMangment": "Accounts And Mangments", "appName": "Elbaab", "your_country": "Your Country", "your_language": "Your Language", "login": "<PERSON><PERSON>", "youDontHaveAccess": "You dont have account?", "authenticateToSignIn": "Please authenticate to sign in", "verifyMobileNumber": "Verify mobile number", "signup": "Signup", "useBiometricAcess": "Use Biometric Access", "back": "Back", "restrictOption": "Adding more option for accepted and matched product is not allowed", "reset": "Reset", "rememberMe": "Remember Me", "clearCache": "<PERSON>ache", "pushNotification": "Manage Notificatios", "reciveOrderEmails": "Recive Order Email", "biometricAccess": "Biometric Access", "verifyMobileNumberContent": "To verify your mobile number, we've sent a One Time Password (OTP) to your phone", "addPhotos": "Add Photos", "addOption": "Add Option", "addedColor": "ADDED Colors", "brand": "Brand", "completeSetup": "Complete Setup", "updateInfo": "Update Info", "addSize": "<PERSON><PERSON>", "uploadColorPhoto": "Upload Color Photo", "category": "Category", "custom": "Custom", "viewColors": "View Colors", "productname": "Product Name", "addColor": "Add Color", "categories": "Categories", "variation": "Variations", "subCategory": "Sub Category", "acceptReturn": "Accept return", "accept": "Accept", "type": "Type", "customOption": "Custom Option", "addcustomOption": "Add Custom Option", "reject": "Reject", "addStory": "Add Story", "addPrice": "Add Price", "add": "Add", "addYourBrandName": "Add Your Brand Name", "maxBoxDimension": "Maximum Box Dimensions", "genrateVariations": "Genrate Variations", "notificationOnMinQtyIsReached": "Notification on <PERSON>. Qty is reached", "availableQuantity": "Available Quantity", "aed": "AED", "size": "Sizes", "returnCondition": "Return Condition", "productPolicy": "Product Policy", "viewListofBrand": "View List of brands", "chooseBrand": "<PERSON><PERSON>", "recentlySelected": "Recently Selected", "selectCategory": "Select Category", "updateRefundStatus": "Update refund status", "ok": "OK", "polices": "Polices", "width": "<PERSON><PERSON><PERSON>", "length": "Length", "weight": "Weight", "height": "Height", "requiredRefregiration": "Required refregiration", "specification": "Specification", "itemPerOrder": "Items per Order", "newSpecification": "New Specification", "oldSpecification": "Old Specification", "driverName": "Driver Name", "deliveryCompany": "Delivery Company", "officeAddress": "Office Address", "confirmAll": "CONFIRM ALL", "send": "Send", "returnDuration": "Return Duration", "warrantyDuration": "Warranty Duration", "keyword": "Keywords", "buyerDetails": "Buyer Details", "deliveryDetails": "Delivery Details", "ordered": "Ordered", "delivered": "Delivered", "confirmed": "Confirmed", "confirm": "Confirm", "rejected": "Rejected", "cancelled": "Cancelled", "shipmentInformation": "Shipment Information", "shipmentFits": "Package Type", "returned": "Returned", "shipment": "Shipment", "paymentMethod": "Payment Method", "returnedConfirmed": "Returned\n Confirmed", "cancelledByCustomer": "Cancelled\n by customer", "shipped": "Shipped", "orderDetail": "Order Detail", "transferRequest": "Transfer Request", "youAreGoingToSend": "You are going to send", "vat": "VAT", "myShop": "My shop", "orderNumber": "Order Number", "totalAmount": "Total Amount", "done": "Done", "transactionID": "Transaction ID", "withdrawalID": "WithDrawal ID", "orderID": "Order ID", "amount": "Amount", "more": "more", "sales": "Sales", "status": "Status", "transferredAmount": "Transferred Am<PERSON>nt", "topSellingItems": "Top Selling Items", "initiateTransferRequest": "Initiate Transfer Request", "totalVat": "Total VAT", "totalRevenue": " Total Revenue", "productforFreeDelivery": "Product for free delivery", "update": "Update", "verify": "Verify", "submit": "Submit", "next": "Next", "pendingAmount": "Pending Amount", "availableAmount": "Available Amount", "price": "Price", "miniQTY": "Minimum Quantity", "deliveryChargesFees": "Delivery Charges Fees", "reply": "Reply", "edit": "Edit", "remove": "Remove", "delete": "Delete", "details": "Details", "resetPasswordContent": "Enter your email to reset password", "tryAnotherWay": "Or you can try another way", "welcome": "Welcome,", "addMobileNumber": "Add Mobile Number", "enterAMobileNumberToSafeguardYourAccount": "Enter a mobile number to safeguard your account.", "resetPassword": "Reset Password", "welcomeAgain": "Welcome again ,", "welcomeBack": "Welcome back!", "cancel": "Cancel", "bankAccount": "Bank Account", "activity": "Activity", "creditCard": "Credit Card", "setting": "SETTING", "yes": "Yes", "no": "No", "removeTermsConditionMessage": "Are you Sure You Want To Remove Your Terms And Condition", "myInformation": "MY Information", "save": "Save", "order": "Orders", "howCanWeHelpYou": "How can we help you?", "callUs": "Call US", "supportEmail": "<EMAIL>", "whatsapp": "WHATSAPP", "support": "Support", "minimumQtyAlert": "Minimum Qty Alert", "minimumQty": "Minimum QTY", "freeDeliveryTarget": "Free Delivery Target", "alert": "<PERSON><PERSON>", "freeDeliveryItems": "Free Delivery Items", "sold": "Sold", "services": "Services", "review": "Review", "incomeAndExpenses": "Income & Expenses", "addProduct": "Add Product", "myTotalIncome": "MY TOTAL INCOME", "matchThisProduct": "Match this product", "createNewProduct": "Create New Product", "similarProduct": "Match Product", "myAccounts": "My Accounts", "sellerDashboard": "Seller Dashboard", "profile": "Profile", "product": "Product", "updateNow": "Update Now", "publishNow": "Publish Now", "saveForLater": "Save As Draft", "shop": "Shop", "addNewBranch": "Add new branch", "addNewColor": "Add New Colour", "openGoogleMap": "OPEN GOOGLE MAP", "description": "Description", "shopDescription": "Describe your shop.", "contactNumbers": "Contact Numbers", "contactEmails": "Contact Emails", "addAnotherBranch": "Add another Branch", "forgetPaswoord": "Forgot password?", "termsAndCondition": "Terms And Conditions", "yourName": "Your Name", "newName": "New Name", "dashboard": "Dashboard", "deleteThisColor": "Delete This Color", "deleteThisProduct": "Delete This Product", "valid": "VALID", "addAnotherContactNumber": "Add Another Contact Number", "addAnotherContactEmail": "Add Another Contact Email", "addLocationMap": "Add Location Map", "addLocationUsingMap": "Add Location Using Map", "targetPrice": "Target Price", "passwordHint": "••••••••", "emailfeildLabel": "Email", "newEmail": "New Email", "newPhoneNumber": "New Phone Number", "addInfoAboutYourShop": "Add Info About Your Shop", "createShop": "Create Shop", "createShopContent": "Now you can create your own shop and add your products", "continu": "Continue", "city": "City", "notification": "Notification", "reviews": "Reviews", "qty": "Qty", "color": "Colors", "pickUp": "Pick-up", "estimatedDelivery": "Estimated delivery", "phone": "Phone", "productId": "Product ID", "productEin": " Product EIN", "variantEin": "<PERSON><PERSON>t <PERSON>", "securityAndMangments": "Accounts And Mangments", "appSettings": "App Settings", "deactivateTheAccount": "Deactivate The Account", "changePassword": "Change Password", "changeYourEmail": "Change Your Email", "changeYourUserName": "Change Your User Name", "changeYourMobileNumber": "Change Your Mobile Number", "editAccount": "Edit Account", "whyThisInformationRequired": "Why this information is required?", "addNewProduct": "Add New Product", "logout": "Logout", "myOrders": "My Orders", "myProfile": "My profile", "faq": "Faq", "faqs": "FAQS", "date": "Date", "updatedDate": "Updated On", "uploadShopBanner": "Upload shop banner", "logo": "Logo", "iban": "IBAN", "message": "Message", "returnedFeilds": "Returned Feilds", "bankName": "Bank Name", "accountHolderName": "Account Holder Name", "bankAccountInfo": "Bank Account Information", "information": "Information", "adminNotes": "Admin Notes", "trashProducts": "Deleted Products", "privacyAndPolicy": "Privacy And Policy", "logoutMessage": "Are you sure you want to logout", "returnRequest": "Requests", "productReviews": "Product Reviews", "aboutTheShop": "About The Shop", "manageBranches": "Manage Branches", "updateBussinessInfo": "Update Business Info", "addBusinessInformation": "Add Business Information", "updateBusinessInformation": "Update Business Information", "loginDescription": "we are happy to see you again", "signupDescription": "We are thrilled to have you on board again", "passwordFeildLabel": "Password", "reEnterPassword": "Re-enter password", "resendOtp": "Resend OTP", "accountNumber": "Account Number", "returnedDate": "Returned Date", "notReceived": "Not Received? ", "secondsRemainToResendOtp": "Seconds Remaing To Resend Otp", "createYourAccount": "Create Your Account", "alreadyHaveAnAccount": "Already have an account? ", "emailHint": "ex : <EMAIL>", "createAnAccount": "Create An Account", "nameHint": "ex : <PERSON>", "morningMsg": "Morning! Ready to boost your sales?", "noonMsg": "Noon! Keep the orders rolling in!", "afterNoonMsg": "Afternoon! Push for more sales!", "eveningMsg": "Evening! Review today's progress.", "greetTimeError": "Time issue! Check your clock!", "searchProductFeildHint": "Headset Earpods white wireless", "skipAddNewProduct": "Skip ( Add New Product )", "matchExsistingProduct": "Match an existing product on Elbaab.", "or": "OR", "searchType": "\n\n Check With :\n\n• Product Name\n\n• EIN code (Elbaab Identification Number)\n\n• Product GTIN (Global Trade Item Number)\n\n• Product UPC (Universal Product Code) \n\n• Product SKU (Stock Keeping Unit)\n", "verificationCode": "Verification Code", "settingOtpDailogMessage": "We have sent the code verification to", "verifyOtp": "Verify Otp", "updateBankAccount": "Update bank account", "addNewBankAccount": "Add new bank account", "waitWhileLoadShop": "Wait until the shop is loaded", "pickupAddress": "Pickup Addresses", "pickupAddressUsageContant": "Pick up address will be used for shipping. It is not visible to customers.", "options": "Options", "priceAndVariations": "Price & variations", "productNameFeildLableEnglish": "Product Name (English)", "productNameFeildLableArbic": "Product Name (Arabic)", "productNameFeildHint": "ex : T-shirt", "productNameFeildReturnMessage": "<PERSON><PERSON> rejected this name", "email": "Email is required.", "invalidEmail": "<PERSON><PERSON><PERSON>.", "invalidText": "Invalid Text You Insert.", "phoneNumber": "Phone number is required.", "invalidPhoneNumber": "Invalid phone number.", "password": "Password field is required.", "urlNotAccepted": "Link and URL are not accepted", "tradeCertificateRequired": "Trade Certificate is Required", "emiratesIDRequired": "Emirates ID Or Passport Copy Is Required", "invalidPassword": "Password must contain minimum six characters", "confirmPassword": "Confirm Password field is required.", "fieldRequired": "Field is Required", "errEmpty": "Field is empty", "gtinFeildLabel": "GTIN OR UPC", "gtinFeildReturnMessage": "Admin rejected this Manufacturer ID", "gtinFeildHint": "ex : Manufacturer ID", "errReachImageLimit": "You reach image limit For Single Color", "errSelectImageForColor": "You will asign maximum 5 photos for each Color", "passwordDoesNotMatch": "Password Does Not Match", "productDescriptionFeildLableEnglish": "Product Description (English)", "productDescriptionFeildLableArbic": "Product Description (Arabic)", "productDescriptionFeildHint": "ex : high neck t-shirt with short seleeves", "productDescriptionFeildReturnMessage": "<PERSON><PERSON> rejected this description", "freeDeliveryTargetMessage": "If customer reaches this target amount or above, you will deliver the product for free", "locationRequired": "Your location is required so shipment company can pick up your item", "whyBankInfo": "To start receiving online payments", "pendingAmountAlertMessage": "Pending amount will be available after 30 days from Customer receiving Date", "messageSent": "Your message has been sent successfully", "addKeywordMessage": "To improve your chance of selling add keywords and use comma between them", "availableQtyMessage": "This information will shown only for you so, you can monitor your stock", "minimumQtyAlertMessage": "You will receive a notification when the number of item reaches to minimum quantity and will be displayed in the user app as (Remaining)", "requiredBanner": "Shop Banner Image Is Required", "requiredLogo": "Shop Logo Image Is Required", "requiredTargetPrice": "You Enable Free Delivery Target But You Didn't Enter Price", "requiredTermsAndConditions": "Upload Your Shop Terms And Conditions", "requiredCity": "Upload Your Shop Terms And Conditions", "requiredMapLocation": "Select Pin Location Using Map", "requiredNumberForBranch": "At least One Number Is Required For Branch", "requiredEmailForBranch": "At least One Email Is Required For Branch", "requiredOtpCode": "<PERSON><PERSON> Valid <PERSON>", "pleaseAcceptTermsAndConditions": "Please Accept Terms and Conditions", "limitReached": "You have reached the limit", "infoForDeliveryCompany": "This Info Is Important For Delivery Company, you can enter the approximate values", "isFreeDeliveryProduct": "Is Product For Free Delivery", "isFreeDeliveryProductMessage": "If you enable, you will pay all delivery charges", "freeReturnTitle": "Free return", "freeReturnMessage": "You will pay the return fees", "notFreeReturnTitle": "Not free return", "notFreeReturnMessage": "Customer will pay the return fees.", "adminReturnConditionNote": "Admin Reject Your Return Condition", "adminReturnDurationNote": "Admin rejected this return duration", "returnDurationFeildHint": "ex : 5 Days", "returnDurationFeildLabell": "Return duration", "returnDurationSecondFeildLabell": "Enter Return duration", "adminWarrantyDurationNote": "Admin rejected this warranty duration", "warrantyDurationFeildHint": "ex : 5 Days", "warrantyDurationFeildLabel": "Warranty duration", "warrantyDurationSecondFeildLabel": "Enter Warranty duration", "adminPolicyReturnNote": "Admin rejected this product policy", "updateReturnData": "Update Return Data", "policyFeildLabelEnglish": "Policy (English)", "policyFeildLabelArabic": "Policy (Arabic)", "policyFeildHint": "ex : Cotton Grown Using Natural Fertilisers And Pesticides. Moreover,", "selectSubCategroy": "Select Sub Category", "adminRejectThisCategory": "<PERSON><PERSON> rejected this category", "adminRejectThisBrand": "Admin rejected this brand", "pleaseSelectBrandOrType": "Please select brand or type your own brand name", "adminRejectThisSubCategory": "Admin rejected this sub category", "pleaseSelectCategorySubCategory": "Please select category and sub category", "enterYourBrandName": "Enter your brand's name here", "enterYourBrandNameFeildError": "Enter your brand name if not found in the list", "searchBrandByName": "Search brand by name", "colorIcon": "Color Icon", "colorFamilyFeildHint": "ex : red", "colorNameFeildLabel": "Add Color Name", "colorNameFeildHint": "Color name ( light red , dark blue … )", "colorFamilyFeildReturnMessage": "<PERSON><PERSON> returned this color family", "selectColorFamily": "Select Color Family", "deleteImageAlertMesssgae": "Are you sure you want to delete this image", "adminRejectColorName": "Admin rejected this color name", "alertMessageForRemoveColor": "Are you sure you want to remove this color", "alertMessageBeforeDiscardProductChanges": "Are you sure you want to close? All changes will be discard.", "discardChangesInProgress": "Discard changes is in-progress", "saveInfo": "Save Info?", "deleteProductFromCache": "Deleting product data from cache", "albums": "Albums", "invalidImageFormate": "Invalid Image Format", "confirmSlection": "Confirm Selection", "imageCropper": "Image Cropper", "pinchZoomInout": "Pinch to zoom in/out", "maxNumberItemsPerOrder": "Maximum number of items per order?", "itemPerOrderQuantityLessAvailable": "It should be less than available quantity", "zeroNotAcceptable": "Zero is not acceptable", "itemPerOrderFeildLabel": "Items", "itemPerOrderFeildHint": "ex : 2", "specFeildLabelEnglish": "Title (English)", "specFeildLabelArabic": "Title (Arabic)", "cityFeildHint": "ex : Dubai", "updateDraftProduct": "Update Draft Product", "alertMessageBeforeDeleteProduct": "Are you sure you want delete this product?", "resetProductWillDiscardChanges": "Reset the product will discard all changes which you have applied", "kindlyAddBankAccount": "kindly add your bank information to get your financial dues", "itemPerOrderQuantityLessVariantAvailable": "It should be less than available variant quantity", "adminRejectShipmentFitErrorMessage": "Oops! you havn't update shipment fits\nAdmin note: This product not fits on this size of shipment which you selected", "youRemoveProductName": "You removed product name from information", "productNameNotProvided": "Product Name Not Provided", "youRemoveProductDescription": "You removed product description from information", "productDescriptionNotProvided": "Product Description Not Provided", "youRemoveProductGtin": "You removed product Gtin from information", "productGtinNotProvided": "Product Gtin Not Provided", "youRemoveProductCategory": "You removed product category from information", "productCategoryNotProvided": "Product Category Not Provided", "youRemoveProductBrand": "You removed product brand from information", "productBrandNotProvided": "Product Brand Not Provided", "backToShop": "Back to Shop", "editShop": "Edit Shop", "badgeProductHidden": "Product is hidden", "begdeModifyRequest": "Modified\nRequest", "alertShopDiscardChanges": "Are you sure you want to discard shop info changes?", "productDetails": "Product Detail", "hideThisItem": "Hide This Item", "alertMessageForhideItem": "The product will not be visible to the customer", "restoreLastSubmission": "Are you sure you want to restore your last submission?", "restoreLastChanges": "Restore Last Changes", "restoreLastChangesAlertMessage": "The product will back to last accepted version and all changes will be discarded", "restoreProduct": "Restore Product", "editProduct": "Edit Product", "change": "Change >", "deleteItemSaveAlert": "Deleted items are saved for 30 days in Profile", "editPendingProductNotAllowed": "Edit on pending product is not allowed", "informationTitleEnglish": "Information ( English )", "informationTitleArabic": "Information ( Arabic )", "shipmentTitleEnglish": "Shipment ( English )", "shipmentTitleArabic": "Shipment ( Arabic )", "policesTitleEnglish": "<PERSON><PERSON> ( English )", "policesTitleArabic": "Polices ( Arabic )", "detailsTitleEnglish": "Details ( English )", "detailsTitleArabic": "Details ( Arabic )", "pending": "Pending", "returnedCost": "Returned Cost", "sellerPayDeliveryCost": "The Seller pay the  Delivery cost.", "customerPayDeliveryCost": " The Customer pay the Delivery cost.", "notFreeForCustomer": "Not free for customer", "productReturned": "Product Returned", "requiredUpdateOnSpec": "Oops! seems like you have not updated the product return specifications yet and check other return details", "aleartCompeleteEditSpec": "You have not completed the edit. Please finish the edit first.", "returnSpecHighlighted": "All highlighted specificatoin returned by admin", "specTitleEnglish": "Title ( English )", "specTitleArabic": "Title ( Arabic )", "specTitleFeildHint": "Screen Size", "specValueEnglish": "Value ( English )", "specValueArabic": "Value ( Arabic )", "specValueFeildHint": "4.7", "unit": "Unit", "adminnRetuenSizeUnit": "Admin returned this size unit", "alertMessageForDelete": "Are you sure you want to delete", "allHideAleartMessage": "You can't hide all values", "hideSizeAlertMessgae": "Hide size will not show this size to the customers", "deleteSizeAlertMessage": "Are you sure you want to delete this size", "updateReturnSizeAlert": "Oops! you havn't update Size Unit", "waitVariationLoading": "Wait product variation has been loading", "hide": "<PERSON>de", "show": "Show", "gtin": "GTIN", "productOptions": "Product Options", "pleaseUploadImage": "Please upload images", "aleartMessageOnHideVariant": "Hide variant will not show this variation to the customers", "colorName": "Color Name", "hideAllVariantAlertMessage": "To hide all product variants, you cannot simply hide the product. Instead, you must either set the quantity of all variants to 0 or manage their visibility individually.", "youCanNotHideAllColor": "You can't hide all colors", "hideColorNotVisible": "Hide color will not show this color to the customers", "haventUpdateColor": "Oops! you havn't update colors", "colorFamily": "color family", "registration": "Registration", "returnedByAdmin": "Returned by the admin", "submittedByAdmin": "Submitted by the supplier", "acceptedByAdmin": "Accepted by the admin", "whatsappNotInstalled": "WhatsApp is not installed on the device", "contactUs": "CONTACT US", "fixProblemOrContactUs": "You can quickly fix your problem here or you can ", "updateName": "Update Name", "userNameInUse": "Username in Use: Choose another. This one's taken", "pleaseEnterDigitalCode": "Please enter the digital code that we have sent", "emailClaimed": "Oops! <PERSON><PERSON>'s claimed. Pick a new one!", "pleaseEnterCompeleteCode": "Please enter complete code", "mobileNumberChanged": "Mobile number has been changed successfully", "passwordCharaterLimit": "The password must be at least 6 characters", "passwordFeildHint": "ex: 123456", "newPassword": "New Password", "confirmPass": "Confirm Password", "currentPassword": "Current Password", "updateBussinessInfoAlert": "Updating business info will hide your shop untill the admin accept the new changes, Are you sure you want to continue?", "bussinessNameFeildHint": "ex : Around<PERSON>", "bussinessNameFeildLabel": "Business Name", "bussinessNameFeildReturnMessage": "Business Name Returned From Admin", "bussinessOwnerNameFeildLabel": "Owner/Manager Name", "bussinessOwnerNameFeildHint": "ex : <PERSON>", "bussinessOwnerNameFeildHintReturnMessae": "Owner/ Manager Name Returned From Admin", "pleaseUploadColorImage": "Please upload color images", "addNewColorName": "Add new color name for this color family", "pleaseSelectColorFamily": "Please select color family", "colorNameAlreadyExist": "Color name already exist for this color family", "alertMessageDeleteOption": "Are you sure you want to delete this option", "customOptionFeildHint": "ex: memory size", "customOptionFeildLabelEnglish": "Title ( English )", "customOptionFeildLabelArabic": "Title ( Arabic )", "customOptionFeildErrorValidTitle": "Please Enter A Valid Title", "customOptionFeildTitleAlreadyExsist": "Option Title Already In List", "customOptionAdminReturnTitle": "<PERSON><PERSON> Has Returned This Title", "addNewOption": "Add new Option", "youHaveAlreadyAddedThisOption": "You have already added this option title to your product option.", "errorAtleastAddtwoValues": "Please Enter At least Two Values ", "errorAtleastAddtwoValue": "Please Enter At least Two Value", "somethingWrongOnOption": "Something wrong on this option", "updateThisOptionFirst": "Update this first then edit other's", "hideOptionAlertMessgae": "Hide option will not show this option to the customers", "valueAlreadyInList": "Value Already In List", "customOptionValueFeildLabelEnglish": "Value ( English )", "customOptionValueFeildLabelArabic": "Value ( Arabic )", "noSalesFound": "No Sales infos was found", "shipmentCode": "Shipment Code", "transfer": "Transfer", "amountLessThenAvailable": "Amount should be less or equal than the available amount", "pleaseEnterAmount": "Please enter amount", "available": "Available", "deliveryFee": "Delivery Fee", "address": "Address", "location": "Location", "mapLocation": "Map Location", "number": "Number", "landNumber": "Land Number", "customerContact": "Customer Contact", "contactDetailsInfo": "Contact details will be shared with customers after placing orders ", "alertMessageDeleteOptionValue": "Are you sure you want to delete this option value", "emailAddress": "Email Address", "orderedIn": "Ordered in", "invoiceSummery": "Invoice Summery", "shipmentInfo": "Shipment Info", "variantFieldsUpdated": "<PERSON><PERSON><PERSON>", "alertVariantPriceQty": "Default price and QTY have been applied to the empty variations fields.", "alertOnPriceReturn": "Admin rejected this price", "alertOnQtyReturn": "Admin rejected this product quantity", "applyToAllVariant": "Apply To All Variations", "alertMessageUpdateCCustomOption": "Oops! you havn't update custom options", "returnCost": "Return Cost", "youRemovedProductAcceptReturnPolicies": "You removed product accept return from Policies", "productAcceptReturnNotProvided": "Product Accept Return Not Provided", "youRemovedProductFreeDeliveryPolicies": "You removed product for free delivery from Policies", "productForFreeDeliveryNotProvided": "Product For Free Delivery Not Provided", "errorCacheValueReturnDuration": "You removed product return duration from policies", "errorServerValueReturnDuration": "Product Return Duration Not Provided", "errorCacheValueWarrantyDuration": "You removed product warranty duration from policies", "errorServerValueWarrantyDuration": "Product Warranty Duration Not Provided", "errorCacheValuePolicies": "You removed product policy from policies", "errorServerValuePolicies": "Product Policy Not Provided", "productPrice": "Product Price", "errorCacheValuePrice": "You removed product price from detail", "errorServerValuePrice": "Product Price Not Provided", "errorCacheValueQty": "You removed product qty from detail", "errorServerValueQty": "Product QTY Not Provided", "errorCacheValueMinQty1": "You turned off minimum qty alert", "errorCacheValueMinQty": "You removed product minimum qty from detail", "errorServerValueMinQty": "Product Minimum QTY Not Provided", "selectLanguage": "Select Language", "selectLanguageDetail": "Want to change the app language? You can switch between English and Arabic for a better experience.", "agreeTermsAndCondition": "By creating an account, you agree to <PERSON><PERSON><PERSON> ", "phoneNumberAlreadyInuse": "Phone number already in use", "signupSuccessMessage2": "Thank you for choosing our app", "signupSuccessMessage1": "Your request is now under review. please check your email for the status ", "verifyEmailAddress": "Verify Em<PERSON> Address", "resetYourPassword": "reset your password", "verifyYourEmail": "verify your email", "to": "To", "weSentOtp": "we have sent a One Time Password (OTP) to your email", "mobileNumber": "Mobile number", "aleartFillSpecFeild": "Please wait while we translate", "aleartOnRemoveSpec": "Are you sure you want to remove this specification", "readySpecTranslation": "Translation is ready to add", "colorNameEnglish": "Color Name ( English )", "colorNameArabic": "Color Name ( Arabic )", "aleartRememberBe": "Enter your credentials then login to remember your details for next time", "updateTradeCertiificateDate": "Update Trade Certificate Date", "tradeCerficateExpireDate": "Trade Certificate Expiry Date", "uploadTradeCertificate": "Upload Trade Certificate", "tradeLicenseReturned": "Trade License Returned", "uploadEmiratesID": "Upload Emirates ID", "ownerIDReturned": "Owner ID Returned", "adminNote": "Admin Note:", "updateEmiratesIDDate": "Update Emirates ID Date", "emiratesIDExpiryDate": "Emirates ID Expiry Date", "aleartUploadTradeCertificate": "Please update your Trade License", "aleartUploadId": "Please update your Emirates ID", "updateShop": "Update Shop", "aleartOnStopShopEdit": "Are you sure you want to stop editing", "rejectTargetPrice": "Admin rejected this target price", "uploadTermsAndCondiition": "Upload Terms & Conditions Doc", "businessSloganFeildLabel": "Business Slogan", "businessSloganFeildHint": "ex :Find every thing here", "businessSloganFeildAdminReject": "<PERSON><PERSON> rejected this slogan", "businessDiscriiptionFeildAdminReject": "<PERSON><PERSON> rejected this description", "addBranchNumber": "Add Branch Number", "verificationCodeSent": "We have sent the code verfication to", "branchPhoneNumberOtp": "Add your phone number. we will send you a verification code ", "numberAlreadyUse": "Number already in use on other branch", "changePhoneNumber": "Change Phone Number?", "waitFechingCities": "Wait we are fetching cities", "pickupCity": "Pickup City", "adminReturned": "<PERSON><PERSON> returned", "pickupLocation": "Pickup Location", "pleaseAddLocation": "Please add location of pickup address", "landNumberFeildlabel": "Land number", "landNumberFeildHint": "ex : 05xxxxxxxx", "landNumberFeildAdminReject": "<PERSON><PERSON> rejected this land number", "contactFeildlabel": "Phone number", "contactFeildHint": "ex : 05xxxxxxxx", "pickupContactNumber": "Pickup Contact number", "verified": "Verified", "pickupFeildlabel": "Address", "pickupFeildHint": "ex : Shop A-23, Tower 03 Khalid Street", "pickupFeildAdminReject": "Pickup Address", "setYourLocation": "Set Your Location", "selectPinLocation": "Select Pin Location", "addAnotherAddress": "Add another pickup address", "branchAlreadyAssign": "Branch already assign to some orders", "aleartOnDeleteAddress": "Are you sure you want to delete this pickup adress", "adminRejectPhoneNumber": "<PERSON><PERSON> rejected this phone number", "adminRejectWhatsapppNumber": "<PERSON><PERSON> rejected this whatsapp number", "whatsappNumber": "Whatsapp Number", "adminRejectContactEmail": "Admin rejected this email addrress", "discardAllChanges": "Discard All Changes", "days": "Days", "selectCategoryFirst": "Please select category first", "errorAddTitleForCustomOption": "Please Enter Title Or Remove Empty Custom Option", "errorEmptyDetsilsOnAddOption": "Please Enter Title With At least Two Values Before Done", "accountSettings": "Account <PERSON><PERSON>", "notifications": "Notifications", "dataManagement": "Data Management", "photos": "Photos", "pickupLocations": "Pickup Locations", "pleaseEnterBranchNumber": "Please enter branch number", "personalInformation": "Personal Information", "security": "Security", "noIncomeYet": "No Income Yet", "salesTrend": "Sales Trend", "@@locale": "en", "helloSupplier": "Hello {storeName}!", "addedImages": "( {imagesLength} / 5 ) Photos", "dynamicPrice": "AED {price}", "dynamicPieceCount": "Piece {count}", "productTab": "Products ({count})", "pendingTab": "Pending ({count})", "draftTab": "Draft ({count})", "qtyAlertTab": "<PERSON><PERSON> ({count})", "outOfStocTab": "Out of Stock ({count})", "variantsTab": "Variants ({count})", "followersCount": "Followers {count}", "productReturnMessage": "Admin Notes: {returnMessage}\nCheck the returned feilds", "variantSize": "Size ( {variantSizeUnit} ) : ", "adminReturn": "Admin Return ( {returnValue} ) : ", "pickUpCity": "Emirate {pickUpCity} ", "emailChangeSuccessfully": "Your Email has been Updated successfully to: {email}", "pendingOrderTab": "Pending {count} ", "inProgressOrderTab": "InProgress {count} ", "historyOrderTab": "History {count} ", "newOrderTab": "New {count} ", "newReurnOrderTab": "Return {count} ", "waitingOrderTab": "Waiting Pickup {count} ", "shippedOrderTab": "Shipped Orders {count} ", "deliverdOrderTab": "Delivered {count} ", "cancelledOrderTab": "Cancelled {count} ", "returnedOrderTab": "Returned {count} ", "secondRemaing": "Seconds Remaing To Resend Otp {count} ", "removingCityBranch": "Removing {city} city branch"}