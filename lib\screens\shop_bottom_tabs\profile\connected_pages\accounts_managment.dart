import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/login_model.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AccountManagement extends StatefulWidget {
  const AccountManagement({super.key});

  @override
  State<AccountManagement> createState() => _AccountManagementState();
}

class _AccountManagementState extends State<AccountManagement>
    implements ServerResponse {
  late GraphQlInitilize _request;

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.securityAndMangments,
        leadingBack: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Container(
            //   width: double.infinity,
            //   padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
            //   decoration: BoxDecoration(
            //     color: Colors.blue.shade50,
            //     borderRadius: const BorderRadius.only(
            //       bottomLeft: Radius.circular(32),
            //       bottomRight: Radius.circular(32),
            //     ),
            //   ),
            //   child: Column(
            //     crossAxisAlignment: CrossAxisAlignment.start,
            //     children: [
            //       Text(
            //         appLocal.securityAndMangments,
            //         style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            //           fontWeight: FontWeight.bold,
            //           color: Colors.blue.shade900,
            //         ),
            //       ),
            //       const SizedBox(height: 8),
            //       Text(
            //         'Manage your account settings and security',
            //         style: TextStyle(
            //           color: Colors.blue.shade700,
            //           fontSize: 14,
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            const SizedBox(height: 24),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  _buildSectionTitle(appLocal.accountSettings),
                  _buildMenuItem(
                    icon: SvgStrings.bankAccountIcon,
                    title: appLocal.bankAccount,
                    color: Colors.blue,
                    onTap: () => Get.toNamed(RouteNames.viewBankInfoScreen),
                  ),
                  _buildMenuItem(
                    icon: SvgStrings.businessInfoIcon,
                    title: appLocal.updateBussinessInfo,
                    color: Colors.blue,
                    onTap: () {
                      BottomSheets.showAlertMessageBottomSheet(
                        appLocal.updateBussinessInfoAlert,
                        appLocal.alert,
                        context,
                        onActionClick: () {
                          _request.runQuery(
                            context: context,
                            query: GraphQlQuries.getBusinessInfo,
                            type: "businessInfo",
                          );
                        },
                      );
                    },
                  ),
                  // const SizedBox(height: 32),
                  _buildSectionTitle(appLocal.personalInformation),
                  _buildMenuItem(
                    icon: SvgStrings.userProfileIcon,
                    title: appLocal.changeYourUserName,
                    color: Colors.indigo,
                    onTap: () => Get.toNamed(RouteNames.updateUserNameScreen),
                  ),
                  _buildMenuItem(
                    icon: SvgStrings.emailIcon,
                    title: appLocal.changeYourEmail,
                    color: Colors.indigo,
                    onTap: () => Get.toNamed(RouteNames.changeYourEmailScreen),
                  ),
                  _buildMenuItem(
                    icon: SvgStrings.phoneIcon,
                    title: appLocal.changeYourMobileNumber,
                    color: Colors.indigo,
                    onTap: () => Get.toNamed(RouteNames.changeYourNumberScreen),
                  ),
                  // const SizedBox(height: 32),
                  _buildSectionTitle(appLocal.security),
                  _buildMenuItem(
                    icon: SvgStrings.passwordIcon,
                    title: appLocal.changePassword,
                    color: Colors.orange,
                    onTap: () => Get.toNamed(RouteNames.chanegPasswordScreen,
                        arguments: [supplierID, false, ""]),
                  ),
                  _buildMenuItem(
                    icon: SvgStrings.deactivateIcon,
                    title: appLocal.deactivateTheAccount,
                    color: Colors.red,
                    isDestructive: true,
                    onTap: () => Get.toNamed(RouteNames.deleteAndDeactiveScreen),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 4, bottom: 20, top: 10),
      child:  Align(
        alignment: Alignment.centerLeft,
        child: Text(
              title,
              style: FontStyles.fontMedium(fontSize: 16),
              // style: Theme.of(context).textTheme.titleMedium?.copyWith(
              //   fontWeight: FontWeight.w700,
              //   color: Colors.grey[800],
              // ),
            ),
      ),
    );
  }

  Widget _buildMenuItem({
    required String icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppColors.headerColorDark,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDestructive ? Colors.red.withOpacity(0.1) : color.withOpacity(0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDestructive ? Colors.red.withOpacity(0.05) : color.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 10,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: Row(
              children: [
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        isDestructive ? Colors.red.shade50 : color.withOpacity(0.1),
                        isDestructive ? Colors.red.shade100 : color.withOpacity(0.2),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: isDestructive ? Colors.red.withOpacity(0.2) : color.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isDestructive ? Colors.red.withOpacity(0.1) : color.withOpacity(0.1),
                        ),
                      ),
                      SvgPicture.string(
                        icon,
                        colorFilter: ColorFilter.mode(
                          isDestructive ? Colors.red.shade700 : color,
                          BlendMode.srcIn,
                        ),
                        width: 20,
                        height: 20,
                      ),
                      Positioned(
                        top: 6,
                        left: 6,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Colors.white.withOpacity(0.2),
                                Colors.white.withOpacity(0),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: FontStyles.fontMedium(
                          fontSize: 15,
                          // fontWeight: FontWeight.w600,
                          color: isDestructive ? Colors.red.shade700 : Colors.white70,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Tap to manage',
                        style: TextStyle(
                          fontSize: 12,
                          color: isDestructive ? Colors.red.withOpacity(0.7) : Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: isDestructive ? Colors.red.withOpacity(0.1) : color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 14,
                    color: isDestructive ? Colors.red.shade700 : color,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == "businessInfo") {
      LoginModel model = LoginModel.fromJson(response);
      if (model.status == statusOK) {
        Get.toNamed(RouteNames.addBusinessInfoScreen,
            arguments: [supplierID, model.user?.supplier, true]);
      }
    }
  }
}
