import 'package:flutter/material.dart';

enum NotificationOrderType {
  newOrders,
  waitingPickupOrders,
  shippedOrders,
  canceledOrders,
  deliveredOrders,
  returnedOrders,
  notFound
}

class OrderStatusProvider extends ChangeNotifier {
  NotificationOrderType _notificationOrderType = NotificationOrderType.notFound;

  NotificationOrderType get notificationType => _notificationOrderType;

  void orderRecive({required NotificationOrderType notificationOrderType}) {
    _notificationOrderType = notificationOrderType;
    notifyListeners();
  }
}
