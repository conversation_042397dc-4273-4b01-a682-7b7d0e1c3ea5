import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/reviews_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/product_reviews.dart';

class ReviewsSingleRow extends StatelessWidget {
  final Items item;
  const ReviewsSingleRow({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.only(top: 16),
        decoration: BoxDecoration(
          color: AppColors.headerColorDark,
          border: Border.all(
            width: 2,
            color: AppColors.headerColorDark,
          ),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          children: [
            Hero(
              tag: "productReview${item.reviews?.first.createdAt}",
              child: Container(
                margin:
                    const EdgeInsets.only(top: 27, left: kLeftSpace, right: 4),
                height: 150,
                child: Row(
                  children: [
                    SizedBox(
                      height: 132,
                      width: 90,
                      child: GlobalMethods.netWorkImage(
                          item.reviews?.first.orderItemId?.items?.first
                                      .variant !=
                                  null
                              ? item.reviews?.first.orderItemId?.items?.first
                                      .variant?.variantImages?.first ??
                                  ""
                              : item.reviews?.first.orderItemId?.items?.first
                                      .product?.productImages?.first ??
                                  "",
                          BorderRadius.circular(5),
                          BoxFit.cover),
                    ),
                    const SizedBox(width: 11),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.reviews?.first.orderItemId?.items?.first.variant !=
                                    null
                                ? item.reviews?.first.orderItemId?.items?.first
                                        .variant?.productId?.productName ??
                                    ""
                                : item.reviews?.first.orderItemId?.items?.first
                                        .product?.productName ??
                                    "",
                            style: FontStyles.fontBold(fontSize: 12),
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  item.reviews?.first.orderItemId?.items?.first
                                              .variant !=
                                          null
                                      ? EnStrings.variantEin
                                      : EnStrings.productEin,
                                  style: FontStyles.fontRegular(fontSize: 12),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  "#${item.reviews?.first.orderItemId?.items?.first.variant != null ? (item.reviews?.first.orderItemId?.items?.first.variant?.variantEIN ?? "") : item.reviews?.first.orderItemId?.items?.first.product?.productEIN ?? ""}",
                                  style: FontStyles.fontRegular(
                                    fontSize: 12,
                                    color: Colors.white.withOpacity(0.6),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  "Shipment Code",
                                  style: FontStyles.fontRegular(fontSize: 12),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  "#${(item.reviews?.first.orderItemId?.orderItemCode ?? "").substring((item.reviews?.first.orderItemId?.orderItemCode ?? "").length - 8)}",
                                  style: FontStyles.fontRegular(
                                    fontSize: 12,
                                    color: Colors.white.withOpacity(0.6),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  EnStrings.qty,
                                  style: FontStyles.fontRegular(fontSize: 12),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  "${item.reviews?.first.orderItemId?.items?.first.quantity ?? 0} pieces",
                                  style: FontStyles.fontRegular(
                                    fontSize: 12,
                                    color: Colors.white.withOpacity(0.6),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (item.reviews?.first.orderItemId?.items?.first
                                  .variant !=
                              null)
                            const Spacer(),
                          if (item.reviews?.first.orderItemId?.items?.first
                                  .variant !=
                              null)
                            Row(
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    item
                                                .reviews
                                                ?.first
                                                .orderItemId
                                                ?.items
                                                ?.first
                                                .variant
                                                ?.variantAttributes
                                                ?.variantColor !=
                                            null
                                        ? EnStrings.color
                                        : item
                                                    .reviews
                                                    ?.first
                                                    .orderItemId
                                                    ?.items
                                                    ?.first
                                                    .variant
                                                    ?.variantAttributes
                                                    ?.variantSize !=
                                                null
                                            ? EnStrings.size
                                            : EnStrings.customOption,
                                    style: FontStyles.fontRegular(fontSize: 12),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: item
                                              .reviews
                                              ?.first
                                              .orderItemId
                                              ?.items
                                              ?.first
                                              .variant
                                              ?.variantAttributes
                                              ?.variantColor !=
                                          null
                                      ? Row(
                                          children: [
                                            SizedBox(
                                              height: 12,
                                              width: 12,
                                              child: GlobalMethods.netWorkImage(
                                                  item
                                                          .reviews
                                                          ?.first
                                                          .orderItemId
                                                          ?.items
                                                          ?.first
                                                          .variant
                                                          ?.variantAttributes
                                                          ?.variantColor
                                                          ?.colorIcon ??
                                                      "",
                                                  BorderRadius.circular(6),
                                                  BoxFit.cover),
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              item
                                                      .reviews
                                                      ?.first
                                                      .orderItemId
                                                      ?.items
                                                      ?.first
                                                      .variant
                                                      ?.variantAttributes
                                                      ?.variantColor
                                                      ?.colorFamily ??
                                                  "",
                                              style: FontStyles.fontRegular(
                                                fontSize: 12,
                                                color: Colors.white
                                                    .withOpacity(0.6),
                                              ),
                                            ),
                                          ],
                                        )
                                      : item
                                                  .reviews
                                                  ?.first
                                                  .orderItemId
                                                  ?.items
                                                  ?.first
                                                  .variant
                                                  ?.variantAttributes
                                                  ?.variantSize !=
                                              null
                                          ? Text(
                                              item
                                                      .reviews
                                                      ?.first
                                                      .orderItemId
                                                      ?.items
                                                      ?.first
                                                      .variant
                                                      ?.variantAttributes
                                                      ?.variantSize
                                                      ?.size ??
                                                  "",
                                              style: FontStyles.fontRegular(
                                                fontSize: 12,
                                                color: Colors.white
                                                    .withOpacity(0.6),
                                              ),
                                            )
                                          : SizedBox(
                                              height: 20,
                                              child: ListView.builder(
                                                  itemCount: item
                                                      .reviews
                                                      ?.first
                                                      .orderItemId
                                                      ?.items
                                                      ?.first
                                                      .variant
                                                      ?.variantAttributes
                                                      ?.variantCustomOptions
                                                      ?.length,
                                                  scrollDirection:
                                                      Axis.horizontal,
                                                  itemBuilder:
                                                      (context, index) {
                                                    VariantCustomOptions
                                                        options = (item
                                                                .reviews
                                                                ?.first
                                                                .orderItemId
                                                                ?.items
                                                                ?.first
                                                                .variant
                                                                ?.variantAttributes
                                                                ?.variantCustomOptions ??
                                                            [])[index];
                                                    return Container(
                                                      height: 20,
                                                      decoration: BoxDecoration(
                                                        border: Border.all(
                                                          width: 1,
                                                          color: Colors.white24,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(5),
                                                      ),
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 5),
                                                      child: Text(
                                                        "${options.attributeTitle ?? ""} - ${options.attributeValue ?? ""}",
                                                        style: FontStyles
                                                            .fontRegular(
                                                          fontSize: 12,
                                                          color: Colors.white
                                                              .withOpacity(0.6),
                                                        ),
                                                      ),
                                                    );
                                                  }),
                                            ),
                                ),
                              ],
                            ),
                          const Spacer(),
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  EnStrings.price,
                                  style: FontStyles.fontRegular(fontSize: 12),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  "${item.reviews?.first.orderItemId?.items?.first.variant != null ? (item.reviews?.first.orderItemId?.items?.first.variant?.variantPrice ?? 0) : (item.reviews?.first.orderItemId?.items?.first.product?.productPrice ?? 0)} AED",
                                  style: FontStyles.fontMedium(
                                    color: Colors.white.withOpacity(0.6),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
            reviewWidget(item.reviews?.first.clientId?.userId?.userName ?? "",
                item.reviews?.first.reviewMessage ?? "", context),
            if (!(item.hasMoreReviews ?? false)) const SizedBox(height: 24),
            if (item.hasMoreReviews ?? false)
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () => Get.to(() => ProductReviews(item: item)),
                  child: Text(
                    "See What Others Say",
                    style: FontStyles.fontMedium(
                      fontSize: 11,
                      color: AppColors.colorPrimary,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ),
          ],
        ));
  }

  reviewWidget(String title, String review, BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        children: [
          Row(
            children: [
              SizedBox(
                height: 24,
                width: 24,
                child: GlobalMethods.netWorkImage(
                  item.reviews?.first.clientId?.userId?.avatar ?? "",
                  BorderRadius.circular(12),
                  BoxFit.cover,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: FontStyles.fontBold(fontSize: 10),
                ),
              ),
              Text(
                GlobalMethods.timeAgo(
                    DateTime.parse(item.reviews?.first.createdAt ?? "")),
                style: FontStyles.fontLight(
                  fontSize: 10,
                  color: Colors.white.withOpacity(0.5),
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(left: 25, top: 10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.star,
                  color: AppColors.colorRatingStar,
                  size: 12,
                ),
                const SizedBox(width: 2),
                Text(
                  "${item.reviews?.first.rate ?? 0}",
                  style: FontStyles.fontMedium(fontSize: 10),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    review,
                    style: FontStyles.fontRegular(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.5),
                    ),
                  ),
                )
              ],
            ),
          ),
          Container(
            height: 1,
            margin: const EdgeInsets.only(left: 25),
            color: Colors.white.withOpacity(0.4),
          ),
        ],
      ),
    );
  }
}
