import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/other/popup_loader_component.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/product_variation_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/screens/products/add_product/views/components/variations_row.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ProductPriceAndVariations extends StatefulWidget {
  const ProductPriceAndVariations({super.key});

  @override
  State<ProductPriceAndVariations> createState() =>
      _ProductPriceAndVariationsState();
}

class _ProductPriceAndVariationsState extends State<ProductPriceAndVariations>
    with InputValidationUtil
    implements ServerResponse {
  RxBool reloadVariations = false.obs;
  late GraphQlInitilize _request;
  var sizeBox24 = const SizedBox(height: 24);
  final controller = Get.find<AddProductController>();
  late AppLocalizations appLocal;

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.isMatchProduct) {
        if (controller.validateVariationsOptions()) {
          reloadVariations.value = false;
          controller.genrateVariations(Get.context!, _request);
        } else if (controller.arrVariations.isNotEmpty) {
          Future.delayed(Durations.short1, () => reloadVariations.value = true);
        }
      } else {
        if (controller.validateVariationsOptions()) {
          reloadVariations.value = false;
          controller.genrateVariations(Get.context!, _request);
        } else if (controller.arrVariations.isNotEmpty) {
          Future.delayed(Durations.short1, () => reloadVariations.value = true);
        }
      }
      if (controller.validationHistory?.returnValues != null) {
        controller.formKey.currentState!.validate();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    ever(controller.onVariantFeildsUpdate, (_) {
      if (controller.onVariantFeildsUpdate.value) {
        PopupLoader.showLoadingDialog(context);
        reloadVariations.value = false;
        if (!Get.isSnackbarOpen) {
          Get.snackbar(
            appLocal.variantFieldsUpdated,
            appLocal.alertVariantPriceQty,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.headerColorDark,
          );
        }
        Future.delayed(const Duration(milliseconds: 10), () {
          reloadVariations.value = true;
          controller.onVariantUpdate.value = false;
          PopupLoader.hideLoadingDialog();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
     appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: controller.formKey,
          child: SingleChildScrollView(
            controller: controller.stepperHideController,
            child: Obx(
              () => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ElbaaabInputTextField(
                    margin: const EdgeInsets.only(top: 24),
                    onChanged: (v) => controller.price = v,
                    initialValue: controller.price,
                    hint: 'ex : 200',
                    label: appLocal.addPrice,
                    charaterlimit: 7,
                    inputFormatter: '[0-9.]',
                    validator: (v) {
                      if (v == "0") {
                        return appLocal.zeroNotAcceptable;
                      } else {
                        return validateFieldEmpty(
                          v,
                          errorMessage: appLocal.alertOnPriceReturn,
                          serverValue: "${controller.product?.productPrice}",
                          isReturend: (controller
                                  .validationHistory?.returnValues
                                  ?.contains("Price") ??
                              false),
                        );
                      }
                    },
                    decimalNumerLimit: 2,
                    prefix: Text(
                      '*',
                      style: TextStyle(
                        color: AppColors.colorDanger,
                        fontSize: 20,
                      ),
                    ),
                    textDirection: appLocal.localeName == "en"
                        ? TextDirection.ltr
                        : TextDirection.rtl,
                    suffix: Container(
                      width: 67,
                      height: 60,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.colorPrimary,
                        borderRadius: appLocal.localeName == "en"
                            ? const BorderRadius.only(
                                topRight: Radius.circular(10),
                                bottomRight: Radius.circular(10),
                              )
                            : const BorderRadius.only(
                                topLeft: Radius.circular(10),
                                bottomLeft: Radius.circular(10),
                              ),
                      ),
                      child: Text(
                        appLocal.aed,
                        style: FontStyles.fontRegular(fontSize: 12),
                      ),
                    ),
                    inputType: const TextInputType.numberWithOptions(
                        signed: true, decimal: true),
                  ),
                  sizeBox24,
                  ElbaaabInputTextField(
                    onChanged: (v) => controller.qty = v,
                    initialValue: controller.qty,
                    hint: 'ex : 5',
                    label: appLocal.availableQuantity,
                    inputFormatter: '[0-9]',
                    charaterlimit: 4,
                    suffix: Icon(
                      Icons.info,
                      color: AppColors.colorPrimary,
                    ),
                    textDirection: appLocal.localeName == "en"
                        ? TextDirection.ltr
                        : TextDirection.rtl,
                    prefix: Text(
                      '*',
                      style: TextStyle(
                        color: AppColors.colorDanger,
                        fontSize: 20,
                      ),
                    ),
                    validator: (v) => validateFieldEmpty(
                      v,
                      errorMessage: appLocal.alertOnQtyReturn,
                      serverValue: "${controller.product?.productAvailableQte}",
                      isReturend: (controller.validationHistory?.returnValues
                              ?.contains("Quantity") ??
                          false),
                    ),
                    suffixClick: () => BottomSheets.showAlertMessageBottomSheet(
                        appLocal.availableQtyMessage,
                        appLocal.availableQuantity,
                        context),
                    inputType: const TextInputType.numberWithOptions(
                        signed: true, decimal: true),
                  ),
                  if (controller.arrVariations.isNotEmpty)
                    ElbaabGradientButtonWidget(
                      onPress: () {
                        if (controller.formKey.currentState!.validate()) {
                          reloadVariations.value = false;
                          Future.delayed(const Duration(milliseconds: 10), () {
                            for (var superIndex = 0;
                                superIndex < controller.arrVariations.length;
                                superIndex++) {
                              for (var index = 0;
                                  index <
                                      (controller.arrVariations[superIndex]
                                                  .variations ??
                                              [])
                                          .length;
                                  index++) {
                                controller.arrVariations[superIndex]
                                    .variations![index].isUpdated = true;
                                controller.arrVariations[superIndex]
                                        .variations![index].variantPrice =
                                    checkDecimal(controller.price);
                                controller
                                    .arrVariations[superIndex]
                                    .variations![index]
                                    .variantQte = int.parse(controller.qty);
                              }
                            }
                            reloadVariations.value = true;
                          });
                        }
                      },
                      text: appLocal.applyToAllVariant,
                      edgeInsets:  EdgeInsets.only(top: 24.h),
                    ),
                  sizeBox24,
                  Row(
                    children: [
                      Text(
                        appLocal.notificationOnMinQtyIsReached,
                        style: FontStyles.fontMedium(fontSize: 12),
                      ),
                      const SizedBox(width: 5),
                      InkWell(
                        onTap: () => BottomSheets.showAlertMessageBottomSheet(
                            appLocal.minimumQtyAlertMessage,
                            appLocal.minimumQtyAlert,
                            context),
                        child: Icon(
                          Icons.info,
                          size: 20,
                          color: AppColors.colorPrimary,
                        ),
                      ),
                      const Spacer(),
                      Switch(
                        value: controller.isHideMinimumQty.value,
                        onChanged: (v) => controller.isHideMinimumQty.value = v,
                        activeColor: AppColors.colorPrimary,
                      ),
                    ],
                  ),
                  if (controller.isHideMinimumQty.value)
                    ElbaaabInputTextField(
                      onChanged: (v) => controller.minQty = v,
                      validator: (v) {
                        if (v!.isEmpty) {
                          return appLocal.fieldRequired;
                        } else if (int.parse(v) >= int.parse(controller.qty)) {
                          return "Minimum quantity should be less than available quantity";
                        } else {
                          return validateFieldEmpty(
                            v,
                            errorMessage:
                                "Admin rejected this producct quantity",
                            serverValue:
                                "${controller.product?.productAvailableQte}",
                            isReturend: (controller
                                    .validationHistory?.returnValues
                                    ?.contains("Quantity") ??
                                false),
                          );
                        }
                      },
                      initialValue: controller.minQty,
                      hint: "ex : 55654585",
                      label: appLocal.minimumQty,
                      inputFormatter: '[0-9]',
                      inputType: const TextInputType.numberWithOptions(
                          signed: true, decimal: true),
                      margin: const EdgeInsets.only(top: 10),
                    ),
                  Obx(
                    () => (!reloadVariations.value)
                        ? Container()
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                                Padding(
                                  padding:
                                       EdgeInsets.only(top: 24.h, bottom: 8.h),
                                  child: Text(
                                    appLocal.variation,
                                    style: FontStyles.fontSemibold(),
                                  ),
                                ),
                                const VariationsRow(isScrollable: false),
                              ]),
                  ),
                  sizeBox24,
                  Obx(
                    () => controller.isRequiredUpdate.value.isNotEmpty
                        ? Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Align(
                              alignment: Alignment.center,
                              child: Text(
                                controller.isRequiredUpdate.value,
                                textAlign: TextAlign.center,
                                style: FontStyles.fontMedium(
                                    color: AppColors.colorDanger),
                              ),
                            ),
                          )
                        : Container(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == 'VARIATIONS') {
      ProductVariationModel productVariationsModel =
          ProductVariationModel.fromJson(response);
      if (productVariationsModel.status == statusOK) {
        // log(response);
        reloadVariations.value =
            controller.filterVeriations(productVariationsModel);
      }
    }
  }

  dynamic checkDecimal(String valueString) {
    num? parsedValue = num.tryParse(valueString);
    if (parsedValue != null &&
        parsedValue is double &&
        parsedValue != parsedValue.toInt()) {
      return parsedValue;
    } else {
      return int.parse(valueString);
    }
  }
}
