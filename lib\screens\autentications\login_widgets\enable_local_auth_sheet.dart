import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class EnableLocalAuthModalBottomSheet extends StatelessWidget {
  final void Function() action;

  const EnableLocalAuthModalBottomSheet({Key? key, required this.action})
      : super(key: key);

  static const Color primaryColor = Color(0xFF13B5A2);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        color: Colors.white,
      ),
      padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(
            Icons.fingerprint_outlined,
            size: 100,
            color: AppColors.colorPrimary,
          ),
          const SizedBox(height: 10),
          Text(
            'Do you want to enable Fingerprint OR Face ID for login?',
            textAlign: TextAlign.center,
            style: FontStyles.fontSemibold(color: Colors.black),
          ),
          const SizedBox(height: 5),
          Text(
              'The next time you log in, you will not be prompted for your login credentials.',
              style: FontStyles.fontRegular(color: Colors.black),
              textAlign: TextAlign.center),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: () {
              action();
              Get.back();
            },
            style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.colorPrimary,
                textStyle:
                    const TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
            child: Text("Yes", style: FontStyles.fontRegular()),
          ),
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.colorSecondary_Red,
              textStyle: const TextStyle(fontSize: 18),
            ),
            child: Text(
              "No, thanks!",
              style: FontStyles.fontRegular(),
            ),
          )
        ],
      ),
    );
  }
}
