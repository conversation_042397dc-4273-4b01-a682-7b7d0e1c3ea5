import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/add_product/shipments_fits_model.dart';
import 'package:overolasuppliers/widgets/elbaab_carousel_feild_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../controller/add_product_controller.dart';

class ReviewShipmentDetail extends StatelessWidget {
  const ReviewShipmentDetail({Key? key, required this.controller})
      : super(key: key);
  final AddProductController controller;

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Padding(
      padding: EdgeInsets.only(top: 24.h),
      child: ElbaabCarouselFeildWidget(
        aspectRatio: 5.8 / 5,
        children: [
          ElbaabFeildContainerWidget(
            borderWidth: 0,
            borderColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.only(
                      left: kLeftSpace, right: kRightSpace, top: 10, bottom: 16)
                  .r,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: Text(
                          appLocal.shipmentTitleEnglish,
                          style: FontStyles.fontBold(fontSize: 12),
                        ),
                      ),
                      IconButton(
                        onPressed: () =>
                            controller.changeTab(isNext: false, isJumpPage: 4),
                        icon: Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.06),
                              shape: BoxShape.circle),
                          child: Center(
                            child: SvgPicture.string(SvgStrings.iconEditGray),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          appLocal.shipmentFits,
                          style: FontStyles.fontRegular(fontSize: 12),
                        ),
                      ),
                      Expanded(flex: 2, child: getProductPackageSize())
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 5).r,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          flex: 2,
                          child: SvgPicture.string(SvgStrings.dimensionsBox),
                        ),
                        Expanded(
                          flex: 4,
                          child: Obx(
                            () => SizedBox(
                              height: 80.h,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  Text(
                                    appLocal.maxBoxDimension,
                                    textAlign: TextAlign.center,
                                    style: FontStyles.fontRegular(
                                        fontSize: 12,
                                        color: AppColors.colorPrimary),
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          "${appLocal.width} : ${controller.selectedSize.value.dimensions?.width ?? "0"}",
                                          textAlign: TextAlign.center,
                                          style: FontStyles.fontRegular(
                                              fontSize: 11),
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          "${appLocal.height} : ${controller.selectedSize.value.dimensions?.height ?? "0"}",
                                          textAlign: TextAlign.center,
                                          style: FontStyles.fontRegular(
                                              fontSize: 11),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          "${appLocal.length} : ${controller.selectedSize.value.dimensions?.length ?? "0"}",
                                          textAlign: TextAlign.center,
                                          style: FontStyles.fontRegular(
                                              fontSize: 11),
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          "${appLocal.weight} : ${controller.selectedSize.value.dimensions?.weight ?? "0"}",
                                          textAlign: TextAlign.center,
                                          style: FontStyles.fontRegular(
                                              fontSize: 11),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          appLocal.itemPerOrder,
                          style: FontStyles.fontRegular(fontSize: 12),
                        ),
                      ),
                      Expanded(flex: 2, child: getItemsPerOrder())
                    ],
                  ),
                ],
              ),
            ),
          ),
          ElbaabFeildContainerWidget(
            borderWidth: 0,
            borderColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.only(
                      left: kLeftSpace, right: kRightSpace, top: 10, bottom: 16)
                  .r,
              child: Column(
                children: [
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: Text(
                          appLocal.shipmentTitleArabic,
                          style: FontStyles.fontBold(fontSize: 12),
                        ),
                      ),
                      IconButton(
                        onPressed: () =>
                            controller.changeTab(isNext: false, isJumpPage: 4),
                        icon: Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.06),
                              shape: BoxShape.circle),
                          child: Center(
                            child: SvgPicture.string(SvgStrings.iconEditGray),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          appLocal.shipmentFits,
                          style: FontStyles.fontRegular(fontSize: 12),
                        ),
                      ),
                      Expanded(child: getProductPackageSize(isArabic: true))
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 5).r,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                            flex: 2,
                            child: SvgPicture.string(SvgStrings.dimensionsBox)),
                        Expanded(
                          flex: 4,
                          child: Obx(
                            () => SizedBox(
                              height: 80,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  Text(
                                    appLocal.maxBoxDimension,
                                    textAlign: TextAlign.center,
                                    style: FontStyles.fontRegular(
                                        fontSize: 12,
                                        color: AppColors.colorPrimary),
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          "${appLocal.width} : ${controller.selectedSize.value.dimensions?.width ?? "0"}",
                                          textAlign: TextAlign.center,
                                          style: FontStyles.fontRegular(
                                              fontSize: 11),
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          "${appLocal.height} : ${controller.selectedSize.value.dimensions?.height ?? "0"}",
                                          textAlign: TextAlign.center,
                                          style: FontStyles.fontRegular(
                                              fontSize: 11),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          "${appLocal.length} : ${controller.selectedSize.value.dimensions?.length ?? "0"}",
                                          textAlign: TextAlign.center,
                                          style: FontStyles.fontRegular(
                                              fontSize: 11),
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          "${appLocal.weight} : ${controller.selectedSize.value.dimensions?.weight ?? "0"}",
                                          textAlign: TextAlign.center,
                                          style: FontStyles.fontRegular(
                                              fontSize: 11),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          appLocal.itemPerOrder,
                          style: FontStyles.fontRegular(fontSize: 12),
                        ),
                      ),
                      Expanded(child: getItemsPerOrder())
                    ],
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget getProductPackageSize({bool isArabic = false}) {
    String shipment = prefs.getString(productShipment) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (shipment.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(shipment);
      if (isArabic) {
        cacheValue = SizeCategories.fromJson(jsonDecode(info["selectedSize"]))
                .sizeCategoriesAr
                ?.name ??
            "";
        serverValue =
            controller.product?.productSizeCategory?.sizeCategoriesAr?.name ??
                "";
      } else {
        cacheValue =
            SizeCategories.fromJson(jsonDecode(info["selectedSize"])).name ??
                "";
        serverValue = controller.product?.productSizeCategory?.name ?? "";
      }
    }

    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: isArabic
            ? controller.selectedSize.value.sizeCategoriesAr?.name ?? ""
            : controller.selectedSize.value.name ?? "",
        errorCacheValue: "You removed product weight from shipment",
        errorServerValue: "Product Weight Not Provided");
  }

  Widget getItemsPerOrder() {
    String shipment = prefs.getString(productShipment) ?? "";
    String cacheValue = "";
    String serverValue = "";
    if (shipment.isNotEmpty && !controller.isDraftProduct) {
      Map<String, dynamic> info = jsonDecode(shipment);
      cacheValue = (info['itemsPerOrder']);
      serverValue = "${controller.product?.itemsPerOrder ?? 0}";
    }

    return controller.validateWidget(
        cacheValue: cacheValue,
        serverValue: serverValue,
        feildValue: controller.itemsPerOrder,
        errorCacheValue: "",
        errorServerValue: "");
  }
}
