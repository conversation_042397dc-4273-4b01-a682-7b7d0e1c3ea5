import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/reviews_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/components/reviews_singlerow.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class Reviews extends StatefulWidget {
  const Reviews({Key? key}) : super(key: key);

  @override
  State<Reviews> createState() => _ReviewsState();
}

class _ReviewsState extends State<Reviews> implements ServerResponse {
  Rx<ReviewsModel> reviews = ReviewsModel().obs;

  RxList<Items> arrReviews = <Items>[].obs;

  late GraphQlInitilize _request;

  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    scrollController.addListener(_onScroll);
    _request = GraphQlInitilize(this);
    Future.delayed(
        Duration.zero,
        () => {
              arrReviews.clear(),
              _request.runQuery(
                  context: context,
                  query: GraphQlQuries.getReviews,
                  variables: GraphQlVariables.getPaginated(
                      itemsNumber: 5,
                      page: ((reviews.value.reviews?.page ?? 0) + 1)))
            });
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      if (reviews.value.reviews?.hasNextPage ?? false) {
        _request.runQuery(
            context: context,
            query: GraphQlQuries.getReviews,
            variables: GraphQlVariables.getPaginated(
                itemsNumber: 5,
                page: ((reviews.value.reviews?.page ?? 0) + 1)));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar:  ElbaabHeader(
        title: appLocal.reviews,
        leadingBack: true,
        notificationBell: true,
      ),
      body: Obx(
        () => reviews.value.status == status400
            ? Padding(
                padding: const EdgeInsets.all(16.0),
                child: Center(
                  child: Text(
                  appLocal.localeName == "ar"? reviews.value.arMessage ?? "": reviews.value.message ?? "",
                    style: FontStyles.fontMedium(),
                  ),
                ),
              )
            : reviews.value.status == statusOK
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: ListView.builder(
                        controller: scrollController,
                        itemCount: arrReviews.length,
                        itemBuilder: (context, section) {
                          return ReviewsSingleRow(item: arrReviews[section]);
                        }),
                  )
                : Container(),
      ),
    );
  }

  @override
  onError(error, String type) {
    reviews.value = ReviewsModel.fromJson(error);
  }

  @override
  onSucess(response, String type) {
    reviews.value = ReviewsModel.fromJson(response);
    if (reviews.value.status == statusOK) {
      arrReviews.addAll(reviews.value.reviews?.items ?? []);
    }
  }
}
