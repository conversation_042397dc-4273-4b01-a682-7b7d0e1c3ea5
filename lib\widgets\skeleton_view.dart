import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class SkeletonView extends StatelessWidget {
  final double width;
  final double height;
  final double borderRadius;

  const SkeletonView({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius = 4,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }
}