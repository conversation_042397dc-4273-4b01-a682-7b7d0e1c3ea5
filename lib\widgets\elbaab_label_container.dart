import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';

class ElbaabLabelContainer extends StatelessWidget {
  final Widget leading;
  final Widget? trailing;
  final String label;
  final String? errorText;
  final double? trailingSpace;
  final double leadingRightSpace;
  final EdgeInsets? edgeInsets;
  final Function? onTap;
  final Color? borderColor;

  const ElbaabLabelContainer({
    Key? key,
    required this.leading,
    required this.label,
    this.trailing,
    this.errorText,
    this.leadingRightSpace = 75,
    this.edgeInsets = EdgeInsets.zero,
    this.trailingSpace,
    this.onTap,
    this.borderColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: edgeInsets,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
      ),
      height: errorText != null ? 90 : 65,
      child: Column(
        children: <Widget>[
          SizedBox(
            height: 65,
            child: Stack(
              children: <Widget>[
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: ElbaabFeildContainerWidget(
                    onPress: () => onTap!(),
                    containerHeight: 55,
                    borderWidth: 1,
                    borderColor: borderColor,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const SizedBox(width: 10,),
                        Expanded(child: leading),
                        const SizedBox(width: 10,),
                        trailing ?? Container(),
                        if(trailing != null)
                        const SizedBox(width: 10,),
                      ],
                    ),
                    // child: ListTile(
                    //   leading: leading,
                    //   trailing: trailing,
                    // ),

                    // child: Stack(
                    //   alignment: Alignment.center,
                    //   children: <Widget>[
                    //     Positioned(
                    //       left: 10,
                    //       right: trailing == null ? 10 : leadingRightSpace,
                    //       child: leading,
                    //     ),
                    //     Positioned(
                    //       right: trailingSpace,
                    //       child: trailing ?? Container(),
                    //     ),
                    //   ],
                    // ),
                  ),
                ),
                Positioned(
                  // left: 10,
                  height: 20,
                  child: Stack(
                    children: <Widget>[
                      Positioned(
                        top: 10,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        child: Container(
                          color: AppColors.headerColorDark,
                        ),
                      ),
                      Padding(
                        padding:
                            const EdgeInsets.only(left: 8, right: 8, top: 3),
                        child: Text(
                          "  $label",
                          style: FontStyles.fontRegular(height: 1),
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
          if (errorText != null)
            SizedBox(
              height: 25,
              width: MediaQuery.of(context).size.width,
              child: Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    errorText ?? "",
                    maxLines: 1,
                    style: FontStyles.fontRegular(
                        fontSize: 12, color: AppColors.colorDanger),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
