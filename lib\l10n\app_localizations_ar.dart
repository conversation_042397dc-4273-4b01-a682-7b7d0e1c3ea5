// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get changeLanguage => 'تغيير اللغة';

  @override
  String get customerService => 'خدمة العملاء';

  @override
  String get deleteProduct => 'المنتجات المحذوفة';

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get accountAndMangment => 'إدارة الحساب';

  @override
  String get appName => 'الباب';

  @override
  String get your_country => 'البلد';

  @override
  String get your_language => 'اللغة';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get youDontHaveAccess => 'ليس لديك حساب؟';

  @override
  String get authenticateToSignIn => 'يرجى المصادقة على تسجيل الدخول';

  @override
  String get verifyMobileNumber => 'تحقق من رقم الجوال';

  @override
  String get signup => 'تسجيل';

  @override
  String get useBiometricAcess => 'استخدام الوصول البيومتري';

  @override
  String get back => 'رجوع';

  @override
  String get restrictOption => 'لا يُسمح بإضافة المزيد من الخيارات للمنتجات المقبولة والمتطابقة';

  @override
  String get reset => 'إعادة تعيين';

  @override
  String get rememberMe => 'تذكرني';

  @override
  String get clearCache => 'مسح الذاكرة المؤقتة';

  @override
  String get pushNotification => 'إدارة الإشعارات';

  @override
  String get reciveOrderEmails => 'استلام بريد الطلبات';

  @override
  String get biometricAccess => 'الوصول البيومتري';

  @override
  String get verifyMobileNumberContent => 'لتحقق من رقم الجوال، أرسلنا لك كلمة مرور لمرة واحدة (OTP) إلى هاتفك';

  @override
  String get addPhotos => 'إضافة صور';

  @override
  String get addOption => 'إضافة خيار';

  @override
  String get addedColor => 'الألوان المضافة';

  @override
  String get brand => 'العلامة التجارية';

  @override
  String get completeSetup => 'إكمال الإعداد';

  @override
  String get updateInfo => 'تحديث المعلومات';

  @override
  String get addSize => 'إضافة حجم';

  @override
  String get uploadColorPhoto => 'تحميل صورة اللون';

  @override
  String get category => 'فئة';

  @override
  String get custom => 'مخصص';

  @override
  String get viewColors => 'عرض الألوان';

  @override
  String get productname => 'اسم المنتج';

  @override
  String get addColor => 'إضافة لون';

  @override
  String get categories => 'فئات';

  @override
  String get variation => 'اختيارات';

  @override
  String get subCategory => 'فئة فرعية';

  @override
  String get acceptReturn => 'قبول الإرجاع';

  @override
  String get accept => 'قبول';

  @override
  String get type => 'نوع';

  @override
  String get customOption => 'خيار مخصص';

  @override
  String get addcustomOption => 'إضافة خيار مخصص';

  @override
  String get reject => 'رفض';

  @override
  String get addStory => 'إضافة قصة';

  @override
  String get addPrice => 'إضافة سعر';

  @override
  String get add => 'إضافة';

  @override
  String get addYourBrandName => 'أضف اسم علامتك التجارية';

  @override
  String get maxBoxDimension => 'أقصى أبعاد للصندوق';

  @override
  String get genrateVariations => 'توليد الاختيارات';

  @override
  String get notificationOnMinQtyIsReached => 'إشعار عند الوصول إلى الحد الأدنى من الكمية';

  @override
  String get availableQuantity => 'الكمية المتاحة';

  @override
  String get aed => 'درهم';

  @override
  String get size => 'أحجام';

  @override
  String get returnCondition => 'حالة الإرجاع';

  @override
  String get productPolicy => 'سياسة المنتج';

  @override
  String get viewListofBrand => 'عرض قائمة العلامات التجارية';

  @override
  String get chooseBrand => 'اختر علامة تجارية';

  @override
  String get recentlySelected => 'تم تحديدها مؤخرًا';

  @override
  String get selectCategory => 'اختر فئة';

  @override
  String get updateRefundStatus => 'تحديث حالة الاسترداد';

  @override
  String get ok => 'حسناً';

  @override
  String get polices => 'السياسات';

  @override
  String get width => 'العرض';

  @override
  String get length => 'الطول';

  @override
  String get weight => 'الوزن';

  @override
  String get height => 'الارتفاع';

  @override
  String get requiredRefregiration => 'التبريد مطلوب';

  @override
  String get specification => 'المواصفات';

  @override
  String get itemPerOrder => 'الكمية المسموحة  لكل طلب';

  @override
  String get newSpecification => 'مواصفات جديدة';

  @override
  String get oldSpecification => 'مواصفات قديمة';

  @override
  String get driverName => 'اسم السائق';

  @override
  String get deliveryCompany => 'شركة التوصيل';

  @override
  String get officeAddress => 'عنوان المكتب';

  @override
  String get confirmAll => 'تأكيد الكل';

  @override
  String get send => 'إرسال';

  @override
  String get returnDuration => 'مدة الإرجاع';

  @override
  String get warrantyDuration => 'مدة الضمان';

  @override
  String get keyword => 'الكلمات المفتاحية';

  @override
  String get buyerDetails => 'تفاصيل الزبون';

  @override
  String get deliveryDetails => 'تفاصيل التوصيل';

  @override
  String get ordered => 'تم الطلب';

  @override
  String get delivered => 'تم التوصيل';

  @override
  String get confirmed => 'تم التأكيد';

  @override
  String get confirm => 'تأكيد';

  @override
  String get rejected => 'مرفوض';

  @override
  String get cancelled => 'ملغى';

  @override
  String get shipmentInformation => 'معلومات الشحنة';

  @override
  String get shipmentFits => 'نوع الشحنة';

  @override
  String get returned => 'عائدة للتصحيح';

  @override
  String get shipment => 'شحنة';

  @override
  String get paymentMethod => 'طريقة الدفع';

  @override
  String get returnedConfirmed => 'تم الإرجاع والتأكيد';

  @override
  String get cancelledByCustomer => 'ملغى من قبل الزبون';

  @override
  String get shipped => 'تم الشحن';

  @override
  String get orderDetail => 'تفاصيل الطلب';

  @override
  String get transferRequest => 'طلب التحويل';

  @override
  String get youAreGoingToSend => 'سترسل';

  @override
  String get vat => 'ضريبة القيمة المضافة';

  @override
  String get myShop => 'متجري';

  @override
  String get orderNumber => 'رقم الطلب';

  @override
  String get totalAmount => 'المبلغ الإجمالي';

  @override
  String get done => 'تم';

  @override
  String get transactionID => 'رقم المعاملة';

  @override
  String get withdrawalID => 'رقم  السحب';

  @override
  String get orderID => 'رقم الطلب';

  @override
  String get amount => 'المبلغ';

  @override
  String get more => 'المزيد';

  @override
  String get sales => 'المبيعات';

  @override
  String get status => 'الحالة';

  @override
  String get transferredAmount => 'المبلغ المحول';

  @override
  String get topSellingItems => 'أفضل المنتجات مبيعًا';

  @override
  String get initiateTransferRequest => 'بدء طلب التحويل';

  @override
  String get totalVat => 'ضريبة القيمة المضافة الإجمالية';

  @override
  String get totalRevenue => 'الإيرادات الإجمالية';

  @override
  String get productforFreeDelivery => 'المنتجات المجانية للتوصيل';

  @override
  String get update => 'تحديث';

  @override
  String get verify => 'تحقق';

  @override
  String get submit => 'إرسال';

  @override
  String get next => 'التالي';

  @override
  String get pendingAmount => 'المبلغ قيد الانتظار';

  @override
  String get availableAmount => 'المبلغ المتاح';

  @override
  String get price => 'السعر';

  @override
  String get miniQTY => 'الكمية الصغرى';

  @override
  String get deliveryChargesFees => 'رسوم التوصيل';

  @override
  String get reply => 'الرد';

  @override
  String get edit => 'تعديل';

  @override
  String get remove => 'حذف';

  @override
  String get delete => 'حذف';

  @override
  String get details => 'التفاصيل';

  @override
  String get resetPasswordContent => 'أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور';

  @override
  String get tryAnotherWay => 'جرب طريقة أخرى';

  @override
  String get welcome => 'مرحباً';

  @override
  String get addMobileNumber => 'أضف رقم الجوال';

  @override
  String get enterAMobileNumberToSafeguardYourAccount => 'أدخل رقم الجوال لحماية حسابك';

  @override
  String get resetPassword => 'إعادة تعيين كلمة المرور';

  @override
  String get welcomeAgain => 'مرحباً مرة أخرى';

  @override
  String get welcomeBack => 'مرحباً بعودتك';

  @override
  String get cancel => 'إلغاء';

  @override
  String get bankAccount => 'حساب البنك';

  @override
  String get activity => 'النشاط';

  @override
  String get creditCard => 'بطاقة الائتمان';

  @override
  String get setting => 'الإعدادات';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get removeTermsConditionMessage => 'هل أنت متأكد من حذف الشروط والأحكام؟';

  @override
  String get myInformation => 'معلوماتي';

  @override
  String get save => 'حفظ';

  @override
  String get order => 'الطلبات';

  @override
  String get howCanWeHelpYou => 'كيف يمكننا مساعدتك؟';

  @override
  String get callUs => 'اتصل بنا';

  @override
  String get supportEmail => '<EMAIL>';

  @override
  String get whatsapp => 'واتساب';

  @override
  String get support => 'الدعم';

  @override
  String get minimumQtyAlert => 'تنبيه نقص الكمية';

  @override
  String get minimumQty => 'الكمية الصغرى';

  @override
  String get freeDeliveryTarget => 'هدف التوصيل المجاني';

  @override
  String get alert => 'تنبيه';

  @override
  String get freeDeliveryItems => 'المنتجات المجانية للتوصيل';

  @override
  String get sold => 'تم البيع';

  @override
  String get services => 'الخدمات';

  @override
  String get review => 'التقييم';

  @override
  String get incomeAndExpenses => 'الإيرادات والنفقات';

  @override
  String get addProduct => 'إضافة منتج';

  @override
  String get myTotalIncome => 'إيراداتي الكلية';

  @override
  String get matchThisProduct => 'مطابقة هذا المنتج';

  @override
  String get createNewProduct => 'إنشاء منتج جديد';

  @override
  String get similarProduct => 'منتج مشابه';

  @override
  String get myAccounts => 'حسابي';

  @override
  String get sellerDashboard => 'لوحة تحكم البائع';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get product => 'المنتج';

  @override
  String get updateNow => 'تحديث الآن';

  @override
  String get publishNow => 'نشر الآن';

  @override
  String get saveForLater => 'حفظ ك مسودة';

  @override
  String get shop => 'المتجر';

  @override
  String get addNewBranch => 'إضافة فرع جديد';

  @override
  String get addNewColor => 'إضافة لون جديد';

  @override
  String get openGoogleMap => 'فتح خرائط جوجل';

  @override
  String get description => 'الوصف';

  @override
  String get shopDescription => 'لا يوجد وصف';

  @override
  String get contactNumbers => 'أرقام الاتصال';

  @override
  String get contactEmails => 'عناوين البريد الإلكتروني';

  @override
  String get addAnotherBranch => 'إضافة فرع آخر';

  @override
  String get forgetPaswoord => 'نسيت كلمة المرور؟';

  @override
  String get termsAndCondition => 'الشروط والأحكام';

  @override
  String get yourName => 'الاسم';

  @override
  String get newName => 'اسم جديد';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get deleteThisColor => 'حذف هذا اللون';

  @override
  String get deleteThisProduct => 'حذف هذا المنتج';

  @override
  String get valid => 'صحيح';

  @override
  String get addAnotherContactNumber => 'إضافة رقم اتصال آخر';

  @override
  String get addAnotherContactEmail => 'إضافة عنوان بريد إلكتروني آخر';

  @override
  String get addLocationMap => 'إضافة موقع على الخريطة';

  @override
  String get addLocationUsingMap => 'إضافة الموقع باستخدام الخريطة';

  @override
  String get targetPrice => 'السعر المستهدف';

  @override
  String get passwordHint => '••••••••';

  @override
  String get emailfeildLabel => 'البريد الإلكتروني';

  @override
  String get newEmail => 'بريد إلكتروني جديد';

  @override
  String get newPhoneNumber => 'رقم هاتف جديد';

  @override
  String get addInfoAboutYourShop => 'إضافة معلومات عن متجرك';

  @override
  String get createShop => 'إنشاء متجر';

  @override
  String get createShopContent => 'يمكنك الآن إنشاء متجرك الخاص وإضافة منتجاتك';

  @override
  String get continu => 'الاستمرار';

  @override
  String get city => 'المدينة';

  @override
  String get notification => 'الإشعارات';

  @override
  String get reviews => 'التقييمات';

  @override
  String get qty => 'الكمية';

  @override
  String get color => 'الألوان';

  @override
  String get pickUp => 'التوصيل';

  @override
  String get estimatedDelivery => 'موعد التوصيل المتوقع';

  @override
  String get phone => 'الهاتف';

  @override
  String get productId => 'رقم المنتج';

  @override
  String get productEin => 'رقم المنتج';

  @override
  String get variantEin => 'رقم الاختيار';

  @override
  String get securityAndMangments => 'إدارة الأمان';

  @override
  String get appSettings => 'إعدادات التطبيق';

  @override
  String get deactivateTheAccount => 'تعطيل الحساب';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get changeYourEmail => 'تغيير البريد الإلكتروني';

  @override
  String get changeYourUserName => 'تغيير اسم المستخدم';

  @override
  String get changeYourMobileNumber => 'تغيير رقم الجوال';

  @override
  String get editAccount => 'تعديل الحساب';

  @override
  String get whyThisInformationRequired => 'لماذا هذه المعلومات مطلوبة؟';

  @override
  String get addNewProduct => 'إضافة منتج جديد';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get myOrders => 'طلباتي';

  @override
  String get myProfile => 'ملفي الشخصي';

  @override
  String get faq => 'الأسئلة الشائعة';

  @override
  String get faqs => 'الأسئلة الشائعة';

  @override
  String get date => 'التاريخ';

  @override
  String get updatedDate => 'التاريخ المحدث';

  @override
  String get uploadShopBanner => 'تحميل شعار المتجر';

  @override
  String get logo => 'الشعار';

  @override
  String get iban => 'رقم الحساب';

  @override
  String get message => 'الرسالة';

  @override
  String get returnedFeilds => 'الحقول المعادة للتصحيح';

  @override
  String get bankName => 'اسم البنك';

  @override
  String get accountHolderName => 'اسم صاحب الحساب';

  @override
  String get bankAccountInfo => 'معلومات حساب البنك';

  @override
  String get information => 'المعلومات';

  @override
  String get adminNotes => 'ملاحظات المشرف';

  @override
  String get trashProducts => 'المنتجات المحذوفة';

  @override
  String get privacyAndPolicy => 'الخصوصية والسياسات';

  @override
  String get logoutMessage => 'هل أنت متأكد من تسجيل الخروج؟';

  @override
  String get returnRequest => 'طلب الإرجاع';

  @override
  String get productReviews => 'تقييمات المنتج';

  @override
  String get aboutTheShop => 'عن المتجر';

  @override
  String get manageBranches => 'إدارة الفروع';

  @override
  String get updateBussinessInfo => 'تحديث معلومات النشاط التجاري';

  @override
  String get addBusinessInformation => 'إضافة معلومات النشاط التجاري';

  @override
  String get updateBusinessInformation => 'تحديث معلومات النشاط التجاري';

  @override
  String get loginDescription => 'نحن سعداء برؤيتك مرة أخرى';

  @override
  String get signupDescription => 'نحن سعداء بك';

  @override
  String get passwordFeildLabel => 'كلمة المرور';

  @override
  String get reEnterPassword => 'إعادة إدخال كلمة المرور';

  @override
  String get resendOtp => 'إعادة إرسال OTP';

  @override
  String get accountNumber => 'رقم الحساب';

  @override
  String get returnedDate => 'التاريخ المرتجع';

  @override
  String get notReceived => 'لم يتم الاستلام؟ ';

  @override
  String get secondsRemainToResendOtp => 'الوقت المتبقي لإعادة إرسال OTP';

  @override
  String get createYourAccount => 'إنشاء حسابك';

  @override
  String get alreadyHaveAnAccount => 'هل لديك حساب؟ ';

  @override
  String get emailHint => 'مثال: <EMAIL>';

  @override
  String get createAnAccount => 'إنشاء حساب';

  @override
  String get nameHint => 'مثال: خالد محمد';

  @override
  String get morningMsg => 'صباح الخير! مستعد تزود مبيعاتك؟';

  @override
  String get noonMsg => 'وقت الظهر! خل الطلبات تتوالى!';

  @override
  String get afterNoonMsg => 'بعد الظهر! زيد من مبيعاتك!';

  @override
  String get eveningMsg => 'مساء الخير! راجع إنجازاتك اليوم.';

  @override
  String get greetTimeError => 'هناك مشكلة في الوقت! تأكد من ساعتك!';

  @override
  String get searchProductFeildHint => 'الوصول البيومتري';

  @override
  String get skipAddNewProduct => 'تخطي (أضف منتج جديد)';

  @override
  String get matchExsistingProduct => 'طابق منتج موجود على تطبيقنا.';

  @override
  String get or => 'أو';

  @override
  String get searchType => 'تحقق من:\n\n• اسم المنتج\n\n• رمز EIN (رقم تعريف Elbaab)\n\n• GTIN المنتج (الرقم العالمي للسلعة التجارية)\n\n• UPC المنتج (رمز المنتج العالمي)\n\n• SKU المنتج (وحدة حفظ المخزون)';

  @override
  String get verificationCode => 'رمز التحقق';

  @override
  String get settingOtpDailogMessage => 'لقد أرسلنا رمز التحقق إلى';

  @override
  String get verifyOtp => 'تحقق من الرمز';

  @override
  String get updateBankAccount => 'تحديث حساب البنك';

  @override
  String get addNewBankAccount => 'إضافة حساب بنك جديد';

  @override
  String get waitWhileLoadShop => 'انتظر حتى يفتح المتجر';

  @override
  String get pickupAddress => 'عناوين الاستلام';

  @override
  String get pickupAddressUsageContant => 'عنوان الاستلام سيُستخدم للشحن. لن يكون مرئيًا للعملاء.';

  @override
  String get options => 'الخيارات';

  @override
  String get priceAndVariations => 'السعر والاختيارات';

  @override
  String get productNameFeildLableEnglish => 'اسم المنتج (الإنجليزية)';

  @override
  String get productNameFeildLableArbic => 'اسم المنتج (عربي)';

  @override
  String get productNameFeildHint => 'سابق : تي شيرت';

  @override
  String get productNameFeildReturnMessage => 'رفض المشرف هذا الاسم';

  @override
  String get email => 'البريد الإلكتروني مطلوب.';

  @override
  String get invalidEmail => 'البريد الإلكتروني غير صالح.';

  @override
  String get invalidText => 'نص غير صالح تم إدخاله.';

  @override
  String get phoneNumber => 'رقم الهاتف مطلوب.';

  @override
  String get invalidPhoneNumber => 'رقم الهاتف غير صالح.';

  @override
  String get password => 'حقل كلمة المرور مطلوب.';

  @override
  String get urlNotAccepted => 'الرابط وعنوان URL غير مقبولين.';

  @override
  String get tradeCertificateRequired => 'الرخصة التجارية مطلوبة.';

  @override
  String get emiratesIDRequired => 'مطلوب نسخة من الهوية الإماراتية أو جواز السفر.';

  @override
  String get invalidPassword => 'يجب أن تحتوي كلمة المرور على ستة أحرف على الأقل.';

  @override
  String get confirmPassword => 'حقل تأكيد كلمة المرور مطلوب.';

  @override
  String get fieldRequired => 'الحقل مطلوب.';

  @override
  String get errEmpty => 'الحقل فارغ.';

  @override
  String get gtinFeildLabel => 'GTIN أو UPC';

  @override
  String get gtinFeildReturnMessage => 'رفض المدير هذا هذا المعرف ';

  @override
  String get gtinFeildHint => 'سابق : معرف الصانع';

  @override
  String get errReachImageLimit => 'لقد وصلت إلى حد الصور للون واحد.';

  @override
  String get errSelectImageForColor => 'يمكنك تحميل 5 صور كحد أقصى لكل لون.';

  @override
  String get passwordDoesNotMatch => 'كلمة المرور غير متطابقة.';

  @override
  String get productDescriptionFeildLableEnglish => 'وصف المنتج (الإنجليزية)';

  @override
  String get productDescriptionFeildLableArbic => 'وصف المنتج (العربية)';

  @override
  String get productDescriptionFeildHint => 'سابق : قميص رقبة عالية مع الأكمام القصيرة';

  @override
  String get productDescriptionFeildReturnMessage => 'رفض المشرف هذا الوصف';

  @override
  String get freeDeliveryTargetMessage => 'إذا وصلت قيمة الطلب إلى هذا المبلغ المستهدف أو أكثر، ستقوم بتوصيل المنتج مجانًا';

  @override
  String get locationRequired => 'موقعك مطلوب حتى تتمكن شركة الشحن من استلام العنصر الخاص بك';

  @override
  String get whyBankInfo => 'لبدء تلقي المدفوعات عبر الإنترنت';

  @override
  String get pendingAmountAlertMessage => 'سيكون المبلغ قيد الانتظار متاحًا بعد 30 يومًا من تاريخ تسليم الطلب ';

  @override
  String get messageSent => 'تم إرسال رسالتك بنجاح';

  @override
  String get addKeywordMessage => 'لتحسين فرصك في البيع، أضف الكلمات المفتاحية واستخدم الفواصل بينها';

  @override
  String get availableQtyMessage => 'هذه المعلومات ستظهر لك فقط حتى تتمكن من مراقبة المخزون الخاص بك';

  @override
  String get minimumQtyAlertMessage => 'ستتلقى إشعارًا عندما يصل عدد العناصر إلى الحد الأدنى وسيتم عرضه في تطبيق المستخدم على أنه (متبقي)';

  @override
  String get requiredBanner => 'صورة غلاف المتجر مطلوبة';

  @override
  String get requiredLogo => 'صورة شعار المتجر مطلوبة';

  @override
  String get requiredTargetPrice => 'لقد قمت بتفعيل هدف التوصيل المجاني لكنك لم تدخل السعر';

  @override
  String get requiredTermsAndConditions => 'قم بتحميل شروط وأحكام متجرك';

  @override
  String get requiredCity => 'قم بتحميل شروط وأحكام متجرك';

  @override
  String get requiredMapLocation => 'حدد الموقع باستخدام الخريطة';

  @override
  String get requiredNumberForBranch => 'مطلوب رقم واحد على الأقل للفرع';

  @override
  String get requiredEmailForBranch => 'مطلوب بريد إلكتروني واحد على الأقل للفرع';

  @override
  String get requiredOtpCode => 'أدخل رمز التحقق الصحيح';

  @override
  String get pleaseAcceptTermsAndConditions => 'يرجى قبول الشروط والأحكام';

  @override
  String get limitReached => 'لقد وصلت إلى الحد المسموح';

  @override
  String get infoForDeliveryCompany => 'هذه المعلومات مهمة لشركة الشحن، يمكنك إدخال القيم التقريبية';

  @override
  String get isFreeDeliveryProduct => 'هل المنتج لتوصيل مجاني';

  @override
  String get isFreeDeliveryProductMessage => 'في حال التفعيل، ستتحمل جميع رسوم التوصيل';

  @override
  String get freeReturnTitle => 'إرجاع مجاني';

  @override
  String get freeReturnMessage => 'ستتحمل رسوم الإرجاع';

  @override
  String get notFreeReturnTitle => 'إرجاع غير مجاني';

  @override
  String get notFreeReturnMessage => 'سيدفع العميل رسوم الإرجاع.';

  @override
  String get adminReturnConditionNote => 'المشرف أعاد شرط الإرجاع الخاص بك';

  @override
  String get adminReturnDurationNote => 'المشرف أعاد مدة الإرجاع هذه';

  @override
  String get returnDurationFeildHint => 'مثال: 5 أيام';

  @override
  String get returnDurationFeildLabell => 'مدة الإرجاع';

  @override
  String get returnDurationSecondFeildLabell => 'أدخل مدة الإرجاع';

  @override
  String get adminWarrantyDurationNote => 'المشرف أعاد مدة الضمان هذه';

  @override
  String get warrantyDurationFeildHint => 'مثال: 5 أيام';

  @override
  String get warrantyDurationFeildLabel => 'مدة الضمان';

  @override
  String get warrantyDurationSecondFeildLabel => 'أدخل مدة الضمان';

  @override
  String get adminPolicyReturnNote => 'المشرف أعاد سياسة المنتج';

  @override
  String get updateReturnData => 'تحديث بيانات الإرجاع';

  @override
  String get policyFeildLabelEnglish => 'السياسة (الإنجليزية)';

  @override
  String get policyFeildLabelArabic => 'السياسة (العربية)';

  @override
  String get policyFeildHint => 'مثال: القطن المزروع باستخدام أسمدة ومبيدات طبيعية. بالإضافة إلى ذلك,';

  @override
  String get selectSubCategroy => 'اختر الفئة الفرعية';

  @override
  String get adminRejectThisCategory => 'أعاد المشرف هذه الفئة';

  @override
  String get adminRejectThisBrand => 'أعاد المشرف هذه العلامة التجارية';

  @override
  String get pleaseSelectBrandOrType => 'يرجى اختيار العلامة التجارية أو اكتب اسم العلامة التجارية الخاصة بك';

  @override
  String get adminRejectThisSubCategory => 'أعاد المشرف هذه الفئة الفرعية';

  @override
  String get pleaseSelectCategorySubCategory => 'يرجى تحديد الفئة والفئة الفرعية';

  @override
  String get enterYourBrandName => 'أدخل اسم علامتك التجارية هنا';

  @override
  String get enterYourBrandNameFeildError => 'أدخل اسم علامتك التجارية إذا لم يتم العثور عليه في القائمة';

  @override
  String get searchBrandByName => 'البحث عن العلامة التجارية بالاسم';

  @override
  String get colorIcon => 'لون أيقونة';

  @override
  String get colorFamilyFeildHint => 'سابق : أحمر';

  @override
  String get colorNameFeildLabel => 'إضافة اسم اللون';

  @override
  String get colorNameFeildHint => 'اسم اللون (أحمر فاتح ، أزرق داكن ...)';

  @override
  String get colorFamilyFeildReturnMessage => 'أعاد المشرف هذا اللون';

  @override
  String get selectColorFamily => 'اختر عائلة اللون';

  @override
  String get deleteImageAlertMesssgae => 'هل أنت متأكد من أنك تريد حذف هذه الصورة';

  @override
  String get adminRejectColorName => 'أعاد المشرف  اسم اللون';

  @override
  String get alertMessageForRemoveColor => 'هل أنت متأكد من أنك تريد إزالة هذا اللون';

  @override
  String get alertMessageBeforeDiscardProductChanges => 'هل أنت متأكد أنك تريد إغلاق؟ جميع التغييرات سيتم حذفها .';

  @override
  String get discardChangesInProgress => 'حذف التغييرات قيد الإنجاز';

  @override
  String get saveInfo => 'حفظ معلومات؟';

  @override
  String get deleteProductFromCache => 'حذف بيانات المنتج من ذاكرة التخزين المؤقت';

  @override
  String get albums => 'الألبومات';

  @override
  String get invalidImageFormate => 'صيغة صورة غير صالحة';

  @override
  String get confirmSlection => 'تأكيد الاختيار';

  @override
  String get imageCropper => 'صورة كروبر';

  @override
  String get pinchZoomInout => 'التكبير والتصغير بالإصبع';

  @override
  String get maxNumberItemsPerOrder => 'الكمية المسموحة  لكل طلب';

  @override
  String get itemPerOrderQuantityLessAvailable => 'يجب أن يكون أقل من الكمية المتاحة';

  @override
  String get zeroNotAcceptable => 'صفر غير مقبول';

  @override
  String get itemPerOrderFeildLabel => 'البنود';

  @override
  String get itemPerOrderFeildHint => 'سابق : 2';

  @override
  String get specFeildLabelEnglish => 'العنوان (الإنجليزية)';

  @override
  String get specFeildLabelArabic => 'العنوان (العربية)';

  @override
  String get cityFeildHint => 'سابق : دبي';

  @override
  String get updateDraftProduct => 'تحديث مشروع المنتج';

  @override
  String get alertMessageBeforeDeleteProduct => 'هل أنت متأكد أنك تريد حذف هذا المنتج؟';

  @override
  String get resetProductWillDiscardChanges => 'إعادة تعيين المنتج سيتم  من جميع التغييرات التي قمت بتطبيقها';

  @override
  String get kindlyAddBankAccount => 'يرجى إضافة معلوماتك المصرفية للحصول على مستحقاتك المالية';

  @override
  String get itemPerOrderQuantityLessVariantAvailable => 'يجب أن يكون أقل من كمية الاختيار المتاحة';

  @override
  String get adminRejectShipmentFitErrorMessage => '! أنت لم تقم بتحديث الشحنة تناسب ملاحظة المشرف: هذا المنتج لا يناسب هذا الحجم من الشحنة التي اخترتها';

  @override
  String get youRemoveProductName => 'أنت حذفت اسم المنتج من المعلومات';

  @override
  String get productNameNotProvided => 'اسم المنتج لم يتم إدخاله';

  @override
  String get youRemoveProductDescription => 'قمت بإزالة وصف المنتج من المعلومات';

  @override
  String get productDescriptionNotProvided => 'وصف المنتج لم يتم إدخاله';

  @override
  String get youRemoveProductGtin => 'أنت حذفت gtin المنتج من المعلومات';

  @override
  String get productGtinNotProvided => 'المنتج Gtinلم يتم إدخال معرف ';

  @override
  String get youRemoveProductCategory => 'أنت حذفت فئة المنتج من المعلومات';

  @override
  String get productCategoryNotProvided => 'فئة المنتج لم يتم اختيارها';

  @override
  String get youRemoveProductBrand => 'أنت حذفت العلامة التجارية للمنتج من المعلومات';

  @override
  String get productBrandNotProvided => 'العلامة التجارية للمنتج غير المقدمة';

  @override
  String get backToShop => 'العودة إلى المتجر';

  @override
  String get editShop => 'تحرير متجر';

  @override
  String get badgeProductHidden => 'المنتج مخفي';

  @override
  String get begdeModifyRequest => 'تعديل\nطلب';

  @override
  String get alertShopDiscardChanges => 'هل أنت متأكد أنك تريد تجاهل تغييرات معلومات المتجر؟';

  @override
  String get productDetails => 'تفاصيل المنتج';

  @override
  String get hideThisItem => 'إخفاء هذا المنتج';

  @override
  String get alertMessageForhideItem => 'لن يكون المنتج مرئيًا للعميل';

  @override
  String get restoreLastSubmission => 'هل أنت متأكد من أنك تريد استعادة آخر طلب لك؟';

  @override
  String get restoreLastChanges => 'استعادة التغييرات الأخيرة';

  @override
  String get restoreLastChangesAlertMessage => 'سيعود المنتج إلى الإصدار الأخير المقبول وسيتم التخلص من جميع التغييرات';

  @override
  String get restoreProduct => 'استعادة المنتج';

  @override
  String get editProduct => 'تحرير المنتج';

  @override
  String get change => 'تغيير >';

  @override
  String get deleteItemSaveAlert => 'يتم حفظ العناصر المحذوفة لمدة 30 يوما في الملف الشخصي';

  @override
  String get editPendingProductNotAllowed => 'لا يسمح بتعديل المنتج في حالة الانتظار';

  @override
  String get informationTitleEnglish => 'معلومات ( الإنجليزية )';

  @override
  String get informationTitleArabic => 'معلومات ( العربية )';

  @override
  String get shipmentTitleEnglish => 'شحنة ( الإنجليزية )';

  @override
  String get shipmentTitleArabic => 'شحنة ( العربية )';

  @override
  String get policesTitleEnglish => 'السياسات ( الإنجليزية )';

  @override
  String get policesTitleArabic => 'السياسات ( العربية )';

  @override
  String get detailsTitleEnglish => 'التفاصيل ( الإنجليزية )';

  @override
  String get detailsTitleArabic => 'التفاصيل ( العربية )';

  @override
  String get pending => 'قيد الانتظار';

  @override
  String get returnedCost => 'التكلفة المرجعة';

  @override
  String get sellerPayDeliveryCost => 'البائع دفع التكلفة المحسوبة.';

  @override
  String get customerPayDeliveryCost => 'يدفع العميل التكلفة المحسوبة';

  @override
  String get notFreeForCustomer => 'ليس مجاناً للعميل';

  @override
  String get productReturned => 'عودة المنتج';

  @override
  String get requiredUpdateOnSpec => 'Oops! يبدو أنك لم تقم بتحديث مواصفات عودة المنتج بعد والتحقق من تفاصيل العودة الأخرى';

  @override
  String get aleartCompeleteEditSpec => 'لم تكمل التعديل. يرجى الانتهاء من التعديل أولا.';

  @override
  String get returnSpecHighlighted => 'جميع المواصفات المسلطة الضوء عليها قد عاد من قبل المشرف';

  @override
  String get specTitleEnglish => 'العنوان ( الإنجليزية )';

  @override
  String get specTitleArabic => 'العنوان ( العربية )';

  @override
  String get specTitleFeildHint => 'حجم الشاشة';

  @override
  String get specValueEnglish => 'القيمة ( الإنجليزية )';

  @override
  String get specValueArabic => 'القيمة ( العربية )';

  @override
  String get specValueFeildHint => '4.7';

  @override
  String get unit => 'الوحدة';

  @override
  String get adminnRetuenSizeUnit => 'أعاد المشرف وحدة المقاس';

  @override
  String get alertMessageForDelete => 'هل أنت متأكد من أنك تريد حذف';

  @override
  String get allHideAleartMessage => 'لا يمكنك إخفاء جميع الاختيارات';

  @override
  String get hideSizeAlertMessgae => 'إخفاء الحجم لن يظهر هذا الحجم للعملاء';

  @override
  String get deleteSizeAlertMessage => 'هل أنت متأكد من أنك تريد حذف هذا الحجم';

  @override
  String get updateReturnSizeAlert => 'Oops! أنت لم تقم بتحديث وحدة الحجم';

  @override
  String get waitVariationLoading => 'تم تحميل اختيارات  المنتج';

  @override
  String get hide => 'إخفاء';

  @override
  String get show => 'عرض';

  @override
  String get gtin => 'GTIN';

  @override
  String get productOptions => 'خيارات المنتج';

  @override
  String get pleaseUploadImage => 'يرجى تحميل الصور';

  @override
  String get aleartMessageOnHideVariant => 'إخفاء الاختيار لن يظهر هذا الاختيار للعملاء';

  @override
  String get colorName => 'اسم اللون';

  @override
  String get hideAllVariantAlertMessage => 'لإخفاء جميع اختيارات المنتج ،  يمكنك ببساطة إخفاء المنتج. بدلاً من ذلك ، يجب عليك إما تعيين كمية جميع الاختيارات إلى 0 أو إدارة رؤيتها بشكل فردي.';

  @override
  String get youCanNotHideAllColor => 'لا يمكنك إخفاء جميع الألوان';

  @override
  String get hideColorNotVisible => 'إخفاء اللون لن يظهر هذا اللون للعملاء';

  @override
  String get haventUpdateColor => ' أنت لم تقم بتحديث الألوان';

  @override
  String get colorFamily => 'عائلة اللون';

  @override
  String get registration => 'التسجيل';

  @override
  String get returnedByAdmin => 'تم ارجاعه  من قبل المشرف';

  @override
  String get submittedByAdmin => 'تم التقديم بواسطه المشرف';

  @override
  String get acceptedByAdmin => 'مقبولة من قبل المشرف';

  @override
  String get whatsappNotInstalled => 'لا يتم تثبيت Whatsapp على الجهاز';

  @override
  String get contactUs => 'اتصل بنا';

  @override
  String get fixProblemOrContactUs => ' يمكنك إصلاح مشكلتك بسرعة هنا أو يمكنك';

  @override
  String get updateName => 'تحديث الاسم';

  @override
  String get userNameInUse => 'اسم المستخدم في الاستخدام: اختر آخر. هذا واحد اتخذ';

  @override
  String get pleaseEnterDigitalCode => 'يرجى إدخال الرمز الرقمي الذي أرسلناه';

  @override
  String get emailClaimed => ' ادعى البريد الإلكتروني. اختر واحدة جديدة!';

  @override
  String get pleaseEnterCompeleteCode => 'يرجى إدخال الرمز كامل';

  @override
  String get mobileNumberChanged => 'تم تغيير رقم الجوال بنجاح';

  @override
  String get passwordCharaterLimit => 'يجب أن تحتوي كلمة المرور على الأقل على 6 حرف';

  @override
  String get passwordFeildHint => 'السابق: 123456';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get confirmPass => 'تأكيد كلمة المرور';

  @override
  String get currentPassword => 'كلمة المرور الحالية';

  @override
  String get updateBussinessInfoAlert => 'تحديث معلومات النشاط التجاري سوف يخفي متجرك حتى يقبل المشرف التغييرات الجديدة ، هل أنت متأكد من أنك تريد الاستمرار؟';

  @override
  String get bussinessNameFeildHint => 'سابق : Aroundix';

  @override
  String get bussinessNameFeildLabel => 'اسم الأعمال';

  @override
  String get bussinessNameFeildReturnMessage => 'اسم النشاط التجاري تك إرجاعه من قبل المشرف';

  @override
  String get bussinessOwnerNameFeildLabel => 'اسم المالك / المدير';

  @override
  String get bussinessOwnerNameFeildHint => 'سابق : خالد موه';

  @override
  String get bussinessOwnerNameFeildHintReturnMessae => 'تم إرجاع اسم المالك من قبل المشرف';

  @override
  String get pleaseUploadColorImage => 'يرجى تحميل صور اللون';

  @override
  String get addNewColorName => 'إضافة اسم لون جديد لهذه العائلة اللون';

  @override
  String get pleaseSelectColorFamily => 'يرجى اختيار لون الأسرة';

  @override
  String get colorNameAlreadyExist => 'اسم اللون موجود بالفعل لهذه الأسرة اللون';

  @override
  String get alertMessageDeleteOption => 'هل أنت متأكد من أنك تريد حذف هذا الخيار';

  @override
  String get customOptionFeildHint => 'السابق: حجم الذاكرة';

  @override
  String get customOptionFeildLabelEnglish => 'العنوان ( الإنجليزية )';

  @override
  String get customOptionFeildLabelArabic => 'العنوان ( العربية )';

  @override
  String get customOptionFeildErrorValidTitle => 'الرجاء إدخال عنوان صالح';

  @override
  String get customOptionFeildTitleAlreadyExsist => 'عنوان الخيار بالفعل في القائمة';

  @override
  String get customOptionAdminReturnTitle => 'المشرف أعاد  هذا العنوان';

  @override
  String get addNewOption => 'إضافة خيار جديد';

  @override
  String get youHaveAlreadyAddedThisOption => 'لقد أضفت بالفعل عنوان الخيار هذا إلى خيار المنتج الخاص بك.';

  @override
  String get errorAtleastAddtwoValues => 'يرجى إدخال قيمتين على الأقل لإضافة صف جديد';

  @override
  String get errorAtleastAddtwoValue => 'الرجاء إدخال ما لا يقل عن اثنين من القيم';

  @override
  String get somethingWrongOnOption => 'شيء خاطئ في هذا الخيار';

  @override
  String get updateThisOptionFirst => 'تحديث هذا أولا ثم تعديل الآخرين';

  @override
  String get hideOptionAlertMessgae => 'إخفاء الخيار لن تظهر هذا الخيار للعملاء';

  @override
  String get valueAlreadyInList => 'القيمة بالفعل في القائمة';

  @override
  String get customOptionValueFeildLabelEnglish => 'القيمة ( الإنجليزية )';

  @override
  String get customOptionValueFeildLabelArabic => 'القيمة ( العربية )';

  @override
  String get noSalesFound => 'لم يتم العثور على معلومات مبيعات';

  @override
  String get shipmentCode => 'رمز الشحن';

  @override
  String get transfer => 'نقل';

  @override
  String get amountLessThenAvailable => 'يجب أن يكون المبلغ أقل من المبلغ المتاح';

  @override
  String get pleaseEnterAmount => 'يرجى إدخال المبلغ';

  @override
  String get available => 'متوفر';

  @override
  String get deliveryFee => 'رسوم التسليم';

  @override
  String get address => 'العنوان';

  @override
  String get location => 'الموقع';

  @override
  String get mapLocation => 'موقع الخريطة';

  @override
  String get number => 'العدد';

  @override
  String get landNumber => 'رقم الهاتف الثابت';

  @override
  String get customerContact => 'الاتصال بالعملاء';

  @override
  String get contactDetailsInfo => 'سيتم مشاركة تفاصيل الاتصال مع العملاء بعد تقديم الطلبات';

  @override
  String get alertMessageDeleteOptionValue => 'هل أنت متأكد من أنك تريد حذف قيمة الخيار هذه';

  @override
  String get emailAddress => 'عنوان البريد الإلكتروني';

  @override
  String get orderedIn => 'أمر في';

  @override
  String get invoiceSummery => 'ملخص الفاتورة';

  @override
  String get shipmentInfo => 'معلومات الشحن';

  @override
  String get variantFieldsUpdated => 'حقول متنوعة تحديث';

  @override
  String get alertVariantPriceQty => 'تم تطبيق السعر  والكمية الافتراضيين على حقول الاختيارات الفارغة.';

  @override
  String get alertOnPriceReturn => 'قام  المشرف بإرجاع هذا السعر';

  @override
  String get alertOnQtyReturn => 'قام  المشرف بإرجاع كمية المنتج هذه';

  @override
  String get applyToAllVariant => 'تنطبق على جميع الاختيارات';

  @override
  String get alertMessageUpdateCCustomOption => '! لم تقم بتحديث الخيارات المخصصة';

  @override
  String get returnCost => 'تكلفة العودة';

  @override
  String get youRemovedProductAcceptReturnPolicies => 'أنت حذفت المنتج قبول العودة من السياسات';

  @override
  String get productAcceptReturnNotProvided => 'قبول المنتج العودة لا تقدم';

  @override
  String get youRemovedProductFreeDeliveryPolicies => 'أنت حذفت المنتج من لائحة التوصيل المجاني';

  @override
  String get productForFreeDeliveryNotProvided => 'التوصيل المجاني لهذا المنتج غسر متاح ';

  @override
  String get errorCacheValueReturnDuration => 'أنت حذفت مدة إرجاع المنتج من السياسات';

  @override
  String get errorServerValueReturnDuration => 'مدة إرجاع المنتج لم يتم إدخالها';

  @override
  String get errorCacheValueWarrantyDuration => 'أنت حذفت مدة ضمان المنتج من السياسات';

  @override
  String get errorServerValueWarrantyDuration => 'مدة ضمان المنتج لم يتم إدخالها';

  @override
  String get errorCacheValuePolicies => 'أنت حذفت سياسة المنتج من السياسات';

  @override
  String get errorServerValuePolicies => 'سياسة المنتج لم يتم إدحالها';

  @override
  String get productPrice => 'سعر المنتج';

  @override
  String get errorCacheValuePrice => 'أنت حذفت سعر المنتج من التفاصيل';

  @override
  String get errorServerValuePrice => 'سعر المنتج لم يتم إدخاله';

  @override
  String get errorCacheValueQty => 'أنت حذفت كمية المنتج من التفاصيل';

  @override
  String get errorServerValueQty => 'لم يتم إدحال الكمية';

  @override
  String get errorCacheValueMinQty1 => 'يمكنك إيقاف الحد الأدنى من تنبيه الكمية';

  @override
  String get errorCacheValueMinQty => 'أنت حذفت الكمية المنتج الحد الأدنى من التفاصيل';

  @override
  String get errorServerValueMinQty => 'لم يتم إدخال  الحد الأدنى للكمية ';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get selectLanguageDetail => 'هل تريد تغيير لغة التطبيق؟ يمكنك التبديل بين اللغة الإنجليزية والعربية للحصول على تجربة أفضل.';

  @override
  String get agreeTermsAndCondition => 'من خلال إنشاء حساب، فإنك توافق على الشروط والأحكام ';

  @override
  String get phoneNumberAlreadyInuse => 'رقم الهاتف قيد الاستخدام بالفعل';

  @override
  String get signupSuccessMessage2 => 'شكرا لاختيار تطبيق الباب';

  @override
  String get signupSuccessMessage1 => 'طلبك الآن قيد المراجعة. يرجى التحقق من البريد الإلكتروني الخاص بك للحصول على الحالة في غضون 24 ساعة';

  @override
  String get verifyEmailAddress => 'التحقق من عنوان البريد الإلكتروني';

  @override
  String get resetYourPassword => 'إعادة تعيين كلمة المرور الخاصة بك';

  @override
  String get verifyYourEmail => 'تحقق من بريدك الإلكتروني';

  @override
  String get to => 'إلى';

  @override
  String get weSentOtp => 'لقد أرسلنا كلمة مرور لمرة واحدة (OTP) إلى بريدك الإلكتروني';

  @override
  String get mobileNumber => 'رقم الهاتف المحمول';

  @override
  String get aleartFillSpecFeild => 'يرجى الانتظار بينما نترجم';

  @override
  String get aleartOnRemoveSpec => 'هل أنت متأكد من أنك تريد إزالة هذه المواصفات';

  @override
  String get readySpecTranslation => 'الترجمة جاهزة للإضافة';

  @override
  String get colorNameEnglish => 'اسم اللون ( الإنجليزية )';

  @override
  String get colorNameArabic => 'اسم اللون ( العربية )';

  @override
  String get aleartRememberBe => 'أدخل بيانات اعتمادك ثم قم بتسجيل الدخول لتذكر بياناتك للمرة القادمة';

  @override
  String get updateTradeCertiificateDate => 'تجديذ الرخصة التجارية';

  @override
  String get tradeCerficateExpireDate => 'تاريخ انتهاء صلاحية الرخصة التجارية';

  @override
  String get uploadTradeCertificate => 'تحميل الرخصة التجارية';

  @override
  String get tradeLicenseReturned => 'تم إرجاع الرخصة التجارية';

  @override
  String get uploadEmiratesID => 'تحميل الهوية الإماراتية';

  @override
  String get ownerIDReturned => 'تم إرجاع هوية المالك';

  @override
  String get adminNote => 'ملاحظة المشرف:';

  @override
  String get updateEmiratesIDDate => 'تحديث تاريخ الهوية الإماراتية';

  @override
  String get emiratesIDExpiryDate => 'تاريخ انتهاء صلاحية الهوية الإماراتية';

  @override
  String get aleartUploadTradeCertificate => 'يرجى تحديث الرخصة التجارية الخاصة بك';

  @override
  String get aleartUploadId => 'يرجى تحديث الرقم الإماراتي الخاص بك';

  @override
  String get updateShop => 'تحديث متجر';

  @override
  String get aleartOnStopShopEdit => 'هل أنت متأكد من أنك تريد التوقف عن التعديل';

  @override
  String get rejectTargetPrice => 'أغاد  المشرف هذا السعر المستهدف';

  @override
  String get uploadTermsAndCondiition => 'تحميل الشروط والأحكام ';

  @override
  String get businessSloganFeildLabel => 'شعار الأعمال';

  @override
  String get businessSloganFeildHint => 'سابق : ابحث عن كل شيء هنا';

  @override
  String get businessSloganFeildAdminReject => 'أعاد المدير هذا الشعار';

  @override
  String get businessDiscriiptionFeildAdminReject => 'أعاد المشرف هذا الوصف';

  @override
  String get addBranchNumber => 'إضافة رقم الفرع';

  @override
  String get verificationCodeSent => 'لقد أرسلنا الرمز التحقق إلى';

  @override
  String get branchPhoneNumberOtp => 'أضف رقم هاتفك. سنرسل لك رمز التحقق حتى نعلم أنك حقيقي';

  @override
  String get numberAlreadyUse => 'الرقم المستخدمة بالفعل على فرع آخر';

  @override
  String get changePhoneNumber => 'تغيير رقم الهاتف؟';

  @override
  String get waitFechingCities => 'انتظر نحن نحمل قائمة المدن';

  @override
  String get pickupCity => 'مدينة دبي';

  @override
  String get adminReturned => 'أعاد المشرف';

  @override
  String get pickupLocation => 'موقع الشحن';

  @override
  String get pleaseAddLocation => 'يرجى إضافة موقع عنوان الاستلام';

  @override
  String get landNumberFeildlabel => 'رقم الأرضي';

  @override
  String get landNumberFeildHint => 'سابق : 05xxxxxxxxx';

  @override
  String get landNumberFeildAdminReject => 'أعاد المدير رقم الهاتف الثابت هذا';

  @override
  String get contactFeildlabel => 'رقم الهاتف';

  @override
  String get contactFeildHint => 'سابق : 05xxxxxxxxx';

  @override
  String get pickupContactNumber => 'رقم الاتصال للشحن';

  @override
  String get verified => 'التحقق';

  @override
  String get pickupFeildlabel => 'العنوان';

  @override
  String get pickupFeildHint => 'سابق : متجر A-23 ، برج 03 شارع خالد';

  @override
  String get pickupFeildAdminReject => 'عنوان الاستلام';

  @override
  String get setYourLocation => 'تعيين موقعك';

  @override
  String get selectPinLocation => 'اختر موقع بواسطة الدبوس';

  @override
  String get addAnotherAddress => 'إضافة عنوان آخر للشحن';

  @override
  String get branchAlreadyAssign => 'فرع تعيين بالفعل لبعض الطلبات';

  @override
  String get aleartOnDeleteAddress => 'هل أنت متأكد من أنك تريد حذف غنوان الشحن هذا';

  @override
  String get adminRejectPhoneNumber => 'أعاد المدير رقم الهاتف هذا';

  @override
  String get adminRejectWhatsapppNumber => 'أعاد المدير  رقم الواتساب هذا';

  @override
  String get whatsappNumber => 'رقم الواتساب';

  @override
  String get adminRejectContactEmail => 'أعاد المدير البريد الإلكتروني  هذا';

  @override
  String get discardAllChanges => 'تجاهل  جميع التغييرات';

  @override
  String get days => 'أيام';

  @override
  String get selectCategoryFirst => 'يرجى اختيار الفئة أولا';

  @override
  String get errorAddTitleForCustomOption => 'يرجى إدخال العنوان أو إزالة الخيار المخصص فارغة';

  @override
  String get errorEmptyDetsilsOnAddOption => 'الرجاء إدخال العنوان مع اثنين على الأقل من القيم ';

  @override
  String get accountSettings => 'إعدادات الحساب';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get dataManagement => 'إدارة البيانات';

  @override
  String get photos => 'الصور';

  @override
  String get pickupLocations => 'مواقع الاستلام';

  @override
  String get pleaseEnterBranchNumber => 'يرجى إدخال رقم الفرع';

  @override
  String get personalInformation => 'المعلومات الشخصية';

  @override
  String get security => 'الأمان';

  @override
  String get noIncomeYet => 'لا يوجد دخل بعد';

  @override
  String get salesTrend => 'اتجاه المبيعات';

  @override
  String helloSupplier(Object storeName) {
    return 'مرحبًا $storeName!';
  }

  @override
  String addedImages(Object imagesLength) {
    return '( $imagesLength / 5) صور';
  }

  @override
  String dynamicPrice(Object price) {
    return 'درهم $price';
  }

  @override
  String dynamicPieceCount(Object count) {
    return 'قطعة $count';
  }

  @override
  String productTab(Object count) {
    return 'المنتجات ($count)';
  }

  @override
  String pendingTab(Object count) {
    return 'قيد الانتظار ($count)';
  }

  @override
  String draftTab(Object count) {
    return 'مسودات ($count)';
  }

  @override
  String qtyAlertTab(Object count) {
    return ' كمية قليلة ($count)';
  }

  @override
  String outOfStocTab(Object count) {
    return 'غير متوفر ($count)';
  }

  @override
  String variantsTab(Object count) {
    return 'الاختيارات ($count)';
  }

  @override
  String followersCount(Object count) {
    return '$count المتابعين';
  }

  @override
  String productReturnMessage(Object returnMessage) {
    return 'ملاحظات مدير: $returnMessage\nتحقق من feilds المرجعي';
  }

  @override
  String variantSize(Object variantSizeUnit) {
    return 'المقاس ( $variantSizeUnit ) : ';
  }

  @override
  String adminReturn(Object returnValue) {
    return 'المشرف أعاد  ( $returnValue ) : ';
  }

  @override
  String pickUpCity(Object pickUpCity) {
    return ' $pickUpCity ';
  }

  @override
  String emailChangeSuccessfully(Object email) {
    return 'تم تحديث بريدك الإلكتروني بنجاح إلى: $email ';
  }

  @override
  String pendingOrderTab(Object count) {
    return 'لم تتم معالجتها $count ';
  }

  @override
  String inProgressOrderTab(Object count) {
    return 'قيد المعالجة $count ';
  }

  @override
  String historyOrderTab(Object count) {
    return 'السجل $count ';
  }

  @override
  String newOrderTab(Object count) {
    return 'جديد $count ';
  }

  @override
  String newReurnOrderTab(Object count) {
    return 'طلب إعادة  $count ';
  }

  @override
  String waitingOrderTab(Object count) {
    return 'في انتظار الشحن $count ';
  }

  @override
  String shippedOrderTab(Object count) {
    return 'تم شحنها $count ';
  }

  @override
  String deliverdOrderTab(Object count) {
    return 'تم تسليمها $count ';
  }

  @override
  String cancelledOrderTab(Object count) {
    return 'إلغاء $count ';
  }

  @override
  String returnedOrderTab(Object count) {
    return 'المعادة $count ';
  }

  @override
  String secondRemaing(Object count) {
    return 'الثواني المتبقية لإعادة إرسال رمز التحقق $count ';
  }

  @override
  String removingCityBranch(Object city) {
    return ' إزالة فرع المدينة  ';
  }
}
