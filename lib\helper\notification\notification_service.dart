import 'dart:developer';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/login_model.dart';
import 'package:overolasuppliers/model/shop/ViewShopModel.dart';
import 'package:overolasuppliers/provider/order_status_provider.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:provider/provider.dart';

AndroidNotificationChannel channel = const AndroidNotificationChannel(
    'high_importance_channel', 'High Importance Notifications',
    description:
        'This channel is used for important notifications.', // description
    importance: Importance.high,
    playSound: true);

class NotificationService implements ServerResponse {
  NotificationService._internal();
  late GraphQlInitilize _request;

  static final NotificationService instance = NotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _started = false;

  void initilizeFireBase() {
    if (!_started) {
      _request = GraphQlInitilize(this);
      _integrateNotification();
      _refreshToken();
      _started = true;
    }
  }

  void _integrateNotification() {
    _registerNotification();
  }

  void _registerNotification() async {
    FirebaseMessaging.onBackgroundMessage((message) => _firebaseMessagingBackgroundHandler());

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    await FirebaseMessaging.instance.requestPermission(
        alert: true, badge: true, sound: true, provisional: false);

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      var notification = message.notification;
      var android = message.notification?.android;
      setNavigation(notification?.title ?? "", false);
      if (notification != null && android != null) {
        flutterLocalNotificationsPlugin.show(
            notification.hashCode,
            notification.title,
            notification.body,
            NotificationDetails(
              android: AndroidNotificationDetails(
                channel.id,
                channel.name,
                channelDescription: channel.description,
                playSound: true,
                icon: '@mipmap/ic_launcher',
              ),
            ));
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      var notification = message.notification;
      setNavigation(notification?.title ?? "", true);
    });
  }

  setNavigation(String title, bool isNotificationTap) {
    if (title.isNotEmpty) {
      if (title.toLowerCase() == "registration accepted" ||
          title.toLowerCase() == "registration returned") {
        if (prefs.getBool(requiredLogin) ?? false) {
          String email = prefs.getString(signupEmail) ?? "";
          String password = prefs.getString(signupPassword) ?? "";
          userAuthToken = '';
          _request.runMutation(
              context: MyApp.navigatorKey.currentContext!,
              query: GraphQlQuries.login,
              variables:
                  GraphQlVariables.login(email: email, password: password,lang: Get.locale?.languageCode ?? "en"));
        } else if (title.toLowerCase() == "registration returned") {
          if (isNotificationTap) {
            _request.runQuery(
              context: MyApp.navigatorKey.currentContext!,
              query: GraphQlQuries.getBusinessInfo,
              type: "businessInfo",
            );
          }
        }
      }
      if (title.toLowerCase() == "shop request returned") {
        Provider.of<UpdatedInfo>(MyApp.navigatorKey.currentContext!,
                listen: false)
            .shopStatusUpdate("Returned");
        if (isNotificationTap) {
          _request.runQuery(
              context: MyApp.navigatorKey.currentContext!,
              query: GraphQlQuries.viewShop,
              type: "SHOP");
        }
      } else if (title.toLowerCase() == "shop request accepted") {
        Provider.of<UpdatedInfo>(MyApp.navigatorKey.currentContext!,
                listen: false)
            .shopStatusUpdate("Accepted");
      }

      if (title.toLowerCase() == "order placed" ||
          title.toLowerCase() == "shipment canceled"||
          title.toLowerCase() == "item canceled") {
        Future.delayed(
            Durations.extralong4,
            () => {
                  Provider.of<OrderStatusProvider>(
                          MyApp.navigatorKey.currentContext!,
                          listen: false)
                      .orderRecive(
                          notificationOrderType:
                              NotificationOrderType.newOrders)
                });
      }
    }
  }

  Future<void> _firebaseMessagingBackgroundHandler() async {
    await Firebase.initializeApp();
  }

  _refreshToken() {
    _firebaseMessaging.getToken().then((token) async {
      firebaseDeviceToken = token ?? "";
    }, onError: _tokenRefreshFailure);
  }

  _tokenRefreshFailure(error) {
    log("FCM token refresh failed with error $error");
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == "businessInfo") {
      LoginModel model = LoginModel.fromJson(response);
      if (model.status == statusOK) {
        Get.toNamed(RouteNames.addBusinessInfoScreen,
            arguments: [supplierID, model.user?.supplier, true]);
      }
    } else if (type == "SHOP") {
      ViewShopModel model = ViewShopModel.fromJson(response);
      if (model.status == statusOK) {
        Get.toNamed(RouteNames.createShopScreen, arguments: [model.shop, true]);
      }
    } else {
      GlobalMethods.checkLoginStatus(LoginModel.fromJson(response));
    }
  }
}
