import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/provider/ConnectivityChangeNotifier.dart';
import 'package:overolasuppliers/provider/shop_info_provider.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ElbaabHomePageHeader extends StatelessWidget
    implements PreferredSizeWidget {
  @override
  Size get preferredSize => Size.fromHeight(80.h);

  final String? title;

  const ElbaabHomePageHeader({Key? key, this.title}) : super(key: key);

  String getSubTitle({required AppLocalizations appLocal}) {
    String strSubTitle = "";
    switch (GlobalMethods.getGreetingMsg()) {
      case "morning":
        strSubTitle = appLocal.morningMsg;
        break;
      case "noon":
        strSubTitle = appLocal.noonMsg;
        break;
      case "afternoon":
        strSubTitle = appLocal.afterNoonMsg;
        break;
      case "evening":
        strSubTitle = appLocal.eveningMsg;
        break;
      case "issue":
        strSubTitle = appLocal.greetTimeError;
        break;
      default:
    }
    return strSubTitle;
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.headerColorDark,
            AppColors.headerColorDark.withOpacity(0.95),
          ],
        ),
      ),
      child: SafeArea(
        child: Stack(
          children: [
            // Decorative circles
            Positioned(
              top: -20,
              right: -20,
              child: Container(
                height: 80.h,
                width: 80.w,
                decoration: BoxDecoration(
                  color: AppColors.colorPrimary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
              ),
            ),
            Positioned(
              bottom: -10,
              left: -10,
              child: Container(
                height: 50.h,
                width: 50.w,
                decoration: BoxDecoration(
                  color: AppColors.colorPrimary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
              ),
            ),
            
            // Main Content
            Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.r,
                    vertical: 8.r,
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Left side content
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              appLocal.helloSupplier(
                                  context.watch<ShopInfoProvider>().shopName),
                              style: TextStyle(
                                fontFamily: 'SF Pro Text',
                                fontSize: 18.sp,
                                color: AppColors.colorPrimary,
                                fontWeight: FontWeight.bold,
                                height: 1.2,
                              ),
                            ),
                            SizedBox(height: 2.h),
                            Text(
                              getSubTitle(appLocal: appLocal),
                              style: TextStyle(
                                fontFamily: 'SF Pro Display',
                                fontSize: 14.sp,
                                color: Colors.white.withOpacity(0.8),
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Notification bell
                      Container(
                        padding: EdgeInsets.all(6.r),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: InkWell(
                          onTap: () => Get.toNamed(RouteNames.notificationsScreen),
                          child: Badge(
                            backgroundColor: Provider.of<UpdatedInfo>(context)
                                        .unReadNotificationCount ==
                                    0
                                ? Colors.transparent
                                : AppColors.colorDanger,
                            label: Text(
                              "${Provider.of<UpdatedInfo>(context).unReadNotificationCount > 0 ? Provider.of<UpdatedInfo>(context).unReadNotificationCount > 9 ? "9+" : Provider.of<UpdatedInfo>(context).unReadNotificationCount : ""}",
                            ),
                            textStyle: FontStyles.fontSemibold(fontSize: 12),
                            textColor: Colors.white,
                            child: SvgPicture.string(
                              SvgStrings.iconBell,
                              colorFilter: ColorFilter.mode(
                                Colors.white.withOpacity(0.9),
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Connectivity Status
                SizedBox(
                  height: 20.h,
                  child: Consumer<ConnectivityChangeNotifier>(
                    builder: (context, connectivityChangeNotifier, child) {
                      return connectivityChangeNotifier.connectivity ==
                              ConnectivityResult.none
                          ? Container(
                              width: MediaQuery.of(context).size.width,
                              color: AppColors.colorDanger,
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.r,
                                vertical: 2.r,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.wifi_off_rounded,
                                    size: 12,
                                    color: Colors.white,
                                  ),
                                  SizedBox(width: 6.w),
                                  Text(
                                    "Your internet connection is lost",
                                    style: FontStyles.fontRegular(fontSize: 11),
                                  ),
                                ],
                              ),
                            )
                          : Container();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
