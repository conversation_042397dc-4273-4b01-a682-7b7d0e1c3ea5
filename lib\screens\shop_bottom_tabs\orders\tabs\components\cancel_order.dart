import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';
import 'package:overolasuppliers/provider/order_status_provider.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/controller/orders_controller.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/orders_loader.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/orders_row/cancel_order_row.dart';
import 'package:provider/provider.dart';

class CancelOrder extends StatefulWidget {
  const CancelOrder({Key? key}) : super(key: key);

  @override
  State<CancelOrder> createState() => _CancelOrderState();
}

class _CancelOrderState extends State<CancelOrder> implements ServerResponse {
  Rx<OrdersModel> ordersModel = OrdersModel().obs;

  late GraphQlInitilize _graphQlInitilize;

  RxList<OrderItems> arrOrders = RxList<OrderItems>([]);
  final controller = Get.find<OrdersController>();

  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _graphQlInitilize = GraphQlInitilize(this);
    scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) => getOrders());
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      if (ordersModel.value.ordersPaggination?.hasNextPage ?? false) {
        getOrders(
            page: (ordersModel.value.ordersPaggination?.page ?? 0) + 1,
            itemsNumber: 15,
            isRequiredLoader: true);
      }
    }
  }

  getOrders(
      {int page = 1, int itemsNumber = 15, bool isRequiredLoader = false}) {
    _graphQlInitilize.runQuery(
      context: context,
      isRequiredLoader: isRequiredLoader,
      query: GraphQlQuries.getOrderItems,
      variables: GraphQlVariables.getOrderPaginated(
        orderStatus: "Canceled",
        page: page,
        itemsNumber: itemsNumber,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (Provider.of<OrderStatusProvider>(context).notificationType ==
        NotificationOrderType.canceledOrders) {
      Future.delayed(Duration.zero, () {
        controller.getOrdersCount();
        arrOrders.clear();
        arrOrders.addAll(controller.dummyList);
        getOrders(isRequiredLoader: true);
        Provider.of<OrderStatusProvider>(context, listen: false)
            .orderRecive(notificationOrderType: NotificationOrderType.notFound);
      });
    }
    return RefreshIndicator(
      onRefresh: () async {
        controller.getOrdersCount();
        arrOrders.clear();
        arrOrders.addAll(controller.dummyList);
        getOrders(isRequiredLoader: true);
        return Future.value();
      },
      child: Obx(
        () => ordersModel.value.status != statusOK
            ? SingleChildScrollView(
                child: SizedBox(
                  height: Get.height - 200,
                  child: Center(
                    child: Text(
                      ordersModel.value.message ?? "",
                      style: FontStyles.fontSemibold(),
                    ),
                  ),
                ),
              )
            : ListView.builder(
                itemCount: arrOrders.length,
                physics: const AlwaysScrollableScrollPhysics(),
                controller: controller.scrollController,
                padding: const EdgeInsets.only(top: 16),
                itemBuilder: (context, index) {
                  return arrOrders[index].id == null
                      ? const OrdersLoader()
                      : CancelOrderRow(orderItems: arrOrders[index]);
                }),
      ),
    );
  }

  @override
  onError(error, String type) {
    ordersModel.value = OrdersModel.fromJson(error);
  }

  @override
  onSucess(response, String type) {
    ordersModel.value = OrdersModel.fromJson(response);
    if (ordersModel.value.status == statusOK) {
      arrOrders.removeWhere((element) => element.id == null);
      arrOrders.addAll(ordersModel.value.ordersPaggination?.items ?? []);
    }
  }
}
