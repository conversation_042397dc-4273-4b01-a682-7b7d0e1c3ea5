import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_name_widget.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AddNumber extends StatelessWidget with InputValidationUtil {
  final TextEditingController numberController;
  final String errorText;
  final bool isChangeNumber;
  const AddNumber(
      {Key? key,
      required this.numberController,
      required this.errorText,
      this.isChangeNumber = false})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    final appLocal =  AppLocalizations.of(context)!;
    return Padding(
      padding: const EdgeInsets.all(16.0).r,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isChangeNumber) const ElbaabNameWidget(),
          if (!isChangeNumber)  SizedBox(height: 30.h),
          if (!isChangeNumber)
            Text(appLocal.addMobileNumber,
                style: FontStyles.fontSemibold(fontSize: 25)),
          if (!isChangeNumber)
            Text(
              appLocal.enterAMobileNumberToSafeguardYourAccount,
              style: FontStyles.fontRegular(
                color: Colors.white.withOpacity(0.55),
              ),
            ),
          const SizedBox(height: 30),
          PhoneFieldHint(
            decoration: InputDecoration(
              fillColor: AppColors.headerColorDark,
              filled: true,
              errorText: errorText == '' ? null : errorText,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(
                  width: 1,
                  color: AppColors.feildBorderColor,
                ),
              ),
              prefix: Text(
                "+971 ",
                style: FontStyles.fontRegular(),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(
                  width: 1,
                  color: Colors.white,
                ),
              ),
              hoverColor: AppColors.colorBlue,
              errorStyle: FontStyles.fontRegular(color: AppColors.colorDanger),
              hintStyle: FontStyles.fontRegular(
                fontSize: 12,
                color: Colors.white.withOpacity(0.5),
              ),
              hintText: '5xxxxxxxx',
              labelText: appLocal.mobileNumber,
              labelStyle: FontStyles.fontRegular(),
            ),
            controller: numberController,
          ),
        ],
      ),
    );
  }
}
