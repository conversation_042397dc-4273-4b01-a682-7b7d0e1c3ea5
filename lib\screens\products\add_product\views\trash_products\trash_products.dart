import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/other/shimmer.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/product_by_shop_model.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:overolasuppliers/widgets/elbaab_border_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/shop/product_listile.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class TrashProducts extends StatefulWidget {
  const TrashProducts({super.key});

  @override
  State<TrashProducts> createState() => _TrashProductsState();
}

class _TrashProductsState extends State<TrashProducts>
    implements ServerResponse {
  RxBool haveProducts = false.obs;

  late GraphQlInitilize _request;

  ProductByShopModel? productByShopModel;

  RxList<ShopProducts> arrProducts = <ShopProducts>[
    ShopProducts(),
    ShopProducts(),
    ShopProducts(),
    ShopProducts(),
    ShopProducts(),
    ShopProducts(),
  ].obs;

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _request.runQuery(
          context: context,
          query: GraphQlQuries.getDeletedProducts,
          type: "Products",
          isRequiredLoader: false,
          variables: GraphQlVariables.getProducts(
              itemsNumber: 10, categoryId: "", subCategoryId: ""));
    });
  }

  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: ElbaabHeader(
          title: appLocalizations.deleteProduct, leadingBack: true),
      body: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification sn) {
          if (!(arrProducts.any((element) => element.id == null)) &&
              sn is ScrollUpdateNotification &&
              sn.metrics.pixels <= sn.metrics.maxScrollExtent - 100) {
            if (productByShopModel?.shopProductPaggination?.hasNextPage ??
                false) {
              arrProducts.addAll([
                ShopProducts(),
                ShopProducts(),
                ShopProducts(),
                ShopProducts(),
                ShopProducts(),
                ShopProducts()
              ]);
              _request.runQuery(
                  context: context,
                  query: GraphQlQuries.getDeletedProducts,
                  type: "Products",
                  isRequiredLoader: false,
                  variables: GraphQlVariables.getProducts(
                      itemsNumber: 6,
                      page:
                          ((productByShopModel?.shopProductPaggination?.page ??
                                  0) +
                              1)));
            }
          }
          return true;
        },
        child: Obx(
          () => haveProducts.value
              ? Center(
                  child: Lottie.network(
                      "https://assets2.lottiefiles.com/packages/lf20_lkjtr2yj.json"),
                )
              : GridView.builder(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisSpacing: 10,
                      mainAxisSpacing: 10,
                      childAspectRatio:
                          (MediaQuery.of(context).size.width / 2 / 430),
                      crossAxisCount: 2),
                  itemCount: arrProducts.length,
                  itemBuilder: (contex, index) {
                    return arrProducts[index].id == null
                        ? Shimmer.fromColors(
                            baseColor:
                                AppColors.headerColorDark.withOpacity(0.5),
                            highlightColor:
                                AppColors.colorPrimary.withOpacity(0.5),
                            child: Container(
                              margin: EdgeInsets.only(
                                  left: (!(index % 2 == 1)) ? 10 : 0,
                                  right: (index % 2 == 1) ? 10 : 0),
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ))
                        : Column(
                            children: [
                              SizedBox(
                                height: 330,
                                child: Stack(
                                  children: [
                                    Positioned.fill(
                                      child: ProductListTile(
                                        product: arrProducts[index],
                                        onTap: (v) {},
                                        width:
                                            MediaQuery.of(context).size.width /
                                                2,
                                        requiredPageView: (arrProducts[index]
                                                        .productOptions
                                                        ?.productColors ??
                                                    [])
                                                .isEmpty
                                            ? true
                                            : false,
                                      ),
                                    ),
                                    Positioned(
                                      right: 0,
                                      child: IconButton(
                                        icon: Icon(
                                          Icons.delete,
                                          color: AppColors.colorDanger,
                                        ),
                                        onPressed: () {
                                          Alerts.alertView(
                                              context: context,
                                              content:
                                                  "Are you sure you want to delete permanently",
                                              cancelAction: () => Get.back(),
                                              action: () {
                                                Get.back();
                                                _request.runQuery(
                                                    context: context,
                                                    query: GraphQlQuries
                                                        .deleteProductPermanentlyById,
                                                    variables: GraphQlVariables
                                                        .restoreProduct(
                                                            productId:
                                                                arrProducts[index]
                                                                        .id ??
                                                                    ""),
                                                    type: "$index");
                                              },
                                              cancelActionText: appLocalizations.no);
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8),
                                child: ElbaabBorderButtonWidget(
                                    onPress: () {
                                      Alerts.alertView(
                                          context: context,
                                          title: appLocalizations.alert,
                                          content:
                                              "Are you sure you want to restore this product",
                                          defaultActionText: appLocalizations.yes,
                                          action: () => {
                                                Get.back(),
                                                _request.runMutation(
                                                    context: context,
                                                    query: GraphQlQuries
                                                        .restoreProduct,
                                                    variables: GraphQlVariables
                                                        .restoreProduct(
                                                            productId:
                                                                arrProducts[index]
                                                                        .id ??
                                                                    ""),
                                                    type: "$index")
                                              },
                                          cancelAction: () => Get.back(),
                                          cancelActionText: appLocalizations.no);
                                    },
                                    text: "Restore Product"),
                              )
                            ],
                          );
                  }),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {
    if (type != "Products") {
      ScaffoldMessenger.of(Get.context!).showSnackBar(
        SnackBar(
          backgroundColor: AppColors.headerColorDark,
          content: Text(
            BaseModel.fromJson(error).message ?? "",
            style: FontStyles.fontMedium(fontSize: 12),
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      haveProducts.value = true;
    }
  }

  @override
  onSucess(response, String type) {
    if (type == "Products") {
      log("onSucess response: $response");
      productByShopModel = ProductByShopModel.fromJson(response);
      if (productByShopModel?.status == statusOK) {
        arrProducts.removeWhere((element) => element.id == null);
        arrProducts
            .addAll(productByShopModel?.shopProductPaggination?.items ?? []);
      } else {
        haveProducts.value = true;
      }
    } else {
      BaseModel model = BaseModel.fromJson(response);
      if (model.status == statusOK) {
        Provider.of<UpdatedInfo>(Get.context!, listen: false)
            .isProductRemoveFromTrash(true);
        // arrProducts.removeAt(int.parse(type));
        arrProducts.clear();
        arrProducts.addAll([
          ShopProducts(),
          ShopProducts(),
          ShopProducts(),
          ShopProducts(),
          ShopProducts(),
          ShopProducts()
        ]);
        _request.runQuery(
            context: context,
            query: GraphQlQuries.getDeletedProducts,
            type: "Products",
            isRequiredLoader: false,
            variables: GraphQlVariables.getProducts(
                itemsNumber: 10, categoryId: "", subCategoryId: ""));
      }
      if (arrProducts.isEmpty) {
        haveProducts.value = true;
      }
    }
  }
}
