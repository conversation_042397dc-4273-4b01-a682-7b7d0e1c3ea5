import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/support_contact_model.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ElbaabSupportContact extends StatelessWidget implements ServerResponse {
  final double screenWidth = Get.width;
  final double screenHeight = Get.height;
  Rx<Support> support = Support().obs;
  final Support? suppot;

  ElbaabSupportContact({
    super.key,
    this.suppot,
  });
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    Future.delayed(
        Duration.zero,
        () => GraphQlInitilize(this).runQuery(
            context: context, isRequiredLoader: false, query: GraphQlQuries.getSupportDetails));

    return Scaffold(
      // backgroundColor: Colors.grey[100],
      appBar: ElbaabHeader(
        title: appLocal.support,
        leadingBack: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              height: screenHeight * 0.2,
              decoration:  BoxDecoration(
                color: AppColors.headerColorDark,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
              ),
              child: Stack(
                children: [
                  Positioned(
                    right: -30,
                    top: -30,
                    child: Container(
                      width: 100.w,
                      height: 100.h,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          appLocal.howCanWeHelpYou,
                          style: FontStyles.fontBold(
                            color: Colors.white,
                            fontSize: 24,
                          ),
                        ),
                         SizedBox(height: 8.h),
                        Text(
                          'Choose your preferred way to contact us',
                          style: FontStyles.fontRegular(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: screenHeight * 0.04),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.05),
              child: Column(
                children: [
                  _buildContactCard(
                    context: context,
                    icon: SvgStrings.iconCall,
                    title: appLocal.callUs,
                    description: 'Talk to our support team',
                    onTap: () async {
                      final Uri uri = Uri(
                        scheme: 'tel',
                        path: support.value.phone ?? "",
                      );
                      launchUrl(uri);
                    },
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  _buildContactCard(
                    context: context,
                    icon: SvgStrings.iconWhatsapp,
                    title: appLocal.whatsapp,
                    description: 'Chat with us on WhatsApp',
                    onTap: () async {
                      var whatsapp = support.value.whatsUp ?? "";
                      var whatsappAndroid = Uri.parse(
                          "whatsapp://send?phone=$whatsapp&text=hello");
                      if (await canLaunchUrl(whatsappAndroid)) {
                        await launchUrl(whatsappAndroid);
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              appLocal.whatsappNotInstalled,
                              style: FontStyles.fontSemibold(),
                            ),
                          ),
                        );
                      }
                    },
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  _buildContactCard(
                    context: context,
                    icon: SvgStrings.iconEmailBlue,
                    title: 'Email',
                    description: Obx(() => Text(
                          support.value.email ?? "",
                          style: FontStyles.fontRegular(
                            color: Colors.white,
                          ),
                        )),
                    onTap: () {
                      final Uri uri = Uri(
                        scheme: 'mailto',
                        path: support.value.email ?? "",
                      );
                      launchUrl(uri);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactCard({
    required BuildContext context,
    required String icon,
    required String title,
    required dynamic description,
    required VoidCallback onTap,
  }) {
    return Container(
      height: screenHeight * 0.11,
      decoration: BoxDecoration(
        color: AppColors.headerColorDark,
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            AppColors.colorPrimary.withOpacity(0.1),
            AppColors.colorPrimary.withOpacity(0.3),
            AppColors.colorPrimary.withOpacity(0.6),
            AppColors.colorPrimary.withOpacity(0.9),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.04, 
              vertical: screenHeight * 0.015
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: SvgPicture.string(
                    icon,
                    height: 25,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: FontStyles.fontBold(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 2),
                      description is String
                          ? Text(
                              description,
                              style: FontStyles.fontRegular(
                                color: Colors.white.withOpacity(0.9),
                                fontSize: 12,
                              ),
                            )
                          : description,
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    SupportContactModel model = SupportContactModel.fromJson(response);
    if (model.status == statusOK) {
      support.value = model.support ?? Support();
    }
  }
}
