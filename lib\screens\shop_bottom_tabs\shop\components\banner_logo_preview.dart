import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/media_picker.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/controller/shop_info_controller.dart';

class BannerLogoPreview extends StatefulWidget {
  final ShopInfoController infoController;

  const BannerLogoPreview({Key? key, required this.infoController})
      : super(key: key);

  @override
  State<BannerLogoPreview> createState() => _BannerLogoPreviewState();
}

class _BannerLogoPreviewState extends State<BannerLogoPreview> {
  ShopInfoController get controller => widget.infoController;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 280,
      width: MediaQuery.of(context).size.width,
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Positioned(
            height: 190,
            left: 0,
            right: 0,
            top: 0,
            child: InkWell(
              onTap: () async {
                showMediaPicker(context,
                        requiredEditor: true, aspectRatioPreset: 16 / 9)
                    .then((result) {
                  if (result != null) {
                    final tempDir = Directory.systemTemp;
                    final newPath =
                        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}.${result.path.split('.').last}';
                    final newFile = File(result.path).copySync(newPath);

                    setState(() {
                      controller.bannerImage = newFile;
                    });
                  }
                });
              },
              child: controller.bannerImage == null
                  ? controller.bannerUrl.isNotEmpty
                      ? Stack(
                          children: [
                            Positioned(
                              height: 195,
                              left: 0,
                              right: 0,
                              top: 0,
                              child: GlobalMethods.netWorkImage(
                                  controller.bannerUrl,
                                  BorderRadius.zero,
                                  BoxFit.fill),
                            ),
                            if (controller.history.returnValues
                                    ?.contains("Shop Banner") ??
                                false)
                              Positioned(
                                  height: 195,
                                  left: 0,
                                  right: 0,
                                  top: 0,
                                  child: Container(
                                    color: Colors.white.withOpacity(0.25),
                                    child: Center(
                                      child: Text(
                                        "Admin rejected this banner",
                                        style: FontStyles.fontSemibold(
                                          color: AppColors.colorDanger,
                                        ),
                                      ),
                                    ),
                                  )),
                          ],
                        )
                      : Stack(
                          alignment: Alignment.center,
                          children: [
                            Container(
                              decoration: const BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage('assets/images/banner.png'),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 60,
                              child: Column(
                                children: <Widget>[
                                  Text(
                                    EnStrings.uploadShopBanner,
                                    style: FontStyles.fontRegular(
                                        fontSize: 12,
                                        color: Colors.white.withOpacity(0.8)),
                                  ),
                                  const SizedBox(height: 6),
                                  SvgPicture.string(SvgStrings.uploadIcon),
                                ],
                              ),
                            )
                          ],
                        )
                  : (controller.bannerImage?.path
                              .split(".")
                              .last
                              .toUpperCase() ==
                          "GIF")
                      ? Image.memory(controller.bannerImage!.readAsBytesSync())
                      : Image.file(
                          controller.bannerImage!,
                          fit: BoxFit.fill,
                        ),
            ),
          ),
          Positioned(
            bottom: 10,
            left: 100,
            top: 200,
            right: kRightSpace,
            child: Text(
              controller.shop?.shopName ?? controller.storeName,
              style: FontStyles.fontSemibold(fontSize: 16),
            ),
          ),
          Positioned(
            bottom: 10,
            left: kLeftSpace,
            child: InkWell(
              onTap: () async {
                showMediaPicker(context,
                        requiredEditor: true, aspectRatioPreset: 2 / 2)
                    .then((result) {
                  if (result != null) {
                    final tempDir = Directory.systemTemp;
                    final newPath =
                        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}.${result.path.split('.').last}';
                    final newFile = File(result.path).copySync(newPath);

                    setState(() {
                      controller.logoImage = newFile;
                    });
                  }
                });
              },
              child: Container(
                height: 72,
                width: 72,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                ),
                child: controller.logoImage == null
                    ? controller.logoUrl.isNotEmpty
                        ? Stack(
                            children: [
                              Positioned(
                                height: 72,
                                width: 72,
                                child: GlobalMethods.netWorkImage(
                                    controller.logoUrl,
                                    BorderRadius.circular(5),
                                    BoxFit.cover),
                              ),
                              if (controller.history.returnValues
                                      ?.contains("Shop Logo") ??
                                  false)
                                Positioned(
                                  height: 72,
                                  width: 72,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.5),
                                    ),
                                    child: Center(
                                      child: Text(
                                        "Logo rejected",
                                        textAlign: TextAlign.center,
                                        style: FontStyles.fontSemibold(
                                          fontSize: 12,
                                          color: AppColors.colorDanger,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          )
                        : Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(5),
                            ),
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    height: 24,
                                    width: 24,
                                    child: SvgPicture.string(
                                        SvgStrings.uploadIcon),
                                  ),
                                  Text(
                                    EnStrings.logo,
                                    textAlign: TextAlign.center,
                                    style: FontStyles.fontRegular(
                                      color: Colors.white.withOpacity(0.5),
                                      fontSize: 10,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                    : ClipRRect(
                        borderRadius: BorderRadius.circular(5),
                        child: Image.file(
                          controller.logoImage!,
                          fit: BoxFit.fill,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
