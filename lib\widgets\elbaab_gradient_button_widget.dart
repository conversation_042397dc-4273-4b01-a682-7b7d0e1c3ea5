import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class ElbaabGradientButtonWidget extends StatelessWidget {
  final String text;
  final double height;
  final Function onPress;
  final Widget? customWidget;
  final EdgeInsets? edgeInsets;

  const ElbaabGradientButtonWidget(
      {Key? key,
      required this.onPress,
      required this.text,
      this.height = 42,
      this.customWidget,
      this.edgeInsets})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: edgeInsets,
      height: height,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment(0.79, -0.88),
          end: Alignment(-0.73, 0.89),
          colors: [
            Color.fromRGBO(143, 156, 244, 1),
            Color.fromRGBO(112, 112, 190, 1),
            Color.fromRGBO(79, 85, 133, 1),
          ],
          stops: [0.0, 0.345, 1.0],
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(Colors.transparent),
          shadowColor: MaterialStateProperty.all(Colors.transparent),
          minimumSize: MaterialStateProperty.all(
              Size(MediaQuery.of(context).size.width, height)),
        ),
        onPressed: () => onPress(),
        child: customWidget ?? Text(text, style: FontStyles.fontSemibold()),
      ),
    );
  }
}
