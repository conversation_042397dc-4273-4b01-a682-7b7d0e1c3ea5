class UploadImageModel {
  final List<FileList>? fileList;
  final String? message;
  final int? status;

  UploadImageModel({
    this.fileList,
    this.message,
    this.status,
  });

  UploadImageModel.fromJson(Map<String, dynamic> json)
    : fileList = (json['fileList'] as List?)?.map((dynamic e) => FileList.fromJson(e as Map<String,dynamic>)).toList(),
      message = json['message'] as String?,
      status = json['status'] as int?;

  Map<String, dynamic> toJson() => {
    'fileList' : fileList?.map((e) => e.toJson()).toList(),
    'message' : message,
    'status' : status
  };
}

class FileList {
  final String? fieldName;
  final List<String>? fileUrls;

  FileList({
    this.fieldName,
    this.fileUrls,
  });

  FileList.fromJson(Map<String, dynamic> json)
    : fieldName = json['fieldName'] as String?,
      fileUrls = (json['fileUrls'] as List?)?.map((dynamic e) => e as String).toList();

  Map<String, dynamic> toJson() => {
    'fieldName' : fieldName,
    'fileUrls' : fileUrls
  };
}