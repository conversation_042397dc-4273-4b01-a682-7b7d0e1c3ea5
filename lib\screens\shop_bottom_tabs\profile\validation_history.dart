import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/add_product/view_prduct/product_detail_model.dart';
import 'package:overolasuppliers/model/validation_history_model.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';

import '../../../helper/constant/constants.dart';

class ValidationHistory extends StatelessWidget implements ServerResponse {
  ValidationHistory({Key? key}) : super(key: key);
  List<History> arrHistory = Get.arguments[0];
  late GraphQlInitilize _request;

  @override
  Widget build(BuildContext context) {
    arrHistory.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
    arrHistory.removeWhere((element) => element.status == null);

    _request = GraphQlInitilize(this);
    return Scaffold(
      appBar:
          const ElbaabHeader(title: "Validation History", leadingBack: true),
      body: ListView.builder(
          itemCount: arrHistory.length,
          itemBuilder: (context, index) {
            History history = arrHistory[index];
            String rejectionType = "";
            for (var type in history.returnValues ?? []) {
              rejectionType =
                  rejectionType + (rejectionType.isEmpty ? "" : "\n") + type;
            }
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Container(
                padding: const EdgeInsets.only(
                    left: 16, top: 16, right: 10, bottom: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: AppColors.headerColorDark,
                ),
                child: Column(
                  children: <Widget>[
                    const SizedBox(height: 15),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            EnStrings.status,
                            style: FontStyles.fontMedium(fontSize: 12),
                          ),
                        ),
                        Text(
                          (history.status ?? "") == "Returned"
                              ? "Returned by the admin"
                              : (history.status ?? "") == "Accepted"
                                  ? "Accepted by the admin"
                                  : "Submitted by the supplier",
                          style: FontStyles.fontMedium(
                              fontSize: 12,
                              color: (history.status ?? "") == "Returned"
                                  ? AppColors.colorDanger
                                  : (history.status ?? "") == "Accepted"
                                      ? AppColors.colorSecondary
                                      : AppColors.colorSecondaryYellow),
                        )
                      ],
                    ),
                    infoLisTile(
                        history.status == "Returned"
                            ? EnStrings.date
                            : EnStrings.updatedDate,
                        GlobalMethods.convertTimeFormate(
                            history.createdAt ?? "",
                            format: "EEE dd,MMM yyyy hh:mm a")),
                    if (history.status == "Returned")
                      infoLisTile(
                          EnStrings.adminNotes, history.returnMessage ?? ""),
                    if (history.status == "Returned")
                      infoLisTile(EnStrings.returnedFeilds, rejectionType),
                    if (history.status == "Returned" && index == 0)
                      Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                            onPressed: () {
                              if (index == 0 && history.status == "Returned") {
                                _request.runQuery(
                                    context: context,
                                    query: GraphQlQuries.getProductById,
                                    variables: GraphQlVariables.getProductByID(
                                        productId: Get.arguments[1]),
                                    type: "Product_Details");
                              }
                            },
                            child: Text(
                              "Update >",
                              style: FontStyles.fontMedium(
                                decoration: TextDecoration.underline,
                                color: AppColors.colorPrimary,
                              ),
                            )),
                      )
                  ],
                ),
              ),
            );
          }),
    );
  }

  infoLisTile(String title, String info) {
    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: Text(title, style: FontStyles.fontMedium(fontSize: 12)),
          ),
          Expanded(
            flex: 1,
            child: Text(
              info,
              style: FontStyles.fontRegular(
                fontSize: 12,
                color: Colors.white.withOpacity(0.5),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == "Product_Details") {
      ProductDetailModel model = ProductDetailModel.fromJson(response);
      if (model.status == statusOK) {
        prefs.remove(productInformation);
        prefs.remove(productDetails);
        prefs.remove(productShipment);
        prefs.remove(productPolicies);
        Get.toNamed(RouteNames.addProductTabViewScreen,
            arguments: [model.product]);
      }
    }
  }
}
