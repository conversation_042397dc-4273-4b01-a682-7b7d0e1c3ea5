{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a1028f36bad9a97cc35562cbe55833f", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986621fb71c1fd233b4282eb859c620c30", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984afc544458b9fb2055aa2709c2b767e0", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c5ccaa1beadff1e1dfe2c7af55d962bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984afc544458b9fb2055aa2709c2b767e0", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9801478861abe835484db7de57a98e7c10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985ef6c2cd265f4889363dda1630de4315", "guid": "bfdfe7dc352907fc980b868725387e98f049779b7a3175d1cf9d97e4c58bb5df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eb9df935c121fb07e6e6714f1deabd9", "guid": "bfdfe7dc352907fc980b868725387e98803cf63a084308c74e5d7d8d42a2bf97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c85c399c2a9142d421ab84e5c37ac483", "guid": "bfdfe7dc352907fc980b868725387e98b7de7c4eeef78d2c97fcf1008f478f8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0f249db772f490233960b62bde659ee", "guid": "bfdfe7dc352907fc980b868725387e98b21b6125c444bf81ca1881d11b95af3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed30496487e74e6d0185fd5b5385b26", "guid": "bfdfe7dc352907fc980b868725387e983fb0cda517265bf4a8bf4c11bde99de0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3a5048ba9a9b4c6ffb43c0b60bf25ba", "guid": "bfdfe7dc352907fc980b868725387e9814d9d38e2ffb91b8e91e4efb36ecf57f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8a89f7ae0c67d8c5397375e89c286e9", "guid": "bfdfe7dc352907fc980b868725387e98758fb6104dc61ee2de52330538040bda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846cea8edf8d7df1c0620f380302cfe19", "guid": "bfdfe7dc352907fc980b868725387e98c3674b57f35e7c48aa0aa59a180ec901", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cfff72501d5603df9fc97a3e43ef023", "guid": "bfdfe7dc352907fc980b868725387e98aa1b8ee5019c35073ab05913ea6cf9b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc5abf21ee6b1ed36e2768d337ede66", "guid": "bfdfe7dc352907fc980b868725387e98a19f026a9e14cfab445de0aaa3fe4b2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b2befbd34a3956a68565d6ea0719fbd", "guid": "bfdfe7dc352907fc980b868725387e981770e219449e49a0829e1186f21a601e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98831224730b28402c169335ea53f45e1f", "guid": "bfdfe7dc352907fc980b868725387e98d5c191db2197c2197fe5b390809851af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc07be567ae73e483f5fec609ff386e8", "guid": "bfdfe7dc352907fc980b868725387e98d9cda9f2b43ec4a5d53e93391b0fc11e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be13a4844870438f78c2d2364b19ad70", "guid": "bfdfe7dc352907fc980b868725387e98dd11f00cb15325a95e6e8f423befb966", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989407309195a03c26bf5f7b74a1c9c4a6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986f4b615d5915b8893caea402ec19fa13", "guid": "bfdfe7dc352907fc980b868725387e983b7f52a2f12ee26f6535053304814cca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f99f860d3d04912577e9905b4cd3063e", "guid": "bfdfe7dc352907fc980b868725387e989778413b057c3f8d82eae72bcd9ee3a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9fc031cd3a3601761a751fcecd8959b", "guid": "bfdfe7dc352907fc980b868725387e98f5487023742378d93fe0c9cf2ada1f33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c044370f5bf22249cab735f2f3f6ba71", "guid": "bfdfe7dc352907fc980b868725387e98eec6bcf0f1686a00cc310fdcc26475de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b492d80a603c03892fe38c36f333a12", "guid": "bfdfe7dc352907fc980b868725387e98e30488aa6fe4c81405ed171b1436219a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f1083639e2ea5e783d6ffc13a62f004", "guid": "bfdfe7dc352907fc980b868725387e9841ec118ad85c1be38b2405ca58efa69c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af10a19a5e6f009b2aa23f8b9000a28d", "guid": "bfdfe7dc352907fc980b868725387e98d105e534d41bf6f067b2ce7886e57219"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf44c21326d31850860190c5fb66a32d", "guid": "bfdfe7dc352907fc980b868725387e98bac5ea2bd42976fcf6980d50273b2c8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849982ea4e61e94f91876b68232b29085", "guid": "bfdfe7dc352907fc980b868725387e98d96cb37c04f3a5e2a62a89647d5fd5a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984de46f7ebc5e800f6cc9d6888f857757", "guid": "bfdfe7dc352907fc980b868725387e98b7bf8ceaed34b493855ab2e78f57f833"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfd8b1e423abef30f0a39879b51cd9c8", "guid": "bfdfe7dc352907fc980b868725387e9813d9607cd26bb79cb8997e49098d6b15"}], "guid": "bfdfe7dc352907fc980b868725387e985c4cf6b4bc20229190e3a447ea0c4e65", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e9890d73d6718e91560a25b2125fbe32a5b"}], "guid": "bfdfe7dc352907fc980b868725387e9834a7332a50fe4f31924399fc6a554940", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a3a5e8744969268a4206e20c760b3a81", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e98835ffb427e5b6dcb9ad8f10b192067a7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}