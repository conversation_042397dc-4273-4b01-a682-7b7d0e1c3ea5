import 'dart:io';
import 'dart:math';
import 'dart:ui';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/elbaab_image_picker/capture_image.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/elbaab_image_picker/gallery_view.dart';
import 'package:overolasuppliers/helper/multi_image_picker/multi_image_crop.dart';
import 'package:overolasuppliers/helper/other/elbaab_permission_handler.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:path_provider/path_provider.dart';

var pickerTextStyle = FontStyles.fontRegular(
  fontSize: 20,
  color: const Color.fromRGBO(10, 132, 255, 1),
);

Future<bool> checkFileSize(String filepath, int decimals) async {
  var file = File(filepath);
  int bytes = await file.length();
  if (bytes <= 0) return false;
  var i = (log(bytes) / log(1024)).floor();
  if (i <= 2) {
    if (i == 2) {
      if (double.parse(((bytes / pow(1024, i)).toStringAsFixed(decimals))) <=
          5) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  } else {
    return false;
  }
}

Future<File> compressAndSaveImage(
    File originalImageFile, String outputPath) async {
  final rawImage = File(originalImageFile.path).readAsBytesSync();
  final image = img.decodeImage(Uint8List.fromList(rawImage));
  final compressedImage = img.copyResize(image!, width: 800);
  File(outputPath)
      .writeAsBytesSync(img.encodeJpg(compressedImage, quality: 85));
  return File(outputPath);
}

Future<File?> showMediaPicker(context,
    {bool requiredDocumentPicker = false,
    bool requiredEditor = false,
    bool requiredViewDocument = false,
    String viewDocumentUrl = "",
    String viewButtonTitle = "View Document",
    double aspectRatioPreset = 16 / 9,
    bool isRequiredCircleCrop = false,
    int count = 1}) async {
  File? file = await showModalBottomSheet<File>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      enableDrag: true,
      isDismissible: true,
      transitionAnimationController: AnimationController(
        duration: const Duration(milliseconds: 400),
        vsync: Navigator.of(context),
      ),
      builder: (BuildContext bc) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: SafeArea(
            child: Container(
              padding: const EdgeInsets.only(bottom: 8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2C2C2E).withOpacity(0.95),
                      borderRadius: BorderRadius.circular(13),
                    ),
                    child: Column(
                      children: [
                        _buildIOSOption(
                          icon: Icons.camera_alt_rounded,
                          title: "Camera",
                          isFirstItem: true,
                          onTap: () async {
                            HapticFeedback.selectionClick();
                            final result = await Get.to(const TakePictureScreen());
                            if (result != File) {
                              File cameraFile = result;
                              final Directory tempDir = await getTemporaryDirectory();
                              final timeStamp = DateTime.now().millisecondsSinceEpoch;
                              var croppedImage = await compressAndSaveImage(
                                  cameraFile, '${tempDir.path}/$timeStamp.png');
                              File file = croppedImage;
                              if (!(await checkFileSize(file.path, 1))) {
                                Alerts.alertView(
                                    context: context,
                                    content: "File should be less than 5 MB",
                                    action: () => Get.back(),
                                    defaultActionText: EnStrings.ok);
                              } else if (requiredEditor) {
                                MultiImageCrop.startCropping(
                                    context: Get.context!,
                                    aspectRatio: aspectRatioPreset,
                                    files: [file],
                                    callBack: (List<File> images) {
                                      if (images.isNotEmpty) {
                                        Get.back(result: images.first);
                                      } else {
                                        Get.back();
                                      }
                                    });
                              } else {
                                Get.back(result: file);
                              }
                            } else {
                              Get.back();
                            }
                          },
                        ),
                        _buildDivider(),
                        _buildIOSOption(
                          icon: Icons.photo_library_rounded,
                          title: "Photo Library",
                          onTap: () async {
                            HapticFeedback.selectionClick();
                            if (Platform.isAndroid) {
                              if (!(await ElbaabPermissionHandler.checkPermission())) {
                                Get.back();
                                return;
                              }
                            }
                            final result = await Get.to(const ElbaabGalleryView(
                                isForMultiImage: false, selectionLimit: 2));

                            if (result != null && result != "-1") {
                              File galleryFile = result;
                              final Directory tempDir = await getTemporaryDirectory();
                              final timeStamp = DateTime.now().millisecondsSinceEpoch;
                              var croppedImage = await compressAndSaveImage(
                                  galleryFile, '${tempDir.path}/$timeStamp.png');
                              File file = croppedImage;
                              if (!(await checkFileSize(file.path, 1))) {
                                Alerts.alertView(
                                    context: context,
                                    content: "File should be less than 5 MB",
                                    action: () => Get.back(),
                                    defaultActionText: EnStrings.ok);
                              } else if (requiredEditor) {
                                MultiImageCrop.startCropping(
                                    context: Get.context!,
                                    aspectRatio: aspectRatioPreset,
                                    files: [file],
                                    callBack: (List<File> images) {
                                      if (images.isNotEmpty) {
                                        Get.back(result: images.first);
                                      } else {
                                        Get.back();
                                      }
                                    });
                              } else {
                                Get.back(result: file);
                              }
                            } else {
                              Get.back();
                            }
                          },
                        ),
                        if (requiredDocumentPicker) ...[
                          _buildDivider(),
                          _buildIOSOption(
                            icon: Icons.upload_file_rounded,
                            title: "Upload Document",
                            onTap: () async {
                              HapticFeedback.selectionClick();
                              FocusManager.instance.primaryFocus?.unfocus();
                              FilePickerResult? result = await FilePicker.platform
                                  .pickFiles(
                                      type: FileType.custom, allowedExtensions: ['pdf']);
                              if (result != null) {
                                if (!(await checkFileSize(
                                    result.files.single.path!, 1))) {
                                  Alerts.alertView(
                                      context: context,
                                      content: "File should be less than 5 MB",
                                      action: () => Get.back(),
                                      defaultActionText: EnStrings.ok);
                                } else {
                                  Get.back(result: File(result.files.single.path!));
                                }
                              } else {
                                Get.back();
                              }
                            },
                          ),
                        ],
                        if (requiredViewDocument) ...[
                          _buildDivider(),
                          _buildIOSOption(
                            icon: Icons.visibility_rounded,
                            title: viewButtonTitle,
                            onTap: () {
                              HapticFeedback.selectionClick();
                              Get.back();
                              GlobalMethods.launchInWebView(viewDocumentUrl);
                            },
                          ),
                        ],
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2C2C2E).withOpacity(0.95),
                      borderRadius: BorderRadius.circular(13),
                    ),
                    child: _buildIOSOption(
                      title: "Cancel",
                      isDestructive: true,
                      onTap: () {
                        HapticFeedback.lightImpact();
                        Get.back();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      });
  return file;
}

Widget _buildDivider() {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 16),
    height: 0.5,
    color: const Color(0xFF48484A),
  );
}

Widget _buildIOSOption({
  IconData? icon,
  required String title,
  bool isFirstItem = false,
  bool isDestructive = false,
  required VoidCallback onTap,
}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      splashColor: Colors.transparent,
      highlightColor: const Color(0xFF48484A).withOpacity(0.3),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        height: 57,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: isFirstItem
              ? const BorderRadius.vertical(top: Radius.circular(13))
              : null,
        ),
        child: Row(
          mainAxisAlignment: icon == null
              ? MainAxisAlignment.center
              : MainAxisAlignment.start,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 24,
                color: const Color(0xFF007AFF),
              ),
              const SizedBox(width: 12),
            ],
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w400,
                color: isDestructive
                    ? const Color(0xFFFF453A)
                    : const Color(0xFF007AFF),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

Future<List<File>?> multiImagePicker(BuildContext context, int count,
      {bool requiredCircleCropper = false  ,double? aspectRatioPreset}) async {
  List<File>? files = await showModalBottomSheet<List<File>>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext bc) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              InkWell(
                onTap: () async {
                  FocusManager.instance.primaryFocus?.unfocus();
                  final result = await Get.to(const TakePictureScreen());
                  if (result != File) {
                    File cameraFile = result;
                    final Directory tempDir = await getTemporaryDirectory();
                    final timeStamp = DateTime.now().millisecondsSinceEpoch;
                    var croppedImage = await compressAndSaveImage(
                        cameraFile, '${tempDir.path}/$timeStamp.png');
                    File file = croppedImage;
                    if (!(await checkFileSize(file.path, 1))) {
                      Alerts.alertView(
                          context: Get.context!,
                          content: "File should be less than 5 MB",
                          action: () => Get.back(),
                          defaultActionText: EnStrings.ok);
                    } else {
                      MultiImageCrop.startCropping(
                          context: Get.context!,
                          aspectRatio: aspectRatioPreset ??
                              (MediaQuery.of(Get.context!).size.width /
                                  (MediaQuery.of(Get.context!).size.width +
                                      (MediaQuery.of(Get.context!).size.width /
                                          3))),
                          files: [result],
                          requiredCirsleCroper: requiredCircleCropper,
                          isProductImages: requiredCircleCropper ? false : true,
                          callBack: (List<File> images) {
                            if (images.isNotEmpty) {
                              Get.back(result: images);
                            } else {
                              Get.back();
                            }
                          });
                    }
                  } else {
                    Get.back();
                  }
                },
                child: Container(
                  height: 55,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: const BoxDecoration(
                    color: Color.fromRGBO(37, 37, 37, 0.78),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      "Camera",
                      style: pickerTextStyle,
                    ),
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 8),
                height: 0.5,
                color: const Color.fromRGBO(217, 217, 217, 30),
              ),
              InkWell(
                onTap: () async {
                  FocusManager.instance.primaryFocus?.unfocus();
                  if (Platform.isAndroid) {
                    if (!(await ElbaabPermissionHandler.checkPermission())) {
                      Get.back();
                      return;
                    }
                  }
                  final result = await Get.to(ElbaabGalleryView(
                    isForMultiImage: true,
                    selectionLimit: count,
                  ));

                  if (result != "1") {
                    MultiImageCrop.startCropping(
                        context: Get.context!,
                        aspectRatio: aspectRatioPreset ??
                            (MediaQuery.of(Get.context!).size.width /
                                (MediaQuery.of(Get.context!).size.width +
                                    (MediaQuery.of(Get.context!).size.width /
                                        3))),
                        files: result,
                        requiredCirsleCroper: requiredCircleCropper,
                        isProductImages: requiredCircleCropper ? false : true,
                        callBack: (List<File> images) async {
                          if (images.isNotEmpty) {
                            Get.back(result: images);
                          } else {
                            Get.back();
                          }
                        });
                  } else {
                    Get.back();
                  }
                },
                child: Container(
                  height: 55,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: const BoxDecoration(
                    color: Color.fromRGBO(37, 37, 37, 0.78),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      "Photo Library",
                      style: pickerTextStyle,
                    ),
                  ),
                ),
              ),
              InkWell(
                onTap: () => Get.back(),
                child: Container(
                  height: 55,
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                      color: const Color.fromRGBO(37, 37, 37, 0.78),
                      borderRadius: BorderRadius.circular(14)),
                  child: Center(
                    child: Text(
                      "Close",
                      style: FontStyles.fontRegular(
                        fontSize: 20,
                        color: const Color.fromRGBO(245, 98, 127, 1),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      });

  return files;
}
