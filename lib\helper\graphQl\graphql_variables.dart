import 'package:overolasuppliers/helper/constant/constants.dart';

class GraphQlVariables {
  static Map<String, dynamic> signup(
      {required String name, required String email, required String password, required String lang}) {
    return {
      'userName': name,
      'userEmail': email,
      'userPassword': password,
      'firebaseDeviceToken': firebaseDeviceToken,
      'lang' : lang
    };
  }

  static Map<String, dynamic> login(
      {required String email, required String password, required String lang}) {
    return {
      'userEmail': email,
      'userPassword': password,
      'firebaseDeviceToken': firebaseDeviceToken,
      'lang' : lang
    };
  }

  static Map<String, dynamic> changeUserName({required String userName}) =>
      {'userName': userName};

  static Map<String, dynamic> addBankAccountDetails(
      {required String accountNumber,
      required String iban,
      required String accountHolderName,
      required String bankName,
      required String city}) {
    return {
      'accountNumber': accountNumber,
      'iban': iban,
      'accountHolderName': accountHolderName,
      "bankName": bankName,
      "city": city
    };
  }

  static Map<String, dynamic> getSalesQuery(
      {int page = 1,
      int itemsNumber = 60,
      String statusFilter = "",
      int dateFilter = 30}) {
    return {
      'page': page,
      'itemsNumber': itemsNumber,
      'statusFilter': statusFilter,
      "dateFilter": dateFilter
    };
  }

  static Map<String, dynamic> forgetpassword({required String userEmail}) {
    return {'userEmail': userEmail};
  }

  static Map<String, dynamic> publishSavedForlaterProduct(
      {required String productId}) {
    return {'productId': productId};
  }

  static Map<String, dynamic> confirmOrderItem(
      {required String orderItemId,
      required String pickupAddressId,
      required String readinessPeriode}) {
    return {
      'orderItemId': orderItemId,
      'pickupAddressId': pickupAddressId,
      'readinessPeriode': readinessPeriode
    };
  }

  static Map<String, dynamic> verifyEmail(
      {required String userId, required String otpNumber}) {
    return {'userId': userId, 'otpNumber': otpNumber};
  }

  static Map<String, dynamic> cancelItemInOrderItem(
      {required String orderItemId,
      required String itemId,
      required String notes}) {
    return {'orderItemId': orderItemId, 'notes': notes, 'itemId': itemId};
  }

  static Map<String, dynamic> changeQuantity(
      {required String productId, required int quantity}) {
    return {'productId': productId, 'quantity': quantity};
  }

  static Map<String, dynamic> setProductHiddenOrUnhidden(
      {required String productId, required bool isHidden}) {
    return {'productId': productId, 'isHidden': isHidden};
  }

  static Map<String, dynamic> addProductToFreeDelivery(
      {required List<String> productIds}) {
    return {'productIds': productIds};
  }

  static Map<String, dynamic> resendOtpEmail({required String userId}) {
    return {'userId': userId};
  }

  static Map<String, dynamic> addWithdrawalrequest(
      {required double requestedAmount}) {
    return {'requestedAmount': requestedAmount};
  }

  static Map<String, dynamic> resetPassword(
      {required String userId, required String newPassword}) {
    return {'userId': userId, 'newPassword': newPassword};
  }

  static Map<String, dynamic> changePassword(
      {required String oldPassword, required String newPassword}) {
    return {'oldPassword': oldPassword, 'newPassword': newPassword};
  }

  static Map<String, dynamic> addNumber(
      {required String userId, required userPhoneNumber}) {
    return {'userId': userId, 'userPhoneNumber': userPhoneNumber};
  }

  static Map<String, dynamic> sendPickupAdrNumberOtp({required phoneNumber}) {
    return {'phoneNumber': phoneNumber};
  }

  static Map<String, dynamic> verifySettingOtp({required String otpNumber}) {
    return {'otpNumber': otpNumber};
  }

  static Map<String, dynamic> viewNotifications(
      {required List<String> notificationIds}) {
    return {'notificationIds': notificationIds};
  }

  static Map<String, dynamic> restoreProduct({required String productId}) {
    return {'productId': productId};
  }

  static Map<String, dynamic> changePhone(
      {required String userPhoneNumber, required String userId}) {
    return {'userPhoneNumber': userPhoneNumber, 'userId': userId};
  }

  static Map<String, dynamic> changeEmail({required email}) {
    return {'email': email};
  }

  static Map<String, dynamic> changeAcceptOrderEmail(
      {required bool acceptOrderEmail}) {
    return {'acceptOrderEmail': acceptOrderEmail};
  }

  static Map<String, dynamic> acceptReturnOrderItem({required orderItemId}) {
    return {'orderItemId': orderItemId};
  }

  static Map<String, dynamic> resendNumberOtp({required String userId}) {
    return {'userId': userId};
  }

  static Map<String, dynamic> verifyNumber(
      {required String userId, required String otpNumber}) {
    return {'userId': userId, 'otpNumber': otpNumber};
  }

  static Map<String, dynamic> verifyPickupAdrNumberOtp(
      {required String phoneNumber, required String otpNumber}) {
    return {'phoneNumber': phoneNumber, 'otpNumber': otpNumber};
  }

  static Map<String, dynamic> getPaginated(
      {int itemsNumber = 10, int page = 1, }) {
    return {'itemsNumber': itemsNumber, 'page': page};
  }

  static Map<String, dynamic> getOrderPaginated(
      {int itemsNumber = 15, int page = 1, required String orderStatus }) {
    return {'itemsNumber': itemsNumber, 'page': page,'status': orderStatus};
  }
  static Map<String, dynamic> getReviewsByProductId(
      {required int itemsNumber,
      required int page,
      required String productId}) {
    return {'itemsNumber': itemsNumber, 'page': page, 'productId': productId};
  }

  static Map<String, dynamic> getProducts(
      {required int itemsNumber,
      int page = 1,
      String categoryId = "",
      String subCategoryId = ""}) {
    return {
      'itemsNumber': itemsNumber,
      'page': page,
      'categoryId': categoryId,
      'subCategoryId': subCategoryId
    };
  }

  static Map<String, dynamic> createShop({
    required bool isUpdate,
    required String shopBanner,
    required String shopLogo,
    required String shopSlogan,
    required bool freeDeliveryTarget,
    required int targetPriceForFdt,
    required String shopDescription,
    required String shopTermsConditions,
    required String phoneNumber,
    required String whatsUpPhoneNumber,
    required String email,
    required List<Map<String, dynamic>> pickupAdr,
  }) {
    return {
      'shopBanner': shopBanner,
      'shopLogo': shopLogo,
      'shopSlogan': shopSlogan,
      'freeDeliveryTarget': freeDeliveryTarget,
      'targetPriceForFdt': targetPriceForFdt,
      'shopDescription': shopDescription,
      isUpdate ? 'shopTermsAndConditions' : 'shopTermsConditions':
          shopTermsConditions,
      'pickupAdr': pickupAdr,
      'phoneNumber': phoneNumber,
      'whatsUpPhoneNumber': whatsUpPhoneNumber,
      'email': email,
    };
  }

  static Map<String, dynamic> genrateVeriations({
    required String productName,
    required double productPrice,
    required int productAvailableQte,
    required List<String> productImages,
    required List<Map<String, dynamic>> productColors,
    required List<Map<String, dynamic>> productSizes,
    required List<Map<String, dynamic>> productCustomOptions,
  }) {
    return {
      'productName': productName,
      'productPrice': productPrice,
      'productAvailableQte': productAvailableQte,
      'productImages': productImages,
      'productColors': productColors,
      'productSizes': productSizes,
      'productCustomOptions': productCustomOptions,
    };
  }

  static Map<String, dynamic> addMatchProduct({
    required String productCatalogId,
    required String oldProductId,
    required double productPrice,
    required String productName,
    required String productDescription,
    required String productManufacturerId,
    required String productCategory,
    required String productSubCategory,
    required String productBrand,
    required List<String> productImages,
    required List<String> productKeyWords,
    required String productReturnDuration,
    required String productWarrantyDuration,
    required String productReturnPolicy,
    required bool productNotifOnMinQte,
    required bool productAcceptReturn,
    required bool productFreeReturn,
    required bool productNotFreeReturn,
    required bool publishProduct,
    required bool isFreeDeliveryItem,
    required bool saveProductForLater,
    required bool isRematched,
    required int productAvailableQte,
    required int itemsPerOrder,
    required String productSizeCategory,
    required int productMinQte,
    required List<Map<String, dynamic>> productColors,
    required List<Map<String, dynamic>> productSizes,
    required List<Map<String, dynamic>> productCustomOptions,
    required List<Map<String, dynamic>> productVariations,
    required List<Map<String, dynamic>> productSpecs,
    required Map<String, dynamic> productAr,
  }) {
    return {
      'productCatalogId': productCatalogId,
      'ar': productAr,
      'oldProductId': oldProductId,
      'productPrice': productPrice,
      'productName': productName,
      'productDescription': productDescription,
      'productManufacturerId': productManufacturerId,
      'productCategory': productCategory,
      'productImages': productImages,
      'productSubCategory': productSubCategory,
      'productBrand': productBrand,
      'productKeyWords': productKeyWords,
      'productNotifOnMinQte': productNotifOnMinQte,
      'productAvailableQte': productAvailableQte,
      'productMinQte': productMinQte,
      'productAcceptReturn': productAcceptReturn,
      'productFreeReturn': productFreeReturn,
      'productNotFreeReturn': productNotFreeReturn,
      'publishProduct': publishProduct,
      'isFreeDeliveryItem': isFreeDeliveryItem,
      'saveProductForLater': saveProductForLater,
      'productReturnDuration': productReturnDuration,
      'productWarrantyDuration': productWarrantyDuration,
      'productReturnPolicy': productReturnPolicy,
      'productColors': productColors,
      'productSpecs': productSpecs,
      'itemsPerOrder': itemsPerOrder,
      'productSizes': productSizes,
      'productSizeCategory': productSizeCategory,
      'productCustomOptions': productCustomOptions,
      'productVariations': productVariations,
      'isRematched': isRematched
    };
  }

  static Map<String, dynamic> createProduct({
    required String productName,
    required String id,
    required String productDescription,
    required String productManufacturerId,
    required String productCategory,
    required String productSubCategory,
    required String productBrand,
    required List<String> productKeyWords,
    required double productPrice,
    required bool isFreeDeliveryItem,
    required int productAvailableQte,
    required bool productNotifOnMinQte,
    required int productMinQte,
    required List<String> productImages,
    required List<Map<String, dynamic>> productColors,
    required List<Map<String, dynamic>> productSizes,
    required List<Map<String, dynamic>> productCustomOptions,
    required List<Map<String, dynamic>> productVariations,
    required List<Map<String, dynamic>> productSpecs,
    required Map<String, dynamic> ar,
    required bool productAcceptReturn,
    required bool productFreeReturn,
    required bool productNotFreeReturn,
    required String productReturnDuration,
    required String productWarrantyDuration,
    required String productReturnPolicy,
    required String productSizeCategory,
    required int itemsPerOrder,
    required bool publishProduct,
    required bool saveProductForLater,
  }) {
    return {
      'productId': id,
      'productName': productName,
      'productDescription': productDescription,
      'productManufacturerId': productManufacturerId,
      'productCategory': productCategory,
      'ar': ar,
      'productSubCategory': productSubCategory,
      'productBrand': productBrand,
      'productKeyWords': productKeyWords,
      'productPrice': productPrice,
      'isFreeDeliveryItem': isFreeDeliveryItem,
      'productAvailableQte': productAvailableQte,
      'productNotifOnMinQte': productNotifOnMinQte,
      'productMinQte': productMinQte,
      'productImages': productImages,
      'productColors': productColors,
      'productSizes': productSizes,
      'productCustomOptions': productCustomOptions,
      'productVariations': productVariations,
      'productSpecs': productSpecs,
      'productAcceptReturn': productAcceptReturn,
      'productFreeReturn': productFreeReturn,
      'productNotFreeReturn': productNotFreeReturn,
      'productReturnDuration': productReturnDuration,
      'productWarrantyDuration': productWarrantyDuration,
      'productReturnPolicy': productReturnPolicy,
      'productSizeCategory': productSizeCategory,
      'itemsPerOrder': itemsPerOrder,
      'publishProduct': publishProduct,
      'saveProductForLater': saveProductForLater,
    };
  }

  static Map<String, dynamic> addBussinessInfo({
    required String bussinessOwnerId,
    required String bussinessTradeCertificate,
    required String bussinessOwnerLegalId,
    required String bussinessName,
    required String bussinessOwnerName,
    required String tradeCertificateExpiryDate,
    required String ownerLegalIdExpiryDate,
  }) {
    return {
      'bussinessOwnerId': bussinessOwnerId,
      'bussinessTradeCertificate': bussinessTradeCertificate,
      'bussinessOwnerLegalId': bussinessOwnerLegalId,
      'bussinessName': bussinessName,
      'bussinessOwnerName': bussinessOwnerName,
      'tradeCertificateExpiryDate': tradeCertificateExpiryDate,
      'ownerLegalIdExpiryDate': ownerLegalIdExpiryDate,
    };
  }

  static Map<String, dynamic> deleteProductById({required String productId}) {
    return {'productId': productId};
  }

  static Map<String, dynamic> deleteAccount({required String userPassword}) {
    return {'userPassword': userPassword};
  }

  static Map<String, dynamic> getProductByID({required String productId}) {
    return {'productId': productId};
  }
}
