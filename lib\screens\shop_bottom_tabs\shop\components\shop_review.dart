import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/branches.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/customer_contact_card.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/shop_info.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/controller/shop_info_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_border_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../../main.dart';

class ShopReview extends GetView<ShopInfoController> implements ServerResponse {
  final Function(int value) tabPosition;
  ShopReview({Key? key, required this.tabPosition}) : super(key: key);
  late GraphQlInitilize _grapghRequest;
  RxString strError = ''.obs;
  @override
  Widget build(BuildContext context) {
    _grapghRequest = GraphQlInitilize(this);
    final appLocal = AppLocalizations.of(context)!;
    String customerContact = prefs.getString(shopCustomerContact) ?? "";
    String information = prefs.getString(shopInfo) ?? "";
    String address = prefs.getString(shopPickUpAddress) ?? "";
    return SingleChildScrollView(
        child: Column(
      children: [
        ShopInfo(
          tabIndex: (v) => tabPosition(v),
          shop: controller.shop,
          isReview: true,
        ),
        Container(
          color: AppColors.headerColorDark,
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: kLeftSpace, right: kRightSpace),
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: Text(
                        appLocal.pickupAddress,
                        style: FontStyles.fontBold(fontSize: 12),
                      ),
                    ),
                    if (controller.shop != null && address.isNotEmpty)
                      IconButton(
                        onPressed: () {
                          Alerts.alertView(
                              context: context,
                              title: appLocal.alert,
                              content:
                                  "Are you sure you want to discard pickup address changes?",
                              defaultActionText: appLocal.yes,
                              cancelActionText: appLocal.no,
                              cancelAction: ()=> Get.back(),
                              action: () {
                                prefs.setString(shopPickUpAddress, "");
                                tabPosition(1);
                                controller.updatePickupAddress(
                                    shop: controller.shop!);
                                Get.back();
                              });
                        },
                        icon: SvgPicture.string(SvgStrings.iconDelete),
                      ),
                    IconButton(
                      onPressed: () => tabPosition(1),
                      icon: Container(
                        height: 30,
                        width: 30,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.06),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: SvgPicture.string(SvgStrings.iconEditGray),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              controller.shop == null
                  ? ListView.builder(
                      itemCount: controller.filterBranch.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        PickupAddressModel model =
                            controller.filterBranch[index];
                        return ElbaabFeildContainerWidget(
                          borderWidth: 1,
                          edgeInsets: const EdgeInsets.only(
                              left: 10, right: 10, top: 10),
                          child: Column(
                            children: [
                              _infoListTile(appLocal.city, model.city.value, true),
                              _infoListTile(appLocal.address, model.address, true),
                              _infoListTile(appLocal.location, appLocal.mapLocation, true,
                                  isLink: true,
                                  locationLat: model.latLng?.latitude ?? 0.0,
                                  locationLong: model.latLng?.longitude ?? 0.0),
                              _infoListTile(appLocal.number,
                                  model.contactNumber.number.value, true),
                              _infoListTile(appLocal.landNumber,
                                  model.landNumber.number.value, true),
                            ],
                          ),
                        );
                      })
                  : ListView.builder(
                      itemCount: controller.branches.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        PickupAddressModel model = controller.branches[index];
                        return ElbaabFeildContainerWidget(
                          borderWidth:
                              (model.isAdd || model.isDelete || model.isUpdate)
                                  ? 2
                                  : 1,
                          borderColor: model.isDelete
                              ? AppColors.colorDanger
                              : model.isUpdate
                                  ? AppColors.colorSecondaryYellow
                                  : model.isAdd
                                      ? AppColors.colorSecondary
                                      : null,
                          edgeInsets: const EdgeInsets.only(
                              left: 10, right: 10, top: 10),
                          child: Column(
                            children: [
                              Padding(
                                padding: EdgeInsets.only(
                                  top: (model.isAdd ||
                                          model.isDelete ||
                                          model.isUpdate)
                                      ? 10
                                      : 0,
                                  right: (model.isAdd ||
                                          model.isDelete ||
                                          model.isUpdate)
                                      ? 10
                                      : 0,
                                ),
                                child: Align(
                                  alignment: Alignment.centerRight,
                                  child: Text(
                                      model.isDelete
                                          ? "You Delete This Address"
                                          : model.isAdd
                                              ? "You Add New Address"
                                              : model.isUpdate
                                                  ? "You Update This Address"
                                                  : "",
                                      style: FontStyles.fontMedium(
                                          fontSize: 12,
                                          color: model.isDelete
                                              ? AppColors.colorDanger
                                              : model.isUpdate
                                                  ? AppColors
                                                      .colorSecondaryYellow
                                                  : model.isAdd
                                                      ? AppColors.colorSecondary
                                                      : Colors.white)),
                                ),
                              ),
                              _infoListTile(appLocal.city, model.city.value, true),
                              _infoListTile(appLocal.address, model.address, true),
                              _infoListTile(appLocal.location, appLocal.mapLocation, true,
                                  isLink: true,
                                  locationLat: model.latLng?.latitude ?? 0.0,
                                  locationLong: model.latLng?.longitude ?? 0.0),
                              _infoListTile(appLocal.number,
                                  model.contactNumber.number.value, true),
                              _infoListTile(appLocal.landNumber,
                                  model.landNumber.number.value, true),
                            ],
                          ),
                        );
                      }),
              const SizedBox(height: 10),
            ],
          ),
        ),
        CustomerContactCard(tabPosition: tabPosition),
        ElbaabNetworkEroor(strError: strError),
        ElbaabGradientButtonWidget(
          onPress: () {
            if (controller.shop == null) {
              List<Map<String, dynamic>> pickupAddress = [];
              for (var element in controller.filterBranch) {
                pickupAddress.add({
                  "pickUpAddress": element.address,
                  "pickUpCity": element.city.value,
                  "pickUpContactMobileNumber": {
                    "number": element.contactNumber.number.value,
                    "isVerified": element.contactNumber.isVerified
                  },
                  "pickUpContactLandNumber": {
                    "number": element.landNumber.number.value,
                    "isVerified": element.landNumber.isVerified
                  },
                  "pickUpMapLocation": {
                    "type": "Point",
                    "coordinates": [
                      element.latLng?.latitude,
                      element.latLng?.longitude
                    ]
                  },
                });
              }

              _grapghRequest.runMutation(
                context: context,
                query: GraphQlQuries.createShop,
                variables: GraphQlVariables.createShop(
                  isUpdate: false,
                  shopBanner: controller.bannerUrl,
                  shopLogo: controller.logoUrl,
                  shopSlogan: controller.slogan,
                  freeDeliveryTarget: controller.freeDeliveryTaget.value,
                  targetPriceForFdt: controller.targetPrice.isEmpty
                      ? 0
                      : int.parse(controller.targetPrice),
                  shopDescription: controller.description,
                  shopTermsConditions: controller.termsUrl,
                  phoneNumber: controller.strContactNumber,
                  whatsUpPhoneNumber: controller.strWhatsappNumber,
                  email: controller.strEmail,
                  pickupAdr: pickupAddress,
                ),
              );
            } else {
              if (information.isNotEmpty ||
                  address.isNotEmpty ||
                  customerContact.isNotEmpty) {
                List<Map<String, dynamic>> pickupAddress = [];
                for (var element in controller.branches) {
                  pickupAddress.add({
                    "_id": element.id,
                    "isUpdated": element.isDelete || element.isAdd
                        ? false
                        : element.isUpdate,
                    "isAdded": element.isAdd,
                    "isDeleted": element.isDelete,
                    "pickUpAddress": element.address,
                    "pickUpCity": element.city.value,
                    "pickUpContactMobileNumber": {
                      "number": element.contactNumber.number.value,
                      "isVerified": element.contactNumber.isVerified
                    },
                    "pickUpContactLandNumber": {
                      "number": element.landNumber.number.value,
                      "isVerified": element.landNumber.isVerified
                    },
                    "pickUpMapLocation": {
                      "type": "Point",
                      "coordinates": [
                        element.latLng?.latitude,
                        element.latLng?.longitude
                      ]
                    },
                  });
                }
                _grapghRequest.runMutation(
                  context: context,
                  query: GraphQlQuries.updateShop,
                  variables: GraphQlVariables.createShop(
                    isUpdate: true,
                    shopBanner: controller.bannerUrl,
                    shopLogo: controller.logoUrl,
                    shopSlogan: controller.slogan,
                    freeDeliveryTarget: controller.freeDeliveryTaget.value,
                    targetPriceForFdt: controller.targetPrice.isEmpty ||
                            (!controller.freeDeliveryTaget.value)
                        ? 0
                        : int.parse(controller.targetPrice),
                    shopDescription: controller.description,
                    shopTermsConditions: controller.termsUrl,
                    phoneNumber: controller.strContactNumber,
                    whatsUpPhoneNumber: controller.strWhatsappNumber,
                    email: controller.strEmail,
                    pickupAdr: pickupAddress,
                  ),
                );
              } else {
                Provider.of<UpdatedInfo>(MyApp.navigatorKey.currentContext!,
                        listen: false)
                    .setTabPosition(2);
                Get.offAllNamed(RouteNames.shopHomeScreen);
              }
            }
          },
          edgeInsets: const EdgeInsets.all(20),
          text: (controller.shop?.isCompleted ?? false)
              ? appLocal.update
              : appLocal.submit,
        ),
        if (controller.shop != null &&
            (information.isNotEmpty ||
                address.isNotEmpty ||
                customerContact.isNotEmpty))
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: ElbaabBorderButtonWidget(
                onPress: () => Alerts.showCustomSnackbar(
                    context: context,
                    contentText: appLocal.discardAllChanges,
                    afterExecuteMethod: () {
                      prefs.setString(shopInfo, "");
                      controller.updateshopInfo(shop: controller.shop!);
                      prefs.setString("customerContact", "");
                      controller.updateContactInfo(shop: controller.shop!);
                      prefs.setString(shopPickUpAddress, "");
                      controller.updatePickupAddress(shop: controller.shop!);
                      tabPosition(0);
                    }),
                text: appLocal.discardAllChanges,
                edgeInsets: const EdgeInsets.symmetric(horizontal: 20),
              ),
            ),
          ),
      ],
    ));
  }

  Widget _infoListTile(String title, String value, bool requiredTopSpace,
      {bool isLink = false,
      double locationLat = 0,
      double locationLong = 0,
      Widget? changes}) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: 16, vertical: requiredTopSpace ? 5 : 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              title,
              style: FontStyles.fontRegular(fontSize: 12),
            ),
          ),
          Expanded(
            flex: 2,
            child: InkWell(
              onTap: () {
                if (isLink) {
                  if (locationLat != 0 && locationLong != 0) {
                    GlobalMethods.openMap(locationLat, locationLong);
                  } else {
                    GlobalMethods.launchInWebView(value);
                  }
                }
              },
              child: changes ??
                  Text(
                    isLink ? value.split('/').last : value,
                    style: FontStyles.fontRegular(
                      fontSize: 12,
                      decoration: isLink
                          ? TextDecoration.underline
                          : TextDecoration.none,
                      color: isLink
                          ? AppColors.colorPrimary
                          : Colors.white.withOpacity(0.5),
                    ),
                  ),
            ),
          )
        ],
      ),
    );
  }

  @override
  onError(error, String type) {
    BaseModel model = BaseModel.fromJson(error);
    strError.value = model.message ?? "";
  }

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (model.status == statusOK) {
      prefs.setString("customerContact", "");
      prefs.setString(shopPickUpAddress, "");
      prefs.setString(shopInfo, "");
      if (controller.shop == null) {
        GlobalMethods.disableFutureLoginCheck();
        prefs.setString(merchantID, supplierID);
      }
      Provider.of<UpdatedInfo>(MyApp.navigatorKey.currentContext!,
              listen: false)
          .setTabPosition(2);
      Get.offAllNamed(RouteNames.shopHomeScreen);
    } else if (model.status == status400 && controller.shop != null) {
      Provider.of<UpdatedInfo>(MyApp.navigatorKey.currentContext!,
              listen: false)
          .setTabPosition(2);
      Get.offAllNamed(RouteNames.shopHomeScreen);
    } else {
      strError.value = model.message ?? "";
    }
  }
}
