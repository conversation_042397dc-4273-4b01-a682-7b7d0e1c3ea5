import 'package:flutter/material.dart';

class UpdatedInfo extends ChangeNotifier {
  int _currentTab = 0;
  String _updateShopStatus = "";
  double _uploadingProgress = 0.0;
  bool _isRecivedOrder = false;
  bool _isProductRemoveFromTrash = false;
  int _notificationCount = 0;

  String get updateShopStatus => _updateShopStatus;
  int get unReadNotificationCount => _notificationCount;
  bool get orderRecived => _isRecivedOrder;
  bool get productRwtriveFromTrash => _isProductRemoveFromTrash;

  isRecivedOrder(bool value) {
    _isRecivedOrder = value;
    notifyListeners();
  }


  isProductRemoveFromTrash(bool value) {
    _isProductRemoveFromTrash = value;
    notifyListeners();
  }

  shopStatusUpdate(String value) {
    _updateShopStatus = value;
    notifyListeners();
  }

  double getUploadingProgress() {
    return _uploadingProgress;
  }

  setUploadingProgress(double progress) {
    _uploadingProgress = progress;
    notifyListeners();
  }

  int getTabPosition() {
    return _currentTab;
  }

  setTabPosition(int position) {
    _currentTab = position;
    notifyListeners();
  }

  setNotificationCount(int count) {
    _notificationCount = count;
    notifyListeners();
  }
}
