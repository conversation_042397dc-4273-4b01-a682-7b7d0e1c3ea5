{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d1053d07ab78a1d37cba9416cdf33746", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCoreInternal", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "FirebaseCoreInternal", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_NAME": "FirebaseCoreInternal_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987796715156311556b671670a5937fa5f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985d572c247f5230bfc41f08ec58782a84", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCoreInternal", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "FirebaseCoreInternal", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_NAME": "FirebaseCoreInternal_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9889576eb32af65d22383e471d6c016167", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985d572c247f5230bfc41f08ec58782a84", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCoreInternal", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "FirebaseCoreInternal", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_NAME": "FirebaseCoreInternal_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e986c29adf6f7a86e9057ae0a9d701b2a90", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98eecf661d1d4b4d4224466e6c8c198347", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985c6ed9a03b25332a90a8fecae38a41e2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba3dd60d473bec163d3d8b3bb23409c0", "guid": "bfdfe7dc352907fc980b868725387e985c8f8889480ac5d0a77ff3f69f0dc77c"}], "guid": "bfdfe7dc352907fc980b868725387e98dfe3734de409358b11f81be5b30ec113", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98c4db3ee10fd3aea38cd0fd6d5693c776", "name": "FirebaseCoreInternal_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}