{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b23cea1aa7220e96493c53c7fc4d99b", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98694cbc9c054183df0aff158291a55fb0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9822ed1d8d35c7bce547f208dd806b366a", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98684b5e6725b0211b058045397598e0c8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9822ed1d8d35c7bce547f208dd806b366a", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8cb8ced14f26c51862f2ec5549e892e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9813625abb9eef4285b81e2999ee13deae", "guid": "bfdfe7dc352907fc980b868725387e98b7a863e8ed36bc94c62f0b0934be10d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f50c99d0561dc8fde7365b57c7283d1", "guid": "bfdfe7dc352907fc980b868725387e9842523bd67818ad9991cf147eb5b72552", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2e65e376d7138963ba8ca932af1b138", "guid": "bfdfe7dc352907fc980b868725387e98f78f47723dcab049fd6219e7081fe63e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd58aef1e592575297284f5db48b3c18", "guid": "bfdfe7dc352907fc980b868725387e98b074ab3c5c3ae776f0f0cbfb20c3b789", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845f8e2d1d223303a933963e6b5841691", "guid": "bfdfe7dc352907fc980b868725387e98b31369390303994abacac4be9fbef04f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98740ec0dac219cdbc92c7a6badcbb23c6", "guid": "bfdfe7dc352907fc980b868725387e983a43a61e4b1f8f8ea7515884b5eab45c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c803b526a48e67c5c25e6ade429d3a1c", "guid": "bfdfe7dc352907fc980b868725387e986916be5fc14f25b39f77a1a711860c10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cad3933726b887c3cd80c6dfb9b0c4aa", "guid": "bfdfe7dc352907fc980b868725387e9851b71bfb779bbdc04ca0b1884491b530", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985661d5da43316fb1101d60d6f73538c4", "guid": "bfdfe7dc352907fc980b868725387e98a903dc236d175a1ffe1a635fa661983c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fd6fa1d0c9e5accc751f80bc3236eee", "guid": "bfdfe7dc352907fc980b868725387e98dcaf904cf551c468b227f6d66de74aee", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ab08311244244781f7c7b3fb427843bf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c16e9d37b0b5d2b36fd2b36991b1d19d", "guid": "bfdfe7dc352907fc980b868725387e98dad011fda1a864ced2b2990352c393ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbf539eec73bd8f0d8bad958c0706d46", "guid": "bfdfe7dc352907fc980b868725387e98affa60f76ff4ed5b0079f9e33a31720c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7003077ac15f1a6a0b26c8dcce197ac", "guid": "bfdfe7dc352907fc980b868725387e98f1c744ef4f9109ca986cea6b2f87c49c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98573b9f094d5c1de613902a4f0e3ea5a7", "guid": "bfdfe7dc352907fc980b868725387e98b6af23637cbf7caf0dc0f9a34df77a92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a242622fc859ba3f98ed1e3a7567a55", "guid": "bfdfe7dc352907fc980b868725387e982b0580814edafe42981dca11b6e56007"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880b32d6e939435c16656c2e34b8675f9", "guid": "bfdfe7dc352907fc980b868725387e98eb7550db36415396feb5c29368ee52f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860562905892db4dbb68f7e0787ada48e", "guid": "bfdfe7dc352907fc980b868725387e9809f08fc266f8b0adfa416279271e9b0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd623a3e6d39ac2811410b6b7b76408b", "guid": "bfdfe7dc352907fc980b868725387e98bb8a663b33a8da59ec100fdf1219ff3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883067cfcaaa2e012df7c79f2003e90ee", "guid": "bfdfe7dc352907fc980b868725387e980f0af62eacbc36d7362ed810f221acd6"}], "guid": "bfdfe7dc352907fc980b868725387e98310725dc1bdf18c7d17a739416af0ae0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e98a631cb683cdd0a0c8816e72e53c8e24d"}], "guid": "bfdfe7dc352907fc980b868725387e989b322b3289c9db31c2af6ee8f96ed860", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988509337b051ad7f057ef9a9f947506d3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}