import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/dio/api_requests.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/no_leading_space_formatter.dart';
import 'package:overolasuppliers/helper/media_picker.dart';
import 'package:overolasuppliers/helper/other/date_text_formatter.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/model/login_model.dart';
import 'package:overolasuppliers/model/upload_image_model.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_label_container.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';

class AddBusinessInformation extends StatefulWidget {
  const AddBusinessInformation({super.key});

  @override
  State<AddBusinessInformation> createState() => _AddBusinessInformationState();
}

class _AddBusinessInformationState extends State<AddBusinessInformation>
    with InputValidationUtil
    implements ServerResponse {
  RxString tradeCertificateName = ''.obs,
      emirateIdName = ''.obs,
      strBusinessName = "".obs,
      strTradeFileError = "".obs,
      strEmiratesIDError = "".obs,
      strOwnerName = "".obs,
      tradeCertificateExpiryDate = "".obs,
      strError = ''.obs,
      ownerLegalIdExpiryDate = "".obs;

  File? tradeCertificate, emiratesId;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late ApiRequest _request;

  late GraphQlInitilize _graphQlInitilize;

  late AppLocalizations appLocal;

  var userId = Get.arguments[0];

  bool isHaveShop = false;

  RxBool isUpdateTradeCertificate = false.obs, isUpdateEmratesId = false.obs;

  Supplier? info;

  List<String> arrParams = [];
  String strReturnedMessage = "";

  List<ValidationHistory> arrValidationHistory = [];

  @override
  Widget build(BuildContext context) {
    appLocal = AppLocalizations.of(context)!;
    List argumegnt = Get.arguments;
    if (argumegnt.length > 1) {
      info = Get.arguments[1];
      isHaveShop = Get.arguments[2] ?? false;
      if (info != null) {
        info?.validationHistory?.sort((a, b) =>
            DateTime.parse(b.createdAt ?? "").millisecondsSinceEpoch.compareTo(
                DateTime.parse(a.createdAt ?? "").millisecondsSinceEpoch));
        for (ValidationHistory element in (info?.validationHistory ?? [])) {
          if (element.returnMessage != null) {
            arrValidationHistory.add(element);
            strReturnedMessage += "*) ${element.returnMessage ?? ""}\n";
          } else {
            break;
          }
        }
        strBusinessName.value = info?.bussinessName ?? "";
        strOwnerName.value = info?.bussinessOwnerName ?? "";
        emirateIdName.value =
            (info?.bussinessOwnerLegalId ?? "").split('/').last;
        tradeCertificateName.value =
            (info?.bussinessTradeCertificate ?? "").split('/').last;
        tradeCertificateExpiryDate.value = GlobalMethods.convertTimeFormate(
            info?.tradeCertificateExpiryDate ?? "",
            format: "yyyy-MM-dd");
        ownerLegalIdExpiryDate.value = GlobalMethods.convertTimeFormate(
            info?.ownerLegalIdExpiryDate ?? "",
            format: "yyyy-MM-dd");
      }
    }
    _request = ApiRequest(this);
    _graphQlInitilize = GraphQlInitilize(this);
    Future.delayed(Duration.zero, () {
      if (arrValidationHistory.isNotEmpty) {
        _formKey.currentState!.validate();
      }
    });

    bool checkContainValue(String value) {
      return (arrValidationHistory.indexWhere(
                  (element) => (element.returnValues ?? []).contains(value))) !=
              -1
          ? true
          : false;
    }

    return Scaffold(
      appBar: ElbaabHeader(
          leadingBack: isHaveShop,
          title: info != null
              ? appLocal.updateBusinessInformation
              : appLocal.addBusinessInformation),
      body: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.all(16.0).r,
          child: Form(
            key: _formKey,
            autovalidateMode: info == null
                ? AutovalidateMode.disabled
                : AutovalidateMode.onUserInteraction,
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  ElbaaabInputTextField(
                    initialValue: info?.bussinessName ?? "",
                    margin: const EdgeInsets.only(top: 20).r,
                    charaterlimit: 70,
                    autoTextDirection: true,
                    onChanged: (v) => strBusinessName.value = v,
                    validator: (v) => validateFieldEmpty(
                      v,
                      errorMessage: appLocal.bussinessNameFeildReturnMessage,
                      serverValue: info?.bussinessName ?? "",
                      isReturend: checkContainValue("Business Name"),
                    ),
                    hint: appLocal.bussinessNameFeildHint,
                    inputType: TextInputType.text,
                    formatter: [
                      FilteringTextInputFormatter.singleLineFormatter,
                      NoLeadingSpaceFormatter()
                    ],
                    label: appLocal.bussinessNameFeildLabel,
                  ),
                  ElbaaabInputTextField(
                    initialValue: info?.bussinessOwnerName ?? "",
                    margin: const EdgeInsets.only(top: 24).r,
                    onChanged: (v) => strOwnerName.value = v,
                    hint: appLocal.bussinessOwnerNameFeildHint,
                    autoTextDirection: true,
                    inputType: TextInputType.text,
                    charaterlimit: 60,
                    validator: (v) => validateFieldEmpty(
                      v,
                      errorMessage:
                          appLocal.bussinessOwnerNameFeildHintReturnMessae,
                      serverValue: info?.bussinessOwnerName ?? "",
                      isReturend: checkContainValue("Owner/ Manager Name"),
                    ),
                    inputFormatter: '[a-zA-Z ا-ي]',
                    label: appLocal.bussinessOwnerNameFeildLabel,
                  ),
                  SizedBox(height: 20.h),
                  Obx(
                    () => ElbaabLabelContainer(
                      leading: InkWell(
                        onTap: () {
                          if (info != null && tradeCertificate == null) {
                            GlobalMethods.launchInWebView(
                                info?.bussinessTradeCertificate ?? "");
                          } else {
                            showMediaPicker(context,
                                    requiredDocumentPicker: true)
                                .then((value) {
                              if (value != null) {
                                tradeCertificate = value;
                                isUpdateTradeCertificate.value = true;
                                tradeCertificateName.value =
                                    tradeCertificate!.path.split('/').last;
                              }
                            });
                          }
                        },
                        child: Text(
                          tradeCertificateName.value.isEmpty
                              ? "ex : aroundix_tradecertificate.pdf"
                              : tradeCertificateName.value,
                          maxLines: 1,
                          style: FontStyles.fontRegular(
                            decoration: info != null
                                ? TextDecoration.underline
                                : TextDecoration.none,
                            color: tradeCertificateName.value.isEmpty
                                ? Colors.white.withOpacity(0.5)
                                : info != null
                                    ? tradeCertificateName.value ==
                                            info?.bussinessTradeCertificate
                                                ?.split("/")
                                                .last
                                        ? AppColors.colorPrimary
                                        : Colors.white
                                    : Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      onTap: () =>
                          showMediaPicker(context, requiredDocumentPicker: true)
                              .then((value) {
                        if (value != null) {
                          tradeCertificate = value;
                          isUpdateTradeCertificate.value = true;
                          tradeCertificateName.value =
                              tradeCertificate!.path.split('/').last;
                        }
                      }),
                      trailing: SvgPicture.string(SvgStrings.iconUpload),
                      borderColor: checkContainValue("Trade License")
                          ? isUpdateTradeCertificate.value
                              ? Colors.transparent
                              : AppColors.colorDanger
                          : strTradeFileError.value.isEmpty
                              ? null
                              : isUpdateTradeCertificate.value
                                  ? Colors.transparent
                                  : AppColors.colorDanger,
                      errorText: checkContainValue("Trade License")
                          ? isUpdateTradeCertificate.value
                              ? ""
                              : appLocal.tradeLicenseReturned
                          : strTradeFileError.value.isEmpty
                              ? null
                              : isUpdateTradeCertificate.value
                                  ? ""
                                  : strTradeFileError.value,
                      label: appLocal.uploadTradeCertificate,
                    ),
                  ),
                  Obx(
                    () => ElbaaabInputTextField(
                      initialValue: tradeCertificateExpiryDate.value,
                      margin: const EdgeInsets.only(top: 15),
                      onChanged: (v) => tradeCertificateExpiryDate.value = v,
                      hint: 'ex : 2022 - 08 - 30',
                      autoTextDirection: true,
                      inputType: TextInputType.datetime,
                      validator: (v) => validateFieldEmpty(
                        v,
                        errorMessage: appLocal.updateTradeCertiificateDate,
                        serverValue:
                            (info?.tradeCertificateExpiryDate ?? "").isNotEmpty
                                ? GlobalMethods.convertTimeFormate(
                                    info?.tradeCertificateExpiryDate ?? "",
                                    format: "yyyy-MM-dd")
                                : "",
                        isReturend:
                            checkContainValue("Trade License Expiry Date"),
                      ),
                      onTap: () async {
                        tradeCertificateExpiryDate.value =
                            await BottomSheets.datePicker(context);
                      },
                      suffix: Icon(
                        Icons.calendar_today,
                        color: AppColors.colorPrimary,
                      ),
                      label: appLocal.tradeCerficateExpireDate,
                    ),
                  ),
                  Obx(
                    () => ElbaabLabelContainer(
                      leading: InkWell(
                        onTap: () {
                          if (info != null && emiratesId == null) {
                            GlobalMethods.launchInWebView(
                                info?.bussinessOwnerLegalId ?? "");
                          } else {
                            showMediaPicker(context,
                                    requiredDocumentPicker: true)
                                .then((value) {
                              if (value != null) {
                                emiratesId = value;
                                isUpdateEmratesId.value = true;
                                emirateIdName.value =
                                    emiratesId!.path.split('/').last;
                              }
                            });
                          }
                        },
                        child: Text(
                          emirateIdName.value.isEmpty
                              ? 'ex : aroundix.pdf'
                              : emirateIdName.value,
                          maxLines: 1,
                          style: FontStyles.fontRegular(
                            decoration: info != null
                                ? TextDecoration.underline
                                : TextDecoration.none,
                            color: emirateIdName.value.isEmpty
                                ? Colors.white.withOpacity(0.5)
                                : info != null
                                    ? emirateIdName.value ==
                                            info?.bussinessOwnerLegalId
                                                ?.split("/")
                                                .last
                                        ? AppColors.colorPrimary
                                        : Colors.white
                                    : Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      onTap: () =>
                          showMediaPicker(context, requiredDocumentPicker: true)
                              .then((value) {
                        if (value != null) {
                          emiratesId = value;
                          isUpdateEmratesId.value = true;
                          emirateIdName.value =
                              emiratesId!.path.split('/').last;
                        }
                      }),
                      edgeInsets: const EdgeInsets.only(top: 20),
                      trailing: SvgPicture.string(SvgStrings.iconUpload),
                      borderColor: checkContainValue("Owner ID")
                          ? isUpdateEmratesId.value
                              ? Colors.transparent
                              : AppColors.colorDanger
                          : strEmiratesIDError.value.isEmpty
                              ? null
                              : isUpdateEmratesId.value
                                  ? Colors.transparent
                                  : AppColors.colorDanger,
                      errorText: checkContainValue("Owner ID")
                          ? isUpdateEmratesId.value
                              ? ""
                              : appLocal.ownerIDReturned
                          : strEmiratesIDError.value.isEmpty
                              ? null
                              : isUpdateEmratesId.value
                                  ? ""
                                  : strEmiratesIDError.value,
                      label: appLocal.uploadEmiratesID,
                    ),
                  ),
                  Obx(
                    () => ElbaaabInputTextField(
                      initialValue: ownerLegalIdExpiryDate.value,
                      onTap: () async {
                        ownerLegalIdExpiryDate.value =
                            await BottomSheets.datePicker(context);
                      },
                      margin: const EdgeInsets.only(top: 15),
                      validator: (v) => validateFieldEmpty(
                        v,
                        errorMessage: appLocal.updateEmiratesIDDate,
                        serverValue:
                            (info?.ownerLegalIdExpiryDate ?? "").isNotEmpty
                                ? GlobalMethods.convertTimeFormate(
                                    info?.ownerLegalIdExpiryDate ?? "",
                                    format: "yyyy-MM-dd")
                                : "",
                        isReturend: checkContainValue("Owner ID Expiry Date"),
                      ),
                      autoTextDirection: true,
                      onChanged: (v) => ownerLegalIdExpiryDate.value = v,
                      hint: 'ex : 2022 / 08 / 30',
                      suffix: Icon(Icons.calendar_today,
                          color: AppColors.colorPrimary),
                      formatter: [DateTextFormatter()],
                      label: appLocal.emiratesIDExpiryDate,
                    ),
                  ),
                  if (arrValidationHistory.isNotEmpty)
                    const SizedBox(height: 20),
                  if (arrValidationHistory.isNotEmpty)
                    Text(
                      appLocal.adminNote,
                      style: FontStyles.fontSemibold(),
                    ),
                  if (arrValidationHistory.isNotEmpty)
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        strReturnedMessage,
                        style: FontStyles.fontRegular(
                            color: AppColors.colorDanger),
                      ),
                    ),
                  const SizedBox(height: 20),
                  ElbaabNetworkEroor(strError: strError),
                  const SizedBox(height: 20),
                ]),
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: SizedBox(
          height: !isHaveShop ? 120 : 80,
          child: Column(
            children: [
              const Spacer(),
              ElbaabGradientButtonWidget(
                edgeInsets: const EdgeInsets.symmetric(horizontal: 14),
                onPress: () {
                  strEmiratesIDError.value = "";
                  strTradeFileError.value = "";
                  if (info == null && tradeCertificate == null) {
                    strTradeFileError.value = appLocal.tradeCertificateRequired;
                  }
                  if (info == null && emiratesId == null) {
                    strEmiratesIDError.value = appLocal.emiratesIDRequired;
                  }

                  if (_formKey.currentState!.validate()) {
                    if (info != null) {
                      if (checkContainValue("Trade License") &&
                          tradeCertificate == null) {
                        strError.value = appLocal.aleartUploadTradeCertificate;
                      } else if (checkContainValue("Owner ID") &&
                          emiratesId == null) {
                        strError.value = appLocal.aleartUploadId;
                      } else {
                        if (tradeCertificate != null || emiratesId != null) {
                          List<File> arrFiles = [];
                          if (tradeCertificate != null) {
                            arrFiles.add(tradeCertificate!);
                            arrParams.add("tradeLicence");
                          }
                          if (emiratesId != null) {
                            arrFiles.add(emiratesId!);
                            arrParams.add("userLegalId");
                          }
                          _request.uploadImage(
                              files: arrFiles,
                              parameters: arrParams,
                              type: "UPLOAD");
                        } else {
                          uploadInfo(info?.bussinessTradeCertificate ?? "",
                              info?.bussinessOwnerLegalId ?? "", true);
                        }
                      }
                    } else {
                      _request.uploadImage(
                          files: [tradeCertificate!, emiratesId!],
                          parameters: ['tradeLicence', 'userLegalId'],
                          type: "UPLOAD");
                    }
                  }
                },
                text:
                    info != null ? appLocal.updateInfo : appLocal.completeSetup,
              ),
              if (!isHaveShop) const Spacer(),
              if (!isHaveShop)
                Center(
                  child: InkWell(
                    onTap: () => GlobalMethods.logout(),
                    child: Text(
                      appLocal.logout,
                      style:
                          FontStyles.fontRegular(color: AppColors.colorPrimary),
                    ),
                  ),
                ),
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {
    strError.value = appLocal.localeName == "en"
        ? (BaseModel.fromJson(error).message ?? "")
        : (BaseModel.fromJson(error).arMessage ?? "");
  }

  @override
  onSucess(response, String type) {
    if (type == "UPLOAD") {
      UploadImageModel model = UploadImageModel.fromJson(response);
      if (model.status == statusOK) {
        if (info != null) {
          String tradeLicence = info?.bussinessTradeCertificate ?? "";
          String userLegalId = info?.bussinessOwnerLegalId ?? "";
          if (arrParams.length == 1) {
            if (arrParams[0] == "tradeLicence") {
              tradeLicence = model.fileList?[0].fileUrls?[0] ?? "";
            } else {
              userLegalId = model.fileList?[0].fileUrls?[0] ?? "";
            }
          } else {
            tradeLicence = model.fileList?[0].fileUrls?[0] ?? "";
            userLegalId = model.fileList?[1].fileUrls?[0] ?? "";
          }
          uploadInfo(tradeLicence, userLegalId, true);
        } else {
          String tradeLicence = model.fileList?[0].fileUrls?[0] ?? "";
          String userLegalId = model.fileList?[1].fileUrls?[0] ?? "";
          uploadInfo(tradeLicence, userLegalId, false);
        }
      }
    } else {
      BaseModel model = BaseModel.fromJson(response);
      if (model.status == statusOK) {
        if (isHaveShop) {
          BottomSheets.showAlertMessageBottomSheet(
                  appLocal.localeName == "en"
                      ? model.message ?? ""
                      : model.arMessage ?? "",
                  appLocal.alert,
                  Get.context!)
              .then((value) => Get.offNamed(RouteNames.shopHomeScreen));
        } else {
          Get.offNamed(RouteNames.signupSuccessScreen);
        }
      } else {
        strError.value = appLocal.localeName == "ar"
            ? model.arMessage ?? ""
            : (model.message ?? "");
      }
    }
  }

  uploadInfo(String tradeLicence, String userLegalId, bool isUpdate) {
    if (info != null) {
      if ((strBusinessName.value == (info?.bussinessName ?? "")) &&
          (strOwnerName.value == (info?.bussinessOwnerName ?? "")) &&
          (tradeCertificateExpiryDate.value ==
              (GlobalMethods.convertTimeFormate(
                  info?.tradeCertificateExpiryDate ?? "",
                  format: "yyyy-MM-dd"))) &&
          (ownerLegalIdExpiryDate.value ==
              (GlobalMethods.convertTimeFormate(
                  info?.ownerLegalIdExpiryDate ?? "",
                  format: "yyyy-MM-dd"))) &&
          (tradeCertificateName.value ==
              (info?.bussinessTradeCertificate ?? "").split('/').last) &&
          (emirateIdName.value ==
              ((info?.bussinessOwnerLegalId ?? "").split('/').last))) {
        strError.value = ("No Changes Found");
        return;
      } else {
        strError.value = "";
      }
    }
    _graphQlInitilize.runMutation(
      context: MyApp.navigatorKey.currentContext!,
      query: isUpdate
          ? GraphQlQuries.updateBussinessInfo
          : GraphQlQuries.addBussinessInfo,
      variables: GraphQlVariables.addBussinessInfo(
        bussinessOwnerId: userId,
        bussinessTradeCertificate: tradeLicence,
        bussinessOwnerLegalId: userLegalId,
        bussinessName: strBusinessName.value,
        bussinessOwnerName: strOwnerName.value,
        tradeCertificateExpiryDate: tradeCertificateExpiryDate.value,
        ownerLegalIdExpiryDate: ownerLegalIdExpiryDate.value,
      ),
    );
  }
}
