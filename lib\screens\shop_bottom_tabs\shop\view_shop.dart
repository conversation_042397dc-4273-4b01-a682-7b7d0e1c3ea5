import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/other/shimmer.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/add_product/product_by_shop_model.dart';
import 'package:overolasuppliers/model/add_product/view_prduct/product_detail_model.dart';
import 'package:overolasuppliers/model/shop/ViewShopModel.dart';
import 'package:overolasuppliers/provider/shop_info_provider.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/shop_info.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/shop/product_listile.dart';
import 'package:provider/provider.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

class ViewShop extends StatefulWidget {
  const ViewShop({Key? key}) : super(key: key);

  @override
  State<ViewShop> createState() => _ViewShopState();
}

class _ViewShopState extends State<ViewShop>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin<ViewShop>
    implements ServerResponse {
  late TabController _tabController;

  RxInt tabIndex = 0.obs, categoryIndex = 0.obs;
  Shop? shop;
  late GraphQlInitilize _request;
  late AutoScrollController controller;
  RxList<SubCategory> arrCategories = <SubCategory>[].obs;
  RxList<ShopProductsList> arrShopProducts = <ShopProductsList>[].obs;
  ProductByShopModel? productByShopModel;
  String strCategoryId = "", strSubCategory = "";
  RxString strError = "".obs;
  int currentPage = 1;
  bool haveProducts = false;
  RxBool isLoading = false.obs, isVariant = false.obs;
  List<AvailableCategories> availableCategories = [];
  late AppLocalizations appLocal;

  RxInt acceptedProductCount = 0.obs,
      pendingProductCount = 0.obs,
      viewsCount = 0.obs,
      draftProductCount = 0.obs,
      qtyAlertProductCount = 0.obs,
      outOfStockProductCount = 0.obs,
      outOfStockProductsCount = 0.obs,
      outOfStockVariantsCount = 0.obs,
      freeDeliveryProductCount = 0.obs;

  @override
  void initState() {
    controller = AutoScrollController(
        viewportBoundaryGetter: () =>
            Rect.fromLTRB(0, 40, 0, MediaQuery.of(context).padding.bottom),
        axis: Axis.vertical);
    _request = GraphQlInitilize(this);
    controller.addListener(_onScroll);
    super.initState();

    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);
    Future.delayed(Duration.zero, () {
      print("shop hit 1");
      _request.runQuery(
          context: context,
          query: GraphQlQuries.viewShop,
          isRequiredLoader: false,
          type: "SHOP");
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging && _tabController.index == 0) return;
    currentPage = 1;
    strCategoryId = "";
    strSubCategory = "";
    categoryIndex = 0.obs;
    arrCategories.clear();
    arrShopProducts.clear();
    checkSelectedTab();
    if (_tabController.index == 1) {
      _request.runQuery(
          context: context,
          query: GraphQlQuries.getOutOfStockVariants,
          type: "getOutOfStockVariants",
          isRequiredLoader: true,
          variables: GraphQlVariables.getPaginated());
    }
  }

  void _onScroll() {
    if (controller.position.pixels == controller.position.maxScrollExtent) {
      if (!haveProducts) {
        _loadMoreItems();
      }
    }
  }

  _loadMoreItems() {
    bool hasNextPage = false;
    if (categoryIndex.value == 0) {
      if (arrShopProducts.first.products == null) {
        hasNextPage = arrShopProducts.first.variants?.hasNextPage ?? false;
        currentPage = arrShopProducts.first.variants?.page ?? 0;
      } else {
        hasNextPage = arrShopProducts.first.products?.hasNextPage ?? false;
        currentPage = arrShopProducts.first.products?.page ?? 0;
      }
    } else {
      int indexWhere = arrShopProducts.indexWhere((element) =>
          element.category ==
          arrCategories[categoryIndex.value].subCategoryName);
      if (indexWhere != -1) {
        if (arrShopProducts[indexWhere].products == null) {
          hasNextPage =
              arrShopProducts[indexWhere].variants?.hasNextPage ?? false;
          currentPage = arrShopProducts[indexWhere].variants?.page ?? 0;
        } else {
          hasNextPage =
              arrShopProducts[indexWhere].products?.hasNextPage ?? false;
          currentPage = arrShopProducts[indexWhere].products?.page ?? 0;
        }
      }
    }
    if (isLoading.value || !hasNextPage) {
      return;
    }
    isLoading.value = true;
    currentPage++;
    checkSelectedTab();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final info = Provider.of<UpdatedInfo>(context);

    appLocal = AppLocalizations.of(context)!;
    Future.delayed(Duration.zero, () {
      if (info.productRwtriveFromTrash) {
        Provider.of<UpdatedInfo>(context, listen: false)
            .isProductRemoveFromTrash(false);
        Future.delayed(
          Duration.zero,
          () => _request.runQuery(
            context: context,
            query: GraphQlQuries.viewShop,
            type: "SHOP",
            isRequiredLoader: false,
          ),
        );
      }
    });

    if (info.updateShopStatus == "Returned") {
      Provider.of<UpdatedInfo>(context, listen: false)
          .shopStatusUpdate("Returned ");
      print("shop hit 3");
      Future.delayed(
        Duration.zero,
        () => _request.runQuery(
          context: context,
          query: GraphQlQuries.viewShop,
          type: "SHOP",
        ),
      );
    }

    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.myShop,
        notificationBell: true,
        trailingWidget: Container(
          padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 6),
          decoration: BoxDecoration(
            color: info.updateShopStatus == "Returned" ||
                    info.updateShopStatus == "Returned "
                ? AppColors.colorDanger.withOpacity(0.1)
                : info.updateShopStatus == "Accepted"
                    ? AppColors.colorSecondary.withOpacity(0.1)
                    : AppColors.colorSecondaryYellow.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            info.updateShopStatus,
            style: FontStyles.fontMedium(
              fontSize: 11,
              color: info.updateShopStatus == "Returned" ||
                      info.updateShopStatus == "Returned "
                  ? AppColors.colorDanger
                  : info.updateShopStatus == "Accepted"
                      ? AppColors.colorSecondary
                      : AppColors.colorSecondaryYellow,
            ),
          ),
        ),
        leadingSpace: 7,
        leadingWidget: InkWell(
          onTap: () => _request.runQuery(
              context: context,
              query: GraphQlQuries.viewShop,
              type: "viewShop"),
          child: Container(
            height: 32,
            padding: const EdgeInsets.symmetric(horizontal: 7),
            decoration: BoxDecoration(
              color: AppColors.colorPrimary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  appLocal.editShop,
                  style: FontStyles.fontMedium(
                    fontSize: 11,
                    color: AppColors.colorPrimary,
                  ),
                ),
                const SizedBox(width: 3),
                SvgPicture.string(
                  SvgStrings.iconEditGray,
                  color: AppColors.colorPrimary,
                  height: 12,
                  width: 12,
                ),
              ],
            ),
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          currentPage = 1;
          categoryIndex = 0.obs;
          strCategoryId = "";
          _tabController.index = 0;
          strSubCategory = "";
          shop = null;
          setState(() {});
          _request.runQuery(
              context: context,
              query: GraphQlQuries.viewShop,
              isRequiredLoader: false,
              type: "SHOP");
        },
        child: DefaultTabController(
          length: 2,
          child: CustomScrollView(
            controller: controller,
            slivers: [
              SliverToBoxAdapter(
                child: SizedBox(
                  height: 600.h,
                  child: AutoScrollTag(
                    key: const ValueKey(-1),
                    controller: controller, 
                    index: -1,
                    child: Obx(
                      () => ShopInfo(
                        tabIndex: (v) {
                          if (v != tabIndex.value) {
                            if (v == 4) {
                              setState(() {});
                            }
                            _tabController.index = 0;
                            tabIndex.value = v;
                            currentPage = 1;
                            strCategoryId = "";
                            strSubCategory = "";
                            categoryIndex = 0.obs;
                            arrCategories.clear();
                            arrShopProducts.clear();
                            checkSelectedTab();
                          }
                        },
                        selectedTabIndex: tabIndex.value,
                        shop: shop,
                        viewsCount: viewsCount,
                        pendingProductCount: pendingProductCount,
                        acceptedProductCount: acceptedProductCount,
                        draftProductCount: draftProductCount,
                        qtyAlertProductCount: qtyAlertProductCount,
                        outOfStockProductCount: outOfStockProductCount,
                        freeDeliveryProductCount: freeDeliveryProductCount,
                      ),
                    ),
                  ),
                ),
              ),
              SliverAppBar(
                pinned: true,
                floating: true,
                backgroundColor: AppColors.backgroundColorDark,
                snap: true,
                expandedHeight: tabIndex.value == 4 ? 35 : 0,
                bottom: PreferredSize(
                  preferredSize:
                      Size(context.width, tabIndex.value == 4 ? 46 : 0),
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              vertical: 3.h, horizontal: 3.w),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.white.withOpacity(0.1),
                                Colors.white.withOpacity(0.05),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                                color: Colors.white.withOpacity(0.1)),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () => isVariant.value = false,
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 200),
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 4.w),
                                    padding:
                                        EdgeInsets.symmetric(vertical: 8.h),
                                    decoration: BoxDecoration(
                                      gradient: !isVariant.value
                                          ? LinearGradient(
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                              colors: [
                                                AppColors.colorPrimary,
                                                AppColors.colorPrimary
                                                    .withOpacity(0.8),
                                              ],
                                            )
                                          : null,
                                      color: isVariant.value
                                          ? Colors.black.withOpacity(0.2)
                                          : null,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: !isVariant.value
                                            ? Colors.white.withOpacity(0.2)
                                            : Colors.white.withOpacity(0.05),
                                      ),
                                      boxShadow: !isVariant.value
                                          ? [
                                              BoxShadow(
                                                color: AppColors.colorPrimary
                                                    .withOpacity(0.3),
                                                blurRadius: 8,
                                                offset: const Offset(0, 2),
                                              ),
                                            ]
                                          : [
                                              BoxShadow(
                                                color: Colors.black
                                                    .withOpacity(0.1),
                                                blurRadius: 4,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                    ),
                                    child: Center(
                                      child: Text(
                                        "Products",
                                        style: TextStyle(
                                          fontSize: 13.sp,
                                          color: !isVariant.value
                                              ? Colors.white
                                              : Colors.white.withOpacity(0.5),
                                          fontWeight: !isVariant.value
                                              ? FontWeight.w600
                                              : FontWeight.w400,
                                          letterSpacing: 0.3,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () => isVariant.value = true,
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 200),
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 4.w),
                                    padding:
                                        EdgeInsets.symmetric(vertical: 8.h),
                                    decoration: BoxDecoration(
                                      gradient: isVariant.value
                                          ? LinearGradient(
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                              colors: [
                                                AppColors.colorPrimary,
                                                AppColors.colorPrimary
                                                    .withOpacity(0.8),
                                              ],
                                            )
                                          : null,
                                      color: !isVariant.value
                                          ? Colors.black.withOpacity(0.2)
                                          : null,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: isVariant.value
                                            ? Colors.white.withOpacity(0.2)
                                            : Colors.white.withOpacity(0.05),
                                      ),
                                      boxShadow: isVariant.value
                                          ? [
                                              BoxShadow(
                                                color: AppColors.colorPrimary
                                                    .withOpacity(0.3),
                                                blurRadius: 8,
                                                offset: const Offset(0, 2),
                                              ),
                                            ]
                                          : [
                                              BoxShadow(
                                                color: Colors.black
                                                    .withOpacity(0.1),
                                                blurRadius: 4,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                    ),
                                    child: Center(
                                      child: Text(
                                        "Variants",
                                        style: TextStyle(
                                          fontSize: 13.sp,
                                          color: isVariant.value
                                              ? Colors.white
                                              : Colors.white.withOpacity(0.5),
                                          fontWeight: isVariant.value
                                              ? FontWeight.w600
                                              : FontWeight.w400,
                                          letterSpacing: 0.3,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              if (arrCategories.isNotEmpty && tabIndex.value != 4)
                SliverAppBar(
                  pinned: true,
                  floating: true,
                  snap: true,
                  expandedHeight: 45,
                  backgroundColor: AppColors.backgroundColorDark,
                  elevation: 0,
                  bottom: PreferredSize(
                    preferredSize: Size(context.width, 45),
                    child: Container(
                      height: 45,
                      width: context.width,
                      decoration: BoxDecoration(
                        color: AppColors.backgroundColorDark,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.08),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      padding:
                          const EdgeInsets.symmetric(horizontal: kLeftSpace),
                      child: Obx(
                        () => ListView.builder(
                          itemCount: arrCategories.length,
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (context, index) {
                            final isSelected = categoryIndex.value == index;
                            final productCount =
                                arrCategories[index].productCount ?? 0;
                            final categoryName = appLocal.localeName == "en"
                                ? arrCategories[index].subCategoryName ?? ""
                                : arrCategories[index]
                                        .subCategoryAr
                                        ?.subCategoryName ??
                                    "";

                            return Padding(
                              padding: const EdgeInsets.only(
                                  right: 8, top: 6, bottom: 6),
                              child: GestureDetector(
                                onDoubleTap: () async {
                                  await controller.scrollToIndex(0,
                                      preferPosition: AutoScrollPosition.begin);
                                },
                                onTap: () async {
                                  if (categoryIndex.value != index) {
                                    await controller.scrollToIndex(0,
                                        preferPosition:
                                            AutoScrollPosition.begin);
                                  } else if (productCount > 4) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        backgroundColor:
                                            AppColors.headerColorDark,
                                        showCloseIcon: true,
                                        closeIconColor: Colors.white,
                                        content: Text(
                                          'Double-tap for an instant lift to the top',
                                          style: FontStyles.fontSemibold(),
                                        ),
                                      ),
                                    );
                                  }

                                  categoryIndex.value = index;
                                  if (categoryIndex.value == 0) {
                                    strCategoryId = "";
                                    strSubCategory = "";
                                  } else {
                                    for (var element in availableCategories) {
                                      int indeWhere = (element
                                                  .productSubCategory ??
                                              [])
                                          .indexWhere((element) =>
                                              element.subCategoryName ==
                                              arrCategories[categoryIndex.value]
                                                  .subCategoryName);
                                      if (indeWhere > -1) {
                                        strCategoryId =
                                            element.productCategory?.id ?? "";
                                        strSubCategory = element
                                                .productSubCategory?[indeWhere]
                                                .id ??
                                            "";
                                        setState(() {});
                                        break;
                                      }
                                    }
                                  }

                                  if (arrShopProducts
                                          .where((element) =>
                                              element.category ==
                                              arrCategories[categoryIndex.value]
                                                  .subCategoryName)
                                          .isEmpty ||
                                      categoryIndex.value == 0) {
                                    currentPage = 1;
                                    checkSelectedTab();
                                  }
                                },
                                child: TweenAnimationBuilder<double>(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                  tween: Tween<double>(
                                    begin: 0,
                                    end: isSelected ? 1.0 : 0.0,
                                  ),
                                  builder: (context, value, child) {
                                    return AnimatedContainer(
                                      duration:
                                          const Duration(milliseconds: 300),
                                      curve: Curves.easeInOut,
                                      height: 32,
                                      transform: Matrix4.identity()
                                        ..scale(1.0 +
                                            (value *
                                                0.05)), // Subtle scale effect
                                      decoration: BoxDecoration(
                                        color: Color.lerp(
                                              AppColors.headerColorDark,
                                              AppColors.colorPrimary,
                                              value,
                                            ) ??
                                            AppColors.headerColorDark,
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: Color.lerp(
                                                AppColors.colotJumbo
                                                    .withOpacity(0.2),
                                                Colors.transparent,
                                                value,
                                              ) ??
                                              AppColors.colotJumbo
                                                  .withOpacity(0.2),
                                          width: 1.5,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.colorPrimary
                                                .withOpacity(0.25 * value),
                                            blurRadius: 8 * value,
                                            offset: Offset(0, 4 * value),
                                          ),
                                        ],
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Color.lerp(
                                                  AppColors.headerColorDark,
                                                  AppColors.colorPrimary,
                                                  value,
                                                ) ??
                                                AppColors.headerColorDark,
                                            Color.lerp(
                                                  AppColors.headerColorDark,
                                                  AppColors.colorPrimary
                                                      .withOpacity(0.8),
                                                  value,
                                                ) ??
                                                AppColors.headerColorDark,
                                          ],
                                        ),
                                      ),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: productCount > 0 ? 12 : 16,
                                        vertical: 2,
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            categoryName,
                                            style: FontStyles.fontMedium(
                                              fontSize: 13,
                                              color: Color.lerp(
                                                    AppColors.colotJumbo,
                                                    Colors.white,
                                                    value,
                                                  ) ??
                                                  AppColors.colotJumbo,
                                            ),
                                          ),
                                          if (productCount > 0) ...[
                                            const SizedBox(width: 6),
                                            AnimatedContainer(
                                              duration: const Duration(
                                                  milliseconds: 300),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 8,
                                                vertical: 2,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Color.lerp(
                                                      AppColors.colotJumbo
                                                          .withOpacity(0.1),
                                                      Colors.white
                                                          .withOpacity(0.25),
                                                      value,
                                                    ) ??
                                                    AppColors.colotJumbo
                                                        .withOpacity(0.1),
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                border: Border.all(
                                                  color: Color.lerp(
                                                        Colors.transparent,
                                                        Colors.white
                                                            .withOpacity(0.3),
                                                        value,
                                                      ) ??
                                                      Colors.transparent,
                                                  width: 1,
                                                ),
                                              ),
                                              child: Text(
                                                productCount.toString(),
                                                style: FontStyles.fontMedium(
                                                  fontSize: 11,
                                                  color: Color.lerp(
                                                        AppColors.colotJumbo,
                                                        Colors.white,
                                                        value,
                                                      ) ??
                                                      AppColors.colotJumbo,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              if (arrCategories.isNotEmpty)
                SliverToBoxAdapter(
                  child: AutoScrollTag(
                    key: const ValueKey(0),
                    controller: controller,
                    index: 0,
                    child: Container(
                      height: tabIndex.value == 4 ? 30 : 0,
                    ),
                  ),
                ),
              products(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget products(BuildContext context) {
    int productCatIndex = 0;
    if (categoryIndex.value > 0) {
      productCatIndex = arrShopProducts.indexWhere((element) =>
          element.category ==
          arrCategories[categoryIndex.value].subCategoryName);
    }
    return strError.value.isNotEmpty
        ? SliverFillRemaining(
            hasScrollBody: false,
            child: Obx(
              () => Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Empty state illustration
                      Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          color: AppColors.colorPrimary.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Icon(
                            Icons.inventory_2_outlined,
                            size: 80,
                            color: AppColors.colorPrimary.withOpacity(0.5),
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      
                      // Main error message
                      Text(
                        strError.value,
                        style: FontStyles.fontMedium(
                          fontSize: 18,
                          color: Colors.white.withOpacity(0.9),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      
                      // Supportive text
                      Text(
                        'No items found in this category. Check other tabs or add new products.',
                        style: FontStyles.fontRegular(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.5),
                        ),
                        textAlign: TextAlign.center,
                      ),
                       SizedBox(height: 32.h),
                      
                      // Action button
                      ElevatedButton(
                        onPressed: () {
                          checkSelectedTab();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.colorPrimary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.refresh, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Refresh',
                              style: FontStyles.fontMedium(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                                             SizedBox(height: 12.h),

                    ],
                  ),
                ),
              ),
            ),
          )
        : productCatIndex == -1 || arrShopProducts.isEmpty
            ? SliverGrid(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    childAspectRatio:
                        (MediaQuery.of(context).size.width / 2 / 380),
                    crossAxisCount: 2),
                delegate: SliverChildBuilderDelegate(
                  (BuildContext context, int index) {
                    return Shimmer.fromColors(
                        baseColor: AppColors.headerColorDark.withOpacity(0.5),
                        highlightColor: AppColors.colorPrimary.withOpacity(0.5),
                        child: Container(
                          margin: EdgeInsets.only(
                              left: (!(index % 2 == 1)) ? 10 : 0,
                              right: (index % 2 == 1) ? 10 : 0),
                          decoration: BoxDecoration(
                            color: Colors.grey,
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ));
                  },
                  childCount: 100,
                ),
              )
            : arrShopProducts[productCatIndex].products == null
                ? SliverList.builder(
                    itemCount: arrShopProducts[productCatIndex]
                        .variants
                        ?.items
                        ?.length,
                    itemBuilder: (context, index) {
                      ProductVariant variation =
                          arrShopProducts[productCatIndex]
                                  .variants
                                  ?.items?[index] ??
                              ProductVariant();
                      return Container(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 8),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.headerColorDark,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: InkWell(
                          onTap: () {
                            _request.runQuery(
                                context: context,
                                query: GraphQlQuries.getProductById,
                                variables: GraphQlVariables.getProductByID(
                                    productId: variation.productId?.id ?? ""),
                                type: "Product_Details");
                          },
                          child: Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 70,
                                    height: 140,
                                    child: GlobalMethods.netWorkImage(
                                      variation.variantImages?.first ?? "",
                                      BorderRadius.circular(5),
                                      BoxFit.cover,
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Expanded(
                                    child: SizedBox(
                                      height: 140,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          RichText(
                                            text: TextSpan(
                                                style: FontStyles.fontRegular(
                                                    fontSize: 12),
                                                children: [
                                                  const TextSpan(
                                                      text: 'Variant EIN : '),
                                                  TextSpan(
                                                    text:
                                                        variation.variantEIN ??
                                                            "",
                                                    style: TextStyle(
                                                      color: Colors.white
                                                          .withOpacity(0.5),
                                                    ),
                                                  ),
                                                ]),
                                          ),
                                          RichText(
                                            text: TextSpan(
                                                style: FontStyles.fontRegular(
                                                    fontSize: 12),
                                                children: [
                                                  const TextSpan(
                                                      text: 'Variant Name : '),
                                                  TextSpan(
                                                    text:
                                                        variation.variantName ??
                                                            "",
                                                    style: TextStyle(
                                                      color: Colors.white
                                                          .withOpacity(0.5),
                                                    ),
                                                  ),
                                                ]),
                                          ),
                                          RichText(
                                            text: TextSpan(
                                                style: FontStyles.fontRegular(
                                                    fontSize: 12),
                                                children: [
                                                  const TextSpan(
                                                      text: 'Brand : '),
                                                  TextSpan(
                                                    text: variation
                                                            .productId
                                                            ?.productBrand
                                                            ?.brandName ??
                                                        "",
                                                    style: TextStyle(
                                                      color: Colors.white
                                                          .withOpacity(0.5),
                                                    ),
                                                  ),
                                                ]),
                                          ),
                                          if ((variation
                                                      .variantAttributes
                                                      ?.variantColor
                                                      ?.colorFamily ??
                                                  "")
                                              .isNotEmpty)
                                            RichText(
                                              text: TextSpan(
                                                  style: FontStyles.fontRegular(
                                                      fontSize: 12),
                                                  children: [
                                                    const TextSpan(
                                                        text: 'Color : '),
                                                    TextSpan(
                                                      text: variation
                                                          .variantAttributes
                                                          ?.variantColor
                                                          ?.colorFamily,
                                                      style: TextStyle(
                                                        color: Colors.white
                                                            .withOpacity(0.5),
                                                      ),
                                                    ),
                                                  ]),
                                            ),
                                          if (variation
                                                  .variantAttributes
                                                  ?.variantSize
                                                  ?.size
                                                  ?.isNotEmpty ??
                                              false)
                                            RichText(
                                              text: TextSpan(
                                                  style: FontStyles.fontRegular(
                                                      fontSize: 12),
                                                  children: [
                                                    TextSpan(
                                                        text:
                                                            'Size ( ${variation.variantAttributes?.variantSize?.unit ?? ""} ) : '),
                                                    TextSpan(
                                                      text: variation
                                                              .variantAttributes
                                                              ?.variantSize
                                                              ?.size ??
                                                          "",
                                                      style: TextStyle(
                                                        color: Colors.white
                                                            .withOpacity(0.5),
                                                      ),
                                                    ),
                                                  ]),
                                            ),
                                          if (variation.variantAttributes
                                                  ?.variantCustomOptions !=
                                              null)
                                            Expanded(
                                              child: ListView.builder(
                                                  itemCount: variation
                                                      .variantAttributes
                                                      ?.variantCustomOptions
                                                      ?.length,
                                                  shrinkWrap: true,
                                                  physics:
                                                      const NeverScrollableScrollPhysics(),
                                                  itemBuilder:
                                                      (context, index) {
                                                    return RichText(
                                                      text: TextSpan(
                                                          style: FontStyles
                                                              .fontRegular(
                                                                  fontSize: 12),
                                                          children: [
                                                            TextSpan(
                                                                text:
                                                                    '${variation.variantAttributes?.variantCustomOptions?[index].attributeTitle} : '),
                                                            TextSpan(
                                                              text: variation
                                                                  .variantAttributes
                                                                  ?.variantCustomOptions?[
                                                                      index]
                                                                  .attributeValue,
                                                              style: TextStyle(
                                                                color: Colors
                                                                    .white
                                                                    .withOpacity(
                                                                        0.5),
                                                              ),
                                                            ),
                                                          ]),
                                                    );
                                                  }),
                                            ),
                                          RichText(
                                            text: TextSpan(
                                                style: GoogleFonts.montserrat(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                                children: [
                                                  const TextSpan(
                                                      text: 'Price : '),
                                                  TextSpan(
                                                    text:
                                                        "AED ${variation.variantPrice ?? 0}",
                                                    style:
                                                        GoogleFonts.montserrat(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w700,
                                                    ),
                                                  ),
                                                ]),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    })
                : SliverGrid.builder(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                        childAspectRatio:
                            (MediaQuery.of(context).size.width / 2 / 365),
                        crossAxisCount: 2),
                    itemCount: (arrShopProducts[productCatIndex]
                                    .products
                                    ?.items
                                    ?.length ??
                                0) <
                            4
                        ? 4
                        : (arrShopProducts[productCatIndex]
                                .products
                                ?.items
                                ?.length ??
                            0),
                    itemBuilder: (contex, section) {
                      return ((arrShopProducts[productCatIndex]
                                      .products
                                      ?.items
                                      ?.length ??
                                  0) <=
                              section)
                          ? Container()
                          : Padding(
                              padding: EdgeInsets.only(
                                  left: (!(section % 2 == 1)) ? 10 : 0,
                                  right: (section % 2 == 1) ? 10 : 0),
                              child: ProductListTile(
                                product: (arrShopProducts[productCatIndex]
                                        .products
                                        ?.items ??
                                    [])[section],
                                onTap: (v) {
                                  _request.runQuery(
                                      context: context,
                                      query: GraphQlQuries.getProductById,
                                      variables:
                                          GraphQlVariables.getProductByID(
                                              productId: v),
                                      type: "Product_Details");
                                },
                                width: MediaQuery.of(context).size.width / 2,
                                requiredPageView:
                                    ((arrShopProducts[productCatIndex]
                                                            .products
                                                            ?.items ??
                                                        [])[section]
                                                    .productOptions
                                                    ?.productColors ??
                                                [])
                                            .isEmpty
                                        ? true
                                        : false,
                              ),
                            );
                    });
  }

  getTabProducts({required String qurey, String type = ""}) {
    _request.runQuery(
      context: context,
      query: qurey,
      type: type,
      isRequiredLoader: false,
      variables: GraphQlVariables.getProducts(
        itemsNumber: 20,
        page: currentPage,
        categoryId: strCategoryId,
        subCategoryId: strSubCategory,
      ),
    );
  }

  @override
  onError(error, String type) {
    arrCategories.clear();
    arrShopProducts.clear();
    haveProducts = true;

    switch (type) {
      case "SHOP":
      case "Product_Details":
        break;
      default:
        arrShopProducts.add(ShopProductsList(""));
        _updateProductCounts(tabIndex.value, reset: true);

        break;
    }
  }

  @override
  onSucess(response, String type) async {
    switch (type) {
      case "SHOP":
        _handleShopResponse(response);
        break;
      case "viewShop":
        _handleViewShopResponse(response);
        break;
      case "Product_Details":
        _handleProductDetailsResponse(response);
        break;
      default:
        _handleProductByShopResponse(response, type);
        break;
    }
  }

  void _handleShopResponse(response) {
    ViewShopModel shopModel = ViewShopModel.fromJson(response);
    if (shopModel.status == statusOK) {
      shop = shopModel.shop;
      setState(() {});
      shopId = shop?.id ?? "";
      Provider.of<ShopInfoProvider>(MyApp.navigatorKey.currentContext!,
              listen: false)
          .setShopInfo(shopModel);
      if (shopModel.shop?.shopStatus == "Returned") {
        Provider.of<UpdatedInfo>(context, listen: false)
            .shopStatusUpdate("Returned ");
      } else {
        Provider.of<UpdatedInfo>(context, listen: false)
            .shopStatusUpdate(shop?.shopStatus ?? "");
      }
      viewsCount.value = shopModel.viewsCount ?? 0;
      acceptedProductCount.value = shopModel.acceptedProductsCount ?? 0;
      pendingProductCount.value = shopModel.pendingProductsCount ?? 0;
      draftProductCount.value = shopModel.draftProductsCount ?? 0;
      qtyAlertProductCount.value =
          shopModel.minimumQuantityAlertProductsCount ?? 0;
      outOfStockProductCount.value = shopModel.outOfStockProductsCount ?? 0;
      freeDeliveryProductCount.value =
          shopModel.freeDeliveryItemProductsCount ?? 0;
      arrCategories.clear();
      arrShopProducts.clear();
      checkSelectedTab(isRequiredDelay: true);
    }
  }

  void _handleViewShopResponse(response) {
    ViewShopModel shopModel = ViewShopModel.fromJson(response);
    shop = shopModel.shop;
    shopId = shop?.id ?? "";
    Get.toNamed(RouteNames.createShopScreen, arguments: [shop, true]);
  }

  void _handleProductDetailsResponse(response) async {
    ProductDetailModel model = ProductDetailModel.fromJson(response);
    if (model.status == statusOK) {
      String id = prefs.getString(lastProductEditId) ?? "";
      if (id.isNotEmpty && id != (model.product?.id ?? "")) {
        prefs.remove(productInformation);
        prefs.remove(productDetails);
        prefs.remove(productShipment);
        prefs.remove(productPolicies);
      }
      final result = await Get.toNamed(
          tabIndex.value == 2
              ? RouteNames.addProductTabViewScreen
              : RouteNames.productDetailScreen,
          arguments: [
            model.product,
            false,
            tabIndex.value == 2 ? true : false
          ]);
      if (result != null && (result || result is! String)) {
        arrCategories.clear();
        arrShopProducts.clear();
        checkSelectedTab(isRequiredDelay: true);
      }
    }
  }

  void _handleProductByShopResponse(response, String type) {
    productByShopModel = ProductByShopModel.fromJson(response);
    strError.value = "";
    isLoading.value = false;
    haveProducts = false;
    if (productByShopModel?.status == statusOK) {
      if (type == "getOutOfStockVariants") {
        arrCategories.add(appLocal.localeName == "en"
            ? SubCategory(subCategoryName: "All")
            : SubCategory(subCategoryName: "الكل"));
        PaginatedVariantType shopVariants =
            productByShopModel?.variants ?? PaginatedVariantType();
        outOfStockVariantsCount.value = shopVariants.totalItems ?? 0;
        if (arrShopProducts.isEmpty) {
          arrShopProducts.add(ShopProductsList(
              arrCategories.first.subCategoryName ?? "",
              variants: shopVariants));
        } else {
          List<ProductVariant> arr =
              arrShopProducts.first.variants?.items ?? [];
          arr.addAll(productByShopModel?.variants?.items ?? []);
          arr.toSet().toList();
          PaginatedVariantType paginatedVariant =
              productByShopModel?.variants ?? PaginatedVariantType();
          paginatedVariant.items = arr;
          arrShopProducts.first = ShopProductsList(
              arrCategories.first.subCategoryName ?? "",
              variants: paginatedVariant);
        }
      } else {
        if (arrCategories.isEmpty) {
          arrCategories.add(appLocal.localeName == "en"
              ? SubCategory(subCategoryName: "All")
              : SubCategory(subCategoryName: "الكل"));
          availableCategories = productByShopModel?.availableCategories ?? [];
          _updateProductCounts(tabIndex.value,
              productByShopModel: productByShopModel);
          for (AvailableCategories avCategories
              in productByShopModel?.availableCategories ?? []) {
            for (SubCategory element in avCategories.productSubCategory ?? []) {
              arrCategories.add(element);
            }
          }
        }
        ShopProductPaggination productPaggination =
            productByShopModel?.shopProductPaggination ??
                ShopProductPaggination();
        if (type == "getOutOfStockProduct") {
          outOfStockProductsCount.value = productPaggination.totalItems ?? 0;
          outOfStockVariantsCount.value = (outOfStockProductCount.value -
              (productPaggination.totalItems ?? 0));
        } else {
          outOfStockProductsCount.value = 0;
        }
        if (categoryIndex.value == 0) {
          if (arrShopProducts.isEmpty) {
            arrShopProducts.add(ShopProductsList(
                arrCategories.first.subCategoryName ?? "",
                products: productPaggination));
          } else {
            List<ShopProducts> arr =
                arrShopProducts.first.products?.items ?? [];
            arr.addAll(productByShopModel?.shopProductPaggination?.items ?? []);
            ShopProductPaggination productPaggination =
                productByShopModel?.shopProductPaggination ??
                    ShopProductPaggination();
            productPaggination.items = arr;
            arrShopProducts.first = ShopProductsList(
                arrCategories.first.subCategoryName ?? "",
                products: productPaggination);
          }
        } else {
          String strSubCategory =
              arrCategories[categoryIndex.value].subCategoryName ?? "";
          var contain = arrShopProducts
              .where((element) => element.category == strSubCategory);
          if (contain.isEmpty) {
            arrShopProducts.add(ShopProductsList(
                arrCategories[categoryIndex.value].subCategoryName ?? "",
                products: productPaggination));
            Future.delayed(Durations.short1, () async {
              await controller.scrollToIndex(0,
                  preferPosition: AutoScrollPosition.begin);
            });
          } else {
            int indexWhere = arrShopProducts.indexWhere((element) =>
                element.category ==
                arrCategories[categoryIndex.value].subCategoryName);
            if (indexWhere != -1) {
              List<ShopProducts> arr =
                  arrShopProducts[indexWhere].products?.items ?? [];
              arr.addAll(
                  productByShopModel?.shopProductPaggination?.items ?? []);
              productPaggination.items = arr;
              arrShopProducts[indexWhere] = ShopProductsList(
                  arrCategories[categoryIndex.value].subCategoryName ?? "",
                  products: productPaggination);
            } else {
              arrShopProducts.add(ShopProductsList(
                  arrCategories[categoryIndex.value].subCategoryName ?? "",
                  products: productPaggination));
            }
          }
        }
      }
    } else {
      _updateProductCounts(tabIndex.value, reset: true);
      strError.value = productByShopModel?.message ?? "";
    }
    for (var element in arrShopProducts) {
      (element.products?.items ?? []).toSet().toList();
      (element.variants?.items ?? []).toSet().toList();
    }
    setState(() {});
  }

  void _updateProductCounts(
    int tabIndex, {
    ProductByShopModel? productByShopModel,
    bool reset = false,
  }) {
    switch (tabIndex) {
      case 0:
        acceptedProductCount.value = reset
            ? 0
            : productByShopModel?.shopProductPaggination?.totalItems ?? 0;
        break;
      case 1:
        pendingProductCount.value = reset
            ? 0
            : productByShopModel?.shopProductPaggination?.totalItems ?? 0;
        break;
      case 2:
        draftProductCount.value = reset
            ? 0
            : productByShopModel?.shopProductPaggination?.totalItems ?? 0;
        break;
      case 3:
        qtyAlertProductCount.value = reset
            ? 0
            : productByShopModel?.shopProductPaggination?.totalItems ?? 0;
        break;
      case 4:
        outOfStockProductCount.value = reset
            ? 0
            : productByShopModel?.shopProductPaggination?.totalItems ?? 0;
        break;
      case 5:
        freeDeliveryProductCount.value = reset
            ? 0
            : productByShopModel?.shopProductPaggination?.totalItems ?? 0;
        break;
      default:
        break;
    }
  }

  checkSelectedTab({bool isRequiredDelay = false}) async {
    if (isRequiredDelay) {
      await Future.delayed(Durations.medium1);
    }
    switch (tabIndex.value) {
      case 2:
        getTabProducts(qurey: GraphQlQuries.getSavedForLaterProducts);
        break;
      case 0:
        getTabProducts(qurey: GraphQlQuries.getProducts);
        break;
      case 1:
        getTabProducts(qurey: GraphQlQuries.getPendingProducts);
        break;
      case 3:
        getTabProducts(qurey: GraphQlQuries.getMinQteAlertProducts);
        break;
      case 4:
        if (_tabController.index == 0) {
          getTabProducts(
              type: "getOutOfStockProduct",
              qurey: GraphQlQuries.getOutOfStockProduct);
        }
        break;
      default:
    }
  }

  @override
  bool get wantKeepAlive => true;
}
