import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'dart:math' as math;
import 'package:overolasuppliers/model/orders/order_model.dart';

class BuyerInfoCard extends StatelessWidget {
  RxBool isExpanded = false.obs;
  final OrderItems orderItems;

  BuyerInfoCard({Key? key, required this.orderItems}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.headerColorDark,
              AppColors.headerColorDark.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: () => isExpanded.value = !isExpanded.value,
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: SvgPicture.string(
                          SvgStrings.iconBuyer,
                          height: 20,
                          color: Colors.white,
                        ),
                      ),
                         SizedBox(width: 16.w),
                      Expanded(
                        child: Text(
                          appLocal.buyerDetails,
                          style: FontStyles.fontMedium(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 300),
                        tween: Tween(
                          begin: 0,
                          end: isExpanded.value ? math.pi : 0,
                        ),
                        builder: (_, value, child) => Transform.rotate(
                          angle: value,
                          child: Icon(
                            Icons.expand_more_rounded,
                            color: Colors.white.withOpacity(0.9),
                            size: 30,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                height: isExpanded.value ? null : 0,
                child: ClipRect(
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 300),
                    opacity: isExpanded.value ? 1.0 : 0.0,
                    child: _buildExpandedContent(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExpandedContent() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        children: [
          const Divider(color: Colors.white24, thickness: 1),
          _buildInfoItem(
            SvgStrings.iconBuyer,
            'Name',
            orderItems.clientId?.userId?.userName ?? "",
          ),
          _buildInfoItem(
            SvgStrings.iconOrderLocationPin,
            'Address',
            orderItems.billingAddress?.address ?? "",
          ),
          _buildInfoItem(
            SvgStrings.iconEmailBlue,
            'Email',
            orderItems.clientId?.userId?.userEmail ?? "",
          ),
          if (orderItems.clientId?.userId?.userPhoneNumber != null)
            _buildInfoItem(
              SvgStrings.iconCallBlue,
              'Phone',
              orderItems.clientId?.userId?.userPhoneNumber ?? "",
            ),
        
        ],
      ),
    );
  }

  Widget _buildInfoItem(String icon, String label, String text) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      padding: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: SvgPicture.string(
              icon,
              height: 18,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
                  text,
                  style: FontStyles.fontMedium(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
          ),
        ],
      ),
    );
  }
}
