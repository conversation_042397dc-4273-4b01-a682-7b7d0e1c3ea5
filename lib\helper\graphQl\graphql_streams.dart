import 'dart:async';

import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/model/add_product/categories_model.dart';

class GraphqlStreams {
  StreamController<CategoriesModel> categoryController =
      StreamController<CategoriesModel>.broadcast();
  getCategoryList() async {
    CategoriesModel category = CategoriesModel.fromJson(
        await GraphQlInitilize.streamQuery(
            query: GraphQlQuries.getAllCategoryAndData));
    categoryController.sink.add(category);
  }

}
