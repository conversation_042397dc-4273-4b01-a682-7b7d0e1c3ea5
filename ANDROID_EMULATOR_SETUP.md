# 🤖 Android Emulator Installation Guide

## 📋 System Requirements

**Minimum Requirements:**
- Windows 10/11 (64-bit)
- 8GB RAM (16GB recommended)
- 10GB free disk space
- Intel/AMD processor with virtualization support

**Check Your System:**
- RAM: You need at least 8GB for smooth emulator performance
- Virtualization: Must be enabled in BIOS/UEFI settings
- Disk Space: Android Studio + SDK + Emulator = ~8-10GB

## 🚀 Step-by-Step Installation

### Step 1: Download Android Studio ✅ (Browser Opened)

The Android Studio download page is now open in your browser.

**Download Instructions:**
1. Click **"Download Android Studio Hedgehog"**
2. Accept the terms and conditions
3. Choose the **Windows (64-bit)** version
4. Save the `.exe` file to your Downloads folder
5. Wait for download to complete (~1GB file)

### Step 2: Install Android Studio

**Installation Process:**
1. **Run the installer** from your Downloads folder
2. **Choose installation type**: Select "Standard" (recommended)
3. **Installation components** - Make sure these are checked:
   - ✅ Android SDK
   - ✅ Android SDK Platform-Tools  
   - ✅ Android Emulator
   - ✅ Intel x86 Emulator Accelerator (HAXM)
   - ✅ Android Virtual Device

4. **Installation paths** (use defaults):
   - Android Studio: `C:\Program Files\Android\Android Studio`
   - Android SDK: `C:\Users\<USER>\AppData\Local\Android\Sdk`

5. **Complete installation** (may take 10-20 minutes)

### Step 3: First-Time Setup Wizard

**When Android Studio first opens:**
1. **Welcome screen**: Click "Next"
2. **Install Type**: Choose "Standard"
3. **UI Theme**: Choose your preference (Light/Dark)
4. **SDK Components**: Let it download (this takes 15-30 minutes)
5. **License Agreements**: Accept all licenses
6. **Finish**: Wait for "Finish" button to appear

### Step 4: Create Android Virtual Device (AVD)

**In Android Studio:**
1. **Open AVD Manager**:
   - Click "More Actions" → "Virtual Device Manager"
   - OR go to Tools → AVD Manager

2. **Create Virtual Device**:
   - Click "Create Virtual Device"
   - **Category**: Select "Phone"
   - **Device**: Choose "Pixel 7" (recommended) or "Pixel 4"
   - Click "Next"

3. **System Image**:
   - **API Level**: Select "34" (Android 14) - recommended
   - **Target**: Choose "Android 14.0 (Google APIs)"
   - **ABI**: x86_64 (Intel) or arm64-v8a (Apple Silicon)
   - Click "Download" if not already downloaded
   - Click "Next"

4. **AVD Configuration**:
   - **Name**: Keep default or rename (e.g., "Pixel_7_API_34")
   - **Startup orientation**: Portrait
   - **Advanced Settings** (optional):
     - RAM: 2048 MB (if you have 16GB+ system RAM)
     - Internal Storage: 6000 MB
     - SD Card: 512 MB
   - Click "Finish"

### Step 5: Configure Flutter for Android

**Open Command Prompt/PowerShell and run:**

```bash
# Configure Flutter to use Android SDK
C:\Users\<USER>\flutter\bin\flutter.bat config --android-sdk C:\Users\<USER>\AppData\Local\Android\Sdk

# Accept Android licenses
C:\Users\<USER>\AppData\Local\Android\Sdk\cmdline-tools\latest\bin\sdkmanager.bat --licenses

# Check Flutter configuration
C:\Users\<USER>\flutter\bin\flutter.bat doctor
```

### Step 6: Start Your Emulator

**Method 1: From Android Studio**
1. Open Android Studio
2. Go to Tools → AVD Manager
3. Click the **Play button** (▶️) next to your AVD
4. Wait for emulator to boot (2-5 minutes first time)

**Method 2: From Command Line**
```bash
# List available emulators
C:\Users\<USER>\flutter\bin\flutter.bat emulators

# Start specific emulator
C:\Users\<USER>\flutter\bin\flutter.bat emulators --launch Pixel_7_API_34
```

### Step 7: Run Your Flutter App on Emulator

**Once emulator is running:**

```bash
# Navigate to your project
cd C:\Users\<USER>\Desktop\github\elbaab-suppliers-web

# Check available devices (should show your emulator)
C:\Users\<USER>\flutter\bin\flutter.bat devices

# Run your app on emulator
C:\Users\<USER>\flutter\bin\flutter.bat run

# Or run specific file
C:\Users\<USER>\flutter\bin\flutter.bat run lib/main_mobile.dart
```

## 🔧 Troubleshooting

### Common Issues and Solutions

**1. Emulator won't start**
- Enable virtualization in BIOS/UEFI
- Disable Hyper-V: `bcdedit /set hypervisorlaunchtype off`
- Restart computer after changes

**2. Flutter doesn't see emulator**
- Restart command prompt/terminal
- Check if emulator is fully booted (shows home screen)
- Run: `flutter doctor` to check configuration

**3. Slow emulator performance**
- Increase RAM allocation in AVD settings
- Enable hardware acceleration
- Close other applications
- Use x86_64 system images (faster than ARM)

**4. Build errors**
```bash
# Clean and rebuild
C:\Users\<USER>\flutter\bin\flutter.bat clean
C:\Users\<USER>\flutter\bin\flutter.bat pub get
C:\Users\<USER>\flutter\bin\flutter.bat run
```

**5. SDK licenses not accepted**
```bash
# Accept all licenses
C:\Users\<USER>\AppData\Local\Android\Sdk\cmdline-tools\latest\bin\sdkmanager.bat --licenses
```

## 📱 Testing Your App

**Once running on emulator:**
1. **Test touch interactions**: Tap, swipe, scroll
2. **Test navigation**: Bottom nav, app bar, drawers
3. **Test responsive design**: Rotate device (Ctrl+F11/F12)
4. **Test performance**: Smooth animations, loading times
5. **Test platform features**: Camera, location (simulated)

## 🎯 Quick Commands Reference

```bash
# Check Flutter setup
flutter doctor

# List devices and emulators
flutter devices
flutter emulators

# Start emulator
flutter emulators --launch [emulator_name]

# Run app
flutter run
flutter run -d [device_id]

# Hot reload while running
Press 'r' in terminal

# Hot restart
Press 'R' in terminal

# Quit app
Press 'q' in terminal
```

## 📊 Expected Timeline

- **Download Android Studio**: 5-10 minutes
- **Install Android Studio**: 10-20 minutes  
- **First-time setup**: 15-30 minutes
- **Create AVD**: 5-10 minutes
- **First emulator boot**: 2-5 minutes
- **Total setup time**: 45-75 minutes

## 🎉 Success Indicators

✅ **Android Studio installed and opens**
✅ **AVD created and visible in AVD Manager**
✅ **Emulator starts and shows Android home screen**
✅ **`flutter devices` shows your emulator**
✅ **`flutter doctor` shows no Android-related issues**
✅ **Flutter app runs on emulator**

## 🚀 Next Steps After Setup

1. **Test your mobile app** on the emulator
2. **Compare with web version** for consistency
3. **Test different screen sizes** (create multiple AVDs)
4. **Explore Android-specific features** (camera, GPS simulation)
5. **Set up release builds** for distribution

---

**Ready to start?** The Android Studio download page is open in your browser. Begin with Step 1!
