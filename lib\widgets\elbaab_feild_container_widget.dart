import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';

class ElbaabFeildContainerWidget extends StatelessWidget {
  final Widget child;
  final double borderWidth;
  final BorderRadius? borderRadius;
  final double? containerHeight;
  final EdgeInsets? edgeInsets;
  final Function? onPress;
  final Color? color;
  final Color? borderColor;

  const ElbaabFeildContainerWidget({
    Key? key,
    this.containerHeight,
    this.edgeInsets,
    this.onPress,
    this.color,
    this.borderColor,
    this.borderRadius,
    required this.borderWidth,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: edgeInsets ?? const EdgeInsets.all(0),
      child: InkWell(
        onTap: onPress == null ? null : () => onPress!(),
        child: Container(
          height: containerHeight,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            borderRadius: borderRadius ?? BorderRadius.circular(10),
            color: color ?? AppColors.feildColorDark,
            border: Border.all(
              color: borderColor ?? AppColors.feildBorderColorDark,
              width: borderWidth,
            ),
          ),
          child: child,
        ),
      ),
    );
  }
}
