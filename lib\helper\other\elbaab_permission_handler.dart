import 'dart:io';

// import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class ElbaabPermissionHandler {
  static Future<bool> checkPermission({
    String permissionName = 'gallery',
  }) async {
    if (Platform.isAndroid) {
      // final androidInfo = await DeviceInfoPlugin().androidInfo;
      // final sdkInt = androidInfo.version.sdkInt;
      // if (sdkInt < 33 && permissionName == 'gallery') {
      //   return true;
      // }
    }
    FocusScope.of(Get.context!).requestFocus(FocusNode());
    Map<Permission, PermissionStatus> statues;
    switch (permissionName) {
      case 'camera':
        {
          statues = await [Permission.camera].request();
          PermissionStatus? statusCamera = statues[Permission.camera];
          if (statusCamera == PermissionStatus.granted) {
            return true;
          } else if (statusCamera == PermissionStatus.permanentlyDenied) {
            openAppSettings();
            return false;
          } else {
            return false;
          }
        }
      case 'gallery':
        {
          statues = await [Permission.photos].request();
          PermissionStatus? statusPhotos = statues[Permission.photos];
          var status = await Permission.manageExternalStorage.request();
          if (!status.isGranted) {
            openAppSettings();
            Get.snackbar(
                'Error!', "You need to allow storage access for this app");
          } else if (statusPhotos == PermissionStatus.granted) {
            return true;
          } else if (statusPhotos == PermissionStatus.permanentlyDenied) {
            openAppSettings();
            return false;
          } else if (statusPhotos == PermissionStatus.limited) {
            openAppSettings();
            return false;
          } else {
            return false;
          }
        }
    }
    return false;
  }
}
