class ViewShopModel {
  final Shop? shop;
  final int? status;
  final int? totalProductsCount;
  final int? draftProductsCount;
  final int? acceptedProductsCount;
  final int? pendingProductsCount;
  final int? outOfStockProductsCount;
  final int? minimumQuantityAlertProductsCount;
  final int? viewsCount;
  final String? message;
  final int? freeDeliveryItemProductsCount;

  ViewShopModel({
    this.shop,
    this.status,
    this.totalProductsCount,
    this.draftProductsCount,
    this.acceptedProductsCount,
    this.pendingProductsCount,
    this.outOfStockProductsCount,
    this.message,
    this.minimumQuantityAlertProductsCount,
    this.viewsCount,
    this.freeDeliveryItemProductsCount,
  });

  ViewShopModel.fromJson(Map<String, dynamic> json)
      : shop = (json['shop'] as Map<String, dynamic>?) != null
            ? Shop.fromJson(json['shop'] as Map<String, dynamic>)
            : null,
        totalProductsCount = json['totalProductsCount'] as int?,
        draftProductsCount = json['draftProductsCount'] as int?,
        acceptedProductsCount = json['acceptedProductsCount'] as int?,
        pendingProductsCount = json['pendingProductsCount'] as int?,
        viewsCount = json['viewsCount'] as int?,
        outOfStockProductsCount = json['outOfStockProductsCount'] as int?,
        minimumQuantityAlertProductsCount =
            json['minimumQuantityAlertProductsCount'] as int?,
        freeDeliveryItemProductsCount =
            json['freeDeliveryItemProductsCount'] as int?,
        status = json['status'] as int?,
        message = json['message'] as String?;

  Map<String, dynamic> toJson() => {
        'shop': shop?.toJson(),
        'status': status,
        'message': message,
        'viewsCount': viewsCount,
        'totalProductsCount': totalProductsCount,
        'draftProductsCount': draftProductsCount,
        'acceptedProductsCount': acceptedProductsCount,
        'minimumQuantityAlertProductsCount': minimumQuantityAlertProductsCount,
        'pendingProductsCount': pendingProductsCount,
        'freeDeliveryItemProductsCount': freeDeliveryItemProductsCount,
        'outOfStockProductsCount': outOfStockProductsCount,
      };
}

class Shop {
  final String? id;
  final String? shopName;
  final int? followerCount;
  final String? shopLogo;
  final dynamic shopRate;
  final String? shopBanner;
  final String? shopSlogan;
  final String? shopStatus;
  final String? shopDescription;
  final String? shopTermsAndConditions;
  final ShopContactDetails? shopContactDetails;
  final List<ShopPickupAddresses>? shopPickupAddresses;
  final bool? freeDeliveryTarget;
  final bool? isCompleted;
  final dynamic targetPriceForFdt;
  final List<ValidationHistory>? validationHistory;
  Shop({
    this.id,
    this.shopName,
    this.isCompleted,
    this.shopStatus,
    this.shopRate,
    this.shopLogo,
    this.shopBanner,
    this.shopPickupAddresses,
    this.followerCount,
    this.shopSlogan,
    this.shopContactDetails,
    this.shopDescription,
    this.validationHistory,
    this.shopTermsAndConditions,
    this.freeDeliveryTarget,
    this.targetPriceForFdt,
  });

  Shop.fromJson(Map<String, dynamic> json)
      : id = json['_id'] as String?,
        shopName = json['shopName'] as String?,
        shopRate = json['shopRate'],
        shopLogo = json['shopLogo'] as String?,
        shopStatus = json['shopStatus'] as String?,
        shopPickupAddresses = (json['shopPickupAddresses'] as List?)
            ?.map((dynamic e) =>
                ShopPickupAddresses.fromJson(e as Map<String, dynamic>))
            .toList(),
        followerCount = json['followerCount'] as int?,
        shopBanner = json['shopBanner'] as String?,
        shopSlogan = json['shopSlogan'] as String?,
        shopContactDetails =
            (json['shopContactDetails'] as Map<String, dynamic>?) != null
                ? ShopContactDetails.fromJson(
                    json['shopContactDetails'] as Map<String, dynamic>)
                : null,
        shopDescription = json['shopDescription'] as String?,
        shopTermsAndConditions = json['shopTermsAndConditions'] as String?,
        freeDeliveryTarget = json['freeDeliveryTarget'] as bool?,
        isCompleted = json['isCompleted'] as bool?,
        targetPriceForFdt = json['targetPriceForFdt'],
        validationHistory = (json['validationHistory'] as List?)
            ?.map((dynamic e) =>
                ValidationHistory.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '_id': id,
        'shopName': shopName,
        'shopLogo': shopLogo,
        'followerCount': followerCount,
        'shopBanner': shopBanner,
        'shopSlogan': shopSlogan,
        'shopContactDetails': shopContactDetails?.toJson(),
        'isCompleted': isCompleted,
        'shopDescription': shopDescription,
        'shopTermsAndConditions': shopTermsAndConditions,
        'freeDeliveryTarget': freeDeliveryTarget,
        'targetPriceForFdt': targetPriceForFdt,
        'shopPickupAddresses':
            shopPickupAddresses?.map((e) => e.toJson()).toList(),
        'validationHistory': validationHistory?.map((e) => e.toJson()).toList()
      };
}

class ShopMapLocation {
  final List<dynamic>? coordinates;

  ShopMapLocation({
    this.coordinates,
  });

  ShopMapLocation.fromJson(Map<String, dynamic> json)
      : coordinates = (json['coordinates'] as List?)
            ?.map((dynamic e) => e as double)
            .toList();

  Map<String, dynamic> toJson() => {'coordinates': coordinates};
}

class ShopContactDetails {
  final String? typename;
  final String? phoneNumber;
  final String? whatsUpPhoneNumber;
  final String? email;

  ShopContactDetails({
    this.typename,
    this.phoneNumber,
    this.whatsUpPhoneNumber,
    this.email,
  });

  ShopContactDetails.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        phoneNumber = json['phoneNumber'] as String?,
        whatsUpPhoneNumber = json['whatsUpPhoneNumber'] as String?,
        email = json['email'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'phoneNumber': phoneNumber,
        'whatsUpPhoneNumber': whatsUpPhoneNumber,
        'email': email
      };
}

class ValidationHistory {
  final String? typename;
  final String? status;
  final String? updatedAt;
  final List<dynamic>? returnValues;
  final dynamic returnMessage;

  ValidationHistory({
    this.typename,
    this.status,
    this.updatedAt,
    this.returnValues,
    this.returnMessage,
  });

  ValidationHistory.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        status = json['status'] as String?,
        updatedAt = json['updatedAt'] as String?,
        returnValues = json['returnValues'] as List?,
        returnMessage = json['returnMessage'];

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'updatedAt': updatedAt,
        'status': status,
        'returnValues': returnValues,
        'returnMessage': returnMessage
      };
}

class ShopPickupAddresses {
  final bool? isUsedInOrder;
  final String? id;
  final String? pickUpCity;
  final String? pickUpAddress;
  final PickUpMapLocation? pickUpMapLocation;
  final PickupContactNumber? pickUpContactLandNumber;
  final PickupContactNumber? pickUpContactMobileNumber;
  final String? createdAt;
  final String? updatedAt;

  ShopPickupAddresses({
    this.isUsedInOrder,
    this.pickUpContactLandNumber,
    this.pickUpContactMobileNumber,
    this.id,
    this.pickUpCity,
    this.pickUpAddress,
    this.pickUpMapLocation,
    this.createdAt,
    this.updatedAt,
  });

  ShopPickupAddresses.fromJson(Map<String, dynamic> json)
      : isUsedInOrder = json['isUsedInOrder'] as bool?,
        id = json['_id'] as String?,
        pickUpCity = json['pickUpCity'] as String?,
        pickUpAddress = json['pickUpAddress'] as String?,
        pickUpMapLocation =
            (json['pickUpMapLocation'] as Map<String, dynamic>?) != null
                ? PickUpMapLocation.fromJson(
                    json['pickUpMapLocation'] as Map<String, dynamic>)
                : null,
        pickUpContactLandNumber =
            (json['pickUpContactLandNumber'] as Map<String, dynamic>?) != null
                ? PickupContactNumber.fromJson(
                    json['pickUpContactLandNumber'] as Map<String, dynamic>)
                : null,
        pickUpContactMobileNumber =
            (json['pickUpContactMobileNumber'] as Map<String, dynamic>?) != null
                ? PickupContactNumber.fromJson(
                    json['pickUpContactMobileNumber'] as Map<String, dynamic>)
                : null,
        createdAt = json['createdAt'] as String?,
        updatedAt = json['updatedAt'] as String?;

  Map<String, dynamic> toJson() => {
        'isUsedInOrder': isUsedInOrder,
        '_id': id,
        'pickUpCity': pickUpCity,
        'pickUpAddress': pickUpAddress,
        'pickUpMapLocation': pickUpMapLocation?.toJson(),
        'pickUpContactLandNumber': pickUpContactLandNumber?.toJson(),
        'pickUpContactMobileNumber': pickUpContactMobileNumber?.toJson(),
        'createdAt': createdAt,
        'updatedAt': updatedAt
      };
}

class PickUpMapLocation {
  final String? typename;
  final List<dynamic>? coordinates;
  final dynamic id;

  PickUpMapLocation({
    this.typename,
    this.coordinates,
    this.id,
  });

  PickUpMapLocation.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        coordinates = (json['coordinates'] as List?)
            ?.map((dynamic e) => e as dynamic)
            .toList(),
        id = json['_id'];

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'coordinates': coordinates, '_id': id};
}

class PickupContactNumber {
  String? number;
  bool? isVerified;

  PickupContactNumber({
    this.number,
    this.isVerified,
  });

  PickupContactNumber.fromJson(Map<String, dynamic> json)
      : number = json['number'] as String?,
        isVerified = json['isVerified'] as bool?;

  Map<String, dynamic> toJson() => {'isVerified': isVerified, 'number': number};
}
