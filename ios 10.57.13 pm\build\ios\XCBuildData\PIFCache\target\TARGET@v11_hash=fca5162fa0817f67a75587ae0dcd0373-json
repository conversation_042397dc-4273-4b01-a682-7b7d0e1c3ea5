{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce302ef2ac1e37a3386d1a461c1927bd", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9821bf4a502f787ad2468661e1f6df0c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980100a374e86e0395107d647cb6ac5dd9", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98901e306d89ea9f8a1a305d549254f4d6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980100a374e86e0395107d647cb6ac5dd9", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986b601541ff1897fb4f54b574b802b2aa", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988b6f0c0463f7bf55ead00db9aff8922b", "guid": "bfdfe7dc352907fc980b868725387e982feea975e9b4c5e5327b7a87cbcb3c36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868ea6a570c8fec440dda16330812ba41", "guid": "bfdfe7dc352907fc980b868725387e9850e508fcef005e774db7feebcda21a32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f09acd10d25e5f4befee1687f0725e0a", "guid": "bfdfe7dc352907fc980b868725387e985dae9aa4dd374c80634a49d79ac2d20b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821305c9157284f7571b00098d193c08a", "guid": "bfdfe7dc352907fc980b868725387e987dc8d30a6c2e82c793a191309b423cd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c683d26cd0d88b952a0ae10f301fb658", "guid": "bfdfe7dc352907fc980b868725387e983b9f9c3d48077ffc9225d53cac517645", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830c111de094ef51d29d7a8364146e149", "guid": "bfdfe7dc352907fc980b868725387e98dd78d85716cbb6e1a8de0f314ef443f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98515e716f1390fbfb2a37534ea90a4bf2", "guid": "bfdfe7dc352907fc980b868725387e98e9d0b1b0c2fbe4cc631b90197b79015d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c01ac363725ee2535d7d9cf2add2140d", "guid": "bfdfe7dc352907fc980b868725387e982a16da290a30f7a5e292b2dd3a3d485e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989df611c59af062f90d4f321846ca8b56", "guid": "bfdfe7dc352907fc980b868725387e981443bad52aea9f2a575fd2fbf164cb71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98146a82deaf6afcfad51cd9906b975b63", "guid": "bfdfe7dc352907fc980b868725387e987b7b317baf2bd444f0dcae677cc29cc8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f72ede4373e7db46d014c05237f5300d", "guid": "bfdfe7dc352907fc980b868725387e9883aa477000252a0956c3c0f46b0d94c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f4e12855ed65942676fff9a45a414cb", "guid": "bfdfe7dc352907fc980b868725387e987266d851667dd78a5b0a9b885815762b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982408d532d4b672a8a84adf3c78366de2", "guid": "bfdfe7dc352907fc980b868725387e9870abe99db1472ebafa634a3d00406a95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827c007a6f6f5189f0f4c3665e4436997", "guid": "bfdfe7dc352907fc980b868725387e980b331146c27c6bd401f04b95fcffa014", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840745910f1949b8bc6822974cbfd6a2a", "guid": "bfdfe7dc352907fc980b868725387e98a818a0893f931b189faa6e8b3b36a241", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98398b442735ef80c0b5c6da3ca012f61b", "guid": "bfdfe7dc352907fc980b868725387e98ccc0627b6b24315fc81036ef0daecab3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98919679fbd5d2cdfeecafdaca8c49dcd0", "guid": "bfdfe7dc352907fc980b868725387e980206bfb6d887bb0adee42437e7252714", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e972da2027ee7684c0912ac85e45e8e", "guid": "bfdfe7dc352907fc980b868725387e989f0aef6e4a0b383a0c27e0cb748d5283", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c7b43cb6ad050ba3da37688b5d343d3", "guid": "bfdfe7dc352907fc980b868725387e9809625434c2e4ad3891bdb35dd4c86d0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bedb5c63ba069c19969a8c81e4e8942", "guid": "bfdfe7dc352907fc980b868725387e989b7342bbb9f79fbebfcb593d8c8eddd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db6e52f2cb12020e059f6127c06abc20", "guid": "bfdfe7dc352907fc980b868725387e9802e0fd631dc124fec1799d6fe9d600a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d11beb7e63f6baf26b5eb06a820d6e35", "guid": "bfdfe7dc352907fc980b868725387e98ff8825921bad8f8c1191208d1b31e4f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8658fac3642276a8b13a42c2a552377", "guid": "bfdfe7dc352907fc980b868725387e98514925f7093bf680b3c82717158fcbf1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e980bce56a30b7ddf7211c165b173042abd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1e86e9fe684ca1d823342c63aaca6a7", "guid": "bfdfe7dc352907fc980b868725387e98d3d296c59ddd65ef6459c9dd47823ae1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98867d28bf1176e0d1c3c8328fe98e84d4", "guid": "bfdfe7dc352907fc980b868725387e98d2a196a357da7727e5c2845f409461b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b93f83b6a6d7973bbc6e3b53b9d16f8", "guid": "bfdfe7dc352907fc980b868725387e985b032fd21d0afdf2e8956283be2187e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988658a7a6d4141dddc6957cdf4f219528", "guid": "bfdfe7dc352907fc980b868725387e98cb010015d1d54790fec37f3169aea5fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf4e5cadbc549dace9afec8f3c376ca", "guid": "bfdfe7dc352907fc980b868725387e984789794352fb26972c2d2c28871cf413"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885cc74d3a611675916c9501323355ed4", "guid": "bfdfe7dc352907fc980b868725387e9872a73b565e961c76c0ddd660a6e24ba9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ff41c6d6b17cfd86e380ae90eeb0b1d", "guid": "bfdfe7dc352907fc980b868725387e98ef70d4d854c3ea84e9c08fb5cb0ffc55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4685c9845f985d373b9f27c1b39b0ff", "guid": "bfdfe7dc352907fc980b868725387e9855660df9b58c091b86de2bfa6d045377"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee7c79c8c8d712cc3c0ed4a5fe4e98b", "guid": "bfdfe7dc352907fc980b868725387e984f3d075cc37f1552fe8cf24420c6af05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98597f5d6882153fb179b024c329bc5e6a", "guid": "bfdfe7dc352907fc980b868725387e989c54493dd4fc72ecffaa3bad22a1e04c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0f4e7385b9b6f213f643160f1b059c3", "guid": "bfdfe7dc352907fc980b868725387e981e8a32c9f3b41ec29c45a72522545a88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c33a16c2ac08fddc1ce8b85d577741d1", "guid": "bfdfe7dc352907fc980b868725387e98b8944e8b3e8488f1adcb583cf54f967f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d87532ae22ab0af73b4eb41d7623a9f7", "guid": "bfdfe7dc352907fc980b868725387e9802dc7c6a45e327425b2a0758331d3e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985177db8dac508613efa572f9588edbbc", "guid": "bfdfe7dc352907fc980b868725387e986bb002116de866def4e2243bd7c0da69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986195895a104902d3ad6e572e69df4f63", "guid": "bfdfe7dc352907fc980b868725387e983d1d70a8a36f1539e5e1141e686ba67f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b23e89f232a2b2ddc3ca54f26c1cc46", "guid": "bfdfe7dc352907fc980b868725387e98d602cf2072195f7b72fcbc6826537ba1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c2ba97b65ed883921ba2d925400361", "guid": "bfdfe7dc352907fc980b868725387e98fe321eb10e0945695b4083103cceda27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861b5811cb4383a82bec43c09263a2a3c", "guid": "bfdfe7dc352907fc980b868725387e981f448ca89c5834fc369493b231a1ddfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f5b4e2659d51738f921d17576cc9557", "guid": "bfdfe7dc352907fc980b868725387e988067b47a8d536353abace81a754d1707"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857b137c0fbb221e5347a40610436204f", "guid": "bfdfe7dc352907fc980b868725387e98f836d4f2650198b731e817d980a1edec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d51655ab78ef3cf885d18ff7c651c9a1", "guid": "bfdfe7dc352907fc980b868725387e98de5c08e11a20bfeeed0eb9b6282fad5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac8da0d7f1c4b2ce4ad9700e458ea27", "guid": "bfdfe7dc352907fc980b868725387e985e43ec00c4aa0066b9418ffc8c73b9d2"}], "guid": "bfdfe7dc352907fc980b868725387e9869409e338aceb8ff4fceae939ab9d175", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e980e30b176b8c2f300f9ce97207689a948"}], "guid": "bfdfe7dc352907fc980b868725387e98c9ef096ab1c4b353179252dbd847308f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98867e07e9ec4c6992ff5111335445f2f8", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e985761c4527a21be427e6c864193bbf9ff", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}