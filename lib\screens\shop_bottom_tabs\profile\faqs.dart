import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/faqs_model.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class Faqs extends StatelessWidget {
  const Faqs({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.faqs,
        leadingBack: true,
      ),
      body: const Padding(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: FaqsList(),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        decoration: BoxDecoration(
          color: AppColors.headerColorDark,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 15,
              offset: const Offset(0, -3),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Text(
                appLocal.fixProblemOrContactUs,
                style: FontStyles.fontRegular(
                  fontSize: 15,
                  color: Colors.white.withOpacity(0.9),
                ),
                textAlign: TextAlign.center,
              ),
            ),
            TextButton(
              onPressed: () => Get.toNamed(RouteNames.elbaabSupportContactScreen),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                backgroundColor: AppColors.colorPrimary.withOpacity(0.1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                appLocal.contactUs,
                style: FontStyles.fontRegular(
                  color: AppColors.colorPrimary,
                  fontSize: 15,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FaqsList extends StatefulWidget {
  const FaqsList({super.key});

  @override
  _FaqsListState createState() => _FaqsListState();
}

class _FaqsListState extends State<FaqsList> implements ServerResponse {
  RxList<Items> arrFaqs = <Items>[].obs;

  late GraphQlInitilize _request;
  late FaqsModel model;
  int currentPage = 1;
  RxBool isLoading = false.obs;
  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    Future.delayed(
      Duration.zero,
      () => _request.runQuery(
        context: context,
        query: GraphQlQuries.getFaqs,
        variables:
            GraphQlVariables.getPaginated(itemsNumber: 20, page: currentPage),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification sn) {
        if (!isLoading.value &&
            sn is ScrollUpdateNotification &&
            sn.metrics.pixels <= sn.metrics.maxScrollExtent - 100) {
          if (model.faqs?.hasNextPage ?? false) {
            _request.runQuery(
              context: context,
              query: GraphQlQuries.getFaqs,
              variables: GraphQlVariables.getPaginated(
                  itemsNumber: 20, page: currentPage),
            );
            currentPage++;
            isLoading.value = true;
          }
        }
        return true;
      },
      child: SingleChildScrollView(
        child: Obx(() => _buildPanel(appLocal)),
      ),
    );
  }

  Widget _buildPanel(AppLocalizations appLocal) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: arrFaqs.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final item = arrFaqs[index];
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          color: AppColors.headerColorDark,
          child: Theme(
            data: Theme.of(context).copyWith(
              dividerColor: Colors.transparent,
            ),
            child: ExpansionTile(
              initiallyExpanded: item.isExpanded ?? false,
              onExpansionChanged: (expanded) {
                setState(() {
                  item.isExpanded = expanded;
                });
              },
              title: Text(
                appLocal.localeName == "ar" ? item.ar?.question ?? "" : item.question ?? "",
                style: FontStyles.fontRegular(
                  fontSize: 16,
                ),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                    left: 16,
                    right: 16,
                    bottom: 16,
                  ),
                  child: Text(
                    appLocal.localeName == "ar" ? item.ar?.answer ?? "" : item.answer ?? "",
                    style: FontStyles.fontRegular(
                      fontSize: 14,
                      color: Colors.white.withOpacity(0.7),
                      height: 1.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    model = FaqsModel.fromJson(response);
    if (model.status == statusOK) {
      arrFaqs.addAll(model.faqs?.items ?? []);
    }
  }
}
