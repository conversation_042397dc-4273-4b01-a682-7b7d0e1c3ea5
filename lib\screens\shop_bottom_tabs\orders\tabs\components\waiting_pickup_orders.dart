import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';
import 'package:overolasuppliers/provider/order_status_provider.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/controller/orders_controller.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/orders_loader.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/orders_row/waiting_pickup_or_shipped_order_row.dart';
import 'package:provider/provider.dart';

class WaitingPickupOrders extends StatefulWidget {
  const WaitingPickupOrders({Key? key}) : super(key: key);

  @override
  State<WaitingPickupOrders> createState() => _WaitingPickupOrdersState();
}

class _WaitingPickupOrdersState extends State<WaitingPickupOrders>
    with AutomaticKeepAliveClientMixin<WaitingPickupOrders>
    implements ServerResponse {
  Rx<OrdersModel> ordersModel = OrdersModel().obs;
  late GraphQlInitilize _graphQlInitilize;
  final controller = Get.find<OrdersController>();
  RxList<OrderItems> waitingPickupOrders = <OrderItems>[].obs;

  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _graphQlInitilize = GraphQlInitilize(this);
    waitingPickupOrders.addAll(controller.dummyList);
    scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) =>
        controller.getOrdersByStatus(
            graphQlInitilize: _graphQlInitilize,
            orderStatus:
                controller.getOrderStatus(OrderStatus.waitingPickupOrders),
            context: context));
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      if (ordersModel.value.ordersPaggination?.hasNextPage ?? false) {
        controller.getOrdersByStatus(
            graphQlInitilize: _graphQlInitilize,
            orderStatus:
                controller.getOrderStatus(OrderStatus.waitingPickupOrders),
            context: context,
            page: (ordersModel.value.ordersPaggination?.page ?? 0) + 1,
            itemsNumber: 15,
            isRequiredLoader: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (Provider.of<OrderStatusProvider>(context).notificationType ==
        NotificationOrderType.newOrders) {
      Future.delayed(Duration.zero, () {
        controller.getOrdersCount();
        waitingPickupOrders.clear();
        waitingPickupOrders.addAll(controller.dummyList);
        controller.getOrdersByStatus(
            graphQlInitilize: _graphQlInitilize,
            orderStatus:
                controller.getOrderStatus(OrderStatus.waitingPickupOrders),
            context: context);
        Provider.of<OrderStatusProvider>(context, listen: false)
            .orderRecive(notificationOrderType: NotificationOrderType.notFound);
      });
    }
    return RefreshIndicator(
      onRefresh: () async {
        waitingPickupOrders.clear();
        waitingPickupOrders.addAll(controller.dummyList);
        controller.getOrdersCount();
        controller.getOrdersByStatus(
            graphQlInitilize: _graphQlInitilize,
            orderStatus:
                controller.getOrderStatus(OrderStatus.waitingPickupOrders),
            context: context);
        return Future.value();
      },
      child: Obx(
        () => ordersModel.value.status != statusOK
            ? SingleChildScrollView(
                child: SizedBox(
                  height: Get.height - 200,
                  child: Center(
                    child: Text(
                      ordersModel.value.message ?? "",
                      style: FontStyles.fontSemibold(),
                    ),
                  ),
                ),
              )
            : ListView.builder(
                itemCount: waitingPickupOrders.length,
                controller: scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.only(top: 16),
                itemBuilder: (context, index) {
                  return waitingPickupOrders[index].id == null
                      ? const OrdersLoader()
                      : WaitingPickOrShippedOrderRow(
                          isShipped: false,
                          orderItems: waitingPickupOrders[index],
                          onUpdate: (bool onUpdate) {
                            if (onUpdate) {
                              waitingPickupOrders.clear();
                              waitingPickupOrders.addAll(controller.dummyList);
                              controller.getOrdersByStatus(
                                graphQlInitilize: _graphQlInitilize,
                                orderStatus: controller.getOrderStatus(
                                    OrderStatus.waitingPickupOrders),
                                context: context,
                              );
                            }
                          });
                }),
      ),
    );
  }

  @override
  onError(error, String type) {
    ordersModel.value = OrdersModel.fromJson(error);
    print("on error ${ordersModel.value.message}");
  }

  @override
  onSucess(response, String type) {
    ordersModel.value = OrdersModel.fromJson(response);
      if (ordersModel.value.status == statusOK) {
      waitingPickupOrders.removeWhere((element) => element.id == null);
      waitingPickupOrders
          .addAll(ordersModel.value.ordersPaggination?.items ?? []);
    }
  }

  @override
  bool get wantKeepAlive => true;
}
