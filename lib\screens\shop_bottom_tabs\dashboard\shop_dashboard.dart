import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/dashboard_model.dart';
import 'package:overolasuppliers/provider/shop_info_provider.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:overolasuppliers/widgets/elbaab_homepage_header.dart';
import 'package:provider/provider.dart';

class ShopDashboard extends StatefulWidget {
  const ShopDashboard({super.key});

  @override
  State<ShopDashboard> createState() => _ShopDashboardState();
}

class _ShopDashboardState extends State<ShopDashboard>
    with AutomaticKeepAliveClientMixin<ShopDashboard>
    implements ServerResponse {
  late GraphQlInitilize _request;
  Rx<DashboardModel> dashboardInfo = DashboardModel().obs;
  final Map<String, SvgPicture> _svgCache = {};
  final RxBool _isLoading = true.obs;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);
    _precacheSvgs();
  }

  void _precacheSvgs() {
    final svgs = [
      SvgStrings.addProduct,
      SvgStrings.income,
      SvgStrings.review,
      SvgStrings.support,
      SvgStrings.freeDeliveryItem,
    ];

    for (var svg in svgs) {
      _svgCache[svg] = SvgPicture.string(svg);
    }
  }

  void _debouncedQuery(BuildContext context) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 250), () {
      _request.runQuery(
        context: context,
        query: GraphQlQuries.getDashboardInfo,
        isRequiredLoader: false,
      );
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final appLocal = AppLocalizations.of(context)!;

    if (context.watch<UpdatedInfo>().getTabPosition() == 0) {
      _debouncedQuery(context);
    }

    return Scaffold(
        appBar: const ElbaabHomePageHeader(),
        body: Obx(
          () => _isLoading.value
              ? const Center(child: CircularProgressIndicator())
              : ErrorBoundary(
                  child: SingleChildScrollView(
                    key: const Key('dashboard_scroll'),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header Stats Section with Gradient
                        Container(
                          margin: EdgeInsets.all(20.r),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppColors.colorPrimary.withOpacity(0.2),
                                AppColors.colorPrimary,
                                AppColors.colorPrimary.withOpacity(0.7),
                                AppColors.colorPrimary.withOpacity(0.5),
                                AppColors.colorPrimary.withOpacity(0.3),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(24),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.colorPrimary.withOpacity(0.3),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: Stack(
                            children: [
                              // Decorative circles
                              Positioned(
                                top: 0,
                                right: 0,
                                child: Container(
                                  height: 70.h,
                                  width: 70.w,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.1),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ),
                              Positioned(
                                bottom: 0,
                                left: 0,
                                child: Container(
                                  height: 70.h,
                                  width: 70.w,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.1),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ),

                              // Content
                              Padding(
                                padding: EdgeInsets.all(24.r),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              appLocal.myTotalIncome,
                                              style: FontStyles.fontMedium(
                                                fontSize: 14,
                                                color: Colors.white
                                                    .withOpacity(0.7),
                                              ),
                                            ),
                                            SizedBox(height: 8.h),
                                            Obx(() => Text(
                                                  "${appLocal.aed} ${(dashboardInfo.value.totalRevenue ?? 0.0).ceil()}",
                                                  style: FontStyles.fontBold(
                                                      fontSize: 28),
                                                )),
                                          ],
                                        ),
                                        const Spacer(),
                                        Container(
                                          padding: EdgeInsets.all(12.r),
                                          decoration: BoxDecoration(
                                            color:
                                                Colors.white.withOpacity(0.2),
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Icon(
                                            Icons.account_balance_wallet,
                                            color: Colors.white,
                                            size: 24.r,
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 24.h),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 16.w,
                                        vertical: 16.h,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: Row(
                                        children: [
                                          _buildHeaderStat(
                                            title: appLocal.product,
                                            value:
                                                "${dashboardInfo.value.productCount ?? 0}",
                                          ),
                                          Container(
                                            height: 24.h,
                                            width: 1,
                                            color:
                                                Colors.white.withOpacity(0.2),
                                          ),
                                          _buildHeaderStat(
                                            title: appLocal.sold,
                                            value:
                                                "${dashboardInfo.value.totalSoldItems ?? 0}",
                                          ),
                                          Container(
                                            height: 24.h,
                                            width: 1,
                                            color:
                                                Colors.white.withOpacity(0.2),
                                          ),
                                          _buildHeaderStat(
                                            title: appLocal.order,
                                            value:
                                                "${dashboardInfo.value.ordersCount ?? 0}",
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Services Section
                        Padding(
                          padding: EdgeInsets.all(20.r),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                appLocal.services,
                                style: FontStyles.fontBold(
                                  fontSize: 18,
                                  color: Colors.white.withOpacity(0.9),
                                ),
                              ),
                              SizedBox(height: 16.h),

                              // Services Grid
                              GridView.count(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                crossAxisCount: 2,
                                mainAxisSpacing: 16.h,
                                crossAxisSpacing: 16.w,
                                childAspectRatio: 1.5,
                                children: [
                                  _buildServiceCard(
                                    title: appLocal.returnRequest,
                                    icon: SvgStrings.addProduct,
                                    color: Colors.blue,
                                    onTap: () => Get.toNamed(
                                        RouteNames.returendRequestsScreen),
                                  ),
                                  _buildServiceCard(
                                    title: appLocal.incomeAndExpenses,
                                    icon: SvgStrings.income,
                                    color: Colors.green,
                                    onTap: () {
                                     
                                      if ((dashboardInfo.value.totalRevenue ??
                                              0) ==
                                          0) {
                                        BottomSheets
                                            .showAlertMessageBottomSheet(
                                                appLocal.noIncomeYet,
                                                appLocal.alert,
                                                context);
                                      } else {
                                        Get.toNamed(RouteNames
                                            .viewIncomeExpensesScreen);
                                      }
                                    },
                                  ),
                                  _buildServiceCard(
                                    title: appLocal.reviews,
                                    icon: SvgStrings.review,
                                    color: Colors.orange,
                                    onTap: () =>
                                        Get.toNamed(RouteNames.reviewsScreen),
                                  ),
                                  _buildServiceCard(
                                    title: appLocal.support,
                                    icon: SvgStrings.support,
                                    color: Colors.purple,
                                    onTap: () => Get.toNamed(
                                        RouteNames.elbaabSupportContactScreen),
                                  ),
                                  _buildServiceCard(
                                    title: appLocal.freeDeliveryItems,
                                    icon: SvgStrings.freeDeliveryItem,
                                    color: Colors.red,
                                    onTap: () => Get.toNamed(
                                        RouteNames.productFreeDeliveryScreen),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
        ));
  }

  Widget _buildHeaderStat({required String title, required String value}) {
    return Expanded(
      child: Column(
        children: [
          Text(
            value,
            style: FontStyles.fontBold(fontSize: 20),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: FontStyles.fontMedium(
              fontSize: 12,
              color: Colors.white.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  String _getIconForService(String title) {
    print(title.toLowerCase());
    switch (title.toLowerCase()) {
      case 'requestsa':
        return '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
          <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42z"/>
        </svg>''';

      case 'income & expenses':
        return '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
          <path fill="currentColor" d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
          <path fill="currentColor" d="M19.5 10.5l-2.12-2.12 1.06-1.06L20 8.88l2.56-2.56 1.06 1.06z"/>
          <path fill="currentColor" d="M19.5 13.5l-2.12 2.12 1.06 1.06L20 15.12l2.56 2.56 1.06-1.06z"/>
        </svg>''';

      case 'reviews':
        return '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
          <path fill="currentColor" d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
          <path fill="currentColor" d="M12 15.4l-3.76 2.27 1-4.28-3.32-2.88 4.38-.38L12 6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28z"/>
        </svg>''';

      case 'support':
        return '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
          <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
          <path fill="currentColor" d="M11 15h2v2h-2zm1-10c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z"/>
        </svg>''';

      case 'free delivery items':
        return '''
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Box -->
  <path d="M4 7h16v12H4V7z" fill="#8B7355" stroke="#000000" stroke-width="2" stroke-linejoin="round"/>
  <!-- Yellow tape/label -->
  <path d="M9 7v4h6V7" fill="#FFD700" stroke="#000000" stroke-width="2"/>
  <!-- Up arrow -->
  <path d="M12 14l0 3M10.5 15.5L12 14l1.5 1.5" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <!-- Hand -->
  <path d="M3 12.5C3 12.5 4 11 6 11C8 11 8 13 8 13" stroke="#FFA07A" fill="#FFA07A" stroke-width="2" stroke-linecap="round"/>
  <!-- Check circle -->
  <circle cx="18" cy="6" r="4" fill="#32CD32" stroke="#000000" stroke-width="1.5"/>
  <path d="M16 6l1.5 1.5L20 5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>''';

      default:
        return '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
          <path fill="currentColor" d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
        </svg>''';
    }
  }

  Widget _buildServiceCard({
    required String title,
    required String icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final customIcon = _getIconForService(title);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        decoration: BoxDecoration(
          color: color.withOpacity(0.15),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Stack(
          children: [
            Positioned(
              right: -20,
              bottom: -20,
              child: Container(
                height: 80.h,
                width: 80.w,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.all(5.r),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: SizedBox(
                      height: 32.h,
                      width: 32.w,
                      child: SvgPicture.string(
                        customIcon,
                        colorFilter:
                            title.toLowerCase() == "free delivery items"
                                ? null
                                : ColorFilter.mode(color, BlendMode.srcIn),
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                  Text(
                    title,
                    style: FontStyles.fontMedium(
                      fontSize: 14,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  onError(error, String type) {
    _isLoading.value = false;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Error: ${error.toString()}')),
    );
  }

  @override
  onSucess(response, String type) {
    _isLoading.value = false;
    try {
      dashboardInfo.value = DashboardModel.fromJson(response);
      Provider.of<ShopInfoProvider>(Get.context!, listen: false)
          .setShopName(dashboardInfo.value.shopName ?? "");
    } catch (e) {
      onError(e, type);
    }
  }

  @override
  bool get wantKeepAlive => true;
}

class Incomechart {
  Incomechart({
    required this.debit,
    required this.date,
    required this.day,
    required this.barHeight,
  });
  late final String debit;
  late final String day;
  late final double barHeight;
  late final String date;
}

class ErrorBoundary extends StatelessWidget {
  final Widget child;

  const ErrorBoundary({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    ErrorWidget.builder = (FlutterErrorDetails details) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Text(
          'An error occurred: ${details.exception}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    };

    return child;
  }
}
