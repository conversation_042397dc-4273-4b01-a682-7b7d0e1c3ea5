import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class HeaderCloseWidget extends GetView<AddProductController> {
  const HeaderCloseWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return IconButton(
      icon: const Icon(Icons.close),
      onPressed: () {
        String info = prefs.getString(productInformation) ?? "";
        String details = prefs.getString(productDetails) ?? "";
        String shipment = prefs.getString(productShipment) ?? "";
        String policy = prefs.getString(productPolicies) ?? "";

        if (info.isEmpty &&
            details.isEmpty &&
            shipment.isEmpty &&
            policy.isEmpty) {
          Get.offAllNamed(RouteNames.shopHomeScreen);
        } else {
          if (controller.product != null) {
            Alerts.alertView(
                context: context,
                title: appLocal.alert,
                content: appLocal.alertMessageBeforeDiscardProductChanges,
                cancelAction: () => Get.back(),
                cancelActionText: appLocal.cancel,
                defaultActionText: appLocal.yes,
                action: () {
                  Get.back();
                  Alerts.showCustomSnackbar(
                      context: context,
                      contentText: appLocal.discardChangesInProgress,
                      afterExecuteMethod: () {
                        prefs.remove(productInformation);
                        prefs.remove(productDetails);
                        prefs.remove(productShipment);
                        prefs.remove(productPolicies);
                        Get.offAllNamed(RouteNames.shopHomeScreen);
                      });
                });
          } else {
            Alerts.alertView(
                context: context,
                title: appLocal.alert,
                content: appLocal.saveInfo,
                cancelAction: () {
                  Get.back();
                  Future.delayed(Duration.zero, () {
                    Alerts.showCustomSnackbar(
                        context: context,
                        contentText: appLocal.deleteProductFromCache,
                        afterExecuteMethod: () {
                          Get.back();
                          prefs.remove(productInformation);
                          prefs.remove(productDetails);
                          prefs.remove(productShipment);
                          prefs.remove(productPolicies);
                          Get.offAllNamed(RouteNames.shopHomeScreen);
                        });
                  });
                },
                cancelActionText: appLocal.no,
                defaultActionText: appLocal.yes,
                action: () {
                  Get.back();
                  Get.offAllNamed(RouteNames.shopHomeScreen);
                });
          }
        }
      },
    );
  }
}
