import 'package:flutter/material.dart';

class AppColors {
  // Background Color
  static Color backgroundColorLight = const Color.fromRGBO(246, 246, 246, 1);
  static Color backgroundColorDark = const Color.fromRGBO(34, 36, 42, 1);
  static Color backgroundColorLightTransparent =
      const Color.fromRGBO(255, 255, 255, 0.8);
  static Color backgroundColorDarkTransparent =
      const Color.fromRGBO(19, 20, 24, 0.75);
  static Color colorTransparent =
      const Color.fromRGBO(19, 20, 24, 0.42745098039215684);

  // Header Color
  static Color headerColorLight = const Color.fromRGBO(255, 255, 255, 1);
  static Color headerColorDark = const Color.fromRGBO(50, 55, 65, 1);

  //Theme Colors
  static Color colorDanger = const Color.fromRGBO(234, 40, 75, 1);
  static Color colorBlue = const Color.fromRGBO(19, 85, 252, 1);
  static Color colorGreen = const Color.fromRGBO(7, 185, 0, 1);
  static Color colorYellow = const Color.fromRGBO(249, 169, 78, 1);

  // Theme Border & Shadow
  static Color borderColorLight = const Color.fromRGBO(30, 30, 34, 1);
  static Color borderColorDark = const Color.fromRGBO(255, 255, 255, 1);
  static Color feildColorLight = const Color.fromRGBO(255, 255, 255, 1);
  static Color feildColorDark = const Color.fromRGBO(50, 55, 65, 1);
  static Color feildBorderColor = const Color.fromRGBO(204, 204, 204, 1);
  static Color silverSandColor = const Color.fromRGBO(194, 195, 198, 1);
  static Color feildBorderColorDark = const Color.fromRGBO(255, 255, 255, 0.3);
  static Color colorShadow = const Color.fromRGBO(38, 50, 56, 0.1);
  static Color colorGray = const Color.fromRGBO(152, 155, 160, 1);
  static Color colorTangerine = const Color.fromRGBO(255, 161, 99, 1);

  static Color colorRatingStar = const Color.fromRGBO(246, 200, 71, 1);
  static Color colormossGreen = const Color.fromRGBO(153, 211, 161, 1);
  static Color colotJumbo = const Color.fromRGBO(118, 118, 128, 1);
  static Color colotMidGray = const Color.fromRGBO(99, 99, 102, 1);
  static Color colotMidBlack = const Color.fromRGBO(42, 46, 53, 1);

  static Color colorPrimary = const Color.fromRGBO(149, 162, 254, 1);
  static Color colorPrimary_80 = const Color.fromRGBO(149, 162, 254, 0.8);
  static Color colorPrimary_60 = const Color.fromRGBO(149, 162, 254, 0.6);
  static Color colorPrimary_40 = const Color.fromRGBO(149, 162, 254, 0.4);

  static Color colorSecondary = const Color.fromRGBO(153, 211, 161, 1);
  static Color colorSecondary_80 = const Color.fromRGBO(153, 211, 161, 0.8);
  static Color colorSecondary_60 = const Color.fromRGBO(153, 211, 161, 0.6);
  static Color colorSecondary_40 = const Color.fromRGBO(153, 211, 161, 0.4);

  static Color colorSecondaryYellow = const Color.fromRGBO(255, 221, 126, 1);
  static Color colorSecondaryYellow_80 =
      const Color.fromRGBO(255, 221, 126, 0.8);
  static Color colorSecondaryYellow_60 =
      const Color.fromRGBO(255, 221, 126, 0.6);
  static Color colorSecondaryYellow_40 =
      const Color.fromRGBO(255, 221, 126, 0.4);

  static Color colorSecondary_Red = const Color.fromRGBO(245, 98, 127, 1);
  static Color colorSecondary_Red_80 = const Color.fromRGBO(245, 98, 127, 0.8);
  static Color colorSecondary_Red_60 = const Color.fromRGBO(245, 98, 127, 0.6);
  static Color colorSecondary_Red_40 = const Color.fromRGBO(245, 98, 127, 0.4);


   static const Color textColor = Color(0xFFD0D7E1);
  static const Color hintColor = Color(0xFF717578);
  static const Color backgroundColor = Color(0xff343941);
  static const Color cardColor = Color(0xff4D565F);
  static const Color trackColor = Color(0xff2C3037);
  static const Color selectedColor = Color(0xffE3D0B2);

  static const List<Color> cardColors = [
    Color(0xff60656D),
    Color(0xff4D565F),
    Color(0xff464D57),
  ];
  static const List<Color> dimmedLightColors = [
    Color(0xff505863),
    Color(0xff424a53),
    Color(0xff343941),
  ];
}
