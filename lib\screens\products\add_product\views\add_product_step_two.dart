import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/dio/api_requests.dart';
import 'package:overolasuppliers/helper/enums/enums.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/media_picker.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/product_variation_model.dart';
import 'package:overolasuppliers/model/add_product/view_prduct/product_detail_model.dart';
import 'package:overolasuppliers/model/upload_image_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/screens/products/add_product/model/product_detail_model.dart';
import 'package:overolasuppliers/screens/products/add_product/views/components/image_slider_widget.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AddProductStepTwo extends GetView<AddProductController>
    with InputValidationUtil
    implements ServerResponse {
  var sizeBox24 = const SizedBox(height: 24);
  late ApiRequest _request;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final PageController _selectedImageIndex = PageController();

  AddProductStepTwo({super.key});

  bool colorCheck(String element, {required String type}) {
    int i = int.parse(element.replaceAll(RegExp(r'[^0-9]'), ''));
    var productColor =
        (controller.product?.productOptions?.productColors ?? [])[i - 1];
    int indexOf = controller.colorList
        .indexWhere((element) => element.id == productColor.id);
    if (indexOf != -1) {
      ColorModel colorModel = controller.colorList[indexOf];
      if (type == "icon") {
        if (colorModel.thumnailUrl == productColor.colorIcon) {
          return true;
        } else {
          return false;
        }
      } else if (type == "name") {
        if (colorModel.colorName == productColor.colorName) {
          return true;
        } else {
          return false;
        }
      } else {
        if (colorModel.colorFamily == productColor.colorFamily) {
          return true;
        } else {
          return false;
        }
      }
    } else {
      return false;
    }
  }

  bool checkReturnedColors() {
    if (controller.product?.productOptions?.productColors?.isNotEmpty ??
        false) {
      for (var element in controller.validationHistory?.returnValues ?? []) {
        if (element.contains("Color Name") &&
            colorCheck(element, type: "name")) {
          return true;
        }

        if (element.contains("Color family") &&
            colorCheck(element, type: "family")) {
          return true;
        }
        if (element.contains("Color Icon") &&
            colorCheck(element, type: "icon")) {
          return true;
        }
        if (element.contains("image")) {
          int i = int.parse(element.replaceAll(RegExp(r'[^0-9]'), ''));
          String image = element.replaceAll(RegExp(r"[0-9]+"), "");
          int indexWhere =
              (controller.product?.productOptions?.productColors ?? [])
                  .indexWhere((element) =>
                      element.colorName ==
                          image.replaceAll("image", "").removeAllWhitespace ||
                      element.colorFamily ==
                          image.replaceAll("image", "").removeAllWhitespace);
          if (indexWhere != -1) {
            String colorId =
                (controller.product?.productOptions?.productColors ??
                            [])[indexWhere]
                        .id ??
                    "";
            int colorIndex = controller.colorList
                .indexWhere((element) => element.id == colorId);
            if (colorIndex != -1) {
              ColorModel color = controller.colorList[colorIndex];
              if (color.imagesUrl.contains(controller.product?.productOptions
                      ?.productColors?[indexWhere].colorImages?[i - 1] ??
                  "")) {
                return true;
              }
            }
          }
        }
      }
    }
    return false;
  }

  bool checkReturnedSizes() {
    if (controller.validationHistory?.returnValues?.contains("Size unit 1") ??
        false) {
      if (controller.product?.productOptions?.productSizes?.isNotEmpty ??
          false) {
        if (controller.product?.productOptions?.productSizes?.first.sizeUnit ==
            controller.size.value.sizeUnit) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool checkReturnedOptions() {
    bool haveReturnedOption = false;
    if (controller.product?.productOptions?.productCustomOptions?.isNotEmpty ??
        false) {
      for (var i = 0;
          i < (controller.validationHistory?.returnValues?.length ?? 0);
          i++) {
        String returnValue =
            controller.validationHistory?.returnValues?[i] ?? "";
        if (returnValue.contains("custom option title")) {
          returnValue = returnValue.split("custom").first;
          int returnIndex =
              int.parse(returnValue.replaceAll(RegExp(r'[^0-9]'), '')) - 1;
          if (returnIndex >= 0) {
            ProductCustomOptions? customOptions = controller
                .product?.productOptions?.productCustomOptions?[returnIndex];
            int indexOf = controller.customOptionList.indexWhere(
                (element) => element.optionId == customOptions?.optionId);
            if (indexOf != -1) {
              CustomOption option = controller.customOptionList[indexOf];
              if (customOptions?.optionTitle == option.title) {
                haveReturnedOption = true;
              }
            }
          }
        } else {
          haveReturnedOption = false;
        }
      }
    }
    return haveReturnedOption;
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    _request = ApiRequest(this);

    return GetX<AddProductController>(
      init: AddProductController(),
      builder: (controller) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.colorPrimary.withOpacity(0.1),
                AppColors.backgroundColor,
              ],
            ),
          ),
          child: SingleChildScrollView(
            controller: controller.stepperHideController,
            physics: const ClampingScrollPhysics(),
            child: Form(
              key: _formKey,
              child: Column(
                children: <Widget>[
                  // Image Slider Section
                  if ((controller.colorList.isNotEmpty) || controller.sliderImages.isNotEmpty)
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.headerColorDark,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      height: MediaQuery.of(context).size.width +
                          ((MediaQuery.of(context).size.width / 3) +
                              (controller.colorList.isNotEmpty ? 120 : 0)),
                      margin: const EdgeInsets.only(top: 24).r,
                      child: ImageSliderWidget(
                        controller: controller,
                        sliderimageController: _selectedImageIndex
                      ),
                    ),

                  Padding(
                    padding: const EdgeInsets.all(16.0).r,
                    child: Column(
                      children: [
                        // Photos Section
                        if (!controller.isMatchProduct && controller.colorList.isEmpty)
                          Container(
                            decoration: BoxDecoration(
                              color: AppColors.headerColorDark,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: controller.haveImages.value ? AppColors.colorDanger : Colors.transparent,
                              ),
                            ),
                            padding: EdgeInsets.all(16.r),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Obx(
                                  () => controller.colorList.isNotEmpty
                                      ? Container()
                                      : Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              appLocal.addPhotos,
                                              style: FontStyles.fontRegular(fontSize: 16),
                                            ),
                                            Text(
                                              '( ${controller.sliderImages.length} / 5 ) Photos',
                                              style: FontStyles.fontRegular(
                                                fontSize: 10,
                                                color: Colors.white.withOpacity(0.38),
                                              ),
                                            ),
                                          ],
                                        ),
                                ),
                                SizedBox(height: 16.h),
                                // Image Grid/List Section
                                Obx(
                                  () => controller.colorList.isNotEmpty
                                      ? Container()
                                      : SizedBox(
                                          height: 93.h,
                                          child: Row(
                                            children: [
                                              Obx(
                                                () => controller.sliderImages.length < 5
                                                    ? Column(
                                                        children: [
                                                          Align(
                                                            alignment:
                                                                Alignment.topLeft,
                                                            child: GestureDetector(
                                                              onTap: () async {
                                                                int count = 5 -
                                                                    controller
                                                                        .sliderImages
                                                                        .length;
                                                                if (count != 0) {
                                                                  multiImagePicker(
                                                                          context,
                                                                          count)
                                                                      .then((value) {
                                                                    if (value != null) {
                                                                      uploadimage(
                                                                          value);
                                                                    }
                                                                  });
                                                                }
                                                              },
                                                              child: Container(
                                                                height: 70.h,
                                                                width: 70.w,
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: AppColors
                                                                      .headerColorDark,
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(5),
                                                                ),
                                                                child: Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                              .all(9.0)
                                                                          .r,
                                                                  child: Column(
                                                                    children: <Widget>[
                                                                      SvgPicture.string(
                                                                          SvgStrings
                                                                              .iconCamera),
                                                                      Text(appLocal.add,
                                                                          style: FontStyles
                                                                              .fontMedium(
                                                                                  fontSize:
                                                                                      12))
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          Obx(
                                                            () => controller
                                                                    .haveImages.value
                                                                ? Text(
                                                                    appLocal
                                                                        .pleaseUploadImage,
                                                                    style: FontStyles
                                                                        .fontRegular(
                                                                            color: AppColors
                                                                                .colorDanger),
                                                                  )
                                                                : Container(),
                                                          ),
                                                        ],
                                                      )
                                                    : Container(),
                                              ),
                                              SizedBox(width: 6.w),
                                              Expanded(
                                                child: ListView.builder(
                                                    itemCount:
                                                        controller.sliderImages.length,
                                                    scrollDirection: Axis.horizontal,
                                                    itemBuilder: (context, index) {
                                                      return InkWell(
                                                        onTap: () => _selectedImageIndex
                                                            .animateToPage(index,
                                                                duration:
                                                                    const Duration(
                                                                        seconds: 1),
                                                                curve: Curves
                                                                    .linearToEaseOut),
                                                        child: Container(
                                                          width: 70.w,
                                                          height: 93.h,
                                                          margin: const EdgeInsets.only(
                                                                  right: 7)
                                                              .r,
                                                          decoration: BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius.circular(
                                                                    5),
                                                            border: ((controller
                                                                            .validationHistory
                                                                            ?.returnValues
                                                                            ?.contains(
                                                                                "Image ${index + 1}") ??
                                                                        false) &&
                                                                    (controller.product
                                                                                ?.productImages?[
                                                                            index]) ==
                                                                        controller
                                                                                .sliderImages[
                                                                            index])
                                                                ? Border.all(
                                                                    color: AppColors
                                                                        .colorDanger,
                                                                    width: 2)
                                                                : Border.all(
                                                                    color: Colors
                                                                        .transparent,
                                                                    width: 0,
                                                                  ),
                                                          ),
                                                          child: Stack(
                                                            alignment: Alignment.center,
                                                            children: <Widget>[
                                                              Positioned(
                                                                top: 0,
                                                                left: 0,
                                                                right: 0,
                                                                height: 70.h,
                                                                child: GlobalMethods
                                                                    .netWorkImage(
                                                                        controller
                                                                                .sliderImages[
                                                                            index],
                                                                        BorderRadius
                                                                            .circular(
                                                                                5),
                                                                        BoxFit.contain),
                                                              ),
                                                              // if (!controller
                                                              //     .isMatchProduct)
                                                              Positioned(
                                                                bottom: 0,
                                                                child: InkWell(
                                                                  onTap: () =>
                                                                      editPhoto(
                                                                          context,
                                                                          index,
                                                                          controller),
                                                                  child: SvgPicture
                                                                      .string(SvgStrings
                                                                          .iconEdit),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      );
                                                    }),
                                              ),
                                            ],
                                          ),    
                                          ),    
                                        )
                              ],
                            ),
                          ),

                        SizedBox(height: 24.h),

                        // Colors Section
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.headerColorDark,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: checkReturnedColors() ? AppColors.colorDanger : Colors.transparent,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.colorPrimary.withOpacity(0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              // Header section
                              Container(
                                padding: const EdgeInsets.symmetric(
                                        vertical: 12, horizontal: 16)
                                    .r,
                                decoration: BoxDecoration(
                                  color: AppColors.colorPrimary.withOpacity(0.1),
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(10),
                                    topRight: Radius.circular(10),
                                  ),
                                ),
                                child: InkWell(
                                  onTap: () {
                                    if ((controller.isApprovedProduct ||
                                            controller.isMatchProduct) &&
                                        (controller.product!.productOptions
                                                ?.productColors?.isEmpty ??
                                            false)) {
                                      BottomSheets.showAlertMessageBottomSheet(
                                        appLocal.restrictOption,
                                        appLocal.alert,
                                        context
                                      );
                                    } else {
                                      Get.toNamed(RouteNames.viewColorScreen);
                                    }
                                  },
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          controller.colorList.isNotEmpty
                                              ? appLocal.color
                                              : appLocal.addColor,
                                          style: FontStyles.fontRegular(
                                            color: checkReturnedColors()
                                                ? AppColors.colorDanger
                                                : Colors.white,
                                          ),
                                        ),
                                      ),
                                      Icon(
                                        Icons.chevron_right,
                                        color: checkReturnedColors()
                                            ? AppColors.colorDanger
                                            : Colors.white,
                                      )
                                    ],
                                  ),
                                ),
                              ),

                              // Content section
                              if (controller.colorList.isNotEmpty)
                                Container(
                                  padding: const EdgeInsets.all(16).r,
                                  child: ListView.builder(
                                    shrinkWrap: true,
                                    physics: const NeverScrollableScrollPhysics(),
                                    itemCount: controller.colorList.length,
                                    itemBuilder: (context, section) {
                                      return Container(
                                        margin: EdgeInsets.only(bottom: 16.h),
                                        padding: const EdgeInsets.all(12).r,
                                        decoration: BoxDecoration(
                                          color: AppColors.colorPrimary.withOpacity(0.05),
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: <Widget>[
                                            Row(
                                              children: <Widget>[
                                                Container(
                                                  width: 20.w,
                                                  height: 20.h,
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(10),
                                                    border: Border.all(
                                                      color: Colors.white.withOpacity(0.1),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  child: GlobalMethods.netWorkImage(
                                                    controller.colorList[section].thumnailUrl,
                                                    BorderRadius.circular(10),
                                                    BoxFit.cover,
                                                  ),
                                                ),
                                                SizedBox(width: 10.w),
                                                Expanded(
                                                  child: Text(
                                                    '${appLocal.localeName == "ar" ? controller.colorList[section].colorFamilyAr : controller.colorList[section].colorFamily}${(controller.colorList[section].colorName.isEmpty) ? "" : " , ${appLocal.localeName == "ar" ? controller.colorList[section].colorNameAr : controller.colorList[section].colorName}"}',
                                                    style: FontStyles.fontRegular(),
                                                  ),
                                                ),
                                                IconButton(
                                                  onPressed: () {
                                                    ColorModel colorModel = controller.colorList[section];
                                                    Get.toNamed(RouteNames.addColorsScreen, arguments: [colorModel, section]);
                                                  },
                                                  icon: SvgPicture.string(SvgStrings.iconEditGray),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10.h),
                                            SizedBox(
                                              height: 50.h,
                                              child: ListView.builder(
                                                scrollDirection: Axis.horizontal,
                                                itemCount: controller.colorList[section].imagesUrl.length,
                                                itemBuilder: (contex, index) {
                                                  return Container(
                                                    margin: const EdgeInsets.only(right: 12).r,
                                                    height: 50.h,
                                                    width: 50.w,
                                                    decoration: BoxDecoration(
                                                      borderRadius: BorderRadius.circular(5),
                                                      border: Border.all(
                                                        color: Colors.white.withOpacity(0.1),
                                                        width: 1,
                                                      ),
                                                    ),
                                                    child: GlobalMethods.netWorkImage(
                                                      controller.colorList[section].imagesUrl[index],
                                                      BorderRadius.circular(5),
                                                      BoxFit.cover
                                                    ),
                                                  );
                                                }
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }
                                  ),
                                ),
                            ],
                          ),
                        ),

                        SizedBox(height: 24.h),

                        // Sizes Section
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.headerColorDark,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: checkReturnedSizes() ? AppColors.colorDanger : Colors.transparent,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.colorPrimary.withOpacity(0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              // Header
                              Container(
                                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16).r,
                                decoration: BoxDecoration(
                                  color: AppColors.colorPrimary.withOpacity(0.1),
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(10),
                                    topRight: Radius.circular(10),
                                  ),
                                ),
                                child: InkWell(
                                  onTap: () {
                                    if ((controller.isApprovedProduct ||
                                            controller.isMatchProduct) &&
                                        (controller.product!.productOptions
                                                ?.productSizes?.isEmpty ??
                                            false)) {
                                      BottomSheets.showAlertMessageBottomSheet(
                                          appLocal.restrictOption,
                                          appLocal.alert,
                                          context);
                                    } else {
                                      Get.toNamed(RouteNames.addSizeScreen);
                                    }
                                  },
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          controller.size.value.sizes.isNotEmpty ? appLocal.size : appLocal.addSize,
                                          style: FontStyles.fontRegular(
                                            color: checkReturnedSizes() ? AppColors.colorDanger : Colors.white,
                                          ),
                                        ),
                                      ),
                                      Icon(
                                        Icons.chevron_right,
                                        color: checkReturnedSizes() ? AppColors.colorDanger : Colors.white,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              // Size List
                              if (controller.size.value.sizes.isNotEmpty)
                                Container(
                                  padding: EdgeInsets.all(16.r),
                                  child: Wrap(
                                    spacing: 12.w,
                                    runSpacing: 12.h,
                                    children: controller.size.value.sizes.map((size) {
                                      return Container(
                                        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 8.h),
                                        decoration: BoxDecoration(
                                          color: AppColors.colorPrimary.withOpacity(0.05),
                                          borderRadius: BorderRadius.circular(8),
                                          border: Border.all(
                                            color: Colors.white.withOpacity(0.1),
                                          ),
                                        ),
                                        child: Text(
                                          size.value,
                                          style: FontStyles.fontRegular(
                                            color: Colors.white.withOpacity(0.8),
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ),
                            ],
                          ),
                        ),

                        SizedBox(height: 24.h),

                        // Custom Options Section
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.headerColorDark,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: checkReturnedOptions() ? AppColors.colorDanger : Colors.transparent,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.colorPrimary.withOpacity(0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              // Header
                              Container(
                                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16).r,
                                decoration: BoxDecoration(
                                  color: AppColors.colorPrimary.withOpacity(0.1),
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(10),
                                    topRight: Radius.circular(10),
                                  ),
                                ),
                                child: InkWell(
                                  onTap: () async {
                                    if ((controller.isApprovedProduct ||
                                            controller.isMatchProduct) &&
                                        (controller.product!.productOptions
                                                ?.productCustomOptions?.isEmpty ??
                                            false)) {
                                      BottomSheets.showAlertMessageBottomSheet(
                                          appLocal.restrictOption,
                                          appLocal.alert,
                                          context);
                                    } else {
                                      final result = await Get.toNamed(
                                          RouteNames.addCustomOptionsScreen);
                                      if (((result is! String)) &&
                                          result != null) {
                                        controller.customOptionList.value =
                                            result;
                                      }
                                    }
                                  },
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          controller.customOptionList.isNotEmpty ? appLocal.customOption : appLocal.addcustomOption,
                                          style: FontStyles.fontRegular(
                                            color: checkReturnedOptions() ? AppColors.colorDanger : Colors.white,
                                          ),
                                        ),
                                      ),
                                      Icon(
                                        Icons.chevron_right,
                                        color: checkReturnedOptions() ? AppColors.colorDanger : Colors.white,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              // Custom Options List
                              if (controller.customOptionList.isNotEmpty)
                                Container(
                                  padding: EdgeInsets.all(16.r),
                                  child: ListView.separated(
                                    shrinkWrap: true,
                                    physics: const NeverScrollableScrollPhysics(),
                                    itemCount: controller.customOptionList.length,
                                    separatorBuilder: (context, index) => SizedBox(height: 16.h),
                                    itemBuilder: (context, section) {
                                      return Container(
                                        padding: EdgeInsets.all(12.r),
                                        decoration: BoxDecoration(
                                          color: AppColors.colorPrimary.withOpacity(0.05),
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              controller.customOptionList[section].title,
                                              style: FontStyles.fontBold(fontSize: 14),
                                            ),
                                            SizedBox(height: 4.h),
                                            Text(
                                              controller.customOptionList[section].titleAr,
                                              style: FontStyles.fontBold(fontSize: 14),
                                            ),
                                            SizedBox(height: 12.h),
                                            Wrap(
                                              spacing: 8.w,
                                              runSpacing: 8.h,
                                              children: controller.customOptionList[section].valueList.map((value) {
                                                return Container(
                                                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                                                  decoration: BoxDecoration(
                                                    color: AppColors.headerColorDark,
                                                    borderRadius: BorderRadius.circular(6),
                                                    border: Border.all(
                                                      color: Colors.white.withOpacity(0.1),
                                                    ),
                                                  ),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text(
                                                        value.value,
                                                        style: FontStyles.fontMedium(
                                                          fontSize: 12,
                                                          color: Colors.white.withOpacity(0.8),
                                                        ),
                                                      ),
                                                      Text(
                                                        value.valueAr,
                                                        style: FontStyles.fontMedium(
                                                          fontSize: 12,
                                                          color: Colors.white.withOpacity(0.8),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              }).toList(),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                            ],
                          ),
                        ),

                        // Error Message
                        Obx(
                          () => controller.isRequiredUpdate.value.isNotEmpty
                              ? Padding(
                                  padding: const EdgeInsets.only(top: 16.0, bottom: 20),
                                  child: Text(
                                    controller.isRequiredUpdate.value,
                                    textAlign: TextAlign.center,
                                    style: FontStyles.fontMedium(color: AppColors.colorDanger),
                                  ),
                                )
                              : Container(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  editPhoto(BuildContext context, int index, AddProductController controller) {
    BottomSheets.editPhoto(context).then((value) async {
      switch (value) {
        case UpdateMedia.delete:
          controller.sliderImages.removeAt(index);
          break;
        case UpdateMedia.replace:
          multiImagePicker(context, 1).then((value) {
            if (value != null) {
              _request.uploadImage(
                  files: [value[0]], parameters: ["replace"], type: "$index");
            }
          });
          break;
      }
    });
  }

  uploadimage(List<File> images) {
    List<String> arrParams = [];
    for (var i = 0; i < images.length; i++) {
      arrParams.add("images$i");
    }
    _request.uploadImage(files: images, parameters: arrParams);
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    UploadImageModel model = UploadImageModel.fromJson(response);
    if (model.status == statusOK) {
      controller.haveImages.value = false;
      model.fileList?.forEach((element) {
        controller.uploadedImages.addAll(element.fileUrls ?? []);
      });
      if (type.isNotEmpty) {
        for (ColorModel element in controller.colorList) {
          if (element.imagesUrl.isNotEmpty) {
            int itemindex = element.imagesUrl.indexWhere((element) =>
                element == controller.sliderImages[int.parse(type)]);
            if (itemindex >= 0) {
              element.imagesUrl[itemindex] =
                  model.fileList?[0].fileUrls?.first ?? "";
            }
          }
        }
        if (controller.arrVariations.isNotEmpty &&
            controller.colorList.isEmpty) {
          for (Variants variants in controller.arrVariations) {
            for (Variations element in variants.variations ?? []) {
              element.isUpdated = true;
              element.variantImages?[int.parse(type)] =
                  model.fileList?[0].fileUrls?.first ?? "";
            }
          }
        }
        controller.sliderImages[int.parse(type)] =
            model.fileList?[0].fileUrls?.first ?? "";
        controller.nonRemovableImages[int.parse(type)] =
            model.fileList?[0].fileUrls?.first ?? "";
      } else {
        for (FileList element in model.fileList ?? []) {
          controller.sliderImages.add(element.fileUrls?.first ?? "");
          controller.nonRemovableImages.add(element.fileUrls?.first ?? "");
        }
        if (controller.arrVariations.isNotEmpty &&
            controller.colorList.isEmpty) {
          for (Variants variants in controller.arrVariations) {
            for (Variations element in variants.variations ?? []) {
              element.isUpdated = true;
              element.variantImages = controller.sliderImages;
            }
          }
        }
      }
    }
  }
}
