class BaseModel {
  BaseModel(
      {this.typename,
      this.status,
      this.message,
      this.error,
      this.userId,
      this.arMessage});
  final String? typename;
  final String? arMessage;
  final String? error;
  final int? status;
  final String? message;
  final String? userId;

  BaseModel.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        status = json['status'] as int?,
        message = json['message'] as String?,
        error = json['error'] as String?,
        arMessage = json['arMessage'] as String?,
        userId = json['userId'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'status': status,
        'message': message,
        'arMessage': arMessage,
        'userId': userId,
        'error': error,
      };
}
