import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/alert_messages_constants.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/screens/autentications/components/add_number.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_name_widget.dart';
import 'package:sms_autofill/sms_autofill.dart';

class NumberVerification extends StatelessWidget
    with CodeAutoFill
    implements ServerResponse {
  final ValueNotifier<int> _currentState = ValueNotifier(0);
  final ValueNotifier<int> _time = ValueNotifier(90);

  var numberController = TextEditingController();
  RxString otpCode = "".obs, errorText = "".obs;
  late GraphQlInitilize _request;
  late AppLocalizations appLocal;

  NumberVerification({super.key});

  void startTimer() {
    _time.value = 90;
    Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (_time.value == 0) {
          timer.cancel();
        } else {
          _time.value--;
        }
      },
    );
  }

  Future<bool> _willPop(BuildContext context) {
    final completer = Completer<bool>();
    if (_currentState.value == 1) {
      _currentState.value = 0;
      completer.complete(true);
    } else {
      completer.complete(false);
    }
    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
     appLocal = AppLocalizations.of(context)!;
    _request = GraphQlInitilize(this);
    listenForCode();
    return WillPopScope(
      onWillPop: () => _willPop(context),
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.backgroundColorDark,
          leading: IconButton(
            icon: const Icon(
              Icons.chevron_left,
              size: 40,
            ),
            onPressed: () {
              if (_currentState.value == 0) {
                Get.offAllNamed(RouteNames.loginScreen);
              }
              if (_currentState.value == 1) {
                _currentState.value = 0;
              }
            },
          ),
          centerTitle: true,
        ),
        body: ValueListenableBuilder(
          valueListenable: _currentState,
          builder: (context, value, child) {
            return value == 0
                ? Obx(
                    () => AddNumber(
                      numberController: numberController,
                      errorText: errorText.value,
                    ),
                  )
                : _verifyNumber(context);
          },
        ),
        bottomNavigationBar: ValueListenableBuilder(
          valueListenable: _currentState,
          builder: (context, value, child) {
            return Container(
              height: 120,
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: Column(
                children: [
                  ElbaabGradientButtonWidget(
                    onPress: () async {
                      String number = numberController.text;
                      if (number.startsWith('0')) {
                        number = number.replaceFirst('0', '+9715');
                      } else if (number.startsWith('5')) {
                        number = number.replaceFirst('5', '+9715');
                      }
                      String userID = Get.arguments[0];
                      String validatePhone =
                          InputValidationUtil.validatePhone(number);
                      if (value == 0) {
                        if (validatePhone.isNotEmpty) {
                          _request.runMutation(
                            context: context,
                            query: GraphQlQuries.addPhoneNumber,
                            variables: GraphQlVariables.addNumber(
                                userId: userID, userPhoneNumber: number),
                          );
                        } else {
                          errorText.value = validatePhone.toString();
                        }
                      } else {
                        if (otpCode.value.isNotEmpty &&
                            otpCode.value.length > 5) {
                          _request.runMutation(
                            context: context,
                            query: GraphQlQuries.numberVerify,
                            type: "Verify",
                            variables: GraphQlVariables.verifyNumber(
                                userId: userID, otpNumber: otpCode.value),
                          );
                        } else {
                          errorText.value =
                              AlertMessagesConstant.requiredOtpCode;
                        }
                      }
                    },
                    text: value == 0
                        ? appLocal.verifyMobileNumber
                        : appLocal.verify,
                  ),
                  const SizedBox(height: 20),
                  InkWell(
                    onTap: () => GlobalMethods.logout(),
                    child: Text(
                      appLocal.logout,
                      style:
                          FontStyles.fontRegular(color: AppColors.colorPrimary),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _verifyNumber(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          const ElbaabNameWidget(),
          const SizedBox(height: 30),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              appLocal.verifyMobileNumber,
              style: FontStyles.fontSemibold(fontSize: 25),
            ),
          ),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              "${appLocal.verifyMobileNumberContent} ${numberController.text}",
              style: FontStyles.fontRegular(
                color: Colors.white.withOpacity(0.55),
              ),
            ),
          ),
          const SizedBox(height: 30),
          Obx(
            () => TextFieldPinAutoFill(
              decoration: InputDecoration(
                errorText: errorText.value.isNotEmpty ? errorText.value : null,
                errorStyle:
                    FontStyles.fontRegular(color: AppColors.colorDanger),
                fillColor: AppColors.feildColorDark,
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(
                    width: 1,
                    color: AppColors.feildBorderColor,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(
                    width: 1,
                    color: Colors.white,
                  ),
                ),
              ),
              currentCode: otpCode.value,
              onCodeChanged: (code) {
                if (code.length == 6) {
                  otpCode.value = code;
                  FocusScope.of(context).requestFocus(FocusNode());
                }
              },
            ),
          ),
          ValueListenableBuilder(
            valueListenable: _time,
            builder: (context, value, child) {
              return Padding(
                padding: const EdgeInsets.only(top: 24.0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: value == 0
                      ? InkWell(
                          onTap: () {
                            String userID = Get.arguments[0];
                            _request.runMutation(
                              context: context,
                              query: GraphQlQuries.resendPhoneNumberOtp,
                              variables: GraphQlVariables.resendNumberOtp(
                                  userId: userID),
                            );
                          },
                          child: RichText(
                            text: TextSpan(children: <TextSpan>[
                              TextSpan(
                                text: appLocal.notReceived,
                                style: FontStyles.fontRegular(fontSize: 10),
                              ),
                              TextSpan(
                                  text: appLocal.resendOtp,
                                  style: FontStyles.fontRegular(
                                    fontSize: 10,
                                    decoration: TextDecoration.underline,
                                    color: AppColors.colorPrimary,
                                  )),
                            ]),
                          ),
                        )
                      : Text(
                          appLocal.secondRemaing('$value'),
                          style: FontStyles.fontRegular(fontSize: 15),
                        ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  onError(error, String type) {
    BaseModel model = BaseModel.fromJson(error);
    errorText.value = model.message ?? "";
  }

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (model.status == statusOK && type == 'Verify') {
      cancel();
      errorText.value = '';
      String userID = Get.arguments[0];
      Get.offNamed(RouteNames.addBusinessInfoScreen, arguments: [userID]);
    } else if (model.status == statusOK) {
      startTimer();
      _currentState.value = 1;
      errorText.value = '';
    }
    if (model.status == status400) {
      errorText.value = model.message ?? "";
    }
  }

  @override
  void codeUpdated() {
    otpCode.value = code!;
    String userID = Get.arguments[0];
    _request.runMutation(
      context: MyApp.navigatorKey.currentContext!,
      query: GraphQlQuries.numberVerify,
      type: "Verify",
      variables: GraphQlVariables.verifyNumber(
          userId: userID, otpNumber: otpCode.value),
    );
  }
}
