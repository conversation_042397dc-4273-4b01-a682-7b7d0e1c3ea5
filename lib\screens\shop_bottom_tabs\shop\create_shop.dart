import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/shop_customer_contact.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/shop_information.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/shop_pickup_adresses.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/components/shop_review.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/shop/controller/shop_info_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class CreateShop extends GetView<ShopInfoController> {
  final RxInt _currentStep = 0.obs;

  bool requiredAction = false;
  final bool isUpdate = Get.arguments[1] ?? false;

  CreateShop({super.key});
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return WillPopScope(
      onWillPop: () async {
        if (controller.shop?.isCompleted != null ||
            controller.shop == null ||
            controller.shop!.isCompleted == true) {
          if (_currentStep.value == 0) {
            return true;
          } else {
            if (controller.shop != null) {
              Get.offAllNamed(RouteNames.shopHomeScreen);
              return false;
            } else {
              _currentStep.value--;
              return false;
            }
          }
        } else {
          return true;
        }
      },
      child: Scaffold(
        appBar: ElbaabHeader(
          title: isUpdate ? appLocal.updateShop : appLocal.createShop,
          trailingWidget: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              if (controller.shop?.isCompleted != null ||
                  controller.shop == null ||
                  controller.shop!.isCompleted == true) {
                if (_currentStep.value == 0) {
                  Get.back();
                } else {
                  if (controller.shop != null) {
                    Alerts.alertView(
                        context: context,
                        content: appLocal.aleartOnStopShopEdit,
                        cancelAction: () => Get.back(),
                        cancelActionText: appLocal.no,
                        action: () {
                          Get.back();
                          prefs.setString(shopInfo, "");
                          controller.updateshopInfo(shop: controller.shop!);
                          prefs.setString("customerContact", "");
                          controller.updateContactInfo(shop: controller.shop!);
                          prefs.setString(shopPickUpAddress, "");
                          controller.updatePickupAddress(
                              shop: controller.shop!);
                          Get.offAllNamed(RouteNames.shopHomeScreen);
                        });
                  } else {
                    _currentStep.value--;
                  }
                }
              } else {
                prefs.setString(shopInfo, "");
                controller.updateshopInfo(shop: controller.shop!);
                prefs.setString("customerContact", "");
                controller.updateContactInfo(shop: controller.shop!);
                prefs.setString(shopPickUpAddress, "");
                controller.updatePickupAddress(shop: controller.shop!);
                Get.back();
              }
            },
          ),
        ),
        body: Obx(
          () => _currentStep.value == 0
              ? ShoopInformation(tabPosition: (v) => _currentStep.value = v)
              : _currentStep.value == 1
                  ? ShopPickupAdresses(
                      tabPosition: (v) => _currentStep.value = v)
                  : _currentStep.value == 2
                      ? ShopCustomerContact(
                          tabPosition: (v) => _currentStep.value = v)
                      : ShopReview(tabPosition: (v) => _currentStep.value = v),
        ),
      ),
    );
  }
}
