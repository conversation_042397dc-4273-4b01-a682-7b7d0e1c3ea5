// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get changeLanguage => 'Change Language';

  @override
  String get customerService => 'Customer Service';

  @override
  String get deleteProduct => 'Deleted Products';

  @override
  String get privacyPolicy => 'Privacy And Policy';

  @override
  String get accountAndMangment => 'Accounts And Mangments';

  @override
  String get appName => 'Elbaab';

  @override
  String get your_country => 'Your Country';

  @override
  String get your_language => 'Your Language';

  @override
  String get login => 'Login';

  @override
  String get youDontHaveAccess => 'You dont have account?';

  @override
  String get authenticateToSignIn => 'Please authenticate to sign in';

  @override
  String get verifyMobileNumber => 'Verify mobile number';

  @override
  String get signup => 'Signup';

  @override
  String get useBiometricAcess => 'Use Biometric Access';

  @override
  String get back => 'Back';

  @override
  String get restrictOption => 'Adding more option for accepted and matched product is not allowed';

  @override
  String get reset => 'Reset';

  @override
  String get rememberMe => 'Remember Me';

  @override
  String get clearCache => 'Clear Cache';

  @override
  String get pushNotification => 'Manage Notificatios';

  @override
  String get reciveOrderEmails => 'Recive Order Email';

  @override
  String get biometricAccess => 'Biometric Access';

  @override
  String get verifyMobileNumberContent => 'To verify your mobile number, we\'ve sent a One Time Password (OTP) to your phone';

  @override
  String get addPhotos => 'Add Photos';

  @override
  String get addOption => 'Add Option';

  @override
  String get addedColor => 'ADDED Colors';

  @override
  String get brand => 'Brand';

  @override
  String get completeSetup => 'Complete Setup';

  @override
  String get updateInfo => 'Update Info';

  @override
  String get addSize => 'Add Size';

  @override
  String get uploadColorPhoto => 'Upload Color Photo';

  @override
  String get category => 'Category';

  @override
  String get custom => 'Custom';

  @override
  String get viewColors => 'View Colors';

  @override
  String get productname => 'Product Name';

  @override
  String get addColor => 'Add Color';

  @override
  String get categories => 'Categories';

  @override
  String get variation => 'Variations';

  @override
  String get subCategory => 'Sub Category';

  @override
  String get acceptReturn => 'Accept return';

  @override
  String get accept => 'Accept';

  @override
  String get type => 'Type';

  @override
  String get customOption => 'Custom Option';

  @override
  String get addcustomOption => 'Add Custom Option';

  @override
  String get reject => 'Reject';

  @override
  String get addStory => 'Add Story';

  @override
  String get addPrice => 'Add Price';

  @override
  String get add => 'Add';

  @override
  String get addYourBrandName => 'Add Your Brand Name';

  @override
  String get maxBoxDimension => 'Maximum Box Dimensions';

  @override
  String get genrateVariations => 'Genrate Variations';

  @override
  String get notificationOnMinQtyIsReached => 'Notification on Min. Qty is reached';

  @override
  String get availableQuantity => 'Available Quantity';

  @override
  String get aed => 'AED';

  @override
  String get size => 'Sizes';

  @override
  String get returnCondition => 'Return Condition';

  @override
  String get productPolicy => 'Product Policy';

  @override
  String get viewListofBrand => 'View List of brands';

  @override
  String get chooseBrand => 'Choose Brand';

  @override
  String get recentlySelected => 'Recently Selected';

  @override
  String get selectCategory => 'Select Category';

  @override
  String get updateRefundStatus => 'Update refund status';

  @override
  String get ok => 'OK';

  @override
  String get polices => 'Polices';

  @override
  String get width => 'Width';

  @override
  String get length => 'Length';

  @override
  String get weight => 'Weight';

  @override
  String get height => 'Height';

  @override
  String get requiredRefregiration => 'Required refregiration';

  @override
  String get specification => 'Specification';

  @override
  String get itemPerOrder => 'Items per Order';

  @override
  String get newSpecification => 'New Specification';

  @override
  String get oldSpecification => 'Old Specification';

  @override
  String get driverName => 'Driver Name';

  @override
  String get deliveryCompany => 'Delivery Company';

  @override
  String get officeAddress => 'Office Address';

  @override
  String get confirmAll => 'CONFIRM ALL';

  @override
  String get send => 'Send';

  @override
  String get returnDuration => 'Return Duration';

  @override
  String get warrantyDuration => 'Warranty Duration';

  @override
  String get keyword => 'Keywords';

  @override
  String get buyerDetails => 'Buyer Details';

  @override
  String get deliveryDetails => 'Delivery Details';

  @override
  String get ordered => 'Ordered';

  @override
  String get delivered => 'Delivered';

  @override
  String get confirmed => 'Confirmed';

  @override
  String get confirm => 'Confirm';

  @override
  String get rejected => 'Rejected';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get shipmentInformation => 'Shipment Information';

  @override
  String get shipmentFits => 'Package Type';

  @override
  String get returned => 'Returned';

  @override
  String get shipment => 'Shipment';

  @override
  String get paymentMethod => 'Payment Method';

  @override
  String get returnedConfirmed => 'Returned\n Confirmed';

  @override
  String get cancelledByCustomer => 'Cancelled\n by customer';

  @override
  String get shipped => 'Shipped';

  @override
  String get orderDetail => 'Order Detail';

  @override
  String get transferRequest => 'Transfer Request';

  @override
  String get youAreGoingToSend => 'You are going to send';

  @override
  String get vat => 'VAT';

  @override
  String get myShop => 'My shop';

  @override
  String get orderNumber => 'Order Number';

  @override
  String get totalAmount => 'Total Amount';

  @override
  String get done => 'Done';

  @override
  String get transactionID => 'Transaction ID';

  @override
  String get withdrawalID => 'WithDrawal ID';

  @override
  String get orderID => 'Order ID';

  @override
  String get amount => 'Amount';

  @override
  String get more => 'more';

  @override
  String get sales => 'Sales';

  @override
  String get status => 'Status';

  @override
  String get transferredAmount => 'Transferred Amount';

  @override
  String get topSellingItems => 'Top Selling Items';

  @override
  String get initiateTransferRequest => 'Initiate Transfer Request';

  @override
  String get totalVat => 'Total VAT';

  @override
  String get totalRevenue => ' Total Revenue';

  @override
  String get productforFreeDelivery => 'Product for free delivery';

  @override
  String get update => 'Update';

  @override
  String get verify => 'Verify';

  @override
  String get submit => 'Submit';

  @override
  String get next => 'Next';

  @override
  String get pendingAmount => 'Pending Amount';

  @override
  String get availableAmount => 'Available Amount';

  @override
  String get price => 'Price';

  @override
  String get miniQTY => 'Minimum Quantity';

  @override
  String get deliveryChargesFees => 'Delivery Charges Fees';

  @override
  String get reply => 'Reply';

  @override
  String get edit => 'Edit';

  @override
  String get remove => 'Remove';

  @override
  String get delete => 'Delete';

  @override
  String get details => 'Details';

  @override
  String get resetPasswordContent => 'Enter your email to reset password';

  @override
  String get tryAnotherWay => 'Or you can try another way';

  @override
  String get welcome => 'Welcome,';

  @override
  String get addMobileNumber => 'Add Mobile Number';

  @override
  String get enterAMobileNumberToSafeguardYourAccount => 'Enter a mobile number to safeguard your account.';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get welcomeAgain => 'Welcome again ,';

  @override
  String get welcomeBack => 'Welcome back!';

  @override
  String get cancel => 'Cancel';

  @override
  String get bankAccount => 'Bank Account';

  @override
  String get activity => 'Activity';

  @override
  String get creditCard => 'Credit Card';

  @override
  String get setting => 'SETTING';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get removeTermsConditionMessage => 'Are you Sure You Want To Remove Your Terms And Condition';

  @override
  String get myInformation => 'MY Information';

  @override
  String get save => 'Save';

  @override
  String get order => 'Orders';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get callUs => 'Call US';

  @override
  String get supportEmail => '<EMAIL>';

  @override
  String get whatsapp => 'WHATSAPP';

  @override
  String get support => 'Support';

  @override
  String get minimumQtyAlert => 'Minimum Qty Alert';

  @override
  String get minimumQty => 'Minimum QTY';

  @override
  String get freeDeliveryTarget => 'Free Delivery Target';

  @override
  String get alert => 'Alert';

  @override
  String get freeDeliveryItems => 'Free Delivery Items';

  @override
  String get sold => 'Sold';

  @override
  String get services => 'Services';

  @override
  String get review => 'Review';

  @override
  String get incomeAndExpenses => 'Income & Expenses';

  @override
  String get addProduct => 'Add Product';

  @override
  String get myTotalIncome => 'MY TOTAL INCOME';

  @override
  String get matchThisProduct => 'Match this product';

  @override
  String get createNewProduct => 'Create New Product';

  @override
  String get similarProduct => 'Match Product';

  @override
  String get myAccounts => 'My Accounts';

  @override
  String get sellerDashboard => 'Seller Dashboard';

  @override
  String get profile => 'Profile';

  @override
  String get product => 'Product';

  @override
  String get updateNow => 'Update Now';

  @override
  String get publishNow => 'Publish Now';

  @override
  String get saveForLater => 'Save As Draft';

  @override
  String get shop => 'Shop';

  @override
  String get addNewBranch => 'Add new branch';

  @override
  String get addNewColor => 'Add New Colour';

  @override
  String get openGoogleMap => 'OPEN GOOGLE MAP';

  @override
  String get description => 'Description';

  @override
  String get shopDescription => 'Describe your shop.';

  @override
  String get contactNumbers => 'Contact Numbers';

  @override
  String get contactEmails => 'Contact Emails';

  @override
  String get addAnotherBranch => 'Add another Branch';

  @override
  String get forgetPaswoord => 'Forgot password?';

  @override
  String get termsAndCondition => 'Terms And Conditions';

  @override
  String get yourName => 'Your Name';

  @override
  String get newName => 'New Name';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get deleteThisColor => 'Delete This Color';

  @override
  String get deleteThisProduct => 'Delete This Product';

  @override
  String get valid => 'VALID';

  @override
  String get addAnotherContactNumber => 'Add Another Contact Number';

  @override
  String get addAnotherContactEmail => 'Add Another Contact Email';

  @override
  String get addLocationMap => 'Add Location Map';

  @override
  String get addLocationUsingMap => 'Add Location Using Map';

  @override
  String get targetPrice => 'Target Price';

  @override
  String get passwordHint => '••••••••';

  @override
  String get emailfeildLabel => 'Email';

  @override
  String get newEmail => 'New Email';

  @override
  String get newPhoneNumber => 'New Phone Number';

  @override
  String get addInfoAboutYourShop => 'Add Info About Your Shop';

  @override
  String get createShop => 'Create Shop';

  @override
  String get createShopContent => 'Now you can create your own shop and add your products';

  @override
  String get continu => 'Continue';

  @override
  String get city => 'City';

  @override
  String get notification => 'Notification';

  @override
  String get reviews => 'Reviews';

  @override
  String get qty => 'Qty';

  @override
  String get color => 'Colors';

  @override
  String get pickUp => 'Pick-up';

  @override
  String get estimatedDelivery => 'Estimated delivery';

  @override
  String get phone => 'Phone';

  @override
  String get productId => 'Product ID';

  @override
  String get productEin => ' Product EIN';

  @override
  String get variantEin => 'Variant EIN';

  @override
  String get securityAndMangments => 'Accounts And Mangments';

  @override
  String get appSettings => 'App Settings';

  @override
  String get deactivateTheAccount => 'Deactivate The Account';

  @override
  String get changePassword => 'Change Password';

  @override
  String get changeYourEmail => 'Change Your Email';

  @override
  String get changeYourUserName => 'Change Your User Name';

  @override
  String get changeYourMobileNumber => 'Change Your Mobile Number';

  @override
  String get editAccount => 'Edit Account';

  @override
  String get whyThisInformationRequired => 'Why this information is required?';

  @override
  String get addNewProduct => 'Add New Product';

  @override
  String get logout => 'Logout';

  @override
  String get myOrders => 'My Orders';

  @override
  String get myProfile => 'My profile';

  @override
  String get faq => 'Faq';

  @override
  String get faqs => 'FAQS';

  @override
  String get date => 'Date';

  @override
  String get updatedDate => 'Updated On';

  @override
  String get uploadShopBanner => 'Upload shop banner';

  @override
  String get logo => 'Logo';

  @override
  String get iban => 'IBAN';

  @override
  String get message => 'Message';

  @override
  String get returnedFeilds => 'Returned Feilds';

  @override
  String get bankName => 'Bank Name';

  @override
  String get accountHolderName => 'Account Holder Name';

  @override
  String get bankAccountInfo => 'Bank Account Information';

  @override
  String get information => 'Information';

  @override
  String get adminNotes => 'Admin Notes';

  @override
  String get trashProducts => 'Deleted Products';

  @override
  String get privacyAndPolicy => 'Privacy And Policy';

  @override
  String get logoutMessage => 'Are you sure you want to logout';

  @override
  String get returnRequest => 'Requests';

  @override
  String get productReviews => 'Product Reviews';

  @override
  String get aboutTheShop => 'About The Shop';

  @override
  String get manageBranches => 'Manage Branches';

  @override
  String get updateBussinessInfo => 'Update Business Info';

  @override
  String get addBusinessInformation => 'Add Business Information';

  @override
  String get updateBusinessInformation => 'Update Business Information';

  @override
  String get loginDescription => 'we are happy to see you again';

  @override
  String get signupDescription => 'We are thrilled to have you on board again';

  @override
  String get passwordFeildLabel => 'Password';

  @override
  String get reEnterPassword => 'Re-enter password';

  @override
  String get resendOtp => 'Resend OTP';

  @override
  String get accountNumber => 'Account Number';

  @override
  String get returnedDate => 'Returned Date';

  @override
  String get notReceived => 'Not Received? ';

  @override
  String get secondsRemainToResendOtp => 'Seconds Remaing To Resend Otp';

  @override
  String get createYourAccount => 'Create Your Account';

  @override
  String get alreadyHaveAnAccount => 'Already have an account? ';

  @override
  String get emailHint => 'ex : <EMAIL>';

  @override
  String get createAnAccount => 'Create An Account';

  @override
  String get nameHint => 'ex : Khalid Moh';

  @override
  String get morningMsg => 'Morning! Ready to boost your sales?';

  @override
  String get noonMsg => 'Noon! Keep the orders rolling in!';

  @override
  String get afterNoonMsg => 'Afternoon! Push for more sales!';

  @override
  String get eveningMsg => 'Evening! Review today\'s progress.';

  @override
  String get greetTimeError => 'Time issue! Check your clock!';

  @override
  String get searchProductFeildHint => 'Headset Earpods white wireless';

  @override
  String get skipAddNewProduct => 'Skip ( Add New Product )';

  @override
  String get matchExsistingProduct => 'Match an existing product on Elbaab.';

  @override
  String get or => 'OR';

  @override
  String get searchType => '\n\n Check With :\n\n• Product Name\n\n• EIN code (Elbaab Identification Number)\n\n• Product GTIN (Global Trade Item Number)\n\n• Product UPC (Universal Product Code) \n\n• Product SKU (Stock Keeping Unit)\n';

  @override
  String get verificationCode => 'Verification Code';

  @override
  String get settingOtpDailogMessage => 'We have sent the code verification to';

  @override
  String get verifyOtp => 'Verify Otp';

  @override
  String get updateBankAccount => 'Update bank account';

  @override
  String get addNewBankAccount => 'Add new bank account';

  @override
  String get waitWhileLoadShop => 'Wait until the shop is loaded';

  @override
  String get pickupAddress => 'Pickup Addresses';

  @override
  String get pickupAddressUsageContant => 'Pick up address will be used for shipping. It is not visible to customers.';

  @override
  String get options => 'Options';

  @override
  String get priceAndVariations => 'Price & variations';

  @override
  String get productNameFeildLableEnglish => 'Product Name (English)';

  @override
  String get productNameFeildLableArbic => 'Product Name (Arabic)';

  @override
  String get productNameFeildHint => 'ex : T-shirt';

  @override
  String get productNameFeildReturnMessage => 'Admin rejected this name';

  @override
  String get email => 'Email is required.';

  @override
  String get invalidEmail => 'Invalid Email.';

  @override
  String get invalidText => 'Invalid Text You Insert.';

  @override
  String get phoneNumber => 'Phone number is required.';

  @override
  String get invalidPhoneNumber => 'Invalid phone number.';

  @override
  String get password => 'Password field is required.';

  @override
  String get urlNotAccepted => 'Link and URL are not accepted';

  @override
  String get tradeCertificateRequired => 'Trade Certificate is Required';

  @override
  String get emiratesIDRequired => 'Emirates ID Or Passport Copy Is Required';

  @override
  String get invalidPassword => 'Password must contain minimum six characters';

  @override
  String get confirmPassword => 'Confirm Password field is required.';

  @override
  String get fieldRequired => 'Field is Required';

  @override
  String get errEmpty => 'Field is empty';

  @override
  String get gtinFeildLabel => 'GTIN OR UPC';

  @override
  String get gtinFeildReturnMessage => 'Admin rejected this Manufacturer ID';

  @override
  String get gtinFeildHint => 'ex : Manufacturer ID';

  @override
  String get errReachImageLimit => 'You reach image limit For Single Color';

  @override
  String get errSelectImageForColor => 'You will asign maximum 5 photos for each Color';

  @override
  String get passwordDoesNotMatch => 'Password Does Not Match';

  @override
  String get productDescriptionFeildLableEnglish => 'Product Description (English)';

  @override
  String get productDescriptionFeildLableArbic => 'Product Description (Arabic)';

  @override
  String get productDescriptionFeildHint => 'ex : high neck t-shirt with short seleeves';

  @override
  String get productDescriptionFeildReturnMessage => 'Admin rejected this description';

  @override
  String get freeDeliveryTargetMessage => 'If customer reaches this target amount or above, you will deliver the product for free';

  @override
  String get locationRequired => 'Your location is required so shipment company can pick up your item';

  @override
  String get whyBankInfo => 'To start receiving online payments';

  @override
  String get pendingAmountAlertMessage => 'Pending amount will be available after 30 days from Customer receiving Date';

  @override
  String get messageSent => 'Your message has been sent successfully';

  @override
  String get addKeywordMessage => 'To improve your chance of selling add keywords and use comma between them';

  @override
  String get availableQtyMessage => 'This information will shown only for you so, you can monitor your stock';

  @override
  String get minimumQtyAlertMessage => 'You will receive a notification when the number of item reaches to minimum quantity and will be displayed in the user app as (Remaining)';

  @override
  String get requiredBanner => 'Shop Banner Image Is Required';

  @override
  String get requiredLogo => 'Shop Logo Image Is Required';

  @override
  String get requiredTargetPrice => 'You Enable Free Delivery Target But You Didn\'t Enter Price';

  @override
  String get requiredTermsAndConditions => 'Upload Your Shop Terms And Conditions';

  @override
  String get requiredCity => 'Upload Your Shop Terms And Conditions';

  @override
  String get requiredMapLocation => 'Select Pin Location Using Map';

  @override
  String get requiredNumberForBranch => 'At least One Number Is Required For Branch';

  @override
  String get requiredEmailForBranch => 'At least One Email Is Required For Branch';

  @override
  String get requiredOtpCode => 'Enter Valid Otp';

  @override
  String get pleaseAcceptTermsAndConditions => 'Please Accept Terms and Conditions';

  @override
  String get limitReached => 'You have reached the limit';

  @override
  String get infoForDeliveryCompany => 'This Info Is Important For Delivery Company, you can enter the approximate values';

  @override
  String get isFreeDeliveryProduct => 'Is Product For Free Delivery';

  @override
  String get isFreeDeliveryProductMessage => 'If you enable, you will pay all delivery charges';

  @override
  String get freeReturnTitle => 'Free return';

  @override
  String get freeReturnMessage => 'You will pay the return fees';

  @override
  String get notFreeReturnTitle => 'Not free return';

  @override
  String get notFreeReturnMessage => 'Customer will pay the return fees.';

  @override
  String get adminReturnConditionNote => 'Admin Reject Your Return Condition';

  @override
  String get adminReturnDurationNote => 'Admin rejected this return duration';

  @override
  String get returnDurationFeildHint => 'ex : 5 Days';

  @override
  String get returnDurationFeildLabell => 'Return duration';

  @override
  String get returnDurationSecondFeildLabell => 'Enter Return duration';

  @override
  String get adminWarrantyDurationNote => 'Admin rejected this warranty duration';

  @override
  String get warrantyDurationFeildHint => 'ex : 5 Days';

  @override
  String get warrantyDurationFeildLabel => 'Warranty duration';

  @override
  String get warrantyDurationSecondFeildLabel => 'Enter Warranty duration';

  @override
  String get adminPolicyReturnNote => 'Admin rejected this product policy';

  @override
  String get updateReturnData => 'Update Return Data';

  @override
  String get policyFeildLabelEnglish => 'Policy (English)';

  @override
  String get policyFeildLabelArabic => 'Policy (Arabic)';

  @override
  String get policyFeildHint => 'ex : Cotton Grown Using Natural Fertilisers And Pesticides. Moreover,';

  @override
  String get selectSubCategroy => 'Select Sub Category';

  @override
  String get adminRejectThisCategory => 'Admin rejected this category';

  @override
  String get adminRejectThisBrand => 'Admin rejected this brand';

  @override
  String get pleaseSelectBrandOrType => 'Please select brand or type your own brand name';

  @override
  String get adminRejectThisSubCategory => 'Admin rejected this sub category';

  @override
  String get pleaseSelectCategorySubCategory => 'Please select category and sub category';

  @override
  String get enterYourBrandName => 'Enter your brand\'s name here';

  @override
  String get enterYourBrandNameFeildError => 'Enter your brand name if not found in the list';

  @override
  String get searchBrandByName => 'Search brand by name';

  @override
  String get colorIcon => 'Color Icon';

  @override
  String get colorFamilyFeildHint => 'ex : red';

  @override
  String get colorNameFeildLabel => 'Add Color Name';

  @override
  String get colorNameFeildHint => 'Color name ( light red , dark blue … )';

  @override
  String get colorFamilyFeildReturnMessage => 'Admin returned this color family';

  @override
  String get selectColorFamily => 'Select Color Family';

  @override
  String get deleteImageAlertMesssgae => 'Are you sure you want to delete this image';

  @override
  String get adminRejectColorName => 'Admin rejected this color name';

  @override
  String get alertMessageForRemoveColor => 'Are you sure you want to remove this color';

  @override
  String get alertMessageBeforeDiscardProductChanges => 'Are you sure you want to close? All changes will be discard.';

  @override
  String get discardChangesInProgress => 'Discard changes is in-progress';

  @override
  String get saveInfo => 'Save Info?';

  @override
  String get deleteProductFromCache => 'Deleting product data from cache';

  @override
  String get albums => 'Albums';

  @override
  String get invalidImageFormate => 'Invalid Image Format';

  @override
  String get confirmSlection => 'Confirm Selection';

  @override
  String get imageCropper => 'Image Cropper';

  @override
  String get pinchZoomInout => 'Pinch to zoom in/out';

  @override
  String get maxNumberItemsPerOrder => 'Maximum number of items per order?';

  @override
  String get itemPerOrderQuantityLessAvailable => 'It should be less than available quantity';

  @override
  String get zeroNotAcceptable => 'Zero is not acceptable';

  @override
  String get itemPerOrderFeildLabel => 'Items';

  @override
  String get itemPerOrderFeildHint => 'ex : 2';

  @override
  String get specFeildLabelEnglish => 'Title (English)';

  @override
  String get specFeildLabelArabic => 'Title (Arabic)';

  @override
  String get cityFeildHint => 'ex : Dubai';

  @override
  String get updateDraftProduct => 'Update Draft Product';

  @override
  String get alertMessageBeforeDeleteProduct => 'Are you sure you want delete this product?';

  @override
  String get resetProductWillDiscardChanges => 'Reset the product will discard all changes which you have applied';

  @override
  String get kindlyAddBankAccount => 'kindly add your bank information to get your financial dues';

  @override
  String get itemPerOrderQuantityLessVariantAvailable => 'It should be less than available variant quantity';

  @override
  String get adminRejectShipmentFitErrorMessage => 'Oops! you havn\'t update shipment fits\nAdmin note: This product not fits on this size of shipment which you selected';

  @override
  String get youRemoveProductName => 'You removed product name from information';

  @override
  String get productNameNotProvided => 'Product Name Not Provided';

  @override
  String get youRemoveProductDescription => 'You removed product description from information';

  @override
  String get productDescriptionNotProvided => 'Product Description Not Provided';

  @override
  String get youRemoveProductGtin => 'You removed product Gtin from information';

  @override
  String get productGtinNotProvided => 'Product Gtin Not Provided';

  @override
  String get youRemoveProductCategory => 'You removed product category from information';

  @override
  String get productCategoryNotProvided => 'Product Category Not Provided';

  @override
  String get youRemoveProductBrand => 'You removed product brand from information';

  @override
  String get productBrandNotProvided => 'Product Brand Not Provided';

  @override
  String get backToShop => 'Back to Shop';

  @override
  String get editShop => 'Edit Shop';

  @override
  String get badgeProductHidden => 'Product is hidden';

  @override
  String get begdeModifyRequest => 'Modified\nRequest';

  @override
  String get alertShopDiscardChanges => 'Are you sure you want to discard shop info changes?';

  @override
  String get productDetails => 'Product Detail';

  @override
  String get hideThisItem => 'Hide This Item';

  @override
  String get alertMessageForhideItem => 'The product will not be visible to the customer';

  @override
  String get restoreLastSubmission => 'Are you sure you want to restore your last submission?';

  @override
  String get restoreLastChanges => 'Restore Last Changes';

  @override
  String get restoreLastChangesAlertMessage => 'The product will back to last accepted version and all changes will be discarded';

  @override
  String get restoreProduct => 'Restore Product';

  @override
  String get editProduct => 'Edit Product';

  @override
  String get change => 'Change >';

  @override
  String get deleteItemSaveAlert => 'Deleted items are saved for 30 days in Profile';

  @override
  String get editPendingProductNotAllowed => 'Edit on pending product is not allowed';

  @override
  String get informationTitleEnglish => 'Information ( English )';

  @override
  String get informationTitleArabic => 'Information ( Arabic )';

  @override
  String get shipmentTitleEnglish => 'Shipment ( English )';

  @override
  String get shipmentTitleArabic => 'Shipment ( Arabic )';

  @override
  String get policesTitleEnglish => 'Polices ( English )';

  @override
  String get policesTitleArabic => 'Polices ( Arabic )';

  @override
  String get detailsTitleEnglish => 'Details ( English )';

  @override
  String get detailsTitleArabic => 'Details ( Arabic )';

  @override
  String get pending => 'Pending';

  @override
  String get returnedCost => 'Returned Cost';

  @override
  String get sellerPayDeliveryCost => 'The Seller pay the  Delivery cost.';

  @override
  String get customerPayDeliveryCost => ' The Customer pay the Delivery cost.';

  @override
  String get notFreeForCustomer => 'Not free for customer';

  @override
  String get productReturned => 'Product Returned';

  @override
  String get requiredUpdateOnSpec => 'Oops! seems like you have not updated the product return specifications yet and check other return details';

  @override
  String get aleartCompeleteEditSpec => 'You have not completed the edit. Please finish the edit first.';

  @override
  String get returnSpecHighlighted => 'All highlighted specificatoin returned by admin';

  @override
  String get specTitleEnglish => 'Title ( English )';

  @override
  String get specTitleArabic => 'Title ( Arabic )';

  @override
  String get specTitleFeildHint => 'Screen Size';

  @override
  String get specValueEnglish => 'Value ( English )';

  @override
  String get specValueArabic => 'Value ( Arabic )';

  @override
  String get specValueFeildHint => '4.7';

  @override
  String get unit => 'Unit';

  @override
  String get adminnRetuenSizeUnit => 'Admin returned this size unit';

  @override
  String get alertMessageForDelete => 'Are you sure you want to delete';

  @override
  String get allHideAleartMessage => 'You can\'t hide all values';

  @override
  String get hideSizeAlertMessgae => 'Hide size will not show this size to the customers';

  @override
  String get deleteSizeAlertMessage => 'Are you sure you want to delete this size';

  @override
  String get updateReturnSizeAlert => 'Oops! you havn\'t update Size Unit';

  @override
  String get waitVariationLoading => 'Wait product variation has been loading';

  @override
  String get hide => 'Hide';

  @override
  String get show => 'Show';

  @override
  String get gtin => 'GTIN';

  @override
  String get productOptions => 'Product Options';

  @override
  String get pleaseUploadImage => 'Please upload images';

  @override
  String get aleartMessageOnHideVariant => 'Hide variant will not show this variation to the customers';

  @override
  String get colorName => 'Color Name';

  @override
  String get hideAllVariantAlertMessage => 'To hide all product variants, you cannot simply hide the product. Instead, you must either set the quantity of all variants to 0 or manage their visibility individually.';

  @override
  String get youCanNotHideAllColor => 'You can\'t hide all colors';

  @override
  String get hideColorNotVisible => 'Hide color will not show this color to the customers';

  @override
  String get haventUpdateColor => 'Oops! you havn\'t update colors';

  @override
  String get colorFamily => 'color family';

  @override
  String get registration => 'Registration';

  @override
  String get returnedByAdmin => 'Returned by the admin';

  @override
  String get submittedByAdmin => 'Submitted by the supplier';

  @override
  String get acceptedByAdmin => 'Accepted by the admin';

  @override
  String get whatsappNotInstalled => 'WhatsApp is not installed on the device';

  @override
  String get contactUs => 'CONTACT US';

  @override
  String get fixProblemOrContactUs => 'You can quickly fix your problem here or you can ';

  @override
  String get updateName => 'Update Name';

  @override
  String get userNameInUse => 'Username in Use: Choose another. This one\'s taken';

  @override
  String get pleaseEnterDigitalCode => 'Please enter the digital code that we have sent';

  @override
  String get emailClaimed => 'Oops! Email\'s claimed. Pick a new one!';

  @override
  String get pleaseEnterCompeleteCode => 'Please enter complete code';

  @override
  String get mobileNumberChanged => 'Mobile number has been changed successfully';

  @override
  String get passwordCharaterLimit => 'The password must be at least 6 characters';

  @override
  String get passwordFeildHint => 'ex: 123456';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmPass => 'Confirm Password';

  @override
  String get currentPassword => 'Current Password';

  @override
  String get updateBussinessInfoAlert => 'Updating business info will hide your shop untill the admin accept the new changes, Are you sure you want to continue?';

  @override
  String get bussinessNameFeildHint => 'ex : Aroundix';

  @override
  String get bussinessNameFeildLabel => 'Business Name';

  @override
  String get bussinessNameFeildReturnMessage => 'Business Name Returned From Admin';

  @override
  String get bussinessOwnerNameFeildLabel => 'Owner/Manager Name';

  @override
  String get bussinessOwnerNameFeildHint => 'ex : Khalid Moh';

  @override
  String get bussinessOwnerNameFeildHintReturnMessae => 'Owner/ Manager Name Returned From Admin';

  @override
  String get pleaseUploadColorImage => 'Please upload color images';

  @override
  String get addNewColorName => 'Add new color name for this color family';

  @override
  String get pleaseSelectColorFamily => 'Please select color family';

  @override
  String get colorNameAlreadyExist => 'Color name already exist for this color family';

  @override
  String get alertMessageDeleteOption => 'Are you sure you want to delete this option';

  @override
  String get customOptionFeildHint => 'ex: memory size';

  @override
  String get customOptionFeildLabelEnglish => 'Title ( English )';

  @override
  String get customOptionFeildLabelArabic => 'Title ( Arabic )';

  @override
  String get customOptionFeildErrorValidTitle => 'Please Enter A Valid Title';

  @override
  String get customOptionFeildTitleAlreadyExsist => 'Option Title Already In List';

  @override
  String get customOptionAdminReturnTitle => 'Admin Has Returned This Title';

  @override
  String get addNewOption => 'Add new Option';

  @override
  String get youHaveAlreadyAddedThisOption => 'You have already added this option title to your product option.';

  @override
  String get errorAtleastAddtwoValues => 'Please Enter At least Two Values ';

  @override
  String get errorAtleastAddtwoValue => 'Please Enter At least Two Value';

  @override
  String get somethingWrongOnOption => 'Something wrong on this option';

  @override
  String get updateThisOptionFirst => 'Update this first then edit other\'s';

  @override
  String get hideOptionAlertMessgae => 'Hide option will not show this option to the customers';

  @override
  String get valueAlreadyInList => 'Value Already In List';

  @override
  String get customOptionValueFeildLabelEnglish => 'Value ( English )';

  @override
  String get customOptionValueFeildLabelArabic => 'Value ( Arabic )';

  @override
  String get noSalesFound => 'No Sales infos was found';

  @override
  String get shipmentCode => 'Shipment Code';

  @override
  String get transfer => 'Transfer';

  @override
  String get amountLessThenAvailable => 'Amount should be less or equal than the available amount';

  @override
  String get pleaseEnterAmount => 'Please enter amount';

  @override
  String get available => 'Available';

  @override
  String get deliveryFee => 'Delivery Fee';

  @override
  String get address => 'Address';

  @override
  String get location => 'Location';

  @override
  String get mapLocation => 'Map Location';

  @override
  String get number => 'Number';

  @override
  String get landNumber => 'Land Number';

  @override
  String get customerContact => 'Customer Contact';

  @override
  String get contactDetailsInfo => 'Contact details will be shared with customers after placing orders ';

  @override
  String get alertMessageDeleteOptionValue => 'Are you sure you want to delete this option value';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get orderedIn => 'Ordered in';

  @override
  String get invoiceSummery => 'Invoice Summery';

  @override
  String get shipmentInfo => 'Shipment Info';

  @override
  String get variantFieldsUpdated => 'Variant Fields Updated';

  @override
  String get alertVariantPriceQty => 'Default price and QTY have been applied to the empty variations fields.';

  @override
  String get alertOnPriceReturn => 'Admin rejected this price';

  @override
  String get alertOnQtyReturn => 'Admin rejected this product quantity';

  @override
  String get applyToAllVariant => 'Apply To All Variations';

  @override
  String get alertMessageUpdateCCustomOption => 'Oops! you havn\'t update custom options';

  @override
  String get returnCost => 'Return Cost';

  @override
  String get youRemovedProductAcceptReturnPolicies => 'You removed product accept return from Policies';

  @override
  String get productAcceptReturnNotProvided => 'Product Accept Return Not Provided';

  @override
  String get youRemovedProductFreeDeliveryPolicies => 'You removed product for free delivery from Policies';

  @override
  String get productForFreeDeliveryNotProvided => 'Product For Free Delivery Not Provided';

  @override
  String get errorCacheValueReturnDuration => 'You removed product return duration from policies';

  @override
  String get errorServerValueReturnDuration => 'Product Return Duration Not Provided';

  @override
  String get errorCacheValueWarrantyDuration => 'You removed product warranty duration from policies';

  @override
  String get errorServerValueWarrantyDuration => 'Product Warranty Duration Not Provided';

  @override
  String get errorCacheValuePolicies => 'You removed product policy from policies';

  @override
  String get errorServerValuePolicies => 'Product Policy Not Provided';

  @override
  String get productPrice => 'Product Price';

  @override
  String get errorCacheValuePrice => 'You removed product price from detail';

  @override
  String get errorServerValuePrice => 'Product Price Not Provided';

  @override
  String get errorCacheValueQty => 'You removed product qty from detail';

  @override
  String get errorServerValueQty => 'Product QTY Not Provided';

  @override
  String get errorCacheValueMinQty1 => 'You turned off minimum qty alert';

  @override
  String get errorCacheValueMinQty => 'You removed product minimum qty from detail';

  @override
  String get errorServerValueMinQty => 'Product Minimum QTY Not Provided';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get selectLanguageDetail => 'Want to change the app language? You can switch between English and Arabic for a better experience.';

  @override
  String get agreeTermsAndCondition => 'By creating an account, you agree to Elbaab ';

  @override
  String get phoneNumberAlreadyInuse => 'Phone number already in use';

  @override
  String get signupSuccessMessage2 => 'Thank you for choosing our app';

  @override
  String get signupSuccessMessage1 => 'Your request is now under review. please check your email for the status ';

  @override
  String get verifyEmailAddress => 'Verify Email Address';

  @override
  String get resetYourPassword => 'reset your password';

  @override
  String get verifyYourEmail => 'verify your email';

  @override
  String get to => 'To';

  @override
  String get weSentOtp => 'we have sent a One Time Password (OTP) to your email';

  @override
  String get mobileNumber => 'Mobile number';

  @override
  String get aleartFillSpecFeild => 'Please wait while we translate';

  @override
  String get aleartOnRemoveSpec => 'Are you sure you want to remove this specification';

  @override
  String get readySpecTranslation => 'Translation is ready to add';

  @override
  String get colorNameEnglish => 'Color Name ( English )';

  @override
  String get colorNameArabic => 'Color Name ( Arabic )';

  @override
  String get aleartRememberBe => 'Enter your credentials then login to remember your details for next time';

  @override
  String get updateTradeCertiificateDate => 'Update Trade Certificate Date';

  @override
  String get tradeCerficateExpireDate => 'Trade Certificate Expiry Date';

  @override
  String get uploadTradeCertificate => 'Upload Trade Certificate';

  @override
  String get tradeLicenseReturned => 'Trade License Returned';

  @override
  String get uploadEmiratesID => 'Upload Emirates ID';

  @override
  String get ownerIDReturned => 'Owner ID Returned';

  @override
  String get adminNote => 'Admin Note:';

  @override
  String get updateEmiratesIDDate => 'Update Emirates ID Date';

  @override
  String get emiratesIDExpiryDate => 'Emirates ID Expiry Date';

  @override
  String get aleartUploadTradeCertificate => 'Please update your Trade License';

  @override
  String get aleartUploadId => 'Please update your Emirates ID';

  @override
  String get updateShop => 'Update Shop';

  @override
  String get aleartOnStopShopEdit => 'Are you sure you want to stop editing';

  @override
  String get rejectTargetPrice => 'Admin rejected this target price';

  @override
  String get uploadTermsAndCondiition => 'Upload Terms & Conditions Doc';

  @override
  String get businessSloganFeildLabel => 'Business Slogan';

  @override
  String get businessSloganFeildHint => 'ex :Find every thing here';

  @override
  String get businessSloganFeildAdminReject => 'Admin rejected this slogan';

  @override
  String get businessDiscriiptionFeildAdminReject => 'Admin rejected this description';

  @override
  String get addBranchNumber => 'Add Branch Number';

  @override
  String get verificationCodeSent => 'We have sent the code verfication to';

  @override
  String get branchPhoneNumberOtp => 'Add your phone number. we will send you a verification code ';

  @override
  String get numberAlreadyUse => 'Number already in use on other branch';

  @override
  String get changePhoneNumber => 'Change Phone Number?';

  @override
  String get waitFechingCities => 'Wait we are fetching cities';

  @override
  String get pickupCity => 'Pickup City';

  @override
  String get adminReturned => 'Admin returned';

  @override
  String get pickupLocation => 'Pickup Location';

  @override
  String get pleaseAddLocation => 'Please add location of pickup address';

  @override
  String get landNumberFeildlabel => 'Land number';

  @override
  String get landNumberFeildHint => 'ex : 05xxxxxxxx';

  @override
  String get landNumberFeildAdminReject => 'Admin rejected this land number';

  @override
  String get contactFeildlabel => 'Phone number';

  @override
  String get contactFeildHint => 'ex : 05xxxxxxxx';

  @override
  String get pickupContactNumber => 'Pickup Contact number';

  @override
  String get verified => 'Verified';

  @override
  String get pickupFeildlabel => 'Address';

  @override
  String get pickupFeildHint => 'ex : Shop A-23, Tower 03 Khalid Street';

  @override
  String get pickupFeildAdminReject => 'Pickup Address';

  @override
  String get setYourLocation => 'Set Your Location';

  @override
  String get selectPinLocation => 'Select Pin Location';

  @override
  String get addAnotherAddress => 'Add another pickup address';

  @override
  String get branchAlreadyAssign => 'Branch already assign to some orders';

  @override
  String get aleartOnDeleteAddress => 'Are you sure you want to delete this pickup adress';

  @override
  String get adminRejectPhoneNumber => 'Admin rejected this phone number';

  @override
  String get adminRejectWhatsapppNumber => 'Admin rejected this whatsapp number';

  @override
  String get whatsappNumber => 'Whatsapp Number';

  @override
  String get adminRejectContactEmail => 'Admin rejected this email addrress';

  @override
  String get discardAllChanges => 'Discard All Changes';

  @override
  String get days => 'Days';

  @override
  String get selectCategoryFirst => 'Please select category first';

  @override
  String get errorAddTitleForCustomOption => 'Please Enter Title Or Remove Empty Custom Option';

  @override
  String get errorEmptyDetsilsOnAddOption => 'Please Enter Title With At least Two Values Before Done';

  @override
  String get accountSettings => 'Account Settings';

  @override
  String get notifications => 'Notifications';

  @override
  String get dataManagement => 'Data Management';

  @override
  String get photos => 'Photos';

  @override
  String get pickupLocations => 'Pickup Locations';

  @override
  String get pleaseEnterBranchNumber => 'Please enter branch number';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get security => 'Security';

  @override
  String get noIncomeYet => 'No Income Yet';

  @override
  String get salesTrend => 'Sales Trend';

  @override
  String helloSupplier(Object storeName) {
    return 'Hello $storeName!';
  }

  @override
  String addedImages(Object imagesLength) {
    return '( $imagesLength / 5 ) Photos';
  }

  @override
  String dynamicPrice(Object price) {
    return 'AED $price';
  }

  @override
  String dynamicPieceCount(Object count) {
    return 'Piece $count';
  }

  @override
  String productTab(Object count) {
    return 'Products ($count)';
  }

  @override
  String pendingTab(Object count) {
    return 'Pending ($count)';
  }

  @override
  String draftTab(Object count) {
    return 'Draft ($count)';
  }

  @override
  String qtyAlertTab(Object count) {
    return 'Qty Alert ($count)';
  }

  @override
  String outOfStocTab(Object count) {
    return 'Out of Stock ($count)';
  }

  @override
  String variantsTab(Object count) {
    return 'Variants ($count)';
  }

  @override
  String followersCount(Object count) {
    return 'Followers $count';
  }

  @override
  String productReturnMessage(Object returnMessage) {
    return 'Admin Notes: $returnMessage\nCheck the returned feilds';
  }

  @override
  String variantSize(Object variantSizeUnit) {
    return 'Size ( $variantSizeUnit ) : ';
  }

  @override
  String adminReturn(Object returnValue) {
    return 'Admin Return ( $returnValue ) : ';
  }

  @override
  String pickUpCity(Object pickUpCity) {
    return 'Emirate $pickUpCity ';
  }

  @override
  String emailChangeSuccessfully(Object email) {
    return 'Your Email has been Updated successfully to: $email';
  }

  @override
  String pendingOrderTab(Object count) {
    return 'Pending $count ';
  }

  @override
  String inProgressOrderTab(Object count) {
    return 'InProgress $count ';
  }

  @override
  String historyOrderTab(Object count) {
    return 'History $count ';
  }

  @override
  String newOrderTab(Object count) {
    return 'New $count ';
  }

  @override
  String newReurnOrderTab(Object count) {
    return 'Return $count ';
  }

  @override
  String waitingOrderTab(Object count) {
    return 'Waiting Pickup $count ';
  }

  @override
  String shippedOrderTab(Object count) {
    return 'Shipped Orders $count ';
  }

  @override
  String deliverdOrderTab(Object count) {
    return 'Delivered $count ';
  }

  @override
  String cancelledOrderTab(Object count) {
    return 'Cancelled $count ';
  }

  @override
  String returnedOrderTab(Object count) {
    return 'Returned $count ';
  }

  @override
  String secondRemaing(Object count) {
    return 'Seconds Remaing To Resend Otp $count ';
  }

  @override
  String removingCityBranch(Object city) {
    return 'Removing $city city branch';
  }
}
