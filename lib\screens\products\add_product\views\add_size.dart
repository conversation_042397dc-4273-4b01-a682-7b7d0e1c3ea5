import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/alert/alerts.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/size_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/screens/products/add_product/model/product_detail_model.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_label_container.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../../model/add_product/view_prduct/product_detail_model.dart';

class AddSize extends StatefulWidget {
  const AddSize({Key? key}) : super(key: key);

  @override
  State<AddSize> createState() => _AddSizeState();
}

class _AddSizeState extends State<AddSize> implements ServerResponse {
  Rx<SizesModel> sizesModel = Rx<SizesModel>(SizesModel());
  List<String> arrUints = [];
  List<String> arrUintsAr = [];
  RxList<SizeValueModel> arrSizes = RxList<SizeValueModel>();
  RxString strUnit = ''.obs, strUnitAr = ''.obs, strReturnedUnit = ''.obs;
  List<SizeValueModel> selectedSizes = [];
  late GraphQlInitilize _grapghRequest;
  final controller = Get.find<AddProductController>();
  List<String> returnSizes = [];
  int indexWhere = -1;
  RxDouble bottomNavigationBarHeight = 90.0.obs;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      _grapghRequest = GraphQlInitilize(this);
      _grapghRequest.runQuery(
        context: context,
        query: GraphQlQuries.getAllSizes,
      );
      if (controller.size.value.sizes.isNotEmpty) {
        setState(() {
          selectedSizes.addAll(controller.size.value.sizes);
          strUnit.value = controller.size.value.sizeUnit;
          strUnitAr.value = controller.size.value.sizeUnitAr;
        });
      }
      if (controller.product != null &&
          (controller.product?.productOptions?.productSizes?.isNotEmpty ??
              false)) {
        if (controller.validationHistory?.returnValues
                ?.contains("Size unit 1") ??
            false) {
          strReturnedUnit.value =
              controller.product?.productOptions?.productSizes?[0].sizeUnit ??
                  "";
          if (strReturnedUnit.value == strUnit.value) {
            bottomNavigationBarHeight.value = 0;
          }
        }
        returnSizes.clear();
        for (var index = 0;
            index <
                (controller.product?.productOptions?.productSizes?[0].sizeValues
                        ?.length ??
                    0);
            index++) {
          if ((controller.validationHistory?.returnValues?.contains(
                  "${controller.product?.productOptions?.productSizes?[0].sizeUnit} Value ${index + 1}") ??
              false)) {
            returnSizes.add(controller.product?.productOptions?.productSizes?[0]
                    .sizeValues?[index].value ??
                "");
          } else {
            returnSizes.add("");
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    const double itemHeight = 55;
    final double itemWidth = size.width / 2;
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.addSize,
        leadingWidget: IconButton(
          icon: Icon(
            Icons.chevron_left,
            size: 35,
            color: Colors.white.withOpacity(0.86),
          ),
          onPressed: () {
            SizeModel sizeModel = SizeModel(controller.size.value.sizeUnit,
                controller.size.value.sizes, controller.size.value.sizeUnitAr);
            controller.size.value = sizeModel;
            Get.back(result: false);
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: <Widget>[
              if (!controller.isApprovedProduct && !controller.isMatchProduct)
                Obx(
                  () => ElbaabLabelContainer(
                    label: appLocal.unit,
                    onTap: () => BottomSheets.showListPicker(context,
                            appLocal.localeName == "ar" ? arrUintsAr : arrUints)
                        .then((value) {
                      arrSizes.clear();
                      selectedSizes.clear();
                      strUnit.value = arrUints[value];
                      strUnitAr.value = arrUintsAr[value];

                      if ((controller.validationHistory?.returnValues
                              ?.contains("Size unit 1") ??
                          false)) {
                        if (strReturnedUnit.value == strUnit.value) {
                          bottomNavigationBarHeight.value = 0;
                        }
                      }
                      for (String element
                          in (sizesModel.value.sizes?[value].sizeValues ??
                              [])) {
                        arrSizes.add(SizeValueModel(element, false));
                      }
                    }),
                    borderColor: ((controller.validationHistory?.returnValues
                                    ?.contains("Size unit 1") ??
                                false) &&
                            strReturnedUnit.value == strUnit.value)
                        ? AppColors.colorDanger
                        : null,
                    errorText: ((controller.validationHistory?.returnValues
                                    ?.contains("Size unit 1") ??
                                false) &&
                            strReturnedUnit.value == strUnit.value)
                        ? appLocal.adminnRetuenSizeUnit
                        : null,
                    leading: Text(
                      appLocal.localeName == "ar"
                          ? strUnitAr.value
                          : strUnit.value,
                      textAlign: TextAlign.left,
                      style: FontStyles.fontRegular(),
                    ),
                    trailing:
                        const Icon(Icons.arrow_drop_down_outlined, size: 20),
                  ),
                ),
              const SizedBox(height: 24),
              Obx(
                () => GridView.builder(
                  itemCount: arrSizes.length,
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    int position = selectedSizes.indexWhere(
                        (element) => element.value == arrSizes[index].value);
                    indexWhere = -1;
                    if (index < returnSizes.length) {
                      indexWhere = returnSizes.indexOf(arrSizes[index].value);
                    }
                    return GestureDetector(
                      onTap: () {
                        if (!returnSizes.contains(arrSizes[index].value)) {
                          if (controller.isApprovedProduct) {
                            int previousSizeIndex = -1;
                            if (controller.product != null &&
                                (controller.product!.productOptions
                                        ?.productSizes?.isNotEmpty ??
                                    false)) {
                              if (index <
                                  (controller
                                          .product!
                                          .productOptions
                                          ?.productSizes
                                          ?.first
                                          .sizeValues
                                          ?.length ??
                                      0)) {
                                previousSizeIndex = selectedSizes.indexWhere(
                                    (element) =>
                                        element.value ==
                                        (controller
                                                .product!
                                                .productOptions
                                                ?.productSizes
                                                ?.first
                                                .sizeValues?[index]
                                                .value ??
                                            ""));
                              }
                            }
                            if (previousSizeIndex == -1) {
                              setState(() {
                                int containIndex = selectedSizes.indexWhere(
                                    (element) =>
                                        element.value == arrSizes[index].value);
                                if (containIndex != -1) {
                                  selectedSizes.removeAt(containIndex);
                                } else {
                                  if (index > selectedSizes.length) {
                                    selectedSizes.add(arrSizes[index]);
                                  } else {
                                    selectedSizes.insert(
                                        index, arrSizes[index]);
                                  }
                                }
                              });
                            }
                          } else {
                            setState(() {
                              int containIndex = selectedSizes.indexWhere(
                                  (element) =>
                                      element.value == arrSizes[index].value);
                              if (containIndex != -1) {
                                selectedSizes.removeAt(containIndex);
                              } else {
                                if (index > selectedSizes.length) {
                                  selectedSizes.add(arrSizes[index]);
                                } else {
                                  SizeValueModel sizeValue = arrSizes[index];
                                  if (controller.product?.productOptions
                                          ?.productSizes?.isNotEmpty ??
                                      false) {
                                    if (controller.product?.productOptions
                                            ?.productSizes?.first.sizeValues
                                            ?.any((element) =>
                                                element.value ==
                                                sizeValue.value) ??
                                        false) {
                                      sizeValue.isReAddedValue = true;
                                      sizeValue.isReGenratedVariant = false;
                                    }
                                  }
                                  selectedSizes.insert(index, sizeValue);
                                }
                              }
                            });
                          }
                        }
                        if (bottomNavigationBarHeight.value == 0) {
                          bottomNavigationBarHeight.value = 90;
                        }
                      },
                      child: Container(
                        height: 30,
                        decoration: BoxDecoration(
                          color: AppColors.headerColorDark,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            width: indexWhere >= 0
                                ? 2
                                : position >= 0
                                    ? 2
                                    : 1,
                            color: indexWhere >= 0
                                ? AppColors.colorDanger
                                : position >= 0
                                    ? AppColors.colorPrimary
                                    : Colors.white.withOpacity(0.3),
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 34),
                        child: Center(
                          child: Text(
                            arrSizes[index].value,
                            style: FontStyles.fontMedium(
                              fontSize: 14,
                              color: position == index
                                  ? AppColors.colorPrimary
                                  : Colors.white.withOpacity(0.5),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio:
                        (MediaQuery.of(context).size.width / 3 / 50),
                    crossAxisSpacing: 5.0,
                    mainAxisSpacing: 5.0,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Obx(
                () => (strUnit.value.isNotEmpty && selectedSizes.isNotEmpty)
                    ? Container(
                        padding: const EdgeInsets.only(left: 10, right: 10),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: const Color.fromRGBO(38, 41, 49, 1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            if (!controller.isApprovedProduct &&
                                !controller.isMatchProduct)
                              IconButton(
                                onPressed: () {
                                  Alerts.alertView(
                                      context: context,
                                      content:
                                          appLocal.alertMessageForDelete,
                                      action: () {
                                        Get.back();
                                        setState(() {
                                          selectedSizes = [];
                                          strUnit.value = '';
                                        });
                                        SizeModel sizeModel = SizeModel(
                                            strUnit.value,
                                            selectedSizes,
                                            strUnitAr.value);
                                        controller.size.value = sizeModel;
                                      },
                                      defaultActionText: appLocal.yes,
                                      cancelAction: () => Get.back(),
                                      cancelActionText: appLocal.no);
                                },
                                icon: Icon(
                                  Icons.cancel,
                                  color: Colors.white.withOpacity(0.42),
                                ),
                              ),
                            if (controller.isApprovedProduct ||
                                controller.isMatchProduct)
                              const SizedBox(height: 20),
                            Text(
                              appLocal.localeName == "ar"
                                  ? strUnitAr.value
                                  : strUnit.value,
                              style: FontStyles.fontSemibold(
                                color: Colors.white.withOpacity(0.5),
                              ),
                            ),
                            const SizedBox(height: 16),
                            GridView.builder(
                                itemCount: selectedSizes.length,
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 2,
                                        mainAxisSpacing: 8,
                                        crossAxisSpacing: 13,
                                        childAspectRatio:
                                            (itemWidth / itemHeight)),
                                itemBuilder: (context, index) {
                                  int previousSizeIndex = -1;
                                  if (controller.product != null &&
                                      (controller.product!.productOptions
                                              ?.productSizes?.isNotEmpty ??
                                          false)) {
                                    if (index <
                                        (controller
                                                .product!
                                                .productOptions
                                                ?.productSizes
                                                ?.first
                                                .sizeValues
                                                ?.length ??
                                            0)) {
                                      previousSizeIndex =
                                          selectedSizes.indexWhere((element) =>
                                              element.value ==
                                              (controller
                                                      .product!
                                                      .productOptions
                                                      ?.productSizes
                                                      ?.first
                                                      .sizeValues?[index]
                                                      .value ??
                                                  ""));
                                    }
                                  }
                                  return Container(
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        color: AppColors.headerColorDark),
                                    child: Stack(
                                      children: [
                                        Positioned.fill(
                                          child: Row(
                                            children: [
                                              const SizedBox(width: 5),
                                              Expanded(
                                                child: Text(
                                                  selectedSizes[index].value,
                                                  textAlign: TextAlign.center,
                                                  style:
                                                      FontStyles.fontMedium(),
                                                ),
                                              ),
                                              IconButton(
                                                onPressed: () {
                                                  if (controller
                                                          .isApprovedProduct ||
                                                      controller
                                                          .isMatchProduct) {
                                                    if (previousSizeIndex ==
                                                        index) {
                                                      int isLastItem = 0;
                                                      for (SizeValueModel sizeModel
                                                          in selectedSizes) {
                                                        if (sizeModel
                                                            .isValueHidden) {
                                                          isLastItem++;
                                                        }
                                                      }
                                                      if (isLastItem ==
                                                          (selectedSizes
                                                                  .length -
                                                              1)) {
                                                        BottomSheets
                                                            .showAlertMessageBottomSheet(
                                                                appLocal.allHideAleartMessage,
                                                                appLocal.alert,
                                                                context);
                                                      } else {
                                                        BottomSheets
                                                            .showAlertMessageBottomSheet(
                                                                appLocal.hideSizeAlertMessgae,
                                                                appLocal.alert,
                                                                context,
                                                                onActionClick:
                                                                    () {
                                                          setState(() {
                                                            selectedSizes[
                                                                    index] =
                                                                SizeValueModel(
                                                                    selectedSizes[
                                                                            index]
                                                                        .value,
                                                                    true);
                                                          });
                                                        });
                                                      }
                                                    } else {
                                                      Alerts.alertView(
                                                        context: context,
                                                        content:
                                                            appLocal.deleteSizeAlertMessage,
                                                        action: () {
                                                          Get.back();
                                                          setState(() {
                                                            selectedSizes
                                                                .removeAt(
                                                                    index);
                                                          });
                                                        },
                                                        cancelAction: () =>
                                                            Get.back(),
                                                        cancelActionText:
                                                            appLocal.no,
                                                      );
                                                    }
                                                  } else {
                                                    Alerts.alertView(
                                                      context: context,
                                                      content:
                                                         appLocal.deleteSizeAlertMessage,
                                                      action: () {
                                                        Get.back();
                                                        setState(() {
                                                          selectedSizes
                                                              .removeAt(index);
                                                        });
                                                      },
                                                      cancelAction: () =>
                                                          Get.back(),
                                                      cancelActionText:
                                                          appLocal.no,
                                                    );
                                                  }
                                                },
                                                icon: Icon(
                                                  (previousSizeIndex == index &&
                                                          (controller
                                                                  .isApprovedProduct ||
                                                              controller
                                                                  .isMatchProduct))
                                                      ? Icons
                                                          .remove_red_eye_sharp
                                                      : Icons.cancel,
                                                  color: Colors.white
                                                      .withOpacity(0.42),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        if (previousSizeIndex != -1)
                                          InkWell(
                                            onTap: () {
                                              setState(() {
                                                selectedSizes[index] =
                                                    SizeValueModel(
                                                        selectedSizes[index]
                                                            .value,
                                                        false);
                                              });
                                            },
                                            child: AnimatedContainer(
                                              curve: Curves.slowMiddle,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                color: !selectedSizes[index]
                                                        .isValueHidden
                                                    ? Colors.transparent
                                                    : Colors.black
                                                        .withOpacity(0.6),
                                              ),
                                              width: (selectedSizes[index]
                                                      .isValueHidden)
                                                  ? itemWidth
                                                  : 0,
                                              height: itemHeight,
                                              duration:
                                                  const Duration(seconds: 1),
                                              child: (!selectedSizes[index]
                                                      .isValueHidden)
                                                  ? Container()
                                                  : const Icon(
                                                      Icons.visibility_off,
                                                      color: Colors.white,
                                                      size: 20,
                                                    ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  );
                                }),
                            const SizedBox(height: 24)
                          ],
                        ),
                      )
                    : Container(),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Obx(
        () => AnimatedContainer(
          height: bottomNavigationBarHeight.value,
          padding: const EdgeInsets.symmetric(horizontal: 30),
          duration: const Duration(milliseconds: 350),
          child: Center(
            child: ElbaabGradientButtonWidget(
              onPress: () {
                bool requriedUpdate = false;
                if (controller.size.value.sizes.length ==
                    selectedSizes.length) {
                  for (var i = 0; i < controller.size.value.sizes.length; i++) {
                    if (controller.size.value.sizes[i].value !=
                            selectedSizes[i].value ||
                        (controller.size.value.sizes[i].isValueHidden !=
                            selectedSizes[i].isValueHidden)) {
                      requriedUpdate = true;
                      break;
                    }
                  }
                } else {
                  requriedUpdate = true;
                }

                if (controller.product != null) {
                  if (controller
                          .product?.productOptions?.productSizes?.isNotEmpty ??
                      false) {
                    if (controller.size.value.sizes.isNotEmpty) {
                      if ((controller.validationHistory?.returnValues
                              ?.contains("Size unit 1") ??
                          false)) {
                        String sizeUnit = controller.product?.productOptions
                                ?.productSizes?[0].sizeUnit ??
                            '';
                        List<OptionValues> sizes = controller.product
                                ?.productOptions?.productSizes?[0].sizeValues ??
                            [];
                        if (sizeUnit == controller.size.value.sizeUnit) {
                          requriedUpdate = true;
                        } else {
                          for (var i = 0; i < sizes.length; i++) {
                            if (sizes[i].value ==
                                    controller.size.value.sizes[i].value &&
                                (sizes[i].isValueHidden ?? false) &&
                                controller.size.value.sizes[i].isValueHidden) {
                              requriedUpdate = true;
                            }
                          }
                        }
                      }
                    }
                  }
                }
                if (!requriedUpdate && controller.size.value.sizes.isNotEmpty) {
                  Get.back(result: false);
                } else {
                  if (selectedSizes.isEmpty) {
                    controller.size.value = SizeModel('', [], '');
                    Get.back(result: true);
                  } else {
                    SizeModel sizeModel = SizeModel(
                        strUnit.value, selectedSizes, strUnitAr.value);
                    controller.size.value = sizeModel;
                    Get.back(result: true);
                  }
                }
              },
              text: appLocal.done,
            ),
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    sizesModel.value = SizesModel.fromJson(response);
    if (sizesModel.value.status == statusOK) {
      for (Sizes sizeObj in sizesModel.value.sizes ?? []) {
        arrUints.add(sizeObj.sizeUnit ?? "");
        arrUintsAr.add(sizeObj.ar?.sizeUnit ?? "");
      }
      int index = 0;
      if (controller.size.value.sizes.isEmpty) {
        strUnit.value = SizesModel.fromJson(response).sizes?[0].sizeUnit ?? "";
        strUnitAr.value =
            SizesModel.fromJson(response).sizes?[0].ar?.sizeUnit ?? "";
      } else {
        index = (SizesModel.fromJson(response).sizes ?? [])
            .indexWhere((element) => element.sizeUnit == strUnit.value);
      }
      SizesModel sizeModel = SizesModel.fromJson(response);
      for (String element in sizeModel.sizes?[index].sizeValues ?? []) {
        arrSizes.add(SizeValueModel(element, false));
      }
    }
  }
}
