import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class InitiateTransferRequest extends StatelessWidget with InputValidationUtil {
  var availableAmount = Get.arguments[0];
  String requestAmount = '';
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  InitiateTransferRequest({super.key});
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar:  ElbaabHeader(
        title: appLocal.initiateTransferRequest,
        leadingBack: true,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            ElbaabFeildContainerWidget(
              borderWidth: 0,
              edgeInsets: const EdgeInsets.only(
                  left: kLeftSpace, right: kRightSpace, top: 23).r,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 25).r,
                child: Column(
                  children: [
                    Wrap(
                      children: <Widget>[
                        Text(
                          "$availableAmount",
                          style: FontStyles.fontMedium(fontSize: 32),
                        ),
                        Text(
                          appLocal.aed,
                          style: FontStyles.fontMedium(fontSize: 12),
                        ),
                      ],
                    ),
                    Text(
                      appLocal.totalAmount,
                      style: FontStyles.fontRegular(
                        color: Colors.white.withOpacity(0.5),
                      ),
                    ),
                     SizedBox(height: 15.h),
                    ElbaaabInputTextField(
                      onChanged: (v) => requestAmount = v,
                      hint: '0.00',
                      validator: (v) {
                        if (v!.isEmpty) {
                          return appLocal.pleaseEnterAmount;
                        }
                        if (double.parse(v) > availableAmount) {
                          return appLocal.amountLessThenAvailable;
                        }
                        return null;
                      },
                      autoTextDirection: true,
                      inputType:
                          const TextInputType.numberWithOptions(decimal: true),
                      inputFormatter: '[0-9.]',
                      label: appLocal.amount,
                    ),
                    const SizedBox(height: 20),
                    ElbaabButtonWidget(
                      onPress: () {
                        if (_formKey.currentState!.validate()) {
                          BottomSheets.initateTransferRequesBottomtSheet(
                                  context, double.parse(requestAmount))
                              .then((value) {
                            if (value) {
                              Get.back(result: true);
                            }
                          });
                        }
                      },
                      colors: AppColors.colorPrimary,
                      text: appLocal.transfer,
                      height: 40.h,
                      icon: const Icon(
                        Icons.chevron_right,
                        size: 20,
                      ),
                      iconOnRight: true,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
