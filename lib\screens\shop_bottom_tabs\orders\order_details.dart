import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/enums/enums.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/order_detail_components/buyer_info_card.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/order_detail_components/delivery_info_card.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/order_detail_components/order_info_card.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/order_detail_components/order_product_row.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';

class OrderDetail extends StatelessWidget {
  final OrderType orderType = Get.arguments[0];
  final OrderItems orderItems = Get.arguments[1];

  RxList<String> rejectItemId = RxList<String>([]);

  OrderDetail({super.key});

  @override
  Widget build(BuildContext context) {
    orderItems.items?.sort((a, b) {
      if ((a.orderItemProductStatus?.last.status ?? "") == 'Ordered' &&
          (b.orderItemProductStatus?.last.status ?? "") != 'Ordered') {
        return -1;
      } else if ((a.orderItemProductStatus?.last.status ?? "") != 'Ordered' &&
          (b.orderItemProductStatus?.last.status ?? "") == 'Ordered') {
        return 1;
      } else {
        return 0;
      }
    });
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.orderDetail,
        leadingBack: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              OrderInfoCard(orderType: orderType, orderItems: orderItems),
               SizedBox(height: 10.h),
              BuyerInfoCard(orderItems: orderItems),
              if (orderType != OrderType.newOrder ||
                  orderType != OrderType.confirmedOrder)
               SizedBox(height: 10.h),
              if (orderType == OrderType.deliveredOrder ||
                  orderType == OrderType.rejectedOrders ||
                  orderType == OrderType.shippedOrder ||
                  orderType == OrderType.rejectedOrders ||
                  orderItems.orderItemStatus?.last.status ==
                      "Accepted by courier" ||
                  orderType == OrderType.returnConfirmOrder)
                DeliveryInfoCard(orderItems: orderItems),
              if (orderType == OrderType.newOrder ||
                  orderType == OrderType.confirmedOrder)
                Obx(
                  () => rejectItemId.length == orderItems.items?.length
                      ? Container()
                      : ElbaabGradientButtonWidget(
                          edgeInsets: const EdgeInsets.all(16),
                          onPress: () async {
                            String pickupAddressId =
                                orderItems.pickupAddress?.id ?? "";
                            String readinessPeriode =
                                orderItems.readinessPeriode ?? "";
                            final result = await BottomSheets.allPickupAddress(
                                context,
                                (orderItems.id ?? ""),
                                orderType == OrderType.confirmedOrder,
                                pickupAddressId: pickupAddressId,
                                ros: readinessPeriode);
                            if (result != null) {
                              Get.back(result: result);
                            }
                          },
                          text: orderType == OrderType.confirmedOrder
                              ? "Update Pickup Details"
                              : orderItems.items?.length == 1
                                  ? "Confirm Order"
                                  : appLocal.confirmAll,
                        ),
                ),
              ListView.builder(
                  itemCount: orderItems.items?.length ?? 0,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    Items item = (orderItems.items ?? [])[index];
                    return OrderProductRow(
                      orderType: orderType,
                      item: item,
                      shipmentCost: orderItems.shipmentCost ?? 0,
                      orderItemId: orderItems.id ?? "",
                      onRejectCall: (String id) => rejectItemId.add(id),
                    );
                  }),
              if (orderType == OrderType.returnOrder)
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: ElbaabButtonWidget(
                        onPress: () {},
                        colors: AppColors.colorSecondary_Red,
                        text: appLocal.reject,
                        height: 40,
                        textStyle: FontStyles.fontRegular(),
                      ),
                    ),
                    const SizedBox(width: 15),
                    Expanded(
                      flex: 1,
                      child: ElbaabButtonWidget(
                        onPress: () {},
                        colors: AppColors.colorPrimary,
                        text: appLocal.acceptReturn,
                        height: 40,
                        textStyle: FontStyles.fontRegular(),
                      ),
                    ),
                  ],
                ),
              if (orderType == OrderType.returnConfirmOrder)
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: ElbaabButtonWidget(
                        onPress: () {},
                        colors: AppColors.colorSecondary,
                        text: appLocal.ok,
                        height: 40,
                        textStyle: FontStyles.fontRegular(),
                      ),
                    ),
                    const SizedBox(width: 15),
                    Expanded(
                      flex: 1,
                      child: ElbaabButtonWidget(
                        onPress: () {},
                        colors: AppColors.colorPrimary,
                        text: appLocal.updateRefundStatus,
                        height: 40,
                        textStyle: FontStyles.fontRegular(fontSize: 13),
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  reviewWidget(
      String title, String review, bool isReply, BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        children: [
          Row(
            children: [
              SizedBox(
                height: 24,
                width: 24,
                child: GlobalMethods.netWorkImage(
                  "",
                  BorderRadius.circular(12),
                  BoxFit.cover,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: FontStyles.fontBold(fontSize: 10),
                ),
              ),
              Text(
                "3hr ago",
                style: FontStyles.fontLight(
                  fontSize: 10,
                  color: Colors.white.withOpacity(0.5),
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(left: 25, top: 10),
            child: Row(
              children: [
                Icon(
                  Icons.star,
                  color: AppColors.colorRatingStar,
                  size: 12,
                ),
                const SizedBox(width: 2),
                Text(
                  "3.5",
                  style: FontStyles.fontMedium(fontSize: 10),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    review,
                    style: FontStyles.fontRegular(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.5),
                    ),
                  ),
                )
              ],
            ),
          ),
          Container(
            height: 1,
            margin: const EdgeInsets.only(left: 25),
            color: Colors.white.withOpacity(0.4),
          ),
          Row(
            children: [
              if (!isReply)
                TextButton(
                  onPressed: () {},
                  child: Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      SvgPicture.string(SvgStrings.iconReply),
                      const SizedBox(width: 4),
                      Text(
                        appLocal.reply,
                        style: FontStyles.fontRegular(
                          fontSize: 12,
                          color: AppColors.colorPrimary,
                        ),
                      )
                    ],
                  ),
                ),
              if (isReply)
                TextButton(
                  onPressed: () {},
                  child: Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      SvgPicture.string(SvgStrings.iconGreenEdit),
                      const SizedBox(width: 4),
                      Text(
                        appLocal.edit,
                        style: FontStyles.fontRegular(
                          fontSize: 12,
                          color: AppColors.colorSecondary,
                        ),
                      )
                    ],
                  ),
                ),
              const SizedBox(width: 24),
              if (isReply)
                TextButton(
                  onPressed: () {},
                  child: Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      SvgPicture.string(SvgStrings.iconDelete),
                      const SizedBox(width: 4),
                      Text(
                        appLocal.remove,
                        style: FontStyles.fontRegular(
                          fontSize: 12,
                          color: AppColors.colorSecondary_Red,
                        ),
                      )
                    ],
                  ),
                ),
            ],
          )
        ],
      ),
    );
  }
}
