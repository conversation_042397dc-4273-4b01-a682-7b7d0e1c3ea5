
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/enums/enums.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/product_info.dart';

class NewOrderRow extends StatelessWidget {
  final OrderItems orderItems;
  final Function(bool? value) onRefresh;
  const NewOrderRow(
      {Key? key, required this.orderItems, required this.onRefresh})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    orderItems.items?.sort((a, b) {
      if ((a.orderItemProductStatus?.last.status ?? "") == 'Ordered' &&
          (b.orderItemProductStatus?.last.status ?? "") != 'Ordered') {
        return -1;
      } else if ((a.orderItemProductStatus?.last.status ?? "") != 'Ordered' &&
          (b.orderItemProductStatus?.last.status ?? "") == 'Ordered') {
        return 1;
      } else {
        return 0;
      }
    });
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: AppColors.headerColorDark,
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          Get.toNamed(RouteNames.orderDetailScreen,
              arguments: [OrderType.newOrder, orderItems])?.then((value) {
            onRefresh(value);
          });
        },
        child: Stack(
          children: [
            Container(
              padding: const EdgeInsets.all(16).r,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            orderItems.orderItemCode ?? "",
                            style: FontStyles.fontMedium(fontSize: 16),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            "ordered at ${GlobalMethods.checkOrderToday(DateTime.parse(orderItems.createdAt ?? ""), "dd MMM, yyyy hh:mm a")}",
                            style: FontStyles.fontRegular(
                                fontSize: 12, color: Colors.white.withOpacity(0.6)),
                          ),
                        ],
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6).r,
                        decoration: BoxDecoration(
                          color: AppColors.colorPrimary,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          "NEW",
                          style: FontStyles.fontMedium(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                   SizedBox(height: 16.h),


                  // Products List
                  SizedBox(
                    height: 132.h,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: orderItems.items?.length ?? 0,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: ProductInfo(item: (orderItems.items ?? [])[index]),
                        );
                      },
                    ),
                  ),
                   SizedBox(height: 16.h),

                  // Footer
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.string(SvgStrings.iconPriceTag),
                           SizedBox(width: 8.w),
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: "Total: ",
                                  style: FontStyles.fontSemibold(fontSize: 14),
                                ),
                                TextSpan(
                                  text: "${orderItems.finalCost ?? 0}",
                                  style: FontStyles.fontBold(fontSize: 18),
                                ),
                                TextSpan(
                                  text: " AED",
                                  style: FontStyles.fontSemibold(fontSize: 14),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [AppColors.colorPrimary, AppColors.colorSecondary],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Wrap(
                          children: [
                            SizedBox(width: 10.w,),
                            Text(
                              EnStrings.details,
                              style: FontStyles.fontMedium(
                                fontSize: 14,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(width: 3.w,),
                          const  Icon(Icons.arrow_forward_ios, color: Colors.white,size: 14,),
                            SizedBox(width: 10.w,),
                          ],
                        ),
                      ),
                      
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

}
