import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/login_model.dart';
import 'package:overolasuppliers/provider/shop_info_provider.dart';
import 'package:overolasuppliers/provider/updated_info.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'constants.dart';
import 'package:shimmer/shimmer.dart';

class GlobalMethods {
  static Widget netWorkImage(String url, BorderRadius radius, BoxFit fit) {
    return ClipRRect(
      borderRadius: radius,
      child: Image.network(
        url,
        fit: fit,
        loadingBuilder: (BuildContext context, Widget child,
            ImageChunkEvent? loadingProgress) {
          if (loadingProgress == null) return child;
          return Shimmer.fromColors(
            baseColor: AppColors.backgroundColorDark,
            highlightColor: AppColors.colotMidBlack,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: radius,
              ),
            ),
          );
        },
        errorBuilder: (context, exception, stackTrace) {
          return Image.asset(
            'assets/images/no_image.png',
            fit: fit,
          );
        },
      ),
    );
  }

  static checkLoginStatus(LoginModel model,
      {String email = "", String password = ""}) {
    prefs.setString(authToken, model.token ?? "");

    prefs.setString(localAuthEmail, email);
    prefs.setString(localAuthPassword, password);
    if (model.status == statusOK) {
      userAuthToken = model.token ?? "";
      String lastUserId = prefs.getString(lastMerchantID) ?? "";
      if (lastUserId.isNotEmpty) {
        if (lastUserId != (model.user?.id ?? "")) {
          prefs.remove(productInformation);
          prefs.remove(productDetails);
          prefs.remove(productShipment);
          prefs.remove(productPolicies);
          prefs.remove(shopInfo);
          prefs.remove(shopPickUpAddress);
          prefs.remove(shopCustomerContact);
        }
      } else {
        prefs.setString(lastMerchantID, model.user?.id ?? "");
      }
      if (model.user?.isEmailVerified == false) {
        prefs.setBool(isListenNavidationStream, true);
        Get.offNamed(RouteNames.emailVerificationScreen,
            arguments: [model.user?.id, false, email]);
      } else if (model.user?.isPhoneVerified == false) {
        Get.offNamed(RouteNames.numberVerificationScreen,
            arguments: [model.user?.id]);
      } else if (model.user?.isPhoneVerified ?? false) {
        prefs.setString(userPhoneNumber, model.user?.userPhoneNumber ?? "");
        if (model.user?.supplier != null) {
          if (model.user?.supplier?.bussinessStatus == "Pending") {
            if (model.user?.supplier?.shopId != null) {
              if (model.user?.supplier?.shopId?.isCompleted ?? false) {
                disableFutureLoginCheck();
                Provider.of<UpdatedInfo>(MyApp.navigatorKey.currentContext!,
                        listen: false)
                    .setTabPosition(0);
                String supplierName =
                    model.user?.supplier?.bussinessOwnerName ?? "";
                supplierID = model.user?.id ?? "";
                Provider.of<ShopInfoProvider>(Get.context!, listen: false)
                    .setShopUserName(supplierName);
                supplierBusinessName =
                    model.user?.supplier?.bussinessName ?? "";
                prefs.setString(authToken, model.token ?? "");
                prefs.setString(merchantID, supplierID);
                Get.offNamed(RouteNames.shopHomeScreen);
              } else {
                Get.offNamed(RouteNames.createShopScreen, arguments: [
                  model.user?.supplier?.shopId,
                  false,
                  model.user?.userPhoneNumber ?? ""
                ]);
              }
            } else {
              Get.offNamed(RouteNames.signupSuccessScreen);
            }
          } else if (model.user?.supplier?.bussinessStatus == "Accepted") {
            supplierID = model.user?.id ?? "";
            String supplierName =
                model.user?.supplier?.bussinessOwnerName ?? "";

            Provider.of<ShopInfoProvider>(Get.context!, listen: false)
                .setShopUserName(supplierName);
            supplierBusinessName = model.user?.supplier?.bussinessName ?? "";
            prefs.setString(ownerName, supplierName);
            if (model.user?.supplier?.shopId != null &&
                (model.user?.supplier?.shopId?.isCompleted ?? false)) {
              disableFutureLoginCheck();
              Provider.of<UpdatedInfo>(MyApp.navigatorKey.currentContext!,
                      listen: false)
                  .setTabPosition(0);
              prefs.setString(authToken, model.token ?? "");
              prefs.setString(merchantID, supplierID);
              Get.offNamed(RouteNames.shopHomeScreen);
            } else {
              Get.offNamed(
                  model.user?.supplier?.shopId != null
                      ? RouteNames.createShopScreen
                      : RouteNames.createShopFirstStepScreen,
                  arguments: [
                    model.user?.supplier?.shopId,
                    false,
                    model.user?.userPhoneNumber ?? ""
                  ]);
            }
          } else if (model.user?.supplier?.bussinessStatus == "Returned") {
            if (model.user?.supplier?.shopId != null) {
              if (model.user?.supplier?.shopId?.isCompleted ?? false) {
                supplierID = model.user?.id ?? "";
                String supplierName =
                    model.user?.supplier?.bussinessOwnerName ?? "";

                Provider.of<ShopInfoProvider>(Get.context!, listen: false)
                    .setShopUserName(supplierName);
                supplierBusinessName =
                    model.user?.supplier?.bussinessName ?? "";
                prefs.setString(ownerName, supplierName);
                disableFutureLoginCheck();
                Provider.of<UpdatedInfo>(MyApp.navigatorKey.currentContext!,
                        listen: false)
                    .setTabPosition(0);
                prefs.setString(authToken, model.token ?? "");
                prefs.setString(merchantID, supplierID);
                Get.offNamed(RouteNames.shopHomeScreen);
              }
            } else {
              Get.offNamed(RouteNames.addBusinessInfoScreen,
                  arguments: [model.user?.id, model.user?.supplier, false]);
            }
          }
        } else {
          Get.offNamed(RouteNames.addBusinessInfoScreen,
              arguments: [model.user?.id, model.user?.supplier, false]);
        }
      }
    } else if (model.status == status400) {
      disableFutureLoginCheck();
      Get.offNamed(RouteNames.loginScreen);
    }
  }

  static enableFutureLoginCheck(
      {required String email, required String password}) {
    prefs.setBool(requiredLogin, true);
    prefs.setString(signupEmail, email);
    prefs.setString(signupPassword, password);
  }

  static disableFutureLoginCheck() {
    prefs.remove(requiredLogin);
    prefs.remove(signupEmail);
    prefs.remove(signupPassword);
  }

  static logout() {
    String isRemember = prefs.getString(rememberMe) ?? "false";
    prefs.remove(merchantID);
    prefs.remove(ownerName);
    prefs.remove(authToken);
    if (isRemember == "false") {
      prefs.remove(localAuthEmail);
      prefs.remove(localAuthPassword);
    }
    prefs.remove(localAuthEnable);
    GlobalMethods.disableFutureLoginCheck();
    userAuthToken = "";
    supplierID = "";
    Get.offAllNamed(RouteNames.loginScreen);
  }

  static Future<void> launchInWebView(String url, {bool inApp = false}) async {
    if (await launchUrl(
      Uri.parse(url),
      mode: url.endsWith("pdf")
          ? inApp
              ? LaunchMode.inAppWebView
              : LaunchMode.externalApplication
          : LaunchMode.inAppWebView,
      webViewConfiguration: const WebViewConfiguration(enableDomStorage: false),
    )) {
      throw 'Could not launch $url';
    }
  }

  static String getGreetingMsg() {
    final now = DateTime.now();
    final hrs = now.hour;
    final mins = now.minute;
    String msg = "";
    if (hrs >= 0 && hrs < 12) {
      // msg = 'Morning! Ready to boost your sales?';
      msg = "morning";
    } else if (hrs == 12 && mins == 0) {
      // msg = 'Noon! Keep the orders rolling in!';
      msg = "noon";
    } else if (hrs >= 12 && hrs < 17) {
      // msg = 'Afternoon! Push for more sales!';
      msg = "afternoon";
    } else if (hrs >= 17 && hrs < 24) {
      // msg = 'Evening! Review today's progress.';
      msg = "evening";
    } else {
      // msg = 'Time issue! Check your clock!';
      msg = "issue";
    }
    return msg;
  }

  static String convertTimeStamp(int timeStamp, {String? format}) {
    DateTime tsdate = DateTime.fromMillisecondsSinceEpoch(timeStamp);
    if (format != null) {
      return DateFormat(format).format(tsdate);
    } else {
      return "${tsdate.year}-${tsdate.month}-${tsdate.day}";
    }
  }

  static String convertTimeFormate(String date, {String? format}) {
    
    DateTime utc = DateTime.parse(date);
    if (format != null) {
      String local  = Get.locale?.languageCode ?? "en";
      if(local == 'ar'){
      return DateFormat(format,'ar').format(utc.toLocal());
      }else{
      return DateFormat(format,).format(utc.toLocal());

      }
    } else {
      return "${utc.toLocal().year}-${utc.toLocal().month}-${utc.toLocal().day}";
    }
  }

  static String convertTimeStampToTimeAgo(int timeStamp) {
    DateTime tsdate = DateTime.fromMillisecondsSinceEpoch(timeStamp);
    return "${timeAgo(tsdate)} ${DateFormat("dd/MM/yyyy").format(tsdate)}";
  }

  static String convertNotificationTimeStampToTimeAgo(String date) {
    DateTime tsdate = DateTime.parse(date);
    return timeAgo(tsdate);
  }

  static deleteFilesFromS3Bucket(
      {required List<String> files, required GraphQlInitilize request}) {
    request.runMutation(
        context: Get.context!,
        query: GraphQlQuries.deleteFiles,
        type: "deleteFiles",
        variables: {"fileList": files});
  }


  static String checkOrderToday(DateTime d, String format) {
    Duration diff = DateTime.now().toLocal().difference(d);
    DateTime now = DateTime.now().toLocal();
    String lang = Get.locale!.languageCode;

    if (diff.inHours > 0 && d.day < now.day) {
      return DateFormat(format).format(d);
    }
    if (diff.inHours > 0) {
      return lang == "en" ?"Today ${DateFormat("hh:mm a").format(d)}":"اليوم ${DateFormat("hh:mm a").format(d)}";
    }
    if (diff.inMinutes > 0) {
      return lang == "en" ?"Today ${DateFormat("hh:mm a").format(d)}":"اليوم ${DateFormat("hh:mm a").format(d)}";
    } 
    return lang == "en" ? "just now":"الآن";
  }

  static String timeAgo(DateTime d) {
    Duration diff = DateTime.now().toLocal().difference(d);
    DateTime now = DateTime.now().toLocal();
    print("local ${Get.locale}");
    String lang = Get.locale!.languageCode;

    if (diff.inDays > 365) {
      return DateFormat("dd, MMM yyyy").format(d);
    }
    if (diff.inDays > 30) {
      return DateFormat("dd, MMM yyyy hh:mm a").format(d);
    }
    if (diff.inDays > 7) {
      return DateFormat("EEE dd, MMM yy hh:mm a").format(d);
    }
    if (diff.inDays > 0) {
      return "${diff.inDays} ${diff.inDays == 1 ? lang == "en" ? "day":"اليوم" :lang == "en" ? "days":"أيام"} ${lang == "en" ? "ago":"منذ"}";
    }
    if (diff.inHours > 0 && d.day < now.day) {
      return  lang == "en" ?"Yesterday":"أمس";
    }
    if (diff.inHours > 0) {
      return lang == "en" ?"Today":"اليوم";
    }
    if (diff.inMinutes > 0) {
      return lang == "en" ?"Today":"اليوم";
    }
    return lang == "en" ? "just now":"الآن";
  }

  static String ordinal(int number) {
    if (number < 1) {
      throw Exception('Invalid number');
    }

    int remainder = number % 10;
    int quotient = number ~/ 10;

    if (quotient == 1 && remainder != 1) {
      return 'th';
    }

    switch (remainder) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }

  static Future<void> openMap(double latitude, double longitude) async {
    String googleUrl =
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
    if (await launchUrl(Uri.parse(googleUrl))) {
      throw 'Could not launch $googleUrl';
    }
  }
}

extension HexColor on Color {
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  /// Prefixes a hash sign if [leadingHashSign] is set to `true` (default is `true`).
  String toHex({bool leadingHashSign = true}) => '${leadingHashSign ? '#' : ''}'
      '${alpha.toRadixString(16).padLeft(2, '0')}'
      '${red.toRadixString(16).padLeft(2, '0')}'
      '${green.toRadixString(16).padLeft(2, '0')}'
      '${blue.toRadixString(16).padLeft(2, '0')}';
}
