import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_streams.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

class SelectCategory extends GetView<AddProductController> {
  RxInt categoryIndex = 0.obs, subCategoryIndex = 0.obs;
  final stream = GraphqlStreams();

  SelectCategory({super.key});

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    var size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: AppColors.backgroundColorDark,
      appBar: ElbaabHeader(
        title: appLocal.selectCategory,
        leadingWidget: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            size: 20,
            color: Colors.white.withOpacity(0.86),
          ),
          onPressed: () => Get.back(result: '1'),
        ),
      ),
      body: Obx(() {
        if (controller.cachedCategories.value == null) {
          controller.loadCategories();
          return Center(
            child: SpinKitSpinningLines(
              color: AppColors.colorPrimary,
              size: 50.0,
            ),
          );
        }

        final data = controller.cachedCategories.value!;
        
        // Handle initial category/subcategory selection
        if (data.categories?.isNotEmpty ?? false) {
          if (controller.categoryId.isNotEmpty) {
            for (int i = 0; i < data.categories!.length; i++) {
              if ((data.categories?[i].categoryName ?? "") ==
                  controller.category.value) {
                categoryIndex.value = i;
                break;
              }
            }
          }
          if (controller.subCategoryId.isNotEmpty) {
            for (int i = 0;
                i < data.categories![categoryIndex.value].subCategories!.length;
                i++) {
              if ((data.categories?[categoryIndex.value]
                          .subCategories?[i].subCategoryName ??
                      "") ==
                  controller.subCategory.value) {
                subCategoryIndex.value = i;
                break;
              }
            }
          }
        }

        return Row(
          children: <Widget>[
            Container(
              width: 85,
              decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    color: Colors.white.withOpacity(0.1),
                    width: 1,
                  ),
                ),
              ),
              child: ListView.builder(
                itemCount: data.categories?.length,
                itemBuilder: (context, index) {
                  var category = data.categories?[index];
                  return Obx(
                    () => InkWell(
                      onTap: () {
                        categoryIndex.value = index;
                        subCategoryIndex.value = 0;
                        controller.category.value = data.categories?[index].categoryName ?? "";
                        controller.categoryId.value = data.categories?[index].id ?? "";
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: categoryIndex.value == index
                              ? AppColors.colorPrimary.withOpacity(0.15)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: <Widget>[
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: categoryIndex.value == index
                                    ? Colors.white.withOpacity(0.1)
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: SizedBox(
                                height: 32,
                                width: 32,
                                child: GlobalMethods.netWorkImage(
                                  category?.categoryDarkIcon ?? "",
                                  BorderRadius.circular(6),
                                  BoxFit.contain,
                                ),
                              ),
                            ),
                            const SizedBox(height: 6),
                            Text(
                              appLocal.localeName == 'en'
                                  ? category?.categoryName ?? ""
                                  : category?.arCategories?.categoryName ?? "",
                              textAlign: TextAlign.center,
                              style: FontStyles.fontRegular(
                                fontSize: 11,
                                color: categoryIndex.value == index
                                    ? AppColors.colorPrimary
                                    : Colors.white.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.03),
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.white.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Text(
                      appLocal.selectSubCategroy,
                      style: FontStyles.fontRegular(
                        fontSize: 13,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ),
                  Expanded(
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      switchInCurve: Curves.easeInOut,
                      switchOutCurve: Curves.easeInOut,
                      transitionBuilder: (Widget child, Animation<double> animation) {
                        return FadeTransition(
                          opacity: animation,
                          child: SlideTransition(
                            position: Tween<Offset>(
                              begin: const Offset(0.05, 0),
                              end: Offset.zero,
                            ).animate(animation),
                            child: child,
                          ),
                        );
                      },
                      child: AnimationLimiter(
                        key: ValueKey<int>(categoryIndex.value), // Important for animation trigger
                        child: GridView.builder(
                          padding: const EdgeInsets.all(12),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            mainAxisSpacing: 12,
                            crossAxisSpacing: 12,
                            childAspectRatio: 0.9,
                          ),
                          itemCount: data.categories?[categoryIndex.value].subCategories?.length,
                          itemBuilder: (context, index) {
                            var subCategory = data.categories?[categoryIndex.value].subCategories?[index];
                            return AnimationConfiguration.staggeredGrid(
                              position: index,
                              duration: const Duration(milliseconds: 375),
                              columnCount: 2,
                              child: SlideAnimation(
                                verticalOffset: 50.0,
                                child: FadeInAnimation(
                                  child: Obx(
                                    () => InkWell(
                                      onTap: () => subCategoryIndex.value = index,
                                      child: TweenAnimationBuilder(
                                        duration: const Duration(milliseconds: 200),
                                        curve: Curves.easeInOut,
                                        tween: Tween<double>(
                                          begin: 1.0,
                                          end: subCategoryIndex.value == index ? 1.05 : 1.0,
                                        ),
                                        builder: (context, double scale, child) {
                                          return Transform.scale(
                                            scale: scale,
                                            child: AnimatedContainer(
                                              duration: const Duration(milliseconds: 200),
                                              decoration: BoxDecoration(
                                                color: subCategoryIndex.value == index
                                                    ? AppColors.colorPrimary.withOpacity(0.1)
                                                    : Colors.white.withOpacity(0.03),
                                                borderRadius: BorderRadius.circular(12),
                                                border: Border.all(
                                                  color: subCategoryIndex.value == index
                                                      ? AppColors.colorPrimary
                                                      : Colors.white.withOpacity(0.1),
                                                  width: 1,
                                                ),
                                                boxShadow: subCategoryIndex.value == index
                                                    ? [
                                                        BoxShadow(
                                                          color: AppColors.colorPrimary.withOpacity(0.2),
                                                          blurRadius: 8,
                                                          spreadRadius: 0,
                                                        )
                                                      ]
                                                    : null,
                                              ),
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: <Widget>[
                                                  Container(
                                                    padding: const EdgeInsets.all(12),
                                                    decoration: BoxDecoration(
                                                      color: subCategoryIndex.value == index
                                                          ? Colors.white.withOpacity(0.05)
                                                          : Colors.transparent,
                                                      borderRadius: BorderRadius.circular(10),
                                                    ),
                                                    child: SizedBox(
                                                      width: 50,
                                                      height: 50,
                                                      child: GlobalMethods.netWorkImage(
                                                        subCategory?.subCategoryDarkIcon ?? '',
                                                        BorderRadius.circular(8),
                                                        BoxFit.contain,
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(height: 12),
                                                  Padding(
                                                    padding: const EdgeInsets.symmetric(horizontal: 12),
                                                    child: Text(
                                                      appLocal.localeName == 'en'
                                                          ? subCategory?.subCategoryName ?? ""
                                                          : subCategory?.arSubCategories?.subCategoryName ?? "",
                                                      maxLines: 2,
                                                      overflow: TextOverflow.ellipsis,
                                                      style: FontStyles.fontRegular(
                                                        fontSize: 12,
                                                        color: subCategoryIndex.value == index
                                                            ? AppColors.colorPrimary
                                                            : Colors.white.withOpacity(0.9),
                                                      ),
                                                      textAlign: TextAlign.center,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                  ElbaabGradientButtonWidget(
                    edgeInsets: const EdgeInsets.all(12),
                    onPress: () {
                      Get.back(
                          result: jsonEncode([
                        data.categories?[categoryIndex.value].categoryName,
                        data
                            .categories?[categoryIndex.value]
                            .subCategories?[subCategoryIndex.value]
                            .subCategoryName,
                        data.categories?[categoryIndex.value]
                            .categoryDarkIcon,
                        data
                            .categories?[categoryIndex.value]
                            .subCategories?[subCategoryIndex.value]
                            .subCategoryDarkIcon,
                        data.categories?[categoryIndex.value].brands,
                        data.categories?[categoryIndex.value].id,
                        data
                            .categories?[categoryIndex.value]
                            .subCategories?[subCategoryIndex.value]
                            .id,
                        data.categories?[categoryIndex.value].arCategories
                                ?.categoryName ??
                            '',
                        data
                                .categories?[categoryIndex.value]
                                .subCategories?[subCategoryIndex.value]
                                .arSubCategories
                                ?.subCategoryName ??
                            "",
                      ]));
                    },
                    text: appLocal.add,
                  ),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }
}
