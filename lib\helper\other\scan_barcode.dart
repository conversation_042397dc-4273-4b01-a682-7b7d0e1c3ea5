import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:scan/scan.dart';

class ScanBarCode extends StatelessWidget {
 final ScanController controller = ScanController();

  ScanBarCode({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: <PERSON><PERSON><PERSON><PERSON><PERSON>(
              controller: controller,
              scanAreaScale: .6,
              scanLineColor: AppColors.colorPrimary,
              onCapture: (data) => {controller.pause(), Get.back(result: data)},
            ),
          ),
          Positioned(
            right: 0,
            top: 0,
            child: Safe<PERSON>rea(
              child: Icon<PERSON><PERSON>on(
                icon: const Icon(Icons.close),
                onPressed: () {
                  controller.pause();
                  Get.back();
                },
              ),
            ),
          )
        ],
      ),
    );
  }
}
