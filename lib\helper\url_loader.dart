import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_share/flutter_share.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';

class UrlLoader extends StatefulWidget {
  final String url;
  const UrlLoader({Key? key, required this.url}) : super(key: key);

  @override
  State<UrlLoader> createState() => _UrlLoaderState();
}

class _UrlLoaderState extends State<UrlLoader> {
  String get url => widget.url;

  @override
  void initState() {
    super.initState();
    // if (Platform.isAndroid) {
    //   WebView.platform = SurfaceAndroidWebView();
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Bill',
          style: FontStyles.fontSemibold(fontSize: 16),
        ),
      ),
      // body: WebView(
      //   initialUrl: url,
      //   javascriptMode: JavascriptMode.unrestricted,
      //   onPageStarted: (url) => PopupLoader.showLoadingDialog(context),
      //   onPageFinished: (url) => PopupLoader.hideLoadingDialog(),
      //   gestureNavigationEnabled: true,
      //   backgroundColor: const Color(0x00000000),
      // ),
      bottomNavigationBar: SafeArea(
          child: SizedBox(
        height: 40,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned(
              right: 16,
              width: 124,
              child: ElbaabButtonWidget(
                colors: AppColors.colorPrimary,
                onPress: () async {
                  File file = await DefaultCacheManager().getSingleFile(url);
                  await FlutterShare.shareFile(
                      title: 'Bill',
                      filePath: file.path,
                      fileType: 'image/png');
                },
                text: "Share",
                height: 32,
              ),
            ),
          ],
        ),
      )),
    );
  }
}
