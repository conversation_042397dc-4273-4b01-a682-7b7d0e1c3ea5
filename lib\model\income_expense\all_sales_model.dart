class SalesModel {
  PaginatedSalesModel? sales;

  int? status;
  String? message;

  SalesModel({
    this.sales,
    this.status,
    this.message,
  });

  factory SalesModel.fromJson(Map<String, dynamic> json) {
    return SalesModel(
      sales: json['sales'] != null
          ? PaginatedSalesModel.fromJson(json['sales'])
          : null,
      status: json['status'],
      message: json['message'],
    );
  }
}

class PaginatedSalesModel {
  List<Sales>? items;
  bool? hasNextPage;
  int? totalPages;
  int? totalItems;
  int? page;

  PaginatedSalesModel({
    this.items,
    this.hasNextPage,
    this.totalPages,
    this.totalItems,
    this.page,
  });

  factory PaginatedSalesModel.fromJson(Map<String, dynamic> json) {
    return PaginatedSalesModel(
      items: json['items'] != null
          ? (json['items'] as List).map((e) => Sales.fromJson(e)).toList()
          : null,
      hasNextPage: json['hasNextPage'],
      totalPages: json['totalPages'],
      totalItems: json['totalItems'],
      page: json['page'],
    );
  }
}

class Sales {
  String? shipmentId;
  dynamic supplierPay;
  dynamic totalItemsCost;
  String? status;
  String? date;

  Sales({
    this.shipmentId,
    this.supplierPay,
    this.totalItemsCost,
    this.status,
    this.date,
  });

  factory Sales.fromJson(Map<String, dynamic> json) {
    return Sales(
        shipmentId: json['shipmentId'],
        supplierPay: json['supplierPay'],
        totalItemsCost: json['totalItemsCost'],
        status: json['status'],
        date: json['date']);
  }
}
