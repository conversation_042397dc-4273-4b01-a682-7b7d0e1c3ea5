import 'package:overolasuppliers/model/add_product/view_prduct/product_detail_model.dart';

class ShopProductList {
  final String category;
  final List<ShopProducts> products;

  ShopProductList(this.category, this.products);
}

class ShopProductsList {
  final String category;
  final ShopProductPaggination? products;
  final PaginatedVariantType? variants;

  ShopProductsList(this.category, {this.products, this.variants});
}

class ProductByShopModel {
  final String? typename;
  final int? status;
  final String? message;
  final ShopProductPaggination? shopProductPaggination;
  final PaginatedVariantType? variants;
  final List<AvailableCategories>? availableCategories;

  ProductByShopModel({
    this.typename,
    this.status,
    this.message,
    this.variants,
    this.shopProductPaggination,
    this.availableCategories,
  });

  ProductByShopModel.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        status = json['status'] as int?,
        message = json['message'] as String?,
        availableCategories = (json['availableCategories'] as List?)
            ?.map((dynamic e) =>
                AvailableCategories.fromJson(e as Map<String, dynamic>))
            .toList(),
        variants = (json['variants'] as Map<String, dynamic>?) != null
            ? PaginatedVariantType.fromJson(
                json['variants'] as Map<String, dynamic>)
            : null,
        shopProductPaggination =
            (json['products'] as Map<String, dynamic>?) != null
                ? ShopProductPaggination.fromJson(
                    json['products'] as Map<String, dynamic>)
                : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'status': status,
        'message': message,
        'variants': variants?.toJson(),
        'products': shopProductPaggination?.toJson()
      };
}

class PaginatedVariantType {
  final String? typename;
  final int? page;
  final int? totalPages;
  final int? totalItems;
  final bool? hasNextPage;
  List<ProductVariant>? items;

  PaginatedVariantType({
    this.typename,
    this.page,
    this.totalPages,
    this.totalItems,
    this.hasNextPage,
    this.items,
  });

  PaginatedVariantType.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        page = json['page'] as int?,
        totalPages = json['totalPages'] as int?,
        totalItems = json['totalItems'] as int?,
        hasNextPage = json['hasNextPage'] as bool?,
        items = (json['items'] as List?)
            ?.map((dynamic e) =>
                ProductVariant.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'page': page,
        'totalPages': totalPages,
        'totalItems': totalItems,
        'hasNextPage': hasNextPage,
        'items': items?.map((e) => e.toJson()).toList()
      };
}
// asd

class ProductVariant {
  final String? typename;
  final String? id;
  final VariantAttributes? variantAttributes;
  final String? variantEIN;
  final int? variantPrice;
  final String? variantName;
  final List<String>? variantImages;
  final ProductType? productId;

  ProductVariant({
    this.typename,
    this.id,
    this.variantAttributes,
    this.variantEIN,
    this.variantPrice,
    this.variantName,
    this.variantImages,
    this.productId,
  });

  ProductVariant.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        id = json['_id'] as String?,
        variantAttributes =
            (json['variantAttributes'] as Map<String, dynamic>?) != null
                ? VariantAttributes.fromJson(
                    json['variantAttributes'] as Map<String, dynamic>)
                : null,
        variantEIN = json['variantEIN'] as String?,
        variantPrice = json['variantPrice'] as int?,
        variantName = json['variantName'] as String?,
        variantImages =
            (json['variantImages'] as List?)?.map((e) => e as String).toList(),
        productId = (json['productId'] as Map<String, dynamic>?) != null
            ? ProductType.fromJson(json['productId'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        '_id': id,
        'variantAttributes': variantAttributes?.toJson(),
        'variantEIN': variantEIN,
        'variantPrice': variantPrice,
        'variantName': variantName,
        'variantImages': variantImages,
        'productId': productId?.toJson()
      };
}

// //asd
// class ProductVariant {
//   final String? typename;
//   final String? id;
//   final int? variantPrice;
//   final String? variantName;
//   final List<String>? variantImages;
//   final ProductType? productId;

//   ProductVariant({
//     this.typename,
//     this.id,
//     this.variantPrice,
//     this.variantName,
//     this.variantImages,
//     this.productId,
//   });

//   ProductVariant.fromJson(Map<String, dynamic> json)
//       : typename = json['__typename'] as String?,
//         id = json['_id'] as String?,
//         variantPrice = json['variantPrice'] as int?,
//         variantName = json['variantName'] as String?,
//         variantImages =
//             (json['variantImages'] as List?)?.map((e) => e as String).toList(),
//         productId = (json['productId'] as Map<String, dynamic>?) != null
//             ? ProductType.fromJson(json['productId'] as Map<String, dynamic>)
//             : null;

//   Map<String, dynamic> toJson() => {
//         '__typename': typename,
//         '_id': id,
//         'variantPrice': variantPrice,
//         'variantName': variantName,
//         'variantImages': variantImages,
//         'productId': productId?.toJson()
//       };
// }

class ProductType {
  final String? typename;
  final String? id;
  final String? productStatus;
  final bool? isHidden;
  final bool? isFreeDeliveryItem;
  final BrandType? productBrand;
  final SubCategoryType? productSubCategory;

  ProductType({
    this.typename,
    this.id,
    this.productStatus,
    this.isHidden,
    this.isFreeDeliveryItem,
    this.productBrand,
    this.productSubCategory,
  });

  ProductType.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        id = json['_id'] as String?,
        productStatus = json['productStatus'] as String?,
        isHidden = json['isHidden'] as bool?,
        isFreeDeliveryItem = json['isFreeDeliveryItem'] as bool?,
        productBrand = (json['productBrand'] as Map<String, dynamic>?) != null
            ? BrandType.fromJson(json['productBrand'] as Map<String, dynamic>)
            : null,
        productSubCategory =
            (json['productSubCategory'] as Map<String, dynamic>?) != null
                ? SubCategoryType.fromJson(
                    json['productSubCategory'] as Map<String, dynamic>)
                : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        '_id': id,
        'productStatus': productStatus,
        'isHidden': isHidden,
        'isFreeDeliveryItem': isFreeDeliveryItem,
        'productBrand': productBrand?.toJson(),
        'productSubCategory': productSubCategory?.toJson()
      };
}

class BrandType {
  final String? typename;
  final String? brandName;

  BrandType({
    this.typename,
    this.brandName,
  });

  BrandType.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        brandName = json['brandName'] as String?;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'brandName': brandName};
}

class SubCategoryType {
  final String? typename;
  final String? subCategoryName;

  SubCategoryType({
    this.typename,
    this.subCategoryName,
  });

  SubCategoryType.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        subCategoryName = json['subCategoryName'] as String?;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'subCategoryName': subCategoryName};
}

class ShopProductPaggination {
  final String? typename;
  List<ShopProducts>? items;
  final int? page;
  final bool? hasNextPage;
  final int? totalPages;
  final int? totalItems;

  ShopProductPaggination({
    this.typename,
    this.items,
    this.page,
    this.hasNextPage,
    this.totalPages,
    this.totalItems,
  });

  ShopProductPaggination.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        items = (json['items'] as List?)
            ?.map(
                (dynamic e) => ShopProducts.fromJson(e as Map<String, dynamic>))
            .toList(),
        page = json['page'] as int?,
        hasNextPage = json['hasNextPage'] as bool?,
        totalPages = json['totalPages'] as int?,
        totalItems = json['totalItems'] as int?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'items': items?.map((e) => e.toJson()).toList(),
        'page': page,
        'hasNextPage': hasNextPage,
        'totalItems': totalItems,
        'totalPages': totalPages
      };
}

class ShopProducts {
  final String? typename;
  final String? id;
  final String? productStatus;
  final bool? isFreeDeliveryItem;
  final List<ValidationHistory>? validationHistory;
  final bool? isHidden;
  final String? productName;
  final ProductBrand? productBrand;
  final dynamic productPrice;
  final List<dynamic>? productImages;
  final SubCategory? productSubCategory;
  final ProductOptions? productOptions;
  final ShopProducts? shopProductAr;

  ShopProducts({
    this.typename,
    this.productName,
    this.productStatus,
    this.isHidden,
    this.isFreeDeliveryItem,
    this.id,
    this.validationHistory,
    this.productBrand,
    this.productPrice,
    this.productImages,
    this.shopProductAr,
    this.productSubCategory,
    this.productOptions,
  });

  ShopProducts.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        productName = json['productName'] as String?,
        productStatus = json['productStatus'] as String?,
        isFreeDeliveryItem = json['isFreeDeliveryItem'] as bool?,
        isHidden = json['isHidden'] as bool?,
        id = json['_id'] as String?,
        productBrand = (json['productBrand'] as Map<String, dynamic>?) != null
            ? ProductBrand.fromJson(
                json['productBrand'] as Map<String, dynamic>)
            : null,
        shopProductAr = (json['ar'] as Map<String, dynamic>?) != null
            ? ShopProducts.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        productPrice = json['productPrice'],
        productImages = json['productImages'] as List?,
        productSubCategory =
            (json['productSubCategory'] as Map<String, dynamic>?) != null
                ? SubCategory.fromJson(
                    json['productSubCategory'] as Map<String, dynamic>)
                : null,
        validationHistory = (json['validationHistory'] as List?)
            ?.map((dynamic e) =>
                ValidationHistory.fromJson(e as Map<String, dynamic>))
            .toList(),
        productOptions =
            (json['productOptions'] as Map<String, dynamic>?) != null
                ? ProductOptions.fromJson(
                    json['productOptions'] as Map<String, dynamic>)
                : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'id': id,
        'productName': productName,
        'isHidden': isHidden,
        'productStatus': productStatus,
        'ar': shopProductAr?.toJson(),
        'isFreeDeliveryItem': isFreeDeliveryItem,
        'productBrand': productBrand?.toJson(),
        'validationHistory': validationHistory?.map((e) => e.toJson()).toList(),
        'productPrice': productPrice,
        'productImages': productImages,
        'productSubCategory': productSubCategory?.toJson(),
        'productOptions': productOptions?.toJson()
      };
}

class ProductBrand {
  final String? typename;
  final String? brandName;
  final ProductBrand? productBrandAr;

  ProductBrand({
    this.typename,
    this.brandName,
    this.productBrandAr,
  });

  ProductBrand.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        productBrandAr = (json['ar'] as Map<String, dynamic>?) != null
            ? ProductBrand.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        brandName = json['brandName'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'brandName': brandName,
        'ar': productBrandAr?.toJson()
      };
}

class SubCategory {
  final String? id;
  final String? subCategoryName;
  final int? productCount;
  final SubCategory? subCategoryAr;

  SubCategory(
      {this.id, this.subCategoryName, this.productCount, this.subCategoryAr});

  SubCategory.fromJson(Map<String, dynamic> json)
      : id = json['_id'] as String?,
        subCategoryName = json['subCategoryName'] as String?,
        subCategoryAr = (json['ar'] as Map<String, dynamic>?) != null
            ? SubCategory.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        productCount = json['productCount'] as int?;

  Map<String, dynamic> toJson() => {
        '_id': id,
        'subCategoryName': subCategoryName,
        'ar': subCategoryAr?.toJson(),
        'productCount': productCount
      };
}

class ProductOptions {
  final String? typename;
  final List<ShopProductColors>? productColors;

  ProductOptions({
    this.typename,
    this.productColors,
  });
  ProductOptions.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        productColors = (json['productColors'] as List?)
            ?.map((dynamic e) =>
                ShopProductColors.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'productColors': productColors?.map((e) => e.toJson()).toList()
      };
}

class ShopProductColors {
  final String? typename;
  final String? colorIcon;
  final List<String>? colorImages;

  ShopProductColors({
    this.typename,
    this.colorIcon,
    this.colorImages,
  });

  ShopProductColors.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        colorIcon = json['colorIcon'] as String?,
        colorImages = (json['colorImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'colorIcon': colorIcon,
        'colorImages': colorImages
      };
}

class ProductCategories {
  String? id;
  String? categoryName;
  ProductCategories? productCategoriesAr;

  ProductCategories({
    this.id,
    this.productCategoriesAr,
    this.categoryName,
  });
  ProductCategories.fromJson(Map<String, dynamic> json) {
    id = json['_id']?.toString();
    productCategoriesAr = (json['ar'] as Map<String, dynamic>?) != null
        ? ProductCategories.fromJson(json['ar'] as Map<String, dynamic>)
        : null;
    categoryName = json['categoryName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['_id'] = id;
    data['ar'] = productCategoriesAr?.toJson();
    data['categoryName'] = categoryName;
    return data;
  }
}

class AvailableCategories {
  ProductCategories? productCategory;
  List<SubCategory>? productSubCategory;

  AvailableCategories({
    this.productCategory,
    this.productSubCategory,
  });
  AvailableCategories.fromJson(Map<String, dynamic> json) {
    productCategory = (json['productCategory'] != null)
        ? ProductCategories.fromJson(json['productCategory'])
        : null;
    if (json['productSubCategory'] != null) {
      final v = json['productSubCategory'];
      final arr0 = <SubCategory>[];
      v.forEach((v) {
        arr0.add(SubCategory.fromJson(v));
      });
      productSubCategory = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (productCategory != null) {
      data['productCategory'] = productCategory!.toJson();
    }
    if (productSubCategory != null) {
      final v = productSubCategory;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v.toJson());
      }
      data['productSubCategory'] = arr0;
    }
    return data;
  }
}
