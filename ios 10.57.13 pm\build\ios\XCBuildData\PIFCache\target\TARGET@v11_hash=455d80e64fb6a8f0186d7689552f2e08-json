{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98775ccb7b83eb42877e624a1ac779cb36", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_core/firebase_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/firebase_core/firebase_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_core/firebase_core.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "firebase_core", "PRODUCT_NAME": "firebase_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985ebd7ad2d4433dbc02e406bb5e8b2433", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b57e640cf54e1cdb19a5b242ee66269", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_core/firebase_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/firebase_core/firebase_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_core/firebase_core.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "firebase_core", "PRODUCT_NAME": "firebase_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802e0c519db3bbc5ca55f2b2fbde1d25e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b57e640cf54e1cdb19a5b242ee66269", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/sdk-flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_core/firebase_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/firebase_core/firebase_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_core/firebase_core.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "firebase_core", "PRODUCT_NAME": "firebase_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9819862a2e615025d1a2ed5b4037f36001", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cd59d027815f5a095eccdd3caf8b8f58", "guid": "bfdfe7dc352907fc980b868725387e98588bad11b5421907088128a41c2f49b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864889f28d2f4f9e12a6945ac52d38032", "guid": "bfdfe7dc352907fc980b868725387e980c03afdeaf120d681c7484788fe427a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98debc2e77e0d961f17485560a2b8f0aa1", "guid": "bfdfe7dc352907fc980b868725387e98464aca5ac44a668aec98f4fa95969bf3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5bdc8205717bb87f4ba8caca34d6cec", "guid": "bfdfe7dc352907fc980b868725387e98089cfdbd61ac314dbe5666270f54428c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98809e75238a4503efe39c040d5d5c0f5a", "guid": "bfdfe7dc352907fc980b868725387e9836a7ed64f399d054edcc4814682fa8c9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987b6223ec067f33e5e6de75e101ee303d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9840b18afc3b48c0e79808dfe33a8d24cb", "guid": "bfdfe7dc352907fc980b868725387e9824f5cc98d9d692146408a0186a20946a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4e5f40748e111bd939c858ce95001e9", "guid": "bfdfe7dc352907fc980b868725387e98fffe3b79fa95776dcc02c6c515bbc897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891d8163eafc304a6eb78c14f431f4480", "guid": "bfdfe7dc352907fc980b868725387e980bc4f7e39a162fdb59f819b4343795c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cc83cb59227ebba29d844306d612a8c", "guid": "bfdfe7dc352907fc980b868725387e98e8535ca670369f6c5780d0535a26d06e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981127109f8c96dc09677211a3d38e57bd", "guid": "bfdfe7dc352907fc980b868725387e9836b5ad90b2568525f9283fada911867a"}], "guid": "bfdfe7dc352907fc980b868725387e98a0fd7e082af1481d7417908b2a93926b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e984bce42002e4e281b69572965c82569ba"}], "guid": "bfdfe7dc352907fc980b868725387e986110340cf93c1f43caaca1542429dec3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98942fd5e665fff14afecc69023a1bb1c0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a32fdd082239c9fc7912ba5b473ab170", "name": "firebase_core.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}