class GraphQlQuries {
  static String register = r'''
        mutation registerUser($userName: String,$userEmail: String,$userPassword: String,$firebaseDeviceToken: String, $lang: String) {
          action: registerUser(userName: $userName, userEmail: $userEmail, userPassword: $userPassword, firebaseDeviceToken: $firebaseDeviceToken, lang: $lang) {
             user{
          _id
          }
          status
                            arMessage
          message
          }
        }
      ''';

  static String login = r'''
        mutation loginUser($userEmail: String,$userPassword: String,$firebaseDeviceToken: String $lang: String) {
          action: loginUser(userEmail: $userEmail, userPassword: $userPassword, firebaseDeviceToken: $firebaseDeviceToken, lang: $lang) {
             user{
    _id
    isEmailVerified
    isPhoneVerified
    userPhoneNumber
    supplier {
      _id
        shopId{
          shopContactDetails{
            phoneNumber
            whatsUpPhoneNumber
            email
          }
          shopPickupAddresses{
            _id
            pickUpCity
            pickUpContactMobileNumber{
              number
              isVerified
            }
            pickUpContactLandNumber{
              number
              isVerified
            }
            pickUpAddress
            pickUpMapLocation{
              coordinates
              _id
            }
            createdAt
            updatedAt
            
          }
     validationHistory{
        updatedAt
        createdAt
        returnValues
        returnMessage
        status
      }
    _id
    shopName
    followerCount
    shopLogo
    shopBanner
    shopSlogan
    shopDescription
    shopTermsAndConditions
    freeDeliveryTarget
    targetPriceForFdt
   isCompleted
  }
      bussinessStatus
      bussinessOwnerLegalId
      bussinessTradeCertificate
      bussinessName
      bussinessOwnerName
      tradeCertificateExpiryDate
      ownerLegalIdExpiryDate
      validationHistory{
        returnValues
        updatedAt
        createdAt
        returnMessage
      }
    }
          }
          
          status
          message
                            arMessage
          token
          }
        }
      ''';

  static String emailVerify = r'''
                        mutation verifEmail($userId: ID,$otpNumber: String) {
                          action: verifEmail(userId: $userId, otpNumber: $otpNumber) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String publishSavedForlaterProduct = r'''
                        mutation publishSavedForlaterProduct($productId: ID) {
                          action: publishSavedForlaterProduct(productId: $productId) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String removeFireBaseToken = r'''
                        mutation removeFireBaseToken($firebaseDeviceToken: String) {
                          action: removeFireBaseToken(firebaseDeviceToken: $firebaseDeviceToken) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String addBankAccountDetails = r'''
                        mutation addBankAccountDetails($accountNumber: String,$iban: String, $accountHolderName: String, $bankName :String, $city:String) {
                          action: addBankAccountDetails(accountNumber: $accountNumber, iban: $iban, accountHolderName: $accountHolderName, bankName: $bankName, city: $city) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String updateBankAccountDetails = r'''
                        mutation updateBankAccountDetails($accountNumber: String,$iban: String, $accountHolderName: String, $bankName :String, $city:String) {
                          action: updateBankAccountDetails(accountNumber: $accountNumber, iban: $iban, accountHolderName: $accountHolderName, bankName: $bankName, city: $city) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String addBussinessInfo = r'''
                        mutation addBussinessInfo($bussinessOwnerId: ID,$bussinessTradeCertificate: String, $bussinessOwnerLegalId: String, $bussinessName: String, $bussinessOwnerName: String, $tradeCertificateExpiryDate: String, $ownerLegalIdExpiryDate: String) {
                          action: addBussinessInfo(bussinessOwnerId: $bussinessOwnerId, bussinessTradeCertificate: $bussinessTradeCertificate, bussinessOwnerLegalId: $bussinessOwnerLegalId, bussinessName: $bussinessName, bussinessOwnerName: $bussinessOwnerName, tradeCertificateExpiryDate: $tradeCertificateExpiryDate, ownerLegalIdExpiryDate: $ownerLegalIdExpiryDate) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String updateBussinessInfo = r'''
                        mutation updateBussinessInfo($bussinessTradeCertificate: String, $bussinessOwnerLegalId: String, $bussinessName: String, $bussinessOwnerName: String, $tradeCertificateExpiryDate: String, $ownerLegalIdExpiryDate: String) {
                          action: updateBussinessInfo(bussinessTradeCertificate: $bussinessTradeCertificate, bussinessOwnerLegalId: $bussinessOwnerLegalId, bussinessName: $bussinessName, bussinessOwnerName: $bussinessOwnerName, tradeCertificateExpiryDate: $tradeCertificateExpiryDate, ownerLegalIdExpiryDate: $ownerLegalIdExpiryDate) {
                            status
                            arMessage
                            message
                            
                          }
                        }
                      ''';

  static String forgetemailVerify = r'''
                        mutation resetPasswordVerifEmailOtp($userId: ID,$otpNumber: String) {
                          action: resetPasswordVerifEmailOtp(userId: $userId, otpNumber: $otpNumber) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String deleteFiles = r'''
                        mutation deleteFiles($fileList: [String]) {
                          action: deleteFiles(fileList: $fileList) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';
  static String acceptReturnOrderItem = r'''
                        mutation acceptReturnOrderItem($orderItemId: ID) {
                          action: acceptReturnOrderItem(orderItemId: $orderItemId) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';
  static String updateUserLang = r'''
                        mutation updateUserLang($lang: String) {
                          action: updateUserLang(lang: $lang) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';
  static String restoreProductState = r'''
                        mutation restoreProductState($productId: ID) {
                          action: restoreProductState(productId: $productId) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String changeAcceptOrderEmail = r'''
                        mutation changeAcceptOrderEmail($acceptOrderEmail: Boolean) {
                          action: changeAcceptOrderEmail(acceptOrderEmail: $acceptOrderEmail) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';
  static String updateUserName = r'''
                        mutation changeUserName($userName: String) {
                          action: changeUserName(userName: $userName) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';
  static String forgetPassword = r'''
                        mutation sendResetPasswordOtpEmail($userEmail: String) {
                          action: sendResetPasswordOtpEmail(userEmail: $userEmail) {
                            status
                            message
                            arMessage
                            userId
                          }
                        }
                      ''';
  static String forgetPasswordByNumber = r'''
                        mutation sendResetPasswordSmsOtp($userPhoneNumber: String) {
                          action: sendResetPasswordSmsOtp(userPhoneNumber: $userPhoneNumber) {
                            status
                            message
                            arMessage
    userId
                          }
                        }
                      ''';

  static String confirmOrderItem = r'''
                        mutation confirmOrderItem($orderItemId: ID, $pickupAddressId: ID,$readinessPeriode: String) {
                          action: confirmOrderItem(orderItemId: $orderItemId, pickupAddressId: $pickupAddressId, readinessPeriode: $readinessPeriode) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String editAcceptedOrderItem = r'''
                        mutation editAcceptedOrderItem($orderItemId: ID, $pickupAddressId: ID,$readinessPeriode: String) {
                          action: editAcceptedOrderItem(orderItemId: $orderItemId, pickupAddressId: $pickupAddressId, readinessPeriode: $readinessPeriode) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String resendOtpEmail = r'''
                        mutation resendVerifEmailOtp($userId: String) {
                          action: resendVerifEmailOtp(userId: $userId) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String changeQuantity = r'''
                        mutation changeQuantity($productId: ID,$quantity: Int) {
                          action: changeQuantity(productId: $productId, quantity: $quantity) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String updatePassword = r'''
                        mutation resetPassword($userId: ID, $newPassword: String) {
                          action: resetPassword(userId: $userId, newPassword: $newPassword) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';
  static String changePassword = r'''
                        mutation changePassword($oldPassword: String, $newPassword: String) {
                          action: changePassword(oldPassword: $oldPassword, newPassword: $newPassword) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String addPhoneNumber = r'''
                        mutation addUserPhoneNumber($userId: ID,$userPhoneNumber: String) {
                          action: addUserPhoneNumber(userId: $userId, userPhoneNumber: $userPhoneNumber) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';
  static String sendPickupAdrNumberOtp = r'''
                        mutation sendPickupAdrNumberOtp($phoneNumber: String) {
                          action: sendPickupAdrNumberOtp( phoneNumber: $phoneNumber) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String deleteAccount = r'''
                        mutation deleteAccount($userPassword: String) {
                          action: deleteAccount( userPassword: $userPassword) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String desactivateAccount = r'''
                        query desactivateAccount($userPassword: String) {
                          action: desactivateAccount( userPassword: $userPassword) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String getReviews = r'''
 query getReviews($itemsNumber: Int,$page: Int){
        action: getReviews(itemsNumber: $itemsNumber, page: $page){
                            status
                            message
                            arMessage
                             reviews{
      page
      totalPages
      totalItems
      hasNextPage
      items{
        reviews{
          rate
          reviewImages
          reviewMessage
          createdAt
          orderItemId{
          orderItemCode
          items{
            quantity
            product{
              _id
              productName
              productEIN
              productPrice
              productImages
            }
            variant{
              productId{
                _id
                productName
              }
              variantImages
              variantEIN
              variantAttributes{
                variantColor{
                  colorIcon
                  colorFamily
                }
                variantSize{
                  unit
                  size
                }
                variantCustomOptions{
                  attributeValue
                  attributeTitle
                }
              }
              variantPrice
            }
          }
        }
        clientId{
          userId{
            avatar
            userName
          }
        }
        }
        hasMoreReviews
        totalReviewCount
      }
    }
                          }
                        }
                      ''';

  static String getReviewsByProductId = r'''
 query getReviewsByProductId($itemsNumber: Int,$page: Int, $productId: ID){
        action: getReviewsByProductId(itemsNumber: $itemsNumber, page: $page, productId: $productId){
                            status
                            arMessage
    message
    reviews{
      page
      hasNextPage
      totalPages
      totalItems
      items{
        clientId{
          userId{
            avatar
            userName
          }
        }
        rate
        reviewImages
        reviewMessage
        createdAt
      }
      
    }
                          }
                        }
                      ''';

  static String activateAccount = r'''
                        query activateAccount($userPassword: String) {
                          action: activateAccount( userPassword: $userPassword) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String verifySettingOtp = r'''
                        mutation verifySettingOtp($otpNumber: String) {
                          action: verifySettingOtp(otpNumber: $otpNumber) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String verifyToken = r'''
                        query verifyToken{
                          action: verifyToken{
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String sendSettingsOtp = r'''
                        query sendSettingsOtp{
                          action: sendSettingsOtp{
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String resendSettingsOtp = r'''
                        query resendSettingsOtp{
                          action: resendSettingsOtp{
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String changePhone = r'''
                        mutation addUserPhoneNumber($userPhoneNumber: String, $userId: ID) {
                          action: addUserPhoneNumber(userPhoneNumber: $userPhoneNumber, userId: $userId) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String viewNotifications = r'''
                        mutation viewNotifications($notificationIds: [ID]) {
                          action: viewNotifications(notificationIds: $notificationIds) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String viewAllNotifications = r'''
                        mutation viewAllNotifications {
                          action: viewAllNotifications{
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String changeEmail = r'''
                        mutation changeEmail($email: String) {
                          action: changeEmail(email: $email) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String addProductToFreeDelivery = r'''
                        mutation addProductToFreeDelivery($productIds: [ID]) {
                          action: addProductToFreeDelivery(productIds: $productIds) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';
  static String removeProductFromFreeDelivery = r'''
                        mutation removeProductFromFreeDelivery($productIds: [ID]) {
                          action: removeProductFromFreeDelivery(productIds: $productIds) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String resendPhoneNumberOtp = r'''
                        mutation resendPhoneOtp($userId: ID) {
                          action: resendPhoneOtp(userId: $userId) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String resendPickupAdrNumberOtp = r'''
                        mutation resendPickupAdrNumberOtp($phoneNumber: String) {
                          action: resendPickupAdrNumberOtp(phoneNumber: $phoneNumber) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String setProductHiddenOrUnhidden = r'''
                        mutation setProductHiddenOrUnhidden($productId: ID, $isHidden: Boolean) {
                          action: setProductHiddenOrUnhidden(productId: $productId, isHidden: $isHidden) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String numberVerify = r'''
                        mutation verifyPhoneOtp($userId: ID,$otpNumber: String) {
                          action: verifyPhoneOtp(userId: $userId, otpNumber: $otpNumber) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String verifyPickupAdrNumberOtp = r'''
                        mutation verifyPickupAdrNumberOtp($phoneNumber: String,$otpNumber: String) {
                          action: verifyPickupAdrNumberOtp(phoneNumber: $phoneNumber, otpNumber: $otpNumber) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String addWithdrawalrequest = r'''
                        mutation addWithdrawalrequest($requestedAmount: Float) {
                          action: addWithdrawalrequest(requestedAmount: $requestedAmount) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';
  static String deleteProductById = r'''
                        mutation deleteProductById($productId: ID) {
                          action: deleteProductById(productId: $productId) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String restoreProduct = r'''
                        mutation restoreProduct($productId: ID) {
                          action: restoreProduct(productId: $productId) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String deleteProductPermanentlyById = r'''
                        query deleteProductPermanentlyById($productId: ID) {
                          action: deleteProductPermanentlyById(productId: $productId) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String getTotalNotViewedNotifications = r'''
                        query getTotalNotViewedNotifications {
                          action: getTotalNotViewedNotifications
                        }
                      ''';
  static String cancelItemInOrderItem = r'''
                        mutation cancelItemInOrderItem($orderItemId: ID,$itemId: ID,$notes: String) {
                          action: cancelItemInOrderItem(orderItemId: $orderItemId,itemId: $itemId,notes: $notes) {
                            status
                            arMessage
                            message
                          }
                        }
                      ''';

  static String getProductById = r'''
  query getProductById($productId: ID) {
        action: getProductById(productId: $productId){
     product{
     ar{
    
      
      productName
      productDescription
      productOptions{
        productSizes{
          sizeUnit
          sizeValues{
            isValueHidden
            value
          }
        }
        productCustomOptions{
          optionId
          optionTitle
          optionValues{
            value
            isValueHidden
          }
        }
        productColors{
          isHidden
          colorId
          colorName
          colorIcon
          colorImages
          colorFamily
        }
      }
      productSpecs{
        specsTitle
        specsValue
        specsId
      }
      productPolicies{
        productAcceptReturn
        productReturnDuration
        productWarrantyDuration
        productFreeReturn
        productNotFreeReturn
        productReturnPolicy
      }
    }
      _id
      productCatalog
      isHidden
      isFreeDeliveryItem
      isMatched
      productEIN
      productOwnerId{
        _id
      }

      validationHistory{
        status
        requestType
        returnValues
        returnMessage
        
      }
      productName
      productDescription
      productManufacturerId
      productCategory{
        categoryName
        ar{
        categoryName
      }
        _id
      }
      productSubCategory{
        subCategoryName
        _id
         ar{
        subCategoryName
      }
      }
      productBrand{
        brandName
        ar{
          brandName
        }
        _id
      }
      productKeyWords
      productImages
      productPrice
      isFreeDeliveryItem
      productAvailableQte
      productNotifOnMinQte
      productMinQte
      productOptions{
        productSizes{
          sizeUnit
          sizeValues{
            isValueHidden
            value
          }
        }
        productCustomOptions{
          optionId
          optionTitle
          optionValues{
            value
            isValueHidden
          }
        }
        productColors{
          isHidden
          colorId
          colorName
          colorIcon
          colorImages
          colorFamily
        }
      }
      productSpecs{
        specsTitle
        specsValue
        specsId
      }
      productVariants{
           ar{
        variantAttributes{
          variantSize{
            size
            unit
          }
          variantColor{
          colorId
            colorName
            colorIcon
            colorFamily
            colorImages
          }
          variantCustomOptions{
            attributeTitle
            attributeValue
          }
        }
      }
        _id
        variantName
        variantCode
        variantPrice
        variantPrice
        variantQte
        variantImages
        variantManufacturerId
        isVariantVisible
        variantEIN
        validationHistory{
       _id
          status
          returnMessage
          infoMessage
          returnValues
          updatedValues
          requestType
          createdAt
          updatedAt
        }
        variantAttributes{
          variantSize{
            size
            unit
          }
          variantColor{
          colorId
            colorName
            colorIcon
            colorFamily
            colorImages
          }
          variantCustomOptions{
            attributeTitle
            attributeValue
          }
        }
      }
         productSizeCategory{
         _id
      name
      code
      ar{
        name
      }
      dimensions{
        width
        height
        weight
        length
      }
      }
      itemsPerOrder
      productPolicies{
        productAcceptReturn
        productReturnDuration
        productWarrantyDuration
        productFreeReturn
        productNotFreeReturn
        productReturnPolicy
      }
      createdAt
      updatedAt
      productStatus
      isReady
      isCompleted
      validationHistory{
        _id
        status
        returnMessage
        returnValues
        updatedValues
        requestType
        createdAt
        updatedAt
      }
      productEIN
      
    }
    status
                            arMessage
    message
  }
  }
''';

  static String getProductCatalogById = r'''
  query getProductCatalogById($productCatalogId: ID) {
        action: getProductCatalogById(productCatalogId: $productCatalogId){
     product{
      _id
     
      productEIN
      productName
      productDescription
      productManufacturerId
      productCategory{
        categoryName
        _id
      }
      productSubCategory{
        subCategoryName
        _id
      }
      productBrand{
        brandName
        _id
      }
      productKeyWords
      productImages
     
      productOptions{
        productSizes{
          sizeUnit
          sizeValues{
            isValueHidden
            value
          }
        }
        productCustomOptions{
          optionId
          optionTitle
          optionValues{
            value
            isValueHidden
          }
        }
        productColors{
          isHidden
          colorId
          colorName
          colorIcon
          colorImages
          colorFamily
        }
      }
      productSpecs{
        specsTitle
        specsId
        specsValue
      }
      productVariants{
        _id
        variantName
        variantCode
        variantPrice
        variantPrice
        variantQte
        variantImages
        variantManufacturerId
        isVariantVisible
        variantEIN
        variantAttributes{
          variantSize{
            size
            unit
          }
          variantColor{
          colorId
            colorName
            colorIcon
            colorFamily
            colorImages
          }
          variantCustomOptions{
            attributeTitle
            attributeValue
          }
        }
      }
         productSizeCategory{
         _id
      name
      code
      dimensions{
        width
        height
        weight
        length
      }
      }
      productEIN
    }
    status
                            arMessage
    message
  }
  }
''';

  static String getAllCities = r'''
  query getAllCities {
        action: getAllCities {
          status
                            arMessage
          message
           cities{
      cityName
      ar{
      cityName
      }
    }
        }
  }
''';

  static String getBrands = r'''
  query getBrands($categoryId: ID) {
        action: getBrands(categoryId: $categoryId) {
             status
                            arMessage
    message
    brands{
      brandName
      brandIcon
      _id
    }
        }
  }
''';

  static String getBusinessInfo = r'''
  query getUserInfo {
        action: getUserInfo {
            status
    user{
        supplier {
      _id
      bussinessStatus
      bussinessOwnerLegalId
      bussinessTradeCertificate
      bussinessName
      bussinessOwnerName
      tradeCertificateExpiryDate
      ownerLegalIdExpiryDate
      validationHistory{
        returnValues
        returnMessage
        createdAt
      }
    }
    }
    message
                            arMessage
        }
  }
''';

  static String getDashboardInfo = r'''
  query getDashboardInfo {
        action: getDashboardInfo {
          totalRevenue
    totalSoldItems
    shopName
    ordersCount
    productCount
    status
                            arMessage
    message
        }
  }
''';

  static String createShop = r'''
                        mutation createShop ($shopBanner: String, $shopLogo: String, $shopSlogan: String, $freeDeliveryTarget: Boolean, $targetPriceForFdt: Int, $shopDescription: String, $shopTermsConditions: String,$pickupAdr: [pickUpAddressType], $phoneNumber: String, $whatsUpPhoneNumber: String, $email: String) {
                          action: createShop (shopBanner: $shopBanner,shopLogo: $shopLogo,shopSlogan: $shopSlogan,freeDeliveryTarget: $freeDeliveryTarget,targetPriceForFdt: $targetPriceForFdt,shopDescription: $shopDescription,shopTermsConditions: $shopTermsConditions,shopPickupAddresses: $pickupAdr,phoneNumber: $phoneNumber,whatsUpPhoneNumber: $whatsUpPhoneNumber,email: $email,) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String updateShop = r'''
                        mutation updateShop ($shopBanner: String, $shopLogo: String, $shopSlogan: String, $freeDeliveryTarget: Boolean, $targetPriceForFdt: Int, $shopDescription: String, $shopTermsAndConditions: String, $pickupAdr: [pickUpAddressUpdateType], $phoneNumber: String, $whatsUpPhoneNumber: String, $email: String) {
                          action: updateShop (shopBanner: $shopBanner,shopLogo: $shopLogo,shopSlogan: $shopSlogan,freeDeliveryTarget: $freeDeliveryTarget,targetPriceForFdt: $targetPriceForFdt,shopDescription: $shopDescription,shopTermsAndConditions: $shopTermsAndConditions,pickupAddresses: $pickupAdr,phoneNumber: $phoneNumber,whatsUpPhoneNumber: $whatsUpPhoneNumber,email: $email,) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String genrateVariation = r'''
                        mutation generateVariations ($ar: arabicProductInputType,$productName: String, $productPrice: Float, $productAvailableQte: Int, $productImages: [String], $productColors: [productColorsInputType], $productSizes: [productSizesInputType], $productCustomOptions: [productCustomOptionsInputType]) {
                          action: generateVariations (ar: $ar, productName: $productName, productPrice: $productPrice, productAvailableQte: $productAvailableQte, productImages: $productImages, productColors: $productColors, productSizes: $productSizes, productCustomOptions: $productCustomOptions) {
                            status
                            arMessage
                            message
                                 colorGroupedVariants{
      colorName
      colorIcon
      colorFamily
      colorImages
      variations{
      variantQte
      
      variantName
      variantCode
      variantPrice
      variantImages
      variantAttributes{
        variantSize{
          unit
          size
        }
        variantColor{
          colorId
          colorName
          colorIcon
          colorFamily
          colorImages
        }
        variantCustomOptions{
          attributeTitle
          attributeValue
        }
      }
      
        ar {
          variantName
          variantAttributes {
            variantSize {
              size
              unit
            }
            variantColor {
              colorName
              colorFamily
            }
            variantCustomOptions {
              attributeTitle
              attributeValue
            }
          }
        }
      isVariantVisible
        isVariantModified
    }
    }
    sizeGroupedVariants{
      unit
      size
        variations{
      variantQte
      
      variantName
      variantCode
      variantPrice
      variantImages
      variantAttributes{
        variantSize{
          unit
          size
        }
        variantColor{
          colorName
          colorIcon
          colorFamily
          colorImages
        }
        variantCustomOptions{
          attributeTitle
          attributeValue
        }
      }
      
        ar {
          variantName
          variantAttributes {
            variantSize {
              size
              unit
            }
            variantColor {
              colorName
              colorFamily
            }
            variantCustomOptions {
              attributeTitle
              attributeValue
            }
          }
        }
      isVariantVisible
        isVariantModified
    }
    }
       variations{
      variantQte
      
      variantName
      variantCode
      variantPrice
      variantImages
      variantAttributes{
        variantSize{
          unit
          size
        }
        variantColor{
          colorName
          colorIcon
          colorFamily
          colorImages
        }
        variantCustomOptions{
          attributeTitle
          attributeValue
        }
      }
      
        ar {
          variantName
          variantAttributes {
            variantSize {
              size
              unit
            }
            variantColor {
              colorName
              colorFamily
            }
            variantCustomOptions {
              attributeTitle
              attributeValue
            }
          }
        }
      isVariantVisible
        isVariantModified
    }
                          }
                        }
                      ''';

  static String createProduct = r'''
                        mutation createProduct ($isFreeDeliveryItem : Boolean,$productName: String, $productDescription: String, $productManufacturerId: String, $productCategory: ID, $productSubCategory: ID, $productBrand: String, $productKeyWords: [String], $productPrice: Float, $productAvailableQte: Int, $productNotifOnMinQte: Boolean,$productMinQte: Int, $productImages: [String], $productColors : [productColorsInputType], $productSizes: [productSizesInputType], $productCustomOptions: [productCustomOptionsInputType],$productVariations: [variantInputType], $productSpecs:  [productSpecsInputType], $productAcceptReturn: Boolean, $productFreeReturn: Boolean, $productNotFreeReturn: Boolean, $productReturnDuration: String, $productWarrantyDuration: String, $productReturnPolicy: String, $productSizeCategory: ID, $itemsPerOrder: Int, $publishProduct: Boolean, $saveProductForLater: Boolean , $ar: arabicProductInputType) {
                          action: createProduct (isFreeDeliveryItem: $isFreeDeliveryItem, productName: $productName,productDescription: $productDescription,productManufacturerId: $productManufacturerId,productCategory: $productCategory,productSubCategory: $productSubCategory,productBrand: $productBrand,productKeyWords: $productKeyWords,productPrice: $productPrice,productAvailableQte: $productAvailableQte,productNotifOnMinQte: $productNotifOnMinQte, productMinQte: $productMinQte, productImages: $productImages, productColors: $productColors, productSizes: $productSizes, productCustomOptions: $productCustomOptions, productVariations: $productVariations, productSpecs: $productSpecs, productAcceptReturn: $productAcceptReturn, productFreeReturn: $productFreeReturn, productNotFreeReturn: $productNotFreeReturn, productReturnDuration: $productReturnDuration, productWarrantyDuration: $productWarrantyDuration, productReturnPolicy: $productReturnPolicy, productSizeCategory: $productSizeCategory, itemsPerOrder: $itemsPerOrder, publishProduct: $publishProduct, saveProductForLater: $saveProductForLater, ar: $ar) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String updateProductInfo = r'''
                        mutation updateProduct ($isFreeDeliveryItem : Boolean,$productId: ID, $productName: String, $productDescription: String, $productManufacturerId: String, $productCategory: ID, $productSubCategory: ID, $productBrand: String, $productKeyWords: [String], $productPrice: Float, $productAvailableQte: Int, $productNotifOnMinQte: Boolean,$productMinQte: Int, $productImages: [String], $productColors : [productColorsInputType], $productSizes: [productSizesInputType], $productCustomOptions: [productCustomOptionsInputType],$productVariations: [variantUpdateInputType], $productSpecs:  [productSpecsInputType], $productAcceptReturn: Boolean, $productFreeReturn: Boolean, $productNotFreeReturn: Boolean, $productReturnDuration: String, $productWarrantyDuration: String, $productReturnPolicy: String,  $productSizeCategory: ID, $itemsPerOrder: Int, $ar: arabicProductInputType) {
                          action: updateProduct (isFreeDeliveryItem: $isFreeDeliveryItem,productId: $productId, productName: $productName,productDescription: $productDescription,productManufacturerId: $productManufacturerId,productCategory: $productCategory,productSubCategory: $productSubCategory,productBrand: $productBrand,productKeyWords: $productKeyWords,productPrice: $productPrice,productAvailableQte: $productAvailableQte,productNotifOnMinQte: $productNotifOnMinQte, productMinQte: $productMinQte, productImages: $productImages, productColors: $productColors, productSizes: $productSizes, productCustomOptions: $productCustomOptions, productVariations: $productVariations, productSpecs: $productSpecs, productAcceptReturn: $productAcceptReturn, productFreeReturn: $productFreeReturn, productNotFreeReturn: $productNotFreeReturn, productReturnDuration: $productReturnDuration, productWarrantyDuration: $productWarrantyDuration, productReturnPolicy: $productReturnPolicy, productSizeCategory: $productSizeCategory, itemsPerOrder: $itemsPerOrder, ar: $ar) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String uploadMatchedProduct = r'''
                        mutation addMatchedProduct ($productName: String,$productDescription: String,$productManufacturerId: String,$productCategory: ID,$productSubCategory: ID,$productBrand: String,$productKeyWords: [String], $isFreeDeliveryItem : Boolean,$productCatalogId: ID, $productPrice: Float, $productAvailableQte: Int, $productNotifOnMinQte: Boolean, $productMinQte: Int,$productColors: [productColorsInputType],$productSizes: [productSizesInputType],$productCustomOptions: [productCustomOptionsInputType],$productSpecs: [productSpecsInputType], $productImages: [String], $productVariations: [matchedProductVariantInputType],$productAcceptReturn: Boolean,$productFreeReturn: Boolean,$productNotFreeReturn: Boolean,$productReturnDuration: String,$productWarrantyDuration: String,$productReturnPolicy:String, $publishProduct: Boolean,$saveProductForLater: Boolean,$itemsPerOrder: Int, $oldProductId: ID, $productSizeCategory: ID, $isRematched: Boolean, $ar: arabicProductInputType)  {
                          action: addMatchedProduct (productName: $productName, productDescription: $productDescription, productManufacturerId: $productManufacturerId, productCategory: $productCategory, productSubCategory: $productSubCategory, productBrand: $productBrand, productKeyWords: $productKeyWords, isFreeDeliveryItem: $isFreeDeliveryItem,productCatalogId: $productCatalogId, productPrice: $productPrice, productAvailableQte: $productAvailableQte, productNotifOnMinQte: $productNotifOnMinQte, productMinQte: $productMinQte, productSpecs: $productSpecs, productImages: $productImages, productColors: $productColors, productSizes: $productSizes, productCustomOptions: $productCustomOptions, productVariations: $productVariations, productAcceptReturn: $productAcceptReturn, productFreeReturn: $productFreeReturn, productNotFreeReturn: $productNotFreeReturn, productReturnDuration: $productReturnDuration, productWarrantyDuration: $productWarrantyDuration, productReturnPolicy: $productReturnPolicy, publishProduct: $publishProduct, saveProductForLater: $saveProductForLater, itemsPerOrder: $itemsPerOrder, oldProductId: $oldProductId, productSizeCategory: $productSizeCategory, isRematched: $isRematched, ar: $ar) {
                            status
                            message
                            arMessage
                          }
                        }
                      ''';

  static String getTermsAndConditions = r'''
  query getTermsAndConditions {
        action: getTermsAndConditions {
          status
          message
                            arMessage
    termsAndConditions{
      termsAndConditionsText
      ar{
         termsAndConditionsText
      }
    }
        }
  }
''';

  static String getAccountSetting = r'''
  query getAccountSetting {
        action: getAccountSetting {
          status
                            arMessage
          message
    accountSetting{
      isUserActive
      isShopActive
      isShopHidden
      isAccountBanned
      isSupplierActive
      isAccountDisabled
      acceptOrderEmails
    }
        }
  }
''';

  static String getAllColors = r'''
  query getAllColorFamilies {
        action: getAllColorFamilies{
    status
    message
                            arMessage
    colorFamilies{
      colorFamilyName
      ar {
        colorFamilyName
      }
    }
  }
  }
''';

  static String getAllSizes = r'''
  query getAllSizes {
        action: getAllSizes{
    sizes{
      sizeUnit
      ar{
        sizeUnit
      }
      sizeValues
    }
    status
    message
                            arMessage
  }
  }
''';

  static String getNotifications = r'''
  query getNotifications($page: Int, $itemsNumber: Int) {
        action: getNotifications(page: $page, itemsNumber: $itemsNumber){
     status
                            arMessage
    message
     notifications{
      items{
          title
      createdAt
      _id
      isViewed
      body
      ar{
          title
      createdAt
      _id
      isViewed
      body
      }
      }
      hasNextPage
      totalPages
      page
    }
  }
  }
''';

  static String getIncomeInfos = r'''
  query getIncomeInfos {
        action: getIncomeInfos{
    status
                            arMessage
    message
    availableAmount
    totalDeliveryChargeFees
    totalRevenue
    pendingAmount
   deliveryFeeCharges{
      shipmentCode
      feeType
      date
      feeAmount
    }
    topSellingItems{
      product{
        productImages
      }
      variant{
        _id
        variantImages
      }
      sales
    }
  }
  }
''';
  static String getSalesQuery = r'''
  query getSalesQuery ($statusFilter: String, $dateFilter: Int, $page: Int, $itemsNumber: Int) {
        action: getSalesQuery(statusFilter: $statusFilter, dateFilter: $dateFilter, page: $page, itemsNumber: $itemsNumber){
  sales{
      items{
        shipmentId
      supplierPay
      totalItemsCost
      status
      date
      }
      hasNextPage
      totalPages
      totalItems
      page
    }
    status
                            arMessage
    message
  }
  }
''';
  static String getWithDrawals = r'''
  query getWithDrawals ($page: Int, $itemsNumber: Int) {
        action: getWithDrawals(page: $page, itemsNumber: $itemsNumber){
  withdrawals{
      items{
        withdrawalId
      requestedAmount
      status
      createdAt
      withdrawalFee
      updatedAt
      receipt
      }
      hasNextPage
      totalPages
      totalItems
      page
    }
    status
                            arMessage
    message
  }
  }
''';

  static String getProductsFromCatalog = r'''
  query ($searchFilter: String, $page: Int, $itemsNumber: Int) {
        action: getProductsFromCatalog(searchFilter: $searchFilter, page: $page, itemsNumber: $itemsNumber){
      status
                            arMessage
    message
     products{
      items{
        _id
      productName
      productBrand{
        brandName
      }
      productOptions{
        productColors{
          colorImages
        }
      }
      productImages
      }
      page
      hasNextPage
      totalPages
      totalItems
    }
  }
  }
''';

  static String getAllReturnDuration = r'''
  query getAllReturnDurations {
        action: getAllReturnDurations{
    returnDurations{
      returnDuration
       ar{
        returnDuration
      }
    }
    status
    message
  }
  }
''';

  static String getAllSizeCategories = r'''
  query getAllSizeCategories {
        action: getAllSizeCategories{
    sizeCategories{
      _id
      name
       ar{
        name
      }
      code
      dimensions{
        width
        height
        weight
        length
      }
    }
    status
                            arMessage
    message
  }
  }
''';

  static String getAllWarrantyDuration = r'''
  query getAllWarrantyDurations {
        action: getAllWarrantyDurations{
    warrantyDurations{
      warrantyDuration
       ar{
        warrantyDuration
      }
    }
    status
                            arMessage
    message
  }
  }
''';

  static String getAllCategoryAndData = r'''
  query getAllCategories {
        action: getAllCategories{
    categories{
     ar{
        categoryName
        
      }
      categoryName
      categoryDarkIcon
      _id
      subCategories{
        ar{
          subCategoryName
        }
        subCategoryName
        subCategoryDarkIcon
        _id
      }
      brands{
        ar{
          brandName
        }
        brandName
        brandIcon
        _id
      }
    }
    status
                            arMessage
    message
  }
  }
''';

  static String getPickupAddresses = r'''
  query getPickupAddresses {
        action: getPickupAddresses{
      status
                            arMessage
    message
    pickUpAddresses{
      _id
      pickUpCity
      pickUpAddress
    }
  }
  }
''';

  static String getRegistrationValidationHistory = r'''
  query getRegistrationValidationHistory {
        action: getRegistrationValidationHistory{
 message
                            arMessage
    status
    registrationHistory{
      _id
      status
      requestType
      returnMessage
      returnValues
      createdAt
      updatedAt
    }
  }
  }
''';

  static String getShopValidationHistory = r'''
  query getShopValidationHistory {
        action: getShopValidationHistory{
 message
                            arMessage
    status
    shopHistory{
      _id
      status
      requestType
      returnMessage
      returnValues
      createdAt
      updatedAt
    }
  }
  }
''';

  static String getProductValidationHistory = r'''
 query getProductValidationHistory($itemsNumber: Int,$page: Int){
        action: getProductValidationHistory(itemsNumber: $itemsNumber, page: $page){
 message
                            arMessage
    status
      productHistory{
        page
        hasNextPage
totalPages
totalItems
      items{
        _id
        productName
        validationHistory{
          status
      _id
      status
      requestType
      returnMessage
      returnValues
      createdAt
      updatedAt
        }
      }
    }
  }
  }
''';

  static String getFaqs = r'''
 query getFaqs($itemsNumber: Int,$page: Int){
        action: getFaqs(itemsNumber: $itemsNumber, page: $page){
      status
    message
    arMessage
  faqs{
    items{
      question
      answer
      ar{
      question
      answer
    }
    }
    page
    hasNextPage
    totalItems
    totalPages
  }
  }
  }
''';

  static String getProducts = r'''
   query getProducts($itemsNumber: Int,$page: Int, $categoryId: String, $subCategoryId: String){
        action: getProducts(itemsNumber: $itemsNumber, page: $page,categoryId: $categoryId, subCategoryId: $subCategoryId){
      status
                            arMessage
    message
    availableCategories{
    productCategory{
      _id
      categoryName
      ar{
        categoryName
      }
    }
    productSubCategory{
      _id
      subCategoryName
       ar{
        subCategoryName
      }
      productCount
    }
  }
    products{
      page
      totalPages
      totalItems
      hasNextPage
      items{
         _id
      isHidden
      isFreeDeliveryItem
      productName
      productBrand{
        brandName
      }
      productPrice
      productImages
      productSubCategory{
        subCategoryName
      }
      productOptions{
        productColors{
          colorIcon
          colorImages
        }
      }
      }
    }
  }
  }
''';

  static String getAllProducts = r'''
 query getAllProducts($itemsNumber: Int,$page: Int){
        action: getAllProducts(itemsNumber: $itemsNumber, page: $page){
      status
    message
                            arMessage
    products{
      page
      totalPages
      totalItems
      hasNextPage
      items{
         _id
      isHidden
      isFreeDeliveryItem
      productName
      productBrand{
        brandName
      }
      productPrice
      productImages
      productSubCategory{
        subCategoryName
      }
      productOptions{
        productColors{
          colorIcon
          colorImages
        }
      }
      }
    }
  }
  }
''';

  static String getOrderItems = r'''
 query getOrderItems($itemsNumber: Int,$page: Int, $status: String){
        action: getOrderItems(itemsNumber: $itemsNumber, page: $page, status: $status){
          status
    message
        orderItems{
    items{ 
      invoice
      orderItemCode
      shipmentQrCode
        orderItemStatus{
        status
      createdAt
      }
      billingAddress{
        address
      }
      pickupDetails{
        pickupDriverId{
          name
        }
      }
      deliveryDetails{
      deliveryDriverId{
      name
      }
      }
      estimatedDeliveryTime
      courrierId{
        companyName 
        officeAddress
        phoneNumber
       
      }
      createdAt
      _id
      clientId{
        userId{
          userName
          userPhoneNumber
          userEmail
          
        }
      }
      readinessPeriode
      pickupAddress{
        _id
        pickUpContactMobileNumber{
          number
        }
        pickUpCity
        pickUpAddress
        
      }
      items{
        _id
        orderItemProductStatus{
          status
        }
        product{
        isFreeDeliveryItem
          productName
          _id
          productEIN
          productPrice
          productImages
          productCategory{
            categoryName
          }
          productSubCategory{
            subCategoryName
          }
          productBrand{
            brandName
          }
        }
        variant{
          _id
            variantEIN
          productId{
          isFreeDeliveryItem
          productCategory{
            categoryName
          }
          productSubCategory{
            subCategoryName
          }
          productBrand{
            brandName
          }
          }
          variantName
          variantAttributes{
            variantSize{
              size
            }
            variantColor{
              colorIcon
              colorFamily
              colorImages
              colorName
            }
            variantCustomOptions{
              attributeTitle
              attributeValue
            }
          }
          variantImages
          variantPrice
        }
        quantity
      }
      finalCost
      totalCost
      shipmentCost
supplierFeeCharges
    }
    page
    totalPages
    hasNextPage
    totalItems
    }
  }
  }
''';

  static String getOrderItemsLengths = r'''
 query getOrderItemsLengths{
        action: getOrderItemsLengths{
         Pending
    Returned
    Canceled
    shipped
    Delivered
    Inprogress
    NewReturn
  }
  }
''';

  static String getCancelOrderItems = r'''
 query getCancelOrderItems($itemsNumber: Int,$page: Int){
        action: getCancelOrderItems(itemsNumber: $itemsNumber, page: $page){
          status
                            arMessage
    message
    orderItems{
    items{ 
      invoice
      orderItemCode
      shipmentQrCode
        orderItemStatus{
        status
      createdAt
      }
      billingAddress{
        address
      }
      pickupDetails{
        pickupDriverId{
          name
        }
      }
      estimatedDeliveryTime
      courrierId{
        companyName 
        officeAddress
        phoneNumber
       
      }
      createdAt
      _id
      clientId{
        userId{
          userName
          userPhoneNumber
          userEmail
          
        }
      }
      pickupAddress{
        pickUpCity
        pickUpAddress
        
      }
      items{
        _id
        orderItemProductStatus{
          status
        }
        product{
          productName
          _id
          productEIN
          productPrice
          productImages
          productCategory{
            categoryName
          }
          productSubCategory{
            subCategoryName
          }
          productBrand{
            brandName
          }
        }
        variant{
          _id
            variantEIN
          productId{
          productCategory{
            categoryName
          }
          productSubCategory{
            subCategoryName
          }
          productBrand{
            brandName
          }
          }
          variantName
          variantAttributes{
            variantSize{
              size
            }
            variantColor{
              colorIcon
              colorFamily
              colorImages
              colorName
            }
            variantCustomOptions{
              attributeTitle
              attributeValue
            }
          }
          variantImages
          variantPrice
        }
        quantity
      }

supplierFeeCharges
      totalCost
      shipmentCost
    }
    page
    totalPages
    hasNextPage
    totalItems
    }
  }
  }
''';

  static String getReturnOrderItems = r'''
  query getReturnOrderItems {
        action: getReturnOrderItems{
    items{ 
      invoice
      orderItemCode
      shipmentQrCode
        orderItemStatus{
        status
      createdAt
      }
      billingAddress{
        address
      }
      createdAt
      _id
      clientId{
        userId{
          userName
          userPhoneNumber
          userEmail
          
        }
      }
      pickupAddress{
        pickUpContactNumber
        pickUpCity
        pickUpAddress
        
      }
      items{
        _id
        orderItemProductStatus{
          status
        }
        product{
          productName
          _id
          productEIN
          productPrice
          productImages
          productCategory{
            categoryName
          }
          productSubCategory{
            subCategoryName
          }
          productBrand{
            brandName
          }
        }
        variant{
          _id
          variantEIN
          productId{
            
          productCategory{
            categoryName
          }
          productSubCategory{
            subCategoryName
          }
          productBrand{
            brandName
          }
          }
          variantName
          variantAttributes{
            variantSize{
              size
            }
            variantColor{
              colorIcon
              colorFamily
              colorImages
              colorName
            }
            variantCustomOptions{
              attributeTitle
              attributeValue
            }
          }
          variantImages
          variantPrice
        }
        quantity
      }

supplierFeeCharges
      totalCost
      shipmentCost
    }
    page
    totalPages
    hasNextPage
    totalItems
  }
  }
''';

  static String getPendingProducts = r'''
   query getPendingProducts($itemsNumber: Int,$page: Int, $categoryId: String, $subCategoryId: String){
        action: getPendingProducts(itemsNumber: $itemsNumber, page: $page,categoryId: $categoryId, subCategoryId: $subCategoryId){
      status
                            arMessage
    message
      availableCategories{
    productCategory{
      _id
      categoryName
      ar{
        categoryName
      }
    }
    productSubCategory{
      _id
      subCategoryName
       ar{
        subCategoryName
      }
      productCount
    }
  }
    products{
      page
      totalPages
      totalItems
      hasNextPage
      items{
       ar{
          productName
          
          
        }
         _id
         validationHistory{
          status
        }
         productStatus
      isHidden
      isFreeDeliveryItem
      productName
      productBrand{
        brandName
         ar{
          brandName
        }
      }
      productPrice
      productImages
      productSubCategory{
        subCategoryName
      }
      productOptions{
        productColors{
          colorIcon
          colorImages
        }
      }
      }
    }
  }
  }
''';

  static String getSavedForLaterProducts = r'''
   query getSavedForLaterProducts($itemsNumber: Int,$page: Int, $categoryId: String, $subCategoryId: String){
        action: getSavedForLaterProducts(itemsNumber: $itemsNumber, page: $page,categoryId: $categoryId, subCategoryId: $subCategoryId){
      status
                            arMessage
    message
     availableCategories{
    productCategory{
      _id
      categoryName
      ar{
        categoryName
      }
    }
    productSubCategory{
      _id
      subCategoryName
       ar{
        subCategoryName
      }
      productCount
    }
  }
    products{
      page
      totalPages
      totalItems
      hasNextPage
      items{
         _id
         productStatus
      isHidden
      isFreeDeliveryItem
      productName
      productBrand{
        brandName
      }
      productPrice
      productImages
      productSubCategory{
        subCategoryName
      }
      productOptions{
        productColors{
          colorIcon
          colorImages
        }
      }
      }
    }
  }
  }
''';

  static String getDeletedProducts = r'''
query getDeletedProducts($itemsNumber: Int,$page: Int){
        action: getDeletedProducts(itemsNumber: $itemsNumber, page: $page){
      status
                            arMessage
    message
    products{
      page
      totalPages
      totalItems
      hasNextPage
      items{
         _id
      isHidden
      isFreeDeliveryItem
      productName
      productBrand{
        brandName
      }
      productPrice
      productImages
      productSubCategory{
        subCategoryName
      }
      productOptions{
        productColors{
          colorIcon
          colorImages
        }
      }
      }
    }
  }
  }
''';

  static String getBankAccountDetails = r'''
  query getBankAccountDetails{
        action: getBankAccountDetails{
    status
                            arMessage
    message
    bankAccountDetails{
      iban
      city
      accountNumber
      accountHolderName
      bankName
    }
  }
  }
''';

  static String getMinQteAlertProducts = r'''
   query getMinQteAlertProducts($itemsNumber: Int,$page: Int, $categoryId: String, $subCategoryId: String){
        action: getMinQteAlertProducts(itemsNumber: $itemsNumber, page: $page,categoryId: $categoryId, subCategoryId: $subCategoryId){
      status
                            arMessage
    message
     availableCategories{
    productCategory{
      _id
      categoryName
      ar{
        categoryName
      }
    }
    productSubCategory{
      _id
      subCategoryName
       ar{
        subCategoryName
      }
      productCount
    }
  }
    products{
      page
      totalPages
      totalItems
      hasNextPage
      items{
         _id
         productStatus
      isHidden
      isFreeDeliveryItem
      productName
      productBrand{
        brandName
      }
      productPrice
      productImages
      productSubCategory{
        subCategoryName
      }
      productOptions{
        productColors{
          colorIcon
          colorImages
        }
      }
      }
    }
  }
  }
''';

  static String getSupportDetails = r'''
 query getSupportDetails{
        action: getSupportDetails{
      status
                            arMessage
    message
    support{
      whatsUp
      phone
      email
    }
  }
  }
''';

  static String getOutOfStockProduct = r'''
   query getOutOfStockProducts($itemsNumber: Int,$page: Int){
        action: getOutOfStockProducts(itemsNumber: $itemsNumber, page: $page){
      status
                            arMessage
    message
    products{
      page
      totalPages
      totalItems
      hasNextPage
      items{
         _id
         productStatus
      isHidden
      isFreeDeliveryItem
      productName
      productBrand{
        brandName
      }
      productPrice
      productImages
      productSubCategory{
        subCategoryName
      }
      }
    }
  }
  }
''';

  static String getOutOfStockVariants = r'''
   query getOutOfStockVariants($itemsNumber: Int,$page: Int){
        action: getOutOfStockVariants(itemsNumber: $itemsNumber, page: $page){
      status
                            arMessage
    message
    variants{  
      page
      totalPages
      totalItems
      hasNextPage
      items{
        _id
        variantAttributes{
          variantSize{
            unit
            size
          }
          variantColor{
            colorName
            colorFamily
            colorIcon
          }
          variantCustomOptions{
            attributeValue
            attributeTitle
          }
        }
        variantEIN
        variantPrice
        variantName
        variantImages
        productId{
             _id
         productStatus
      isHidden
      isFreeDeliveryItem
      productBrand{
        brandName
      }
      productSubCategory{
        subCategoryName
      }
        }
      }
    }
  }
  }
''';

  static String viewShop = r'''
                        query getShopInfo {
                          action: getShopInfo {

       totalProductsCount
draftProductsCount
acceptedProductsCount
pendingProductsCount
outOfStockProductsCount
minimumQuantityAlertProductsCount
freeDeliveryItemProductsCount
viewsCount
  shop{
     validationHistory{
        updatedAt
        returnValues
        returnMessage
        status
      }
    _id
    shopRate
    shopName
    followerCount
    shopLogo
    shopBanner
    shopSlogan
    shopDescription
shopStatus
   isCompleted
    shopTermsAndConditions
    freeDeliveryTarget
    targetPriceForFdt
        shopContactDetails{
            phoneNumber
            whatsUpPhoneNumber
            email
          }
          
          shopPickupAddresses{
            _id
            pickUpCity
        isUsedInOrder
           
            pickUpAddress
            pickUpMapLocation{
              coordinates
              _id
            }
            pickUpContactLandNumber{
              number
            }
            pickUpContactMobileNumber{
              number
              isVerified
            }
            createdAt
            updatedAt
            
          }
  }
    
  
  status
                            arMessage
  message
                          }
                        }
                      ''';
}
