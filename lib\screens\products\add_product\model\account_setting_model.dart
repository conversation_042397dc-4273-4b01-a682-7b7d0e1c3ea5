class AccountSettingModel {
  final String? typename;
  final int? status;
  final String? message;
  final AccountSetting? accountSetting;

  AccountSettingModel({
    this.typename,
    this.status,
    this.message,
    this.accountSetting,
  });

  AccountSettingModel.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      status = json['status'] as int?,
      message = json['message'] as String?,
      accountSetting = (json['accountSetting'] as Map<String,dynamic>?) != null ? AccountSetting.fromJson(json['accountSetting'] as Map<String,dynamic>) : null;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'status' : status,
    'message' : message,
    'accountSetting' : accountSetting?.toJson()
  };
}

class AccountSetting {
  final String? typename;
  final bool? isUserActive;
  final bool? isShopActive;
  final bool? isShopHidden;
  final bool? isAccountBanned;
  final bool? isSupplierActive;
  final bool? isAccountDisabled;
  final bool? acceptOrderEmails;

  AccountSetting({
    this.typename,
    this.isUserActive,
    this.isShopActive,
    this.isShopHidden,
    this.isAccountBanned,
    this.isSupplierActive,
    this.isAccountDisabled,
    this.acceptOrderEmails,
  });

  AccountSetting.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      isUserActive = json['isUserActive'] as bool?,
      isShopActive = json['isShopActive'] as bool?,
      isShopHidden = json['isShopHidden'] as bool?,
      isAccountBanned = json['isAccountBanned'] as bool?,
      isSupplierActive = json['isSupplierActive'] as bool?,
      isAccountDisabled = json['isAccountDisabled'] as bool?,
      acceptOrderEmails = json['acceptOrderEmails'] as bool?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'isUserActive' : isUserActive,
    'isShopActive' : isShopActive,
    'isShopHidden' : isShopHidden,
    'isAccountBanned' : isAccountBanned,
    'isSupplierActive' : isSupplierActive,
    'isAccountDisabled' : isAccountDisabled,
    'acceptOrderEmails' : acceptOrderEmails
  };
}