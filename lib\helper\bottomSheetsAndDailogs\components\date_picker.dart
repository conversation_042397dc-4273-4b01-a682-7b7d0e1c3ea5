import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';

class DatePicker extends StatelessWidget {
  final RxString selectedDates = ''.obs, strError = ''.obs;

  DatePicker({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(20),
          topLeft: Radius.circular(20),
        ),
        color: AppColors.backgroundColorDark,
      ),
      child: Column(
        children: <Widget>[
          Container(
            margin: const EdgeInsets.all(10),
            width: 90,
            height: 5,
            decoration: BoxDecoration(
                color: Colors.grey, borderRadius: BorderRadius.circular(5)),
          ),
          SizedBox(
            height: 50,
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                Obx(
                  () => Text(
                    selectedDates.value,
                    style: FontStyles.fontRegular(),
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () {
                      if (selectedDates.value.isEmpty) {
                        strError.value = ('Please Select Your Own Date Range');
                      } else {
                        Get.back(result: selectedDates.value);
                      }
                    },
                    child: Text(
                      EnStrings.done,
                      style: FontStyles.fontMedium(),
                    ),
                  ),
                )
              ],
            ),
          ),
          ElbaabNetworkEroor(strError: strError),
          CalendarDatePicker(
            initialDate: DateTime.now(),
            firstDate: DateTime.now(),
            lastDate: DateTime.now().add(const Duration(days: (365 * 5))),
            onDateChanged: (v) => selectedDates.value =
                DateFormat('yyyy-MM-dd').format(v).toString(),
          )
        ],
      ),
    );
  }
}
