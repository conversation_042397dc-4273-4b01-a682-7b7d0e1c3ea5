import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

import 'fade_slide_transition.dart';

class Header extends StatelessWidget {
  final Animation<double> animation;
  final bool isSignup;
  const Header({
    Key? key,
    required this.animation,
    this.isSignup = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Padding(
      padding: const EdgeInsets.only(left: kLeftSpace, right: kRightSpace),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          FadeSlideTransition(
            animation: animation,
            additionalOffset: 0.0,
            child: Image.asset("assets/images/app_icon.png",
                height: 48.0, width: 48.0),
          ),
          FadeSlideTransition(
            animation: animation,
            additionalOffset: 0.0,
            child: Text(
              isSignup ? appLocal.welcomeBack : appLocal.welcomeAgain,
              style: FontStyles.fontSemibold(fontSize: 25),
            ),
          ),
          FadeSlideTransition(
            animation: animation,
            additionalOffset: 16.0,
            child: Text(
              isSignup ? appLocal.signupDescription : appLocal.loginDescription,
              style: FontStyles.fontRegular(
                color: Colors.white.withOpacity(0.55),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
