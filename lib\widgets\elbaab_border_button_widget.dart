import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';

class ElbaabBorderButtonWidget extends StatelessWidget {
  final String text;
  final double height;
  final Function onPress;
  final EdgeInsets? edgeInsets;

  const ElbaabBorderButtonWidget({
    Key? key,
    required this.onPress,
    required this.text,
     this.height = 42,
    this.edgeInsets,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: edgeInsets,
      height: height,
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.colorPrimary,
          width: 1,
        ),
        color: AppColors.headerColorDark,
        borderRadius: BorderRadius.circular(10),
      ),
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(Colors.transparent),
          shadowColor: MaterialStateProperty.all(Colors.transparent),
          minimumSize: MaterialStateProperty.all(
              Size(MediaQuery.of(context).size.width, height)),
        ),
        onPressed: () => onPress(),
        child: Text(
          text,
          style: FontStyles.fontRegular(),
        ),
      ),
    );
  }
}
