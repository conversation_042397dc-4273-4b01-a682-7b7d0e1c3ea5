{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98160d958c70f7fbbdb6ef8eca9971b9ba", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983ae0088fc560d6d4281b4554810454f2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b24dc6ff23750b58329f1cc712bde717", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984c0e82b260d5191e288111b3a052ff1c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b24dc6ff23750b58329f1cc712bde717", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "OTHER_SWIFT_FLAGS": "$(inherited) -skip-privacy-manifest-validation", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98167026f4e6ff119d94e6edec769cc6c3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98016fbf75150042d54c0bca1251b2313f", "guid": "bfdfe7dc352907fc980b868725387e981990851746ba5d513f8eed0d5b73a4ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4932ddba5babb54d809925df9b7ae1c", "guid": "bfdfe7dc352907fc980b868725387e98923ee2928691d7bfcbacf9740ce34041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6e339ab1cc9b9031fa3fe531fd6c304", "guid": "bfdfe7dc352907fc980b868725387e98d10ec645af294b1cff466c457b6e886c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a66dbf5e7c452816583a4a0026da87ec", "guid": "bfdfe7dc352907fc980b868725387e98f1f61d3082729eaf4e2da80c17fcb812"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820f0c1ca97cd2a5b8abf4e5abb7ed23e", "guid": "bfdfe7dc352907fc980b868725387e98fe94b403ea357d2cbeb0f8e87dc46050"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98336e0a3e83b702ac350ed1d10c43e67f", "guid": "bfdfe7dc352907fc980b868725387e9872aac5b91926d9966d722d894e97e7f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988910d5ebe32ecde04866648b0184c7c3", "guid": "bfdfe7dc352907fc980b868725387e9800af5f247220a2ccddae6df3af26d74a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7065d5572e194552ae759d71472658c", "guid": "bfdfe7dc352907fc980b868725387e98c2c33042d7a8041b53d7bce711233b86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6dac369dede0886775d4739b1112ad8", "guid": "bfdfe7dc352907fc980b868725387e98670dc7afd485ae0c5b295d0ee79ba3fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860c9d89de8f56d04492e4495ac45f9ac", "guid": "bfdfe7dc352907fc980b868725387e98a195439fb20c084bcbec76044c645646"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986778e16e3a8d2328d27dc9afc4939e7d", "guid": "bfdfe7dc352907fc980b868725387e98e3511df21d565c9cff9f99e0cdf568f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f84ce8591b8e1980e677adb163d7d7bf", "guid": "bfdfe7dc352907fc980b868725387e98365afb451a7c9d1197b34949ad5c5407", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c646ff46eb0e9e501ff257fe9642752", "guid": "bfdfe7dc352907fc980b868725387e981c79c4f788253e4c11ef19bc033f4df3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899732e1020f7f7e1e52cd385ef78b0fc", "guid": "bfdfe7dc352907fc980b868725387e981ff20ed265d5bcbb30d69f1c710276e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98274fa8ddb147a79e1545c62c30feb9c3", "guid": "bfdfe7dc352907fc980b868725387e983b4680d0ba222d1d813f211f6db3c50e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98848df10bf1034189e7936f68fff918e5", "guid": "bfdfe7dc352907fc980b868725387e98faa4522c0c0356bf3b06c786c0fe37fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc9b84f1bcf625f4f53fd3f6c53b4292", "guid": "bfdfe7dc352907fc980b868725387e98d55b898240a38b686dd2c548205afe65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5dec4a0af5381f6c12df1f5c25c85f", "guid": "bfdfe7dc352907fc980b868725387e98adc708f90e645ad9de005fe67ccb588e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc033ba5e3cbb8bb8a809d2bc1471cd2", "guid": "bfdfe7dc352907fc980b868725387e9874081093039c957d160ee2f3d472e8c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e57f83ae15d5fde479163d648309c2", "guid": "bfdfe7dc352907fc980b868725387e980b6ac041ee998fbfde80a92cd68af751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1ba364159f3ea840e2014ecca5c5223", "guid": "bfdfe7dc352907fc980b868725387e981fa7873d3e7470900f4e8174aa2517da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f8df49b8ffcb0ed28295b452f222226", "guid": "bfdfe7dc352907fc980b868725387e981b9c73aaf6c0717681933792a1f5da50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a267c87455ba7cf6dcfa7e5650823d18", "guid": "bfdfe7dc352907fc980b868725387e98b506935289abb369c3d38a0cae3269a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886cf08af69f8e8661e4eefb7b00d21ba", "guid": "bfdfe7dc352907fc980b868725387e9867a4271f6ecc5a91d47be30c13b52d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2fef402fef0dd125ce9df4c24f7027", "guid": "bfdfe7dc352907fc980b868725387e98fc2d7efbe7ecd12de72e1433a0723767"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989890f690caf05cb37b203522dac830fb", "guid": "bfdfe7dc352907fc980b868725387e98b7df2326fb6bd7202e5c06150be098ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2b0f3a1311a60bc532769d11788a738", "guid": "bfdfe7dc352907fc980b868725387e988dc07c44964f64c530147d472750ecb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981307553586bc6b331f9a952a7b27d883", "guid": "bfdfe7dc352907fc980b868725387e9899e415cda3b9a5c7e5ffdc7a92359ef6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98689a709423e8475aeb2289652b39eae2", "guid": "bfdfe7dc352907fc980b868725387e98b4612f1b3fa95bfe32da2089e5c4995a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a50248232fc724c81bf0f403fcb7d1fc", "guid": "bfdfe7dc352907fc980b868725387e9885de3a35c36f1859e3031febb777634c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b403e241bf2d624b2353cdae77f1706b", "guid": "bfdfe7dc352907fc980b868725387e9871a44233ffbfe58b963a890eab38eb5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d41af6253385493ce6338f389daac37", "guid": "bfdfe7dc352907fc980b868725387e98eae3807d1faf615b1186d2f4f3e6d554"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b900e900792133b1ce7da7d996a26a1a", "guid": "bfdfe7dc352907fc980b868725387e98cbfa52ffe1b2e0cdd9cd77452567b38b"}], "guid": "bfdfe7dc352907fc980b868725387e98633819fc6aebc1b37a8c87fc4ffdf88a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b7eaf7f0dca019b9dea4417e1d3e310", "guid": "bfdfe7dc352907fc980b868725387e9862741dfcca6aacee01e5552d3ca66381"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803146240432e80a7e4701f84a84b1903", "guid": "bfdfe7dc352907fc980b868725387e98916e527521af15f57495be076731bccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5005203676997bb521fb87b0f545ddf", "guid": "bfdfe7dc352907fc980b868725387e98e6e28a47d294dc99525cd6f9bad25430"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831145d8a55a65c2be50b513a6f356871", "guid": "bfdfe7dc352907fc980b868725387e98441c79c402983a42a09dbef1d18329d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829c6bc86a680ceb7aa7d577173671d2e", "guid": "bfdfe7dc352907fc980b868725387e98cc9464796b6059a51de75c2ea08304fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e00ed6e5391563e91a71931bc87d15f5", "guid": "bfdfe7dc352907fc980b868725387e987f6f5682f2033d6a855482569f774924"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbd9ffaeca0a01e85affe1a8da7eb460", "guid": "bfdfe7dc352907fc980b868725387e9818706272b7070dc579058e067e71ed2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813472e2c91cca29291e28c62df3a97eb", "guid": "bfdfe7dc352907fc980b868725387e98486e4fd8c9f414062b4e7603203754c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cc196e09d5598ad5ea6628e0c9b2a5d", "guid": "bfdfe7dc352907fc980b868725387e98a07558b83f9e89b7470dd21e2c95d4f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881eef081c580d407b04d1fd2f38c6378", "guid": "bfdfe7dc352907fc980b868725387e9819e9ec41cf4ba589829f359d13913c0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5ed8191dd9d01169aaf88e70f3328fa", "guid": "bfdfe7dc352907fc980b868725387e9804e1d1e15279f17dda950a9efc020463"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98359049940af66100e57775621e504988", "guid": "bfdfe7dc352907fc980b868725387e981ef4e325a2f833ea60beb0f04e135723"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863a90635039c80ffdfa08d5c89cd84e2", "guid": "bfdfe7dc352907fc980b868725387e982402c5a2c6d7b1428ba11adb7ebd3741"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6d4f3c82e106ca33f059595ba474609", "guid": "bfdfe7dc352907fc980b868725387e98b8b9d0c39d70ca916b14fbf6e8a51eb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e624bdda89f1959b66db8a50db813e72", "guid": "bfdfe7dc352907fc980b868725387e989564f4797a90e3301e8556ac9e39fb48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833770a68930749b87116c6a6fd0cb215", "guid": "bfdfe7dc352907fc980b868725387e985cb1fedcead4e82f770b147c2d803986"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981234cf1e30a5f22e30781b6e95d35b4a", "guid": "bfdfe7dc352907fc980b868725387e98c727179f5db7f46dcf1b732cc93abd03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b8a73f2e8282bf20edb90564f71b696", "guid": "bfdfe7dc352907fc980b868725387e987069a8a369de46d97747e7b20005e95d"}], "guid": "bfdfe7dc352907fc980b868725387e984c225b378f75dabe882a628b50042b38", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804978d2586437d7191a6f1475778216e", "guid": "bfdfe7dc352907fc980b868725387e987ac2e2b7513e61443e2b34eea3c94fbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3a06ca66f4cff56a5c2a0fbbee9848a", "guid": "bfdfe7dc352907fc980b868725387e98ac515d01116fdc5266ce69b4be0f5c89"}], "guid": "bfdfe7dc352907fc980b868725387e98d08b27eca42e442106d5e1a1c2fbd9a2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988b36dcd6251f1a1718c20be71ee7e458", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98897606b764fc587e4fb8f93a638d625d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}