import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';

class InputValidationUtil {
  static const patternPhone = r'(^(?:[+0]9)?[0-9]{9,14}$)';
  static const Pattern passwordMinLen8withLowerCaseAndSpecialChar =
      r'^((?=.*\d)(?=.*[a-z])(?=.*[\W_]).{8,20})';
  static const Pattern passwordMinLenWithOneCharAndNumber =
      r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,}$';

  static bool _isPhoneValid(String phone) {
    RegExp regexPhone = RegExp(patternPhone);
    return regexPhone.hasMatch(phone);
  }

  static bool _isContactValid(String phone) {
    RegExp regexPhone = RegExp(r'(^(?:[+0]9)?[0-9]{10}$)');
    return regexPhone.hasMatch(phone);
  }

  static bool _isPasswordValid(String password) {
    RegExp regexPassword = RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,}$');
    return regexPassword.hasMatch(password);
  }

  @protected
  String? validateContactNumber(String? phone,
      {String? errorMessage,
      String? serverValue,
      bool? isReturend,
      bool isOptionalFeild = false}) {
    final appLocal = AppLocalizations.of(Get.context!)!;

    if (isReturend == false) {
      errorMessage = null;
    }
    if (phone!.isEmpty) {
      if (isOptionalFeild) {
        return null;
      } else if (errorMessage == null) {
        return appLocal.phoneNumber;
      } else {
        return errorMessage;
      }
    } else if (phone.isURL) {
      return appLocal.urlNotAccepted;
    } else if (isReturend ?? false) {
      if (phone != serverValue) {
        if (_isContactValid(phone)) {
          return null;
        } else {
          return appLocal.invalidPhoneNumber;
        }
      } else {
        return errorMessage;
      }
    } else if (_isContactValid(phone)) {
      return null;
    } else {
      return appLocal.invalidPhoneNumber;
    }
  }

  static String validatePhone(String phone) {
    final appLocal = AppLocalizations.of(Get.context!)!;
    if (phone.isEmpty) {
      return appLocal.phoneNumber;
    } else if (phone.isURL) {
      return appLocal.urlNotAccepted;
    } else if (_isPhoneValid(phone)) {
      return appLocal.valid;
    } else {
      return appLocal.invalidPhoneNumber;
    }
  }

  @protected
  String? validateEmail(String? email,
      {String? errorMessage,
      String? serverValue,
      bool? isReturend,
      bool isOptionalFeild = false}) {
    final appLocal = AppLocalizations.of(Get.context!)!;
    if (isReturend == false) {
      errorMessage = null;
    }
    if (email!.isEmpty) {
      if (isOptionalFeild) {
        return null;
      } else {
        return appLocal.email;
      }
    } else if (!GetUtils.isEmail(email)) {
      return appLocal.invalidEmail;
    } else if ((isReturend ?? false) || (serverValue?.isNotEmpty ?? false)) {
      if (email != serverValue) {
        return null;
      } else {
        return errorMessage;
      }
    } else if (GetUtils.isEmail(email)) {
      return null;
    } else {
      if (errorMessage == null) {
        return appLocal.invalidEmail;
      } else {
        return errorMessage;
      }
    }
  }

  @protected
  String? validateFieldEmpty(String? input,
      {String? errorMessage,
      FocusNode? focusNode,
      String? serverValue,
      bool? isReturend,
      bool isOptionalFeild = false}) {
    final appLocal = AppLocalizations.of(Get.context!)!;
    if (isReturend == false) {
      errorMessage = null;
    }
    if (input != null) {
      input = input.trimRight();
    }

    if (serverValue != null) {
      serverValue = serverValue.trimRight();
    }

    if (input!.isEmpty) {
      if (isOptionalFeild) {
        return null;
      } else if (errorMessage == null) {
        if (focusNode != null) {
          focusNode.requestFocus();
        }
        return appLocal.fieldRequired;
      } else {
        return errorMessage;
      }
    } else if (input.isURL) {
      return appLocal.urlNotAccepted;
    } else if (isReturend ?? false) {
      if (input != serverValue) {
        return null;
      } else {
        return errorMessage;
      }
    } else {
      return null;
    }
  }

  @protected
  String? validateResetFeild(String? text) {
    final appLocal = AppLocalizations.of(Get.context!)!;
    if (text!.isEmpty) {
      return appLocal.fieldRequired;
    } else if (text.startsWith("+971") || text.startsWith("0")) {
      if (_isPhoneValid(text)) {
        return null;
      } else {
        return appLocal.invalidPhoneNumber;
      }
    } else if (text.isURL) {
      return appLocal.urlNotAccepted;
    } else if (GetUtils.isEmail(text)) {
      return null;
    } else {
      if (text.startsWith("+971") || text.startsWith("0")) {
        return appLocal.invalidText;
      } else {
        return appLocal.invalidEmail;
      }
    }
  }

  String? validatePassword(String? password,
      {String? errorMessage, String? previousPassword}) {
    final appLocal = AppLocalizations.of(Get.context!)!;
    if (password!.isEmpty) {
      return appLocal.password;
    } else if (previousPassword != null && password != previousPassword) {
      return appLocal.passwordDoesNotMatch;
    } else if (password.isURL) {
      return appLocal.urlNotAccepted;
    } else if (password.length >= 6) {
      return null;
    } else {
      return errorMessage ?? appLocal.invalidPassword;
    }
  }
}
