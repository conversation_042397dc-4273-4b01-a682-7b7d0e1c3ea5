class WithDrawalsModel {
  PaginatedWithDrawalModel? withdrawals;

  int? status;
  String? message;

  WithDrawalsModel({
    this.withdrawals,
    this.status,
    this.message,
  });

  factory WithDrawalsModel.fromJson(Map<String, dynamic> json) {
    return WithDrawalsModel(
      withdrawals: json['withdrawals'] != null
          ? PaginatedWithDrawalModel.fromJson(json['withdrawals'])
          : null,
      status: json['status'],
      message: json['message'],
    );
  }
}

class PaginatedWithDrawalModel {
  List<WithDrawals>? items;
  bool? hasNextPage;
  int? totalPages;
  int? totalItems;
  int? page;

  PaginatedWithDrawalModel({
    this.items,
    this.hasNextPage,
    this.totalPages,
    this.totalItems,
    this.page,
  });

  factory PaginatedWithDrawalModel.fromJson(Map<String, dynamic> json) {
    return PaginatedWithDrawalModel(
      items: json['items'] != null
          ? (json['items'] as List).map((e) => WithDrawals.fromJson(e)).toList()
          : null,
      hasNextPage: json['hasNextPage'],
      totalPages: json['totalPages'],
      totalItems: json['totalItems'],
      page: json['page'],
    );
  }
}

class WithDrawals {

  String? withdrawalId;
  dynamic requestedAmount;
  dynamic withdrawalFee;
  String? status;
  String? createdAt;
  String? updatedAt;
  String? receipt;

  WithDrawals({
    this.withdrawalId,
    this.requestedAmount,
    this.withdrawalFee,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.receipt,
  });

  factory WithDrawals.fromJson(Map<String, dynamic> json) {
    return WithDrawals(
        withdrawalId: json['withdrawalId'],
        requestedAmount: json['requestedAmount'],
        withdrawalFee: json['withdrawalFee'],
        status: json['status'],
        updatedAt: json['updatedAt'],
        receipt: json['receipt'],
        createdAt: json['createdAt']);
  }
}
