class NotificationsModel {
        final String? typename;
  final int? status;
  final String? message;
  final Notifications? notifications;

  NotificationsModel({
    this.typename,
    this.status,
    this.message,
    this.notifications,
  });

  NotificationsModel.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      status = json['status'] as int?,
      message = json['message'] as String?,
      notifications = (json['notifications'] as Map<String,dynamic>?) != null ? Notifications.fromJson(json['notifications'] as Map<String,dynamic>) : null;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'status' : status,
    'message' : message,
    'notifications' : notifications?.toJson()
  };
}
class Notifications {
  final String? typename;
  final List<Items>? items;
  final bool? hasNextPage;
  final int? totalPages;
  final int? page;

  Notifications({
    this.typename,
    this.items,
    this.hasNextPage,
    this.totalPages,
    this.page,
  });

  Notifications.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      items = (json['items'] as List?)?.map((dynamic e) => Items.fromJson(e as Map<String,dynamic>)).toList(),
      hasNextPage = json['hasNextPage'] as bool?,
      totalPages = json['totalPages'] as int?,
      page = json['page'] as int?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'items' : items?.map((e) => e.toJson()).toList(),
    'hasNextPage' : hasNextPage,
    'totalPages' : totalPages,
    'page' : page
  };
}

class Items {
  final String? typename;
  final String? title;
  final String? createdAt;
  final String? id;
  final bool? isViewed;
  final String? body;
  final Items? ar;

  Items({
    this.typename,
    this.title,
    this.createdAt,
    this.id,
    this.isViewed,
    this.body,
    this.ar
  });

  Items.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      title = json['title'] as String?,
      createdAt = json['createdAt'] as String?,
      id = json['_id'] as String?,
      ar = (json['ar'] as Map<String,dynamic>?) != null ? Items.fromJson(json['ar'] as Map<String,dynamic>) : null,
      isViewed = json['isViewed'] as bool?,
      body = json['body'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'title' : title,
    'createdAt' : createdAt,
    '_id' : id,
    'ar' : ar?.toJson(),
    'isViewed' : isViewed,
    'body' : body
  };
}
class SortedNotification {
  final String date;
  final List<Items> notifications;

  SortedNotification({
    required this.date,
    required this.notifications,
  });
}
