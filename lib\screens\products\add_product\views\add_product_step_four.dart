import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/add_product/return_duration_model.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';
import 'package:overolasuppliers/widgets/elbaab_carousel_feild_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:translator/translator.dart';

import '../../../../model/add_product/warranty_duration_model.dart';

class AddProductStepFour extends StatelessWidget
    with InputValidationUtil
    implements ServerResponse {
  late GraphQlInitilize _grapghRequest;
  final controller = Get.find<AddProductController>();

  AddProductStepFour({super.key});
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    log("pollicies ${controller.txtProductPolicyAr.text} ==  ${controller.txtProductPolicy.text}");
    Future.delayed(Duration.zero, () {
      _grapghRequest = GraphQlInitilize(this);
      _grapghRequest.runQueryWithCache(
          context: context,
          isRequiredLoader: false,
          query: GraphQlQuries.getAllReturnDuration,
          type: "ReturnDuration");
      _grapghRequest.runQueryWithCache(
          context: context,
          isRequiredLoader: false,
          query: GraphQlQuries.getAllWarrantyDuration,
          type: "warrantyDuration");
      if (controller.validationHistory?.returnValues != null) {
        controller.policyFormKey.currentState!.validate();
      }
    });
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      controller: controller.stepperHideController,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Form(
          key: controller.policyFormKey,
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    Row(
                      children: <Widget>[
                        Expanded(
                          child: Text(
                            appLocal.isFreeDeliveryProduct,
                            style: FontStyles.fontMedium(fontSize: 15),
                          ),
                        ),
                        IconButton(
                          onPressed: () =>
                              BottomSheets.showAlertMessageBottomSheet(
                                  appLocal.isFreeDeliveryProductMessage,
                                  appLocal.alert,
                                  context),
                          icon: Icon(
                            Icons.info,
                            color: AppColors.colorPrimary,
                          ),
                        ),
                        // const Spacer(),
                        Obx(
                          () => Switch(
                            value: controller.freeDelivery.value,
                            onChanged: (value) =>
                                controller.freeDelivery.value = value,
                            activeColor: AppColors.colorPrimary,
                          ),
                        )
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: <Widget>[
                        Expanded(
                          child: Text(
                            appLocal.acceptReturn,
                            style: FontStyles.fontMedium(fontSize: 15),
                          ),
                        ),
                        Obx(
                          () => Switch(
                            value: controller.acceptReturn.value,
                            onChanged: (value) => {
                              controller.acceptReturn.value = value,
                              if (!value) {controller.returnType.value = ""}
                            },
                            activeColor: AppColors.colorPrimary,
                          ),
                        )
                      ],
                    ),
                    Obx(
                      () => controller.acceptReturn.value
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                ListTile(
                                  contentPadding: EdgeInsets.zero,
                                  onTap: () =>
                                      controller.returnType.value = '0',
                                  leading: controller.returnType.value == '0'
                                      ? Icon(Icons.radio_button_checked,
                                          color: AppColors.colorPrimary)
                                      : Icon(
                                          Icons.circle,
                                          color: Colors.white.withOpacity(0.1),
                                        ),
                                  title: Text(
                                    appLocal.freeReturnTitle,
                                    style: FontStyles.fontRegular(),
                                  ),
                                  subtitle: Text(
                                    appLocal.freeReturnMessage,
                                    style: FontStyles.fontRegular(
                                      fontSize: 12,
                                      color: Colors.white.withOpacity(0.6),
                                    ),
                                  ),
                                ),
                                ListTile(
                                  contentPadding: EdgeInsets.zero,
                                  onTap: () =>
                                      controller.returnType.value = '1',
                                  leading: controller.returnType.value == '1'
                                      ? Icon(Icons.radio_button_checked,
                                          color: AppColors.colorPrimary)
                                      : Icon(
                                          Icons.circle,
                                          color: Colors.white.withOpacity(0.1),
                                        ),
                                  title: Text(
                                    appLocal.notFreeReturnTitle,
                                    style: FontStyles.fontRegular(),
                                  ),
                                  subtitle: Text(
                                    appLocal.notFreeReturnMessage,
                                    style: FontStyles.fontRegular(
                                      fontSize: 12,
                                      color: Colors.white.withOpacity(0.6),
                                    ),
                                  ),
                                ),
                                AnimatedContainer(
                                  duration: const Duration(seconds: 1),
                                  curve: Curves.easeInCirc,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    color: controller.acceptReturn.value
                                        ? Colors.black.withOpacity(0.2)
                                        : Colors.transparent,
                                  ),
                                ),
                              ],
                            )
                          : const SizedBox.shrink(),
                    ),
                    if ((controller.validationHistory?.returnValues
                            ?.contains("Does The Product Accepts Return") ??
                        false))
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            appLocal.adminReturnConditionNote,
                            style: FontStyles.fontRegular(
                              fontSize: 12,
                              color: AppColors.colorDanger,
                            ),
                          ),
                        ),
                      ),
                    Obx(
                      () => controller.acceptReturn.value
                          ? ElbaaabInputTextField(
                              margin: const EdgeInsets.only(top: 24),
                              onChanged: (value) {},
                              onTap: () => BottomSheets.showListPicker(
                                      context,
                                      controller.returnDurationList
                                          .map((e) =>
                                              appLocal.localeName == "en"
                                                  ? (e.returnDuration ?? "")
                                                  : (e.returnDurationsAr
                                                          ?.returnDuration ??
                                                      ""))
                                          .toList())
                                  .then((value) {
                                controller.returnPolicy.value = controller
                                        .returnDurationList[value]
                                        .returnDuration ??
                                    "";

                                controller.returnPolicyAr.value = controller
                                        .returnDurationList[value]
                                        .returnDurationsAr
                                        ?.returnDuration ??
                                    "";
                              }),
                              initialValue: appLocal.localeName == "en"
                                  ? controller.returnPolicy.value
                                  : controller.returnPolicyAr.value,
                              validator: (v) => validateFieldEmpty(
                                v,
                                errorMessage: appLocal.adminReturnDurationNote,
                                serverValue: controller.product?.productPolicies
                                        ?.productReturnDuration ??
                                    "",
                                isReturend: (controller
                                        .validationHistory?.returnValues
                                        ?.contains("Return Duration") ??
                                    false),
                                isOptionalFeild: (!(controller
                                            .validationHistory?.returnValues
                                            ?.contains("Return Duration") ??
                                        false) &&
                                    (!controller.acceptReturn.value)),
                              ),
                              textDirection: appLocal.localeName == "en"
                                  ? TextDirection.ltr
                                  : TextDirection.rtl,
                              suffix: const Icon(
                                Icons.arrow_drop_down_outlined,
                                size: 18,
                              ),
                              hint: appLocal.returnDurationFeildHint,
                              label: appLocal.returnDurationFeildLabell,
                            )
                          : Container(),
                    ),
                    Obx(() => controller.returnPolicy.value == 'Other' &&
                            controller.acceptReturn.value
                        ? ElbaaabInputTextField(
                            margin: const EdgeInsets.only(top: 16),
                            onChanged: (value) =>
                                controller.customReturnPolicy = value,
                            initialValue: controller.customReturnPolicy,
                            textDirection: appLocal.localeName == "en"
                                ? TextDirection.ltr
                                : TextDirection.rtl,
                            formatter: [
                              FilteringTextInputFormatter.singleLineFormatter
                            ],
                            validator: (v) => validateFieldEmpty(
                              v,
                              errorMessage: appLocal.adminReturnDurationNote,
                              serverValue: controller.product?.productPolicies
                                      ?.productReturnDuration ??
                                  "",
                              isReturend: (controller
                                      .validationHistory?.returnValues
                                      ?.contains("Return Duration") ??
                                  false),
                              isOptionalFeild: (!(controller
                                          .validationHistory?.returnValues
                                          ?.contains("Return Duration") ??
                                      false) &&
                                  (!controller.acceptReturn.value)),
                            ),
                            charaterlimit: 2,
                            inputFormatter: '[0-9.]',
                            inputType: const TextInputType.numberWithOptions(
                                signed: true, decimal: true),
                            suffix: Container(
                              width: 67,
                              height: 60,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: AppColors.colorPrimary,
                                borderRadius: appLocal.localeName == "en"
                                    ? const BorderRadius.only(
                                        topRight: Radius.circular(10),
                                        bottomRight: Radius.circular(10),
                                      )
                                    : const BorderRadius.only(
                                        topLeft: Radius.circular(10),
                                        bottomLeft: Radius.circular(10),
                                      ),
                              ),
                              child: Text(
                                appLocal.days,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            hint: appLocal.returnDurationFeildHint,
                            label: appLocal.returnDurationSecondFeildLabell,
                          )
                        : Container()),
                    Obx(
                      () => ElbaaabInputTextField(
                        margin: const EdgeInsets.only(top: 24),
                        onChanged: (value) {},
                        onTap: () => BottomSheets.showListPicker(
                                context,
                                controller.warrantyDurationList
                                    .map((e) => appLocal.localeName == "en"
                                        ? (e.warrantyDuration ?? "")
                                        : (e.warrantyDurationsAr
                                                ?.warrantyDuration ??
                                            ""))
                                    .toList())
                            .then((value) => {
                                  controller.warrantyPolicy.value = controller
                                          .warrantyDurationList[value]
                                          .warrantyDuration ??
                                      "",
                                  controller.warrantyPolicyAr.value = controller
                                          .warrantyDurationList[value]
                                          .warrantyDurationsAr
                                          ?.warrantyDuration ??
                                      "",
                                }),
                        initialValue: appLocal.localeName == "en"
                            ? controller.warrantyPolicy.value
                            : controller.warrantyPolicyAr.value,
                        validator: (v) => validateFieldEmpty(
                          v,
                          errorMessage: appLocal.adminWarrantyDurationNote,
                          serverValue: controller.product?.productPolicies
                                  ?.productWarrantyDuration ??
                              "",
                          isReturend: (controller
                                  .validationHistory?.returnValues
                                  ?.contains("Warranty Duration") ??
                              false),
                        ),
                        suffix: const Icon(
                          Icons.arrow_drop_down_outlined,
                          size: 18,
                        ),
                        textDirection: appLocal.localeName == "en"
                            ? TextDirection.ltr
                            : TextDirection.rtl,
                        hint: appLocal.warrantyDurationFeildHint,
                        label: appLocal.warrantyDurationFeildLabel,
                      ),
                    ),
                    Obx(() => controller.warrantyPolicy.value == 'Other'
                        ? ElbaaabInputTextField(
                            margin: const EdgeInsets.only(top: 16),
                            onChanged: (value) =>
                                controller.customWarrantyPolicy = value,
                            initialValue: controller.customWarrantyPolicy,
                            formatter: [
                              FilteringTextInputFormatter.singleLineFormatter
                            ],
                            textDirection: appLocal.localeName == "en"
                                ? TextDirection.ltr
                                : TextDirection.rtl,
                            charaterlimit: 50,
                            validator: (v) => validateFieldEmpty(
                              v,
                              errorMessage: appLocal.adminWarrantyDurationNote,
                              serverValue: controller.product?.productPolicies
                                      ?.productWarrantyDuration ??
                                  "",
                              isReturend: (controller
                                      .validationHistory?.returnValues
                                      ?.contains("Warranty Duration") ??
                                  false),
                            ),
                            inputType: TextInputType.text,
                            hint: appLocal.warrantyDurationFeildHint,
                            label: appLocal.warrantyDurationSecondFeildLabel,
                          )
                        : Container()),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              ElbaabCarouselFeildWidget(aspectRatio: 8 / 5, children: [
                Center(
                  child: ElbaaabInputTextField(
                    height: 190,
                    onChanged: (value) {},
                    editingController: controller.txtProductPolicy,
                    initialValue: controller.txtProductPolicy.text,
                    onFieldSubmitted: (value) {
                      if (appLocal.localeName == 'en') {
                        controller.txtProductPolicyAr.clear();
                        GoogleTranslator().translate(value, to: 'ar').then(
                            (result) => controller.txtProductPolicyAr.text =
                                result.text);
                      }
                    },
                    charaterlimit: 600,
                    requiredCounter: true,
                    validator: (v) => validateFieldEmpty(
                      v,
                      errorMessage: appLocal.adminPolicyReturnNote,
                      serverValue: controller
                              .product?.productPolicies?.productReturnPolicy ??
                          "",
                      isReturend: (controller.validationHistory?.returnValues
                              ?.contains("Policies") ??
                          false),
                      isOptionalFeild: !(controller
                              .validationHistory?.returnValues
                              ?.contains("Policies") ??
                          false),
                    ),
                    inputType: TextInputType.multiline,
                    inputAction: TextInputAction.newline,
                    label: appLocal.policyFeildLabelEnglish,
                    hint: appLocal.policyFeildHint,
                  ),
                ),
                Center(
                  child: ElbaaabInputTextField(
                    height: 190,
                    // onChanged: (value) =>
                    //     controller.txtProductPolicyAr.text = value,
                    onChanged: (value) {},
                    editingController: controller.txtProductPolicyAr,
                    initialValue: controller.txtProductPolicyAr.text,
                    formatter: [
                      FilteringTextInputFormatter.singleLineFormatter
                    ],
                    onFieldSubmitted: (value) {
                      if (appLocal.localeName == 'ar') {
                        controller.txtProductPolicy.clear();
                        GoogleTranslator().translate(value, to: 'en').then(
                            (result) =>
                                controller.txtProductPolicy.text = result.text);
                      }
                    },
                    charaterlimit: 600,
                    requiredCounter: true,
                    textDirection: TextDirection.rtl,
                    validator: (v) => validateFieldEmpty(
                      v,
                      errorMessage: appLocal.adminPolicyReturnNote,
                      serverValue: controller
                              .product?.productPolicies?.productReturnPolicy ??
                          "",
                      isReturend: (controller.validationHistory?.returnValues
                              ?.contains("Policies") ??
                          false),
                      isOptionalFeild: !(controller
                              .validationHistory?.returnValues
                              ?.contains("Policies") ??
                          false),
                    ),
                    inputType: TextInputType.text,
                    label: appLocal.policyFeildLabelArabic,
                    hint: appLocal.policyFeildHint,
                  ),
                ),
              ]),
              Obx(
                () => controller.isRequiredUpdate.value.isNotEmpty
                    ? Padding(
                        padding: const EdgeInsets.only(top: 16.0),
                        child: Text(
                          appLocal.updateReturnData,
                          style: FontStyles.fontMedium(
                              color: AppColors.colorDanger),
                        ),
                      )
                    : Container(),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == 'ReturnDuration') {
      ReturnDurationModel model = ReturnDurationModel.fromJson(response);
      if (model.status == statusOK) {
        controller.returnDurationList.clear();
        controller.returnDurationList.addAll(model.returnDurations ?? []);
        controller.returnDurationList.add(ReturnDurations(
            returnDuration: 'Other',
            returnDurationsAr: ReturnDurations(returnDuration: 'أخرى')));
      }
    } else if (type == "warrantyDuration") {
      WarrantyDurationModel model = WarrantyDurationModel.fromJson(response);
      if (model.status == statusOK) {
        controller.warrantyDurationList.clear();
        controller.warrantyDurationList.addAll(model.warrantyDurations ?? []);
        controller.warrantyDurationList.add(WarrantyDurations(
            warrantyDuration: 'Other',
            warrantyDurationsAr: WarrantyDurations(warrantyDuration: 'أخرى')));
      }
    }
  }
}
