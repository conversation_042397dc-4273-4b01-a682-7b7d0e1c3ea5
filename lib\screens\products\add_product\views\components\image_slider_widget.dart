import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/state_manager.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/products/add_product/controller/add_product_controller.dart';

import '../../../../../model/add_product/view_prduct/product_detail_model.dart';

class ImageSliderWidget extends StatelessWidget {
  final AddProductController? controller;
  final bool isReview;
  final bool isDetailPage;
  final PageController _controller = PageController();
  final PageController? sliderimageController;
  final Product? matchedProduct;
  ImageSliderWidget(
      {Key? key,
      this.controller,
      this.isReview = false,
      this.isDetailPage = false,
      this.matchedProduct,
      this.sliderimageController})
      : super(key: key);

  RxInt colorIndex = 0.obs, currentIndex = 0.obs;
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        Positioned(
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          child: isReview
              ? (matchedProduct?.productOptions?.productColors?.isEmpty ??
                      false)
                  ? PageView.builder(
                      onPageChanged: (value) => currentIndex.value = value,
                      itemCount: matchedProduct?.productImages?.length,
                      itemBuilder: (context, index) => ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: GlobalMethods.netWorkImage(
                            matchedProduct?.productImages?[index] ?? "",
                            BorderRadius.circular(10),
                            BoxFit.contain),
                      ),
                    )
                  : Obx(
                      () => Column(
                        children: [
                          Expanded(
                            child: PageView.builder(
                                controller: _controller,
                                onPageChanged: (value) =>
                                    currentIndex.value = value,
                                itemCount: matchedProduct
                                        ?.productOptions
                                        ?.productColors?[colorIndex.value]
                                        .colorImages
                                        ?.length ??
                                    0,
                                itemBuilder: (context, index) {
                                  return Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: GlobalMethods.netWorkImage(
                                      matchedProduct
                                              ?.productOptions
                                              ?.productColors?[colorIndex.value]
                                              .colorImages?[index] ??
                                          "",
                                      BorderRadius.circular(8),
                                      BoxFit.contain,
                                    ),
                                  );
                                }),
                          ),
                          const SizedBox(height: 10),
                          SizedBox(
                            height: 50,
                            child: ListView.builder(
                                itemCount: matchedProduct
                                    ?.productOptions
                                    ?.productColors?[colorIndex.value]
                                    .colorImages
                                    ?.length,
                                scrollDirection: Axis.horizontal,
                                shrinkWrap: true,
                                itemBuilder: (context, index) {
                                  return Container(
                                    height: 50,
                                    width: 50,
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 6),
                                    child: GlobalMethods.netWorkImage(
                                      matchedProduct
                                              ?.productOptions
                                              ?.productColors?[colorIndex.value]
                                              .colorImages?[index] ??
                                          "",
                                      BorderRadius.circular(5),
                                      BoxFit.contain,
                                    ),
                                  );
                                }),
                          ),
                          const SizedBox(height: 10),
                          Container(
                            height: 50,
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(10),
                                bottomRight: Radius.circular(10),
                              ),
                            ),
                            child: Center(
                              child: ListView.builder(
                                  itemCount: matchedProduct
                                      ?.productOptions?.productColors?.length,
                                  shrinkWrap: true,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder: (context, index) {
                                    return Center(
                                      child: InkWell(
                                        onTap: () => {
                                          colorIndex.value = index,
                                          currentIndex.value = 0,
                                          _controller.animateToPage(0,
                                              duration:
                                                  const Duration(seconds: 1),
                                              curve: Curves.linearToEaseOut)
                                        },
                                        child: Container(
                                          width: 30,
                                          height: 30,
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 6),
                                          padding: EdgeInsets.all(
                                              colorIndex.value == index
                                                  ? 2
                                                  : 0),
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                                width: 2,
                                                color: colorIndex.value == index
                                                    ? AppColors.colorPrimary
                                                    : Colors.transparent),
                                          ),
                                          child: GlobalMethods.netWorkImage(
                                            matchedProduct
                                                    ?.productOptions
                                                    ?.productColors?[index]
                                                    .colorIcon ??
                                                "",
                                            BorderRadius.circular(15),
                                            BoxFit.none,
                                          ),
                                        ),
                                      ),
                                    );
                                  }),
                            ),
                          ),
                        ],
                      ),
                    )
              : (controller != null && controller!.colorList.isNotEmpty)
                  ? Obx(
                      () => Column(
                        children: [
                          Expanded(
                            child: PageView.builder(
                                controller: _controller,
                                onPageChanged: (value) =>
                                    currentIndex.value = value,
                                itemCount: controller!
                                    .colorList[colorIndex.value]
                                    .imagesUrl
                                    .length,
                                itemBuilder: (context, index) {
                                  return Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: GlobalMethods.netWorkImage(
                                      controller!.colorList[colorIndex.value]
                                          .imagesUrl[index],
                                      BorderRadius.circular(8),
                                      BoxFit.contain,
                                    ),
                                  );
                                }),
                          ),
                          const SizedBox(height: 10),
                          SizedBox(
                            height: 50,
                            child: ListView.builder(
                                itemCount: controller!
                                    .colorList[colorIndex.value]
                                    .imagesUrl
                                    .length,
                                scrollDirection: Axis.horizontal,
                                shrinkWrap: true,
                                itemBuilder: (context, index) {
                                  return InkWell(
                                    onTap: () {
                                      _controller.animateToPage(index,
                                          duration: const Duration(seconds: 1),
                                          curve: Curves.linearToEaseOut);
                                    },
                                    child: Obx(
                                      () => Container(
                                        height: 50,
                                        width: 50,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            width: 2,
                                            color: currentIndex.value == index
                                                ? AppColors.colorPrimary
                                                : Colors.transparent,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(5),
                                        ),
                                        padding: const EdgeInsets.all(1),
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 3),
                                        child: GlobalMethods.netWorkImage(
                                          controller!
                                              .colorList[colorIndex.value]
                                              .imagesUrl[index],
                                          BorderRadius.circular(5),
                                          BoxFit.fill,
                                        ),
                                      ),
                                    ),
                                  );
                                }),
                          ),
                          const SizedBox(height: 10),
                          Container(
                            height: 50,
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(10),
                                bottomRight: Radius.circular(10),
                              ),
                            ),
                            child: Center(
                              child: ListView.builder(
                                  itemCount: controller!.colorList.length,
                                  shrinkWrap: true,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder: (context, index) {
                                    return Center(
                                      child: InkWell(
                                        onTap: () => {
                                          colorIndex.value = index,
                                          currentIndex.value = 0,
                                          _controller.animateToPage(0,
                                              duration:
                                                  const Duration(seconds: 1),
                                              curve: Curves.linearToEaseOut)
                                        },
                                        child: Container(
                                          width: 30,
                                          height: 30,
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 6),
                                          padding: EdgeInsets.all(
                                              colorIndex.value == index
                                                  ? 2
                                                  : 0),
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                                width: 2,
                                                color: colorIndex.value == index
                                                    ? AppColors.colorPrimary
                                                    : Colors.transparent),
                                          ),
                                          child: GlobalMethods.netWorkImage(
                                            controller!
                                                .colorList[index].thumnailUrl,
                                            BorderRadius.circular(15),
                                            BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                    );
                                  }),
                            ),
                          ),
                        ],
                      ),
                    )
                  : (controller != null &&
                          (controller?.product != null) &&
                          (controller?.product?.productOptions?.productColors
                                  ?.isNotEmpty ??
                              false) &&
                          (isDetailPage || controller!.colorList.isNotEmpty))
                      ? Obx(
                          () => Column(
                            children: [
                              Expanded(
                                child: PageView.builder(
                                    controller: _controller,
                                    onPageChanged: (value) =>
                                        currentIndex.value = value,
                                    itemCount: controller
                                        ?.product
                                        ?.productOptions
                                        ?.productColors?[colorIndex.value]
                                        .colorImages
                                        ?.length,
                                    itemBuilder: (context, index) {
                                      return Container(
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: GlobalMethods.netWorkImage(
                                          controller
                                                  ?.product
                                                  ?.productOptions
                                                  ?.productColors?[
                                                      colorIndex.value]
                                                  .colorImages?[index] ??
                                              "",
                                          BorderRadius.circular(8),
                                          BoxFit.contain,
                                        ),
                                      );
                                    }),
                              ),
                              const SizedBox(height: 10),
                              SizedBox(
                                height: 50,
                                child: ListView.builder(
                                    itemCount: controller
                                        ?.product
                                        ?.productOptions
                                        ?.productColors?[colorIndex.value]
                                        .colorImages
                                        ?.length,
                                    scrollDirection: Axis.horizontal,
                                    shrinkWrap: true,
                                    itemBuilder: (context, index) {
                                      return InkWell(
                                        onTap: () {
                                          _controller.animateToPage(index,
                                              duration:
                                                  const Duration(seconds: 1),
                                              curve: Curves.linearToEaseOut);
                                        },
                                        child: Obx(
                                          () => Container(
                                            height: 50,
                                            width: 50,
                                            decoration: BoxDecoration(
                                              border: Border.all(
                                                width: 2,
                                                color:
                                                    currentIndex.value == index
                                                        ? AppColors.colorPrimary
                                                        : Colors.transparent,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                            ),
                                            padding: const EdgeInsets.all(1),
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 3),
                                            child: GlobalMethods.netWorkImage(
                                              controller
                                                      ?.product
                                                      ?.productOptions
                                                      ?.productColors?[
                                                          colorIndex.value]
                                                      .colorImages?[index] ??
                                                  "",
                                              BorderRadius.circular(5),
                                              BoxFit.fill,
                                            ),
                                          ),
                                        ),
                                      );
                                    }),
                              ),
                              const SizedBox(height: 10),
                              Container(
                                height: 50,
                                decoration: const BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(10),
                                    bottomRight: Radius.circular(10),
                                  ),
                                ),
                                child: Center(
                                  child: ListView.builder(
                                      itemCount: controller
                                          ?.product
                                          ?.productOptions
                                          ?.productColors
                                          ?.length,
                                      shrinkWrap: true,
                                      scrollDirection: Axis.horizontal,
                                      itemBuilder: (context, index) {
                                        return Center(
                                          child: InkWell(
                                            onTap: () => {
                                              colorIndex.value = index,
                                              currentIndex.value = 0,
                                              _controller.animateToPage(0,
                                                  duration: const Duration(
                                                      seconds: 1),
                                                  curve: Curves.linearToEaseOut)
                                            },
                                            child: Container(
                                              width: 30,
                                              height: 30,
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 6),
                                              padding: EdgeInsets.all(
                                                  colorIndex.value == index
                                                      ? 2
                                                      : 0),
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                    width: 2,
                                                    color: colorIndex.value ==
                                                            index
                                                        ? AppColors.colorPrimary
                                                        : Colors.transparent),
                                              ),
                                              child: GlobalMethods.netWorkImage(
                                                controller
                                                        ?.product
                                                        ?.productOptions
                                                        ?.productColors?[index]
                                                        .colorIcon ??
                                                    "",
                                                BorderRadius.circular(15),
                                                BoxFit.cover,
                                              ),
                                            ),
                                          ),
                                        );
                                      }),
                                ),
                              ),
                            ],
                          ),
                        )
                      : Obx(
                          () => PageView.builder(
                              onPageChanged: (value) =>
                                  currentIndex.value = value,
                              controller: sliderimageController,
                              itemCount: controller!.sliderImages.length,
                              itemBuilder: (context, index) {
                                return ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: Stack(
                                    children: [
                                      Positioned(
                                        top: 0,
                                        bottom: 0,
                                        left: 0,
                                        right: 0,
                                        child: GlobalMethods.netWorkImage(
                                            controller!.sliderImages[index],
                                            BorderRadius.circular(10),
                                            BoxFit.contain),
                                      ),
                                      if ((controller?.validationHistory
                                                  ?.returnValues
                                                  ?.contains(
                                                      "Image ${index + 1}") ??
                                              false) &&
                                          !isReview &&
                                          (controller?.product
                                                  ?.productImages?[index]) ==
                                              controller?.sliderImages[index])
                                        Positioned(
                                          top: 0,
                                          bottom: 0,
                                          left: 0,
                                          right: 0,
                                          child: BackdropFilter(
                                            filter: ImageFilter.blur(
                                                sigmaX: 10, sigmaY: 10),
                                            child: Center(
                                              child: Text(
                                                "Admin Reject This Image",
                                                style: FontStyles.fontBold(
                                                  fontSize: 16,
                                                  color: AppColors.colorDanger,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                );
                              }),
                        ),
        ),
        Positioned(
          left: 0,
          right: 0,
          bottom: controller == null
              ? 20
              : controller!.colorList.isEmpty
                  ? 40
                  : 130,
          child: controller == null || controller!.colorList.isEmpty
              ? (isReview &&
                      (matchedProduct
                              ?.productOptions?.productColors?.isNotEmpty ??
                          false))
                  ? Container()
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        isReview
                            ? (matchedProduct?.productImages?.length ?? 0)
                            : controller!.sliderImages.length,
                        (index) => buildDot(index: index),
                      ),
                    )
              : Obx(
                  () => Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      isReview
                          ? 0
                          : controller!.colorList.isEmpty
                              ? controller!.sliderImages.length
                              : controller!
                                  .colorList[colorIndex.value].imagesUrl.length,
                      (index) => buildDot(index: index),
                    ),
                  ),
                ),
        ),
      ],
    );
  }

  AnimatedContainer buildDot({index}) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(right: 5),
      height: 8,
      width: 8,
      child: Obx(
        () => Container(
          decoration: BoxDecoration(
            color: currentIndex.value == index
                ? AppColors.colorPrimary
                : Colors.grey,
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}
