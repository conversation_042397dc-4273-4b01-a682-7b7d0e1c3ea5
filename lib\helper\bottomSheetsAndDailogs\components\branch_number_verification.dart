import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';


class BranchNumberVerification extends StatelessWidget
    with CodeAutoFill
    implements ServerResponse {
      final String phoneNumber;
      final VoidCallback onVerificationSuccess;
  BranchNumberVerification({super.key, required this.phoneNumber, required this.onVerificationSuccess});

  final ValueNotifier<int> _time = ValueNotifier(90);
  RxString otpCode = "".obs, errorText = "".obs;
  late GraphQlInitilize _request;

  void startTimer() {
    _time.value = 90;
    Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (_time.value == 0) {
          timer.cancel();
        } else {
          _time.value--;
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    _request = GraphQlInitilize(this);
    startTimer();
    listenForCode();
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(10.0).r,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: AppColors.backgroundColorDark,
            ),
            child: Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                alignment: WrapAlignment.center,
                children: [
                  Align(
                    alignment: Alignment.centerRight,
                    child: IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.cancel),
                    ),
                  ),
                  Center(
                    child: Text(
                       appLocal.verificationCode,
                      textAlign: TextAlign.center,
                      style: FontStyles.fontBold(),
                    ),
                  ),
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                              horizontal: 16)
                          .r,
                      child: Text(
                       appLocal.verificationCodeSent
                            ,
                        textAlign: TextAlign.center,
                        style: FontStyles.fontSemibold(),
                      ),
                    ),
                  ),
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16).r,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              phoneNumber,
                              textAlign: TextAlign.center,
                              style: FontStyles.fontSemibold(),
                            ),
                            TextButton(
                              onPressed: () => Get.back(),
                              child: Text(
                                appLocal.changePhoneNumber,
                                style: FontStyles.fontMedium(
                                  fontSize: 12,
                                  color: AppColors.colorPrimary,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  Obx(
                      () => Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 30),
                        child: TextFieldPinAutoFill(
                          decoration: InputDecoration(
                            errorText: errorText.value.isNotEmpty
                                ? errorText.value
                                : null,
                            errorStyle: FontStyles.fontRegular(
                                color: AppColors.colorDanger),
                            fillColor: AppColors.feildColorDark,
                            filled: true,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: BorderSide(
                                width: 1,
                                color: AppColors.feildBorderColor,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: const BorderSide(
                                width: 1,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          currentCode: otpCode.value,
                          onCodeChanged: (code) {
                            if (code.length == 6) {
                              otpCode.value = code;
                              FocusScope.of(context).requestFocus(FocusNode());
                              _request.runMutation(
                                context: context,
                                query: GraphQlQuries.verifyPickupAdrNumberOtp,
                                type: "Verify",
                                variables: GraphQlVariables.verifyPickupAdrNumberOtp(
                                    phoneNumber: phoneNumber,
                                    otpNumber: code),
                              );
                              otpCode.value = "";
                            }
                          },
                        ),
                      ),
                    ),
                    Center(
                      child: ValueListenableBuilder(
                        valueListenable: _time,
                        builder: (context, value, child) {
                          return Align(
                            alignment: Alignment.center,
                            child: value == 0
                                ? InkWell(
                                    onTap: () {
                                      _request.runMutation(
                                        context: context,
                                        query: GraphQlQuries
                                            .resendPickupAdrNumberOtp,
                                        variables: GraphQlVariables
                                            .sendPickupAdrNumberOtp(
                                                phoneNumber: phoneNumber),
                                      );
                                    },
                                    child: RichText(
                                      text: TextSpan(children: <TextSpan>[
                                        TextSpan(
                                          text: EnStrings.notReceived,
                                          style: FontStyles.fontRegular(
                                              fontSize: 10),
                                        ),
                                        TextSpan(
                                            text: EnStrings.resendOtp,
                                            style: FontStyles.fontRegular(
                                              fontSize: 10,
                                              decoration:
                                                  TextDecoration.underline,
                                              color: AppColors.colorPrimary,
                                            )),
                                      ]),
                                    ),
                                  )
                                : Text(
                                    appLocal.secondRemaing('$value'),
                                    style: FontStyles.fontRegular(fontSize: 15),
                                  ),
                          );
                        },
                      ),
                    ),
                  ElbaabGradientButtonWidget(
                    edgeInsets: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 50),
                    onPress: () async {
                     if (otpCode.value.isNotEmpty &&
                            otpCode.value.length > 5) {
                          _request.runMutation(
                            context: context,
                            query: GraphQlQuries.verifyPickupAdrNumberOtp,
                            type: "Verify",
                            variables:
                                GraphQlVariables.verifyPickupAdrNumberOtp(
                                    phoneNumber: phoneNumber,
                                    otpNumber: otpCode.value),
                          );
                        } else {
                          errorText.value = appLocal.requiredOtpCode;
                        }
                    },
                    text:  appLocal.verify,
                  ),
                ],
              ),
            
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {
    BaseModel model = BaseModel.fromJson(error);
    errorText.value = model.message ?? "";
  }

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (model.status == statusOK) {
      onVerificationSuccess();
    }
  }

  @override
  void codeUpdated() {
    otpCode.value = code!;
    _request.runMutation(
      context: Get.context!,
      query: GraphQlQuries.verifyPickupAdrNumberOtp,
      isLoader: false,
      type: "Verify",
      variables: GraphQlVariables.verifyPickupAdrNumberOtp(
          phoneNumber: phoneNumber, otpNumber: otpCode.value),
    );
  }
}
