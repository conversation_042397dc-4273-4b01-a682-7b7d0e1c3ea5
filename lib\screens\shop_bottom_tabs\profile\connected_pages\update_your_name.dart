import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/inputFormattersOrValidations/input_validation_util.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/provider/shop_info_provider.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_network_error.dart';
import 'package:provider/provider.dart';

class UpdateUserName extends StatefulWidget {
  const UpdateUserName({super.key});

  @override
  State<UpdateUserName> createState() => _UpdateUserNameState();
}

class _UpdateUserNameState extends State<UpdateUserName>
    with InputValidationUtil
    implements ServerResponse {
  String strName = "";
  RxString strError = "".obs;
  late GraphQlInitilize graphQlInitilize;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    graphQlInitilize = GraphQlInitilize(this);
    strName = context.read<ShopInfoProvider>().userName;
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.changeYourUserName,
        leadingBack: true,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElbaaabInputTextField(
              margin: const EdgeInsets.only(
                  left: kLeftSpace, right: kRightSpace, top: 24),
              onChanged: (v) => strName = v,
              charaterlimit: 50,
              initialValue: context.read<ShopInfoProvider>().userName,
              hint: appLocal.nameHint,
              label: appLocal.newName,
              autoTextDirection: true,
              validator: (v) {
                if (v?.isEmpty ?? false) {
                  return appLocal.fieldRequired;
                } else if ((v ?? "") ==
                    context.read<ShopInfoProvider>().userName) {
                  return appLocal.userNameInUse;
                } else {
                  return null;
                }
              },
              inputFormatter: '[a-zA-Z ]',
              inputType: TextInputType.emailAddress,
            ),
            ElbaabNetworkEroor(strError: strError),
            ElbaabGradientButtonWidget(
              onPress: () {
                if (_formKey.currentState!.validate()) {
                  graphQlInitilize.runMutation(
                    context: context,
                    query: GraphQlQuries.updateUserName,
                    variables:
                        GraphQlVariables.changeUserName(userName: strName),
                  );
                }
              },
              text: appLocal.updateName,
              edgeInsets: const EdgeInsets.only(
                  left: kLeftSpace, right: kRightSpace, top: 50).r,
            )
          ],
        ),
      ),
    );
  }

  @override
  onError(error, String type) {
    strError.value = Get.locale?.languageCode == "ar"
        ? BaseModel.fromJson(error).arMessage ?? ""
        : BaseModel.fromJson(error).message ?? "";
  }

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (model.status == statusOK) {
      prefs.setString(ownerName, strName);
      Provider.of<ShopInfoProvider>(context, listen: false)
          .setShopUserName(strName);
      Get.back();
    } else {
      strError.value = Get.locale?.languageCode == "ar"
          ? model.arMessage ?? ""
          : model.message ?? "";
    }
  }
}
