class SimilarProductModel {
  final String? typename;
  final int? status;
  final String? message;
  final Pagination? pagination;

  SimilarProductModel({
    this.typename,
    this.status,
    this.message,
    this.pagination,
  });

  SimilarProductModel.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      status = json['status'] as int?,
      message = json['message'] as String?,
      pagination = (json['products'] as Map<String,dynamic>?) != null ? Pagination.fromJson(json['products'] as Map<String,dynamic>) : null;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'status' : status,
    'message' : message,
    'products' : pagination?.toJson()
  };
}

class Pagination {
  final String? typename;
  final List<ProductCatelog>? items;
  final int? page;
  final bool? hasNextPage;
  final int? totalPages;
  final int? totalItems;

  Pagination({
    this.typename,
    this.items,
    this.page,
    this.hasNextPage,
    this.totalPages,
    this.totalItems,
  });

  Pagination.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      items = (json['items'] as List?)?.map((dynamic e) => ProductCatelog.fromJson(e as Map<String,dynamic>)).toList(),
      page = json['page'] as int?,
      hasNextPage = json['hasNextPage'] as bool?,
      totalPages = json['totalPages'] as int?,
      totalItems = json['totalItems'] as int?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'items' : items?.map((e) => e.toJson()).toList(),
    'page' : page,
    'hasNextPage' : hasNextPage,
    'totalPages' : totalPages,
    'totalItems' : totalItems
  };
}

class ProductCatelog {
  final String? typename;
  final String? id;
  final String? productName;
  final ProductBrand? productBrand;
  final ProductOptions? productOptions;
  final List<dynamic>? productImages;

  ProductCatelog({
    this.typename,
    this.id,
    this.productName,
    this.productBrand,
    this.productOptions,
    this.productImages,
  });

  ProductCatelog.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      id = json['_id'] as String?,
      productName = json['productName'] as String?,
      productBrand = (json['productBrand'] as Map<String,dynamic>?) != null ? ProductBrand.fromJson(json['productBrand'] as Map<String,dynamic>) : null,
      productOptions = (json['productOptions'] as Map<String,dynamic>?) != null ? ProductOptions.fromJson(json['productOptions'] as Map<String,dynamic>) : null,
      productImages = json['productImages'] as List?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    '_id' : id,
    'productName' : productName,
    'productBrand' : productBrand?.toJson(),
    'productOptions' : productOptions?.toJson(),
    'productImages' : productImages
  };
}

class ProductBrand {
  final String? typename;
  final String? brandName;

  ProductBrand({
    this.typename,
    this.brandName,
  });

  ProductBrand.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      brandName = json['brandName'] as String?;

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'brandName' : brandName
  };
}

class ProductOptions {
  final String? typename;
  final List<ProductColors>? productColors;

  ProductOptions({
    this.typename,
    this.productColors,
  });

  ProductOptions.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      productColors = (json['productColors'] as List?)?.map((dynamic e) => ProductColors.fromJson(e as Map<String,dynamic>)).toList();

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'productColors' : productColors?.map((e) => e.toJson()).toList()
  };
}

class ProductColors {
  final String? typename;
  final List<String>? colorImages;

  ProductColors({
    this.typename,
    this.colorImages,
  });

  ProductColors.fromJson(Map<String, dynamic> json)
    : typename = json['__typename'] as String?,
      colorImages = (json['colorImages'] as List?)?.map((dynamic e) => e as String).toList();

  Map<String, dynamic> toJson() => {
    '__typename' : typename,
    'colorImages' : colorImages
  };
}