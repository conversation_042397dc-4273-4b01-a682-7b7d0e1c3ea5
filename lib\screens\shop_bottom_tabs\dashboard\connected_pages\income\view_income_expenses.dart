import 'dart:math' as math;
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/income_expense/all_sales_model.dart';
import 'package:overolasuppliers/model/income_expense/all_withdrawals_model.dart';
import 'package:overolasuppliers/model/income_expense/income_expenses_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/shop_dashboard.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:pie_chart/pie_chart.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

class ViewIncomeExpenses extends StatefulWidget {
  const ViewIncomeExpenses({super.key});

  @override
  State<ViewIncomeExpenses> createState() => _ViewIncomeExpensesState();
}

class _ViewIncomeExpensesState extends State<ViewIncomeExpenses>
    implements ServerResponse {
  RxString chartSort = "Weekly".obs;

  RxInt selectedChartIndex = 0.obs;

  Rx<IncomeExpensesModel> incomeExpenses = IncomeExpensesModel().obs;

  RxList<Incomechart> incomeChart = <Incomechart>[].obs;
  RxList<Sales> arrSales = <Sales>[].obs;
  RxList<WithDrawals> arrWithDrawals = <WithDrawals>[].obs;
  late AppLocalizations appLocal;

  var arrSort = [
    "Weekly",
    "10 Days",
    "15 Days",
    "Monthly",
    "6 Months",
    "Custom Date"
  ];

  final dataMap = <String, double>{};

  late GraphQlInitilize _request;

  RxBool isLoading = true.obs;

  getUpdatedData() {
    _request.runQuery(context: context, query: GraphQlQuries.getIncomeInfos,isRequiredLoader: false,);
    _request.runQuery(
        context: context,
        query: GraphQlQuries.getSalesQuery,
        isRequiredLoader: false,
        type: "getSalesQuery",
        variables: GraphQlVariables.getSalesQuery());
    _request.runQuery(
        context: context,
        query: GraphQlQuries.getWithDrawals,
        isRequiredLoader: false,
        type: "getWithDrawals",
        variables: GraphQlVariables.getPaginated(itemsNumber: 30));
  }

  @override
  void initState() {
    super.initState();
    _request = GraphQlInitilize(this);

    WidgetsBinding.instance.addPostFrameCallback((_) => getUpdatedData());
  }

  @override
  Widget build(BuildContext context) {
    appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: ElbaabHeader(
        title: appLocal.incomeAndExpenses,
        leadingBack: true,
      ),
      body: Obx(() => isLoading.value 
          ? _buildSkeletonLoader()
          : RefreshIndicator(
              onRefresh: () async => getUpdatedData(),
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 16.h),
                          _buildDateFilterChips(),
                          SizedBox(height: 24.h),
                          _buildRevenueSummaryCards(),
                          SizedBox(height: 24.h),
                          _buildAnalyticsSection(),
                          if (dataMap.isNotEmpty) 
                            _buildDistributionChart(),
                          
                          SizedBox(height: 24.h),

                          _buildTopSellingProducts(),
                          SizedBox(height: 24.h),
                          _buildTransactionsList(),
                          SizedBox(height: 24.h),
                          if (arrWithDrawals.isNotEmpty)
                            _buildWithdrawalsSection(),
                          SizedBox(height: 24.h),
                          if (arrSales.isNotEmpty)
                            _buildSalesSection(),
                          SizedBox(height: 24.h),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 16.h),
          // Date filter skeleton
          Shimmer.fromColors(
            baseColor: Colors.white70,
            highlightColor: Colors.black38,
            child: Container(
              width: 120.w,
              height: 40.h,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.05),
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
          SizedBox(height: 24.h),
          // Revenue card skeleton
          Shimmer.fromColors(
            baseColor: Colors.white70,
            highlightColor: Colors.black38,
            child: Container(
              width: double.infinity,
              height: 120.h,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.05),
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
          SizedBox(height: 24.h),
          // Chart skeleton
          Shimmer.fromColors(
            baseColor: Colors.white70,
            highlightColor: Colors.black38,
            child: Container(
              width: double.infinity,
              height: 200.h,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.05),
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
          SizedBox(height: 24.h),
          // Top selling products skeleton
          ...List.generate(3, (index) => _buildTopSellingItemSkeleton()),
          SizedBox(height: 24.h),
          // Withdrawals skeleton
          ...List.generate(2, (index) => _buildWithdrawalItemSkeleton()),
          SizedBox(height: 24.h),
          // Sales skeleton
          ...List.generate(2, (index) => _buildSaleItemSkeleton()),
        ],
      ),
    );
  }

  Widget _buildTopSellingItemSkeleton() {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Shimmer.fromColors(
            baseColor: Colors.white70,
            highlightColor: Colors.black38,
            child: Container(
              width: 65.w,
              height: 65.w,
              decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Shimmer.fromColors(
                  baseColor: Colors.white70,
                  highlightColor: Colors.black38,
                  child: Container(
                    width: double.infinity,
                    height: 16.h,
                    decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  ),
                ),
                SizedBox(height: 8.h),
                Shimmer.fromColors(
                  baseColor: Colors.white70,
                  highlightColor: Colors.black38,
                  child: Container(
                    width: 100.w,
                    height: 16.h,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 12.w),
          Shimmer.fromColors(
            baseColor: Colors.white70,
            highlightColor: Colors.black38,
            child: Container(
              width: 36.w,
              height: 36.w,
              decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWithdrawalItemSkeleton() {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Shimmer.fromColors(
                  baseColor: Colors.white70,
                  highlightColor: Colors.black38,
                  child: Container(
                    width: 120.w,
                    height: 16.h,
                    decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  ),
                ),
                SizedBox(height: 8.h),
                Shimmer.fromColors(
                  baseColor: Colors.white70,
                  highlightColor: Colors.black38,
                  child: Container(
                    width: 80.w,
                    height: 16.h,
                    decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  ),
                ),
              ],
            ),
          ),
          Shimmer.fromColors(
            baseColor: Colors.white70,
            highlightColor: Colors.black38,
            child:  Container(
              width: 80.w,
              height: 28.h,
              decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(14),
            ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaleItemSkeleton() {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Shimmer.fromColors(
                  baseColor: Colors.white70,
                  highlightColor: Colors.black38,
                  child: Container(
                    width: 100.w,
                    height: 16.h,
                    decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  ),
                ),
                SizedBox(height: 8.h),
                Shimmer.fromColors(
                  baseColor: Colors.white70,
                  highlightColor: Colors.black38,
                  child: Container(
                    width: 140.w,
                    height: 16.h,
                    decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  ),
                ),
                SizedBox(height: 8.h),
                Shimmer.fromColors(
                  baseColor: Colors.white70,
                  highlightColor: Colors.black38,
                  child: Container(
                    width: 80.w,
                    height: 16.h,
                    decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  ),
                ),
              ],
            ),
          ),
          Shimmer.fromColors(
            baseColor: Colors.white70,
            highlightColor: Colors.black38,
            child: Container(
              width: 80.w,
              height: 28.h,
              decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(14),
            ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList() {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTransactionItem(
            title: appLocal.totalRevenue,
            amount: incomeExpenses.value.totalRevenue ?? 0,
            isClickable: false,
          ),
          _buildTransactionItem(
            title: appLocal.deliveryChargesFees,
            amount: incomeExpenses.value.totalDeliveryChargeFees ?? 0,
            isClickable: true,
            onTap: () => Get.toNamed(
              RouteNames.deliveryChargesFeesScreen,
              arguments: [incomeExpenses.value.deliveryFeeCharges],
            ),
          ),
          _buildTransactionItem(
            title: appLocal.availableAmount,
            amount: incomeExpenses.value.availableAmount ?? 0,
            isClickable: false,
          ),
          _buildTransactionItem(
            title: appLocal.pendingAmount,
            amount: incomeExpenses.value.pendingAmount ?? 0,
            isClickable: true,
            showInfoIcon: true,
            onTap: () => BottomSheets.showAlertMessageBottomSheet(
              appLocal.pendingAmountAlertMessage,
              appLocal.pendingAmount,
              context,
            ),
          ),
          SizedBox(height: 16.h),
          _buildInitiateTransferButton(),
        ],
      ),
    );
  }

  Widget _buildTransactionItem({
    required String title,
    required dynamic amount,
    required bool isClickable,
    bool showInfoIcon = false,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: FontStyles.fontRegular(
                          fontSize: 14,
                          color: isClickable 
                              ? AppColors.colorPrimary 
                              : Colors.white.withOpacity(0.5),
                          decoration: isClickable 
                              ? TextDecoration.underline 
                              : TextDecoration.none,
                        ),
                      ),
                      if (showInfoIcon) ...[
                        SizedBox(width: 8.w),
                        Icon(
                          Icons.info_outline,
                          color: AppColors.colorPrimary,
                          size: 18,
                        ),
                      ],
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    appLocal.dynamicPrice('$amount'),
                    style: FontStyles.fontSemibold(fontSize: 18),
                  ),
                ],
              ),
            ),
            if (isClickable)
              Icon(
                Icons.chevron_right,
                color: Colors.white.withOpacity(0.5),
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitiateTransferButton() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: ElevatedButton(
        onPressed: () async {
          final result = await Get.toNamed(
            RouteNames.initiateTransferRequestScreen,
            arguments: [incomeExpenses.value.availableAmount ?? 0],
          );
          if (result == true) {
            getUpdatedData();
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.colorPrimary,
          padding: EdgeInsets.symmetric(vertical: 12.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          appLocal.initiateTransferRequest,
          style: FontStyles.fontMedium(fontSize: 16),
        ),
      ),
    );
  }

  Widget _buildWithdrawalsSection() {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                appLocal.transferredAmount,
                style: FontStyles.fontSemibold(fontSize: 18),
              ),
                TextButton(
                  onPressed: () => Get.toNamed(
                    RouteNames.allTransferdAmountScreen,
                    arguments: [arrWithDrawals],
                  ),
                  child: Text(
                    appLocal.more,
                    style: FontStyles.fontMedium(
                      fontSize: 14,
                      color: AppColors.colorPrimary,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 16.h),
          ...arrWithDrawals.take(5).map((withdrawal) => _buildWithdrawalItem(withdrawal)),
        ],
      ),
    );
  }

  Widget _buildWithdrawalItem(WithDrawals withdrawal) {
    final status = withdrawal.status?.toLowerCase() ?? '';
    final statusColor = status == 'released' 
        ? AppColors.colorSecondary 
        : status == 'pending' 
            ? AppColors.colorDanger 
            : AppColors.colorPrimary;

    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ID: ${withdrawal.withdrawalId}',
                  style: FontStyles.fontRegular(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.7),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  appLocal.dynamicPrice('${withdrawal.requestedAmount}'),
                  style: FontStyles.fontSemibold(fontSize: 16),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 6.h,
            ),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              withdrawal.status ?? '',
              style: FontStyles.fontMedium(
                fontSize: 12,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesSection() {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                appLocal.sales,
                style: FontStyles.fontSemibold(fontSize: 18),
              ),
              if (arrSales.length > 5)
                TextButton(
                  onPressed: () => Get.toNamed(
                    RouteNames.allSalesScreen,
                    arguments: [arrSales,
                                                  incomeExpenses.value.availableAmount ?? 0
],
                  ),
                  child: Text(
                    appLocal.more,
                    style: FontStyles.fontMedium(
                      fontSize: 14,
                      color: AppColors.colorPrimary,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 16.h),
          ...arrSales.take(5).map((sale) => _buildSaleItem(sale)),
        ],
      ),
    );
  }

  Widget _buildSaleItem(Sales sale) {
        final status = sale.status?.toLowerCase() ?? '';

        final statusColor = status == 'released' 
        ? AppColors.colorSecondary 
        : status == 'pending' 
            ? AppColors.colorDanger 
            : AppColors.colorPrimary;
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ID: ${sale.shipmentId}',
                  style: FontStyles.fontRegular(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.7),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  appLocal.dynamicPrice('${sale.totalItemsCost}'),
                  style: FontStyles.fontSemibold(fontSize: 16),
                ),
                SizedBox(height: 4.h),
                Text(
                  GlobalMethods.convertTimeFormate(
                    sale.date ?? '',
                    format: "dd MMM, yyyy",
                  ),
                  style: FontStyles.fontRegular(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.5),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 6.h,
            ),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              sale.status ?? '',
              style: FontStyles.fontMedium(
                fontSize: 12,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          ...List.generate(
            arrSort.length,
            (index) => Padding(
              padding: EdgeInsets.only(right: 8.w),
              child: ChoiceChip(
                labelPadding: EdgeInsets.symmetric(horizontal: 8.w),
                label: Text(
                  arrSort[index],
                  style: FontStyles.fontMedium(
                    fontSize: 14,
                    color: chartSort.value == arrSort[index]
                        ? AppColors.colorPrimary
                        : Colors.white.withOpacity(0.7),
                  ),
                ),
                selected: chartSort.value == arrSort[index],
                onSelected: (selected) async {
                  if (selected) {
                    if (index == 5) {
                      var dateRange = await BottomSheets.dateRangeSelector(
                          context, 'income');
                      final split = dateRange.split(' - ');
                      chartSort.value = '${split[0]} - ${split[1]}';
                    } else {
                      chartSort.value = arrSort[index];
                      int dateFilter = [7, 10, 15, 30, 180][index];
                      _request.runQuery(
                        context: context,
                        query: GraphQlQuries.getSalesQuery,
                        type: "getSalesQuery",
                        variables: GraphQlVariables.getSalesQuery(
                            dateFilter: dateFilter),
                      );
                    }
                  }
                },
                backgroundColor: Colors.transparent,
                selectedColor: AppColors.colorPrimary.withOpacity(0.2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRevenueSummaryCards() {
    return Column(
      children: [
        _buildMainRevenueCard(),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(child: _buildMetricCard(
              title: appLocal.availableAmount,
              amount: incomeExpenses.value.availableAmount ?? 0.0,
              icon: Icons.account_balance_wallet,
              color: AppColors.colorSecondary,
            )),
            SizedBox(width: 16.w),
            Expanded(child: _buildMetricCard(
              title: appLocal.pendingAmount,
              amount: incomeExpenses.value.pendingAmount ?? 0,
              icon: Icons.pending,
              color: AppColors.colorDanger,
            )),
          ],
        ),
      ],
    );
  }

  Widget _buildMainRevenueCard() {
    return Container(
      padding: EdgeInsets.all(24.r),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.colorPrimary,
            AppColors.colorPrimary.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.colorPrimary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                appLocal.totalRevenue,
                style: FontStyles.fontMedium(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
              const Icon(Icons.trending_up, color: Colors.white, size: 24),
            ],
          ),
          SizedBox(height: 16.h),
          Text(
            appLocal.dynamicPrice('${incomeExpenses.value.totalRevenue ?? 0}'),
            style: FontStyles.fontBold(fontSize: 32),
          ),
          SizedBox(height: 8.h),
          Text(
            '${appLocal.deliveryChargesFees}: ${appLocal.dynamicPrice('${incomeExpenses.value.totalDeliveryChargeFees ?? 0}')}',
            style: FontStyles.fontRegular(
              fontSize: 14,
              color: Colors.white.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard({
    required String title,
    required double amount,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  title,
                  style: FontStyles.fontMedium(
                    fontSize: 14,
                    height: 1.2,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            appLocal.dynamicPrice('$amount'),
            style: FontStyles.fontBold(fontSize: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsSection() {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            appLocal.salesTrend,
            style: FontStyles.fontSemibold(fontSize: 18),
          ),
          SizedBox(height: 24.h),
          SizedBox(
            height: 200.h,
            child: incomeChart.isEmpty
                ? Center(
                    child: Text(
                      appLocal.noSalesFound,
                      style: FontStyles.fontMedium(),
                    ),
                  )
                : ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: incomeChart.length,
                    itemBuilder: (context, index) => _buildBarChartItem(index),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChartItem(int index) {
    return GestureDetector(
      onTap: () => selectedChartIndex.value = index,
      child: Container(
        width: 60.w,
        margin: EdgeInsets.only(right: 8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Obx(() => selectedChartIndex.value == index
                ? Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.colorPrimary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      appLocal.dynamicPrice(incomeChart[index].debit),
                      style: FontStyles.fontMedium(fontSize: 12),
                    ),
                  )
                : SizedBox(height: 24.h)),
            SizedBox(height: 8.h),
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: incomeChart[index].barHeight.h,
              width: 40.w,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.colorPrimary,
                    AppColors.colorPrimary.withOpacity(0.3),
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              GlobalMethods.convertTimeFormate(
                incomeChart[index].day,
                format: "dd/MM",
              ),
              style: FontStyles.fontRegular(
                fontSize: 12,
                color: Colors.white.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDistributionChart() {
    return PieChart(
      key: const ValueKey(0),
      dataMap: dataMap,
      animationDuration: Durations.extralong4,
      chartLegendSpacing: 30,
      chartRadius:
          math.min(MediaQuery.of(context).size.width / 2, 300),
      colorList: [
        AppColors.colorDanger,
        AppColors.colorPrimary,
        AppColors.colorYellow,
      ],
      initialAngleInDegree: 0,
      chartType: ChartType.disc,
      legendOptions: LegendOptions(
        showLegendsInRow: true,
        legendPosition: LegendPosition.bottom,
        showLegends: true,
        legendShape: BoxShape.circle,
        legendTextStyle: FontStyles.fontMedium(fontSize: 12),
      ),
      chartValuesOptions: const ChartValuesOptions(
        showChartValueBackground: false,
        showChartValues: true,
        showChartValuesInPercentage: true,
        showChartValuesOutside: false,
      ),
      baseChartColor: Colors.transparent,
    );
  }

  Widget _buildTopSellingProducts() {
    final topSellingItems = incomeExpenses.value.topSellingItems ?? [];
    if (topSellingItems.isEmpty) return const SizedBox();

    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                appLocal.topSellingItems,
                style: FontStyles.fontSemibold(fontSize: 18),
              ),
              if (topSellingItems.length > 5)
                TextButton(
                  onPressed: () => Get.toNamed(
                    RouteNames.topSellingItemScreen,
                    arguments: [topSellingItems],
                  ),
                  child: Text(
                    appLocal.more,
                    style: FontStyles.fontMedium(
                      fontSize: 14,
                      color: AppColors.colorPrimary,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 16.h),
          ...topSellingItems.take(5).map((item) => _buildTopSellingItem(item)),
        ],
      ),
    );
  }

  Widget _buildTopSellingItem(TopSellingItems item) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.colorSecondary.withOpacity(0.1),
            Colors.transparent,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.colorSecondary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Stack(
            children: [
              Container(
                width: 65.w,
                height: 65.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.colorSecondary.withOpacity(0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    imageUrl: item.product?.productImages?.firstOrNull ?? '',
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppColors.colorSecondary.withOpacity(0.1),
                      child: Icon(
                        Icons.image,
                        color: AppColors.colorSecondary.withOpacity(0.3),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: AppColors.colorSecondary.withOpacity(0.1),
                      child: Icon(
                        Icons.error_outline,
                        color: AppColors.colorSecondary.withOpacity(0.3),
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.all(4.r),
                  decoration: BoxDecoration(
                    color: AppColors.colorSecondary,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(12.r),
                      bottomLeft: Radius.circular(12.r),
                    ),
                  ),
                  child: Text(
                    '#${item.sales ?? 1}',
                    style: FontStyles.fontBold(
                      fontSize: 12,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.colorSecondary.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.local_fire_department,
                        size: 14,
                        color: AppColors.colorSecondary,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '${item.sales ?? 0} ${appLocal.sales}',
                        style: FontStyles.fontSemibold(
                          fontSize: 12,
                          color: AppColors.colorSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
             
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(8.r),
            decoration: BoxDecoration(
              color: AppColors.colorSecondary.withOpacity(0.15),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.trending_up_rounded,
              color: AppColors.colorSecondary,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    isLoading.value = false;
    if (type == "getSalesQuery") {
      SalesModel model = SalesModel.fromJson(response);
      if (model.status == statusOK) {
        arrSales.addAll(model.sales?.items ?? []);
        arrSales.sort((a, b) => (b.date ?? "").compareTo(a.date ?? ""));
        incomeChart.clear();
        Map<String, Sales> salesByDate = {};
        for (Sales sale in arrSales) {
          if (salesByDate.containsKey(GlobalMethods.convertTimeFormate(
              (sale.date ?? ""),
              format: "dd MMM, yyyy"))) {
            var sameDateSale = salesByDate[GlobalMethods.convertTimeFormate(
                (sale.date ?? ""),
                format: "dd MMM, yyyy")];
            int totalSale =
                sale.totalItemsCost + sameDateSale?.totalItemsCost ?? 0;
            Sales addPriceSale = sale;
            addPriceSale.totalItemsCost = totalSale;
            salesByDate[GlobalMethods.convertTimeFormate((sale.date ?? ""),
                format: "dd MMM, yyyy")] = addPriceSale;
          } else {
            salesByDate.putIfAbsent(
                GlobalMethods.convertTimeFormate((sale.date ?? ""),
                    format: "dd MMM, yyyy"),
                () => sale);
          }
        }
        List<int> debit = [];
        salesByDate.forEach((key, value) {
          debit.add(int.parse("${value.totalItemsCost ?? 0}"));
        });
        salesByDate.forEach((key, value) {
          double height =
              ((int.parse("${value.totalItemsCost ?? 0}") / debit.reduce(max)) *
                  128);
          incomeChart.add(Incomechart(
              debit: "${value.totalItemsCost ?? 0}",
              date: value.date ?? "",
              day: value.date ?? "",
              barHeight: height));
        });
      } else {
        arrSales.clear();
        incomeChart.clear();
      }
    } else if (type == "getWithDrawals") {
      WithDrawalsModel model = WithDrawalsModel.fromJson(response);
      if (model.status == statusOK) {
        arrWithDrawals.addAll(model.withdrawals?.items ?? []);
      }
    } else {
      IncomeExpensesModel model = IncomeExpensesModel.fromJson(response);
      if (model.status == statusOK) {
        dataMap.clear();
        setState(() {
          
          dataMap.addAll({
            appLocal.deliveryFee:
                double.parse("${model.totalDeliveryChargeFees ?? 0.0}"),
           appLocal.available: double.parse("${model.availableAmount ?? 0}"),
            appLocal.pending: double.parse("${model.pendingAmount ?? 0}"),
          });
        });
        incomeExpenses.value = model;
      }
    }
  }
  // Add shimmer effect
  Widget _addShimmerEffect(Widget child) {
    return Shimmer.fromColors(
      baseColor: Colors.white.withOpacity(0.04),
      highlightColor: Colors.white.withOpacity(0.1),
      child: child,
    );
  }
}
