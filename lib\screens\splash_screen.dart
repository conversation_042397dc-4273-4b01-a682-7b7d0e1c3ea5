import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/model/login_model.dart';
import 'package:overolasuppliers/provider/shop_info_provider.dart';
import 'package:provider/provider.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> implements ServerResponse {
  late GraphQlInitilize _request;

  Timer? _timer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
    supplierID = prefs.getString(merchantID) ?? "";
    Provider.of<ShopInfoProvider>(context, listen: false)
        .setShopUserName(prefs.getString(ownerName) ?? "");
    userAuthToken = prefs.getString(authToken) ?? "";
       bool isFirstLaunch = prefs.getBool("isFirstLaunch") ?? false;
    if (!isFirstLaunch) {
      Get.offNamed(RouteNames.selecLangugage);
    }else{
 _request = GraphQlInitilize(this);
    Future.delayed(Duration.zero, () {
      _checkStatus(context);
    });
    }
    });
   
   
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Center(
      child: Stack(
        children: [
          Center(
            child: Lottie.asset('assets/images/elbaab.json'),
          ),
          Center(
            child: Text(
              "\n${appLocal.appName}     ",
              style: FontStyles.fontSemibold(fontSize: 50),
            ),
          )
        ],
      ),
    );
  }

  _checkStatus(BuildContext context) async {
    if (prefs.getBool(requiredLogin) ?? false) {
      String email = prefs.getString(signupEmail) ?? "";
      String password = prefs.getString(signupPassword) ?? "";
      _request.runMutation(
          context: context,
          isLoader: false,
          type: "login",
          query: GraphQlQuries.login,
          variables: GraphQlVariables.login(email: email, password: password,lang: Get.locale?.languageCode ?? "en"));
    } else if (supplierID.isNotEmpty) {
      Future.delayed(const Duration(seconds: 1)).then((value) {
        _request.runQuery(context: context, query: GraphQlQuries.verifyToken);
      });
    } else {
      Future.delayed(const Duration(seconds: 2)).then((value) {
        Get.offNamed(RouteNames.loginScreen);
      });
    }
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    if (type == "login") {
      String email = prefs.getString(signupEmail) ?? "";
      String password = prefs.getString(signupPassword) ?? "";
      GlobalMethods.checkLoginStatus(LoginModel.fromJson(response),
          email: email, password: password);
    } else {
      if (BaseModel.fromJson(response).status == statusOK) {
        Get.offNamed(RouteNames.shopHomeScreen);
      } else {
        Get.offNamed(RouteNames.loginScreen,
            arguments: [BaseModel.fromJson(response).message]);
      }
    }
  }
}
