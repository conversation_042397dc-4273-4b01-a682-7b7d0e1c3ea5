import 'dart:io';
import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/multi_image_picker/src/multi_image_crop.dart';

class MultiImageCrop {
  static Future<void> startCropping({
    required BuildContext context,
    required List<File> files,
    required double aspectRatio,
    bool requiredCirsleCroper = false,
    bool isProductImages = false,
    required Function callBack,
  }) async {
    // Validate input
    if (files.isEmpty) {
      return;
    }

    // Split into batches if too many images
    if (files.length > 30) {
      final batches = <List<File>>[];
      for (var i = 0; i < files.length; i += 30) {
        batches.add(
          files.sublist(
            i,
            i + 30 > files.length ? files.length : i + 30,
          ),
        );
      }

      final List<File> processedFiles = [];
      for (var batch in batches) {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MultiImageCropService(
              files: batch,
              aspectRatio: aspectRatio,
              requiredCirsleCroper: requiredCirsleCroper,
              isProductImages: isProductImages,
            ),
          ),
        );
        if (result != null) {
          processedFiles.addAll(result);
        }
      }
      callBack(processedFiles);
    } else {
      // Original flow for smaller sets
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MultiImageCropService(
            files: files,
            aspectRatio: aspectRatio,
            requiredCirsleCroper: requiredCirsleCroper,
            isProductImages: isProductImages,
          ),
        ),
      ).then((value) {
        if (value != null) {
          callBack(value);
        }
      });
    }
  }
}
