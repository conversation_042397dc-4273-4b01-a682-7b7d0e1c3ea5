import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';


class UserUnauthrized extends StatelessWidget {
  const UserUnauthrized({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Container(
      color: Colors.white,
      child: Stack(
        children: [
          Center(
            child: Lottie.asset('assets/images/error.json'),
          ),
          Positioned(
            bottom: 50,
            left: 20,
            right: 20,
            child: ElbaabGradientButtonWidget(
              onPress: () {
                Get.back(result: true);
                Future.delayed(const Duration(milliseconds: 500), () {
                  prefs.remove(merchantID);
                  prefs.remove(ownerName);
                  prefs.remove(authToken);
                  prefs.remove(localAuthEnable);
                  prefs.remove(localAuthEmail);
                  prefs.remove(localAuthPassword);
                  userAuthToken = "";
                  supplierID = "";
                  prefs.setString("information", "");
                  prefs.setString("customerContact", "");
                  prefs.setString("pickupAddress", "");
                  Get.offAllNamed(RouteNames.loginScreen);
                });
              },
              text: appLocal.login,
            ),
          )
        ],
      ),
    );
  }
}
