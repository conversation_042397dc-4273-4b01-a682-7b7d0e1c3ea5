import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/helper/platform/platform_helper.dart';
import '../lib/widgets/responsive/responsive_layout.dart';
import 'desktop_test_helper.dart';

void main() {
  group('Platform Helper Tests', () {
    setUp(() {
      DesktopTestHelper.setUpDesktopEnvironment();
    });

    tearDown(() {
      DesktopTestHelper.tearDown();
    });

    test('should detect desktop platform correctly', () {
      expect(PlatformHelper.isDesktop, isTrue);
      expect(PlatformHelper.isMobile, isFalse);
      expect(PlatformHelper.isWeb, isFalse);
    });

    test('should return correct feature availability for desktop', () {
      expect(PlatformHelper.hasCameraSupport, isFalse);
      expect(PlatformHelper.hasLocationSupport, isFalse);
      expect(PlatformHelper.hasBiometricSupport, isFalse);
      expect(PlatformHelper.hasWindowManagement, isTrue);
      expect(PlatformHelper.hasFileDropSupport, isTrue);
    });

    test('should provide appropriate UI adaptations for desktop', () {
      expect(PlatformHelper.shouldUseDrawer, isFalse);
      expect(PlatformHelper.shouldUseNavigationRail, isTrue);
      expect(PlatformHelper.shouldUseTabBar, isTrue);
    });

    test('should handle safe feature execution', () async {
      // Test supported feature
      final result = await PlatformHelper.executeIfSupported<String>(
        true,
        () async => 'success',
        fallback: 'fallback',
      );
      expect(result, equals('success'));

      // Test unsupported feature
      final fallbackResult = await PlatformHelper.executeIfSupported<String>(
        false,
        () async => 'success',
        fallback: 'fallback',
      );
      expect(fallbackResult, equals('fallback'));
    });
  });

  group('Responsive Layout Tests', () {
    testWidgets('should display mobile layout on mobile', (tester) async {
      DesktopTestHelper.setUpMobileEnvironment();
      
      await tester.pumpWidget(
        DesktopTestHelper.createMobileTestWidget(
          ResponsiveLayout(
            mobile: const Text('Mobile Layout'),
            desktop: const Text('Desktop Layout'),
          ),
        ),
      );

      expect(find.text('Mobile Layout'), findsOneWidget);
      expect(find.text('Desktop Layout'), findsNothing);
      
      DesktopTestHelper.tearDown();
    });

    testWidgets('should display desktop layout on desktop', (tester) async {
      DesktopTestHelper.setUpDesktopEnvironment();
      
      await tester.pumpWidget(
        DesktopTestHelper.createDesktopTestWidget(
          ResponsiveLayout(
            mobile: const Text('Mobile Layout'),
            desktop: const Text('Desktop Layout'),
          ),
        ),
      );

      expect(find.text('Desktop Layout'), findsOneWidget);
      expect(find.text('Mobile Layout'), findsNothing);
      
      DesktopTestHelper.tearDown();
    });

    testWidgets('should adapt container width responsively', (tester) async {
      await tester.pumpWidget(
        DesktopTestHelper.createDesktopTestWidget(
          ResponsiveContainer(
            child: Container(
              color: Colors.blue,
              height: 100,
            ),
          ),
        ),
      );

      final container = tester.widget<Container>(
        find.descendant(
          of: find.byType(ResponsiveContainer),
          matching: find.byType(Container).first,
        ),
      );

      expect(container.constraints?.maxWidth, isNotNull);
    });
  });

  group('Responsive Grid Tests', () {
    testWidgets('should adapt column count based on screen size', (tester) async {
      await DesktopTestHelper.testAcrossScreenSizes(
        tester,
        (size) => ResponsiveGrid(
          children: List.generate(
            6,
            (index) => Container(
              color: Colors.blue,
              child: Text('Item $index'),
            ),
          ),
        ),
        (size) {
          // Verify grid adapts to screen size
          final grid = tester.widget<GridView>(find.byType(GridView));
          expect(grid.gridDelegate, isA<SliverGridDelegateWithFixedCrossAxisCount>());
        },
      );
    });
  });

  group('Adaptive Navigation Tests', () {
    final navigationItems = [
      const NavigationItem(
        icon: Icon(Icons.home),
        label: 'Home',
      ),
      const NavigationItem(
        icon: Icon(Icons.search),
        label: 'Search',
      ),
      const NavigationItem(
        icon: Icon(Icons.person),
        label: 'Profile',
      ),
    ];

    testWidgets('should use navigation rail on desktop', (tester) async {
      DesktopTestHelper.setUpDesktopEnvironment();
      
      await tester.pumpWidget(
        DesktopTestHelper.createDesktopTestWidget(
          AdaptiveNavigation(
            items: navigationItems,
            selectedIndex: 0,
            onItemSelected: (index) {},
            body: const Text('Body Content'),
          ),
        ),
      );

      expect(find.byType(NavigationRail), findsOneWidget);
      expect(find.byType(BottomNavigationBar), findsNothing);
      
      DesktopTestHelper.tearDown();
    });

    testWidgets('should use bottom navigation on mobile', (tester) async {
      DesktopTestHelper.setUpMobileEnvironment();
      
      await tester.pumpWidget(
        DesktopTestHelper.createMobileTestWidget(
          AdaptiveNavigation(
            items: navigationItems,
            selectedIndex: 0,
            onItemSelected: (index) {},
            body: const Text('Body Content'),
          ),
        ),
      );

      expect(find.byType(BottomNavigationBar), findsOneWidget);
      expect(find.byType(NavigationRail), findsNothing);
      
      DesktopTestHelper.tearDown();
    });
  });

  group('Responsive Text Tests', () {
    testWidgets('should adapt font size for desktop', (tester) async {
      DesktopTestHelper.setUpDesktopEnvironment();
      
      await tester.pumpWidget(
        DesktopTestHelper.createDesktopTestWidget(
          const ResponsiveText(
            'Test Text',
            style: TextStyle(fontSize: 16),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.text('Test Text'));
      expect(textWidget.style?.fontSize, greaterThan(16));
      
      DesktopTestHelper.tearDown();
    });
  });

  group('Integration Tests', () {
    testWidgets('should handle platform-specific features gracefully', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return Column(
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        if (PlatformHelper.hasCameraSupport) {
                          // Would open camera
                        } else {
                          PlatformHelper.showUnsupportedFeatureDialog(
                            context,
                            'Camera',
                          );
                        }
                      },
                      child: const Text('Open Camera'),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      );

      await tester.tap(find.text('Open Camera'));
      await tester.pumpAndSettle();

      // Should show unsupported feature dialog on desktop
      expect(find.text('Feature Not Available'), findsOneWidget);
    });
  });
}
