@echo off
REM Flutter Desktop Build Script for Windows
REM This script builds the application for Windows desktop

setlocal enabledelayedexpansion

echo 🚀 Starting Flutter Desktop Build Process for Windows...

REM Check if Flutter is installed
where flutter >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Flutter is not installed or not in PATH
    exit /b 1
)

REM Check Flutter version
echo [INFO] Checking Flutter version...
flutter --version

REM Clean previous builds
echo [INFO] Cleaning previous builds...
flutter clean
flutter pub get

REM Run tests
echo [INFO] Running tests...
flutter test
if %errorlevel% neq 0 (
    echo [WARNING] Some tests failed, but continuing with build...
)

REM Build for Windows
echo [INFO] Building for Windows...
flutter build windows --release
if %errorlevel% neq 0 (
    echo [ERROR] Windows build failed!
    exit /b 1
)

echo [SUCCESS] Windows build completed!

REM Generate build info
echo [INFO] Generating build information...
for /f "tokens=*" %%i in ('date /t') do set BUILD_DATE=%%i
for /f "tokens=*" %%i in ('time /t') do set BUILD_TIME=%%i
for /f "tokens=*" %%i in ('git rev-parse --short HEAD 2^>nul') do set BUILD_COMMIT=%%i
if "!BUILD_COMMIT!"=="" set BUILD_COMMIT=unknown
for /f "tokens=*" %%i in ('git branch --show-current 2^>nul') do set BUILD_BRANCH=%%i
if "!BUILD_BRANCH!"=="" set BUILD_BRANCH=unknown

echo Build Information > build_info.txt
echo ================= >> build_info.txt
echo Date: %BUILD_DATE% %BUILD_TIME% >> build_info.txt
echo Commit: %BUILD_COMMIT% >> build_info.txt
echo Branch: %BUILD_BRANCH% >> build_info.txt
echo Platform: Windows >> build_info.txt
flutter --version | findstr "Flutter" >> build_info.txt

echo [SUCCESS] Build information saved to build_info.txt

REM Prepare distribution
echo [INFO] Preparing distribution...
if not exist dist mkdir dist
xcopy /E /I /Y build\windows\x64\runner\Release dist\windows >nul 2>nul
copy build_info.txt dist\ >nul 2>nul
copy README.md dist\ >nul 2>nul
copy LICENSE dist\ >nul 2>nul

echo [SUCCESS] Distribution prepared in dist\ folder

REM Check for NSIS to create installer
where makensis >nul 2>nul
if %errorlevel% equ 0 (
    echo [INFO] NSIS found, creating installer...
    REM NSIS installer creation would go here
    echo [SUCCESS] Windows installer created!
) else (
    echo [WARNING] NSIS not found, skipping installer creation
    echo [INFO] You can manually create an installer using NSIS or other tools
)

echo [SUCCESS] 🎉 Windows desktop build process completed successfully!
echo.
echo Next steps:
echo 1. Test the application in dist\windows folder
echo 2. Create a Windows installer using NSIS or Inno Setup
echo 3. Sign the application for distribution
echo 4. Upload to Microsoft Store or other distribution platforms

pause
