# Flutter Setup Script for Windows (User Installation)
# This script downloads and installs Flutter SDK in user directory

Write-Host "🚀 Flutter Desktop Development Setup for Windows (User Installation)" -ForegroundColor Cyan
Write-Host "=================================================================" -ForegroundColor Cyan

# Set installation directory in user profile
$UserProfile = $env:USERPROFILE
$FlutterDir = "$UserProfile\flutter"
$FlutterBin = "$FlutterDir\bin"
$TempDir = $env:TEMP

Write-Host "[INFO] Installing Flutter to user directory: $FlutterDir" -ForegroundColor Blue

# Check if Flutter is already installed
if (Test-Path "$FlutterBin\flutter.bat") {
    Write-Host "[INFO] Flutter is already installed at $FlutterDir" -ForegroundColor Blue
} else {
    Write-Host "[INFO] Creating Flutter installation directory..." -ForegroundColor Blue
    
    # Create directory if it doesn't exist
    if (!(Test-Path $FlutterDir)) {
        New-Item -ItemType Directory -Path $FlutterDir -Force | Out-Null
    }

    # Download Flutter SDK to temp directory
    Write-Host "[INFO] Downloading Flutter SDK..." -ForegroundColor Blue
    Write-Host "[INFO] This may take several minutes depending on your internet connection..." -ForegroundColor Blue

    $url = "https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.24.3-stable.zip"
    $output = "$TempDir\flutter_sdk.zip"

    try {
        $ProgressPreference = 'SilentlyContinue'
        Write-Host "[INFO] Downloading from: $url" -ForegroundColor Blue
        Write-Host "[INFO] Saving to: $output" -ForegroundColor Blue
        
        # Use .NET WebClient as alternative
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($url, $output)
        $webClient.Dispose()
        
        Write-Host "[SUCCESS] Flutter SDK downloaded successfully" -ForegroundColor Green
    } catch {
        Write-Host "[ERROR] Failed to download Flutter SDK: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "[INFO] Trying alternative download method..." -ForegroundColor Blue
        
        try {
            Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
            Write-Host "[SUCCESS] Flutter SDK downloaded successfully (alternative method)" -ForegroundColor Green
        } catch {
            Write-Host "[ERROR] All download methods failed: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "[ERROR] Please check your internet connection and try again" -ForegroundColor Red
            exit 1
        }
    }

    # Verify download
    if (!(Test-Path $output)) {
        Write-Host "[ERROR] Download file not found at $output" -ForegroundColor Red
        exit 1
    }

    $fileSize = (Get-Item $output).Length / 1MB
    Write-Host "[INFO] Downloaded file size: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Blue

    # Extract Flutter SDK
    Write-Host "[INFO] Extracting Flutter SDK..." -ForegroundColor Blue
    try {
        # Extract to parent directory of FlutterDir
        $extractPath = Split-Path $FlutterDir -Parent
        Expand-Archive -Path $output -DestinationPath $extractPath -Force
        Write-Host "[SUCCESS] Flutter SDK extracted to $FlutterDir" -ForegroundColor Green
    } catch {
        Write-Host "[ERROR] Failed to extract Flutter SDK: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "[INFO] Trying alternative extraction method..." -ForegroundColor Blue
        
        try {
            # Use Shell.Application COM object
            $shell = New-Object -ComObject Shell.Application
            $zip = $shell.NameSpace($output)
            $destination = $shell.NameSpace((Split-Path $FlutterDir -Parent))
            $destination.CopyHere($zip.Items(), 4)
            Write-Host "[SUCCESS] Flutter SDK extracted successfully (alternative method)" -ForegroundColor Green
        } catch {
            Write-Host "[ERROR] All extraction methods failed: $($_.Exception.Message)" -ForegroundColor Red
            exit 1
        }
    }

    # Clean up downloaded zip file
    Remove-Item $output -ErrorAction SilentlyContinue
    Write-Host "[INFO] Cleaned up temporary files" -ForegroundColor Blue
}

# Verify Flutter installation
if (!(Test-Path "$FlutterBin\flutter.bat")) {
    Write-Host "[ERROR] Flutter installation failed - flutter.bat not found" -ForegroundColor Red
    exit 1
}

# Add Flutter to PATH
Write-Host "[INFO] Configuring Flutter PATH..." -ForegroundColor Blue

$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($currentPath -notlike "*$FlutterBin*") {
    Write-Host "[INFO] Adding Flutter to user PATH..." -ForegroundColor Blue
    try {
        $newPath = if ($currentPath) { "$currentPath;$FlutterBin" } else { $FlutterBin }
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Host "[SUCCESS] Flutter added to user PATH" -ForegroundColor Green
        Write-Host "[INFO] Please restart your terminal or IDE to use Flutter globally" -ForegroundColor Yellow
    } catch {
        Write-Host "[ERROR] Failed to add Flutter to PATH: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "[INFO] Flutter is already in PATH" -ForegroundColor Blue
}

# Update current session PATH
$env:PATH += ";$FlutterBin"

# Test Flutter command
Write-Host "[INFO] Testing Flutter installation..." -ForegroundColor Blue
try {
    $flutterTest = & "$FlutterBin\flutter.bat" --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[SUCCESS] Flutter is working correctly!" -ForegroundColor Green
        Write-Host "Flutter version: $($flutterTest[0])" -ForegroundColor White
    } else {
        Write-Host "[WARNING] Flutter test returned error code: $LASTEXITCODE" -ForegroundColor Yellow
        Write-Host "Output: $flutterTest" -ForegroundColor Yellow
    }
} catch {
    Write-Host "[WARNING] Could not test Flutter: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Enable desktop support
Write-Host "[INFO] Enabling Flutter desktop support..." -ForegroundColor Blue
try {
    & "$FlutterBin\flutter.bat" config --enable-windows-desktop
    & "$FlutterBin\flutter.bat" config --enable-macos-desktop  
    & "$FlutterBin\flutter.bat" config --enable-linux-desktop
    & "$FlutterBin\flutter.bat" config --no-analytics
    Write-Host "[SUCCESS] Desktop support enabled" -ForegroundColor Green
} catch {
    Write-Host "[WARNING] Could not configure Flutter settings: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Run Flutter doctor
Write-Host "[INFO] Running Flutter doctor to check installation..." -ForegroundColor Blue
try {
    & "$FlutterBin\flutter.bat" doctor
} catch {
    Write-Host "[WARNING] Could not run Flutter doctor: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Navigate to project directory and get dependencies
Write-Host "[INFO] Setting up project dependencies..." -ForegroundColor Blue
$projectDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $projectDir

try {
    & "$FlutterBin\flutter.bat" pub get
    Write-Host "[SUCCESS] Project dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to install project dependencies: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "[INFO] You can run 'flutter pub get' manually later" -ForegroundColor Blue
}

Write-Host ""
Write-Host "[SUCCESS] Flutter setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Flutter installed at: $FlutterDir" -ForegroundColor Cyan
Write-Host "Flutter binary: $FlutterBin\flutter.bat" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Restart your command prompt or IDE to use Flutter globally" -ForegroundColor White
Write-Host "2. Run 'flutter doctor' to verify installation" -ForegroundColor White
Write-Host "3. Run 'flutter devices' to see available devices" -ForegroundColor White
Write-Host "4. Run 'flutter run -d windows' to run the app on Windows" -ForegroundColor White
Write-Host ""
Write-Host "For immediate use in this session:" -ForegroundColor Cyan
Write-Host "Use the full path: $FlutterBin\flutter.bat" -ForegroundColor White
Write-Host ""

Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
