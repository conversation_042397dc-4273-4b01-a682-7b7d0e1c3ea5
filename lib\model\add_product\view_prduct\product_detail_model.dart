import 'package:overolasuppliers/model/add_product/shipments_fits_model.dart';

class ProductDetailModel {
  final String? typename;
  final Product? product;
  final int? status;
  final String? message;

  ProductDetailModel({
    this.typename,
    this.product,
    this.status,
    this.message,
  });

  ProductDetailModel.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        product = (json['product'] as Map<String, dynamic>?) != null
            ? Product.fromJson(json['product'] as Map<String, dynamic>)
            : null,
        status = json['status'] as int?,
        message = json['message'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'product': product?.toJson(),
        'status': status,
        'message': message
      };
}

class Product {
  final String? productCatalog;
  final String? id;
  final ProductOwnerId? productOwnerId;
  final String? productName;
  final String? productDescription;
  final String? productManufacturerId;
  final ProductCategory? productCategory;
  final ProductSubCategory? productSubCategory;
  final ProductBrand? productBrand;
  final List<String>? productKeyWords;
  final List<String>? productImages;
  final dynamic productPrice;
  final bool? isFreeDeliveryItem;
  final bool? isHidden;
  final bool? isMatched;
  final int? productAvailableQte;
  final bool? productNotifOnMinQte;
  final int? productMinQte;
  final ProductOptions? productOptions;
  final List<ProductSpecs>? productSpecs;
  final List<ProductVariants>? productVariants;
  final SizeCategories? productSizeCategory;
  final int? itemsPerOrder;
  final ProductPolicies? productPolicies;
  final String? createdAt;
  final String? updatedAt;
  final String? productStatus;
  final bool? isReady;
  final bool? isCompleted;
  final List<ValidationHistory>? validationHistory;
  final dynamic productEIN;
  final Product? ar;

  Product({
    this.productCatalog,
    this.id,
    this.productOwnerId,
    this.productName,
    this.productDescription,
    this.productManufacturerId,
    this.isMatched,
    this.productCategory,
    this.isHidden,
    this.productSubCategory,
    this.productBrand,
    this.productKeyWords,
    this.productImages,
    this.productPrice,
    this.isFreeDeliveryItem,
    this.productAvailableQte,
    this.productNotifOnMinQte,
    this.productMinQte,
    this.productOptions,
    this.productSpecs,
    this.productVariants,
    this.productSizeCategory,
    this.itemsPerOrder,
    this.productPolicies,
    this.createdAt,
    this.updatedAt,
    this.productStatus,
    this.isReady,
    this.isCompleted,
    this.validationHistory,
    this.productEIN,
    this.ar,
  });

  Product.fromJson(Map<String, dynamic> json)
      : productCatalog = json['productCatalog'] as String?,
        id = json['_id'] as String?,
        productOwnerId =
            (json['productOwnerId'] as Map<String, dynamic>?) != null
                ? ProductOwnerId.fromJson(
                    json['productOwnerId'] as Map<String, dynamic>)
                : null,
        productName = json['productName'] as String?,
        productDescription = json['productDescription'] as String?,
        productManufacturerId = json['productManufacturerId'] as String?,
        productCategory =
            (json['productCategory'] as Map<String, dynamic>?) != null
                ? ProductCategory.fromJson(
                    json['productCategory'] as Map<String, dynamic>)
                : null,
        ar = (json['ar'] as Map<String, dynamic>?) != null
            ? Product.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        productSubCategory =
            (json['productSubCategory'] as Map<String, dynamic>?) != null
                ? ProductSubCategory.fromJson(
                    json['productSubCategory'] as Map<String, dynamic>)
                : null,
        productBrand = (json['productBrand'] as Map<String, dynamic>?) != null
            ? ProductBrand.fromJson(
                json['productBrand'] as Map<String, dynamic>)
            : null,
        productKeyWords = (json['productKeyWords'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        productImages = (json['productImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        productPrice = json['productPrice'],
        isFreeDeliveryItem = json['isFreeDeliveryItem'] as bool?,
        isHidden = json['isHidden'] as bool?,
        productAvailableQte = json['productAvailableQte'] as int?,
        productNotifOnMinQte = json['productNotifOnMinQte'] as bool?,
        isMatched = json['isMatched'] as bool?,
        productMinQte = json['productMinQte'] as int?,
        productOptions =
            (json['productOptions'] as Map<String, dynamic>?) != null
                ? ProductOptions.fromJson(
                    json['productOptions'] as Map<String, dynamic>)
                : null,
        productSpecs = (json['productSpecs'] as List?)
            ?.map(
                (dynamic e) => ProductSpecs.fromJson(e as Map<String, dynamic>))
            .toList(),
        productVariants = (json['productVariants'] as List?)
            ?.map((dynamic e) =>
                ProductVariants.fromJson(e as Map<String, dynamic>))
            .toList(),
        productSizeCategory =
            (json['productSizeCategory'] as Map<String, dynamic>?) != null
                ? SizeCategories.fromJson(
                    json['productSizeCategory'] as Map<String, dynamic>)
                : null,
        itemsPerOrder = json['itemsPerOrder'] as int?,
        productPolicies =
            (json['productPolicies'] as Map<String, dynamic>?) != null
                ? ProductPolicies.fromJson(
                    json['productPolicies'] as Map<String, dynamic>)
                : null,
        createdAt = json['createdAt'] as String?,
        updatedAt = json['updatedAt'] as String?,
        productStatus = json['productStatus'] as String?,
        isReady = json['isReady'] as bool?,
        isCompleted = json['isCompleted'] as bool?,
        validationHistory = (json['validationHistory'] as List?)
            ?.map((dynamic e) =>
                ValidationHistory.fromJson(e as Map<String, dynamic>))
            .toList(),
        productEIN = json['productEIN'];

  Map<String, dynamic> toJson() => {
        'productCatalog': productCatalog,
        '_id': id,
        'ar': ar?.toJson(),
        'productOwnerId': productOwnerId?.toJson(),
        'productName': productName,
        'productDescription': productDescription,
        'isMatched': isMatched,
        'isHidden': isHidden,
        'productManufacturerId': productManufacturerId,
        'productCategory': productCategory?.toJson(),
        'productSubCategory': productSubCategory?.toJson(),
        'productBrand': productBrand?.toJson(),
        'productKeyWords': productKeyWords,
        'productImages': productImages,
        'productPrice': productPrice,
        'isFreeDeliveryItem': isFreeDeliveryItem,
        'productAvailableQte': productAvailableQte,
        'productNotifOnMinQte': productNotifOnMinQte,
        'productMinQte': productMinQte,
        'productOptions': productOptions?.toJson(),
        'productSpecs': productSpecs?.map((e) => e.toJson()).toList(),
        'productVariants': productVariants?.map((e) => e.toJson()).toList(),
        'productSizeCategory': productSizeCategory?.toJson(),
        'itemsPerOrder': itemsPerOrder,
        'productPolicies': productPolicies?.toJson(),
        'createdAt': createdAt,
        'updatedAt': updatedAt,
        'productStatus': productStatus,
        'isReady': isReady,
        'isCompleted': isCompleted,
        'validationHistory': validationHistory?.map((e) => e.toJson()).toList(),
        'productEIN': productEIN
      };
}

class ValidationHistory {
  final String? typename;
  final String? status;
  final dynamic requestType;
  final List<dynamic>? returnValues;
  final dynamic returnMessage;
  final String? id;
  final List<dynamic>? updatedValues;
  final String? createdAt;
  final String? updatedAt;

  ValidationHistory({
    this.typename,
    this.status,
    this.requestType,
    this.returnValues,
    this.returnMessage,
    this.id,
    this.updatedValues,
    this.createdAt,
    this.updatedAt,
  });

  ValidationHistory.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        status = json['status'] as String?,
        requestType = json['requestType'],
        returnValues = json['returnValues'] as List?,
        returnMessage = json['returnMessage'],
        id = json['_id'] as String?,
        updatedValues = json['updatedValues'] as List?,
        createdAt = json['createdAt'] as String?,
        updatedAt = json['updatedAt'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'status': status,
        'requestType': requestType,
        'returnValues': returnValues,
        'returnMessage': returnMessage,
        '_id': id,
        'updatedValues': updatedValues,
        'createdAt': createdAt,
        'updatedAt': updatedAt
      };
}

class ProductOwnerId {
  final String? typename;
  final String? id;

  ProductOwnerId({
    this.typename,
    this.id,
  });

  ProductOwnerId.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        id = json['_id'] as String?;

  Map<String, dynamic> toJson() => {'__typename': typename, '_id': id};
}

class ProductCategory {
  final String? typename;
  final String? categoryName;
  final String? id;
  final ProductCategory? ar;

  ProductCategory({
    this.typename,
    this.categoryName,
    this.id,
    this.ar,
  });

  ProductCategory.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        categoryName = json['categoryName'] as String?,
        ar = (json['ar'] as Map<String, dynamic>?) != null
            ? ProductCategory.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        id = json['_id'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'categoryName': categoryName,
        '_id': id,
        'ar': ar?.toJson()
      };
}

class ProductSubCategory {
  final String? typename;
  final String? subCategoryName;
  final String? id;
  final ProductSubCategory? ar;

  ProductSubCategory({
    this.typename,
    this.subCategoryName,
    this.id,
    this.ar,
  });

  ProductSubCategory.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        subCategoryName = json['subCategoryName'] as String?,
        ar = (json['ar'] as Map<String, dynamic>?) != null
            ? ProductSubCategory.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        id = json['_id'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'subCategoryName': subCategoryName,
        '_id': id,
        'ar': ar?.toJson()
      };
}

class ProductBrand {
  final String? typename;
  final String? brandName;
  final String? id;
  final ProductBrand? ar;

  ProductBrand({
    this.typename,
    this.brandName,
    this.id,
    this.ar,
  });

  ProductBrand.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        brandName = json['brandName'] as String?,
        ar = (json['ar'] as Map<String, dynamic>?) != null
            ? ProductBrand.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        id = json['_id'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'brandName': brandName,
        '_id': id,
        'ar': ar?.toJson()
      };
}

class ProductOptions {
  final String? typename;
  final List<ProductSizes>? productSizes;
  final List<ProductCustomOptions>? productCustomOptions;
  final List<ProductColors>? productColors;

  ProductOptions({
    this.typename,
    this.productSizes,
    this.productCustomOptions,
    this.productColors,
  });

  ProductOptions.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        productSizes = (json['productSizes'] as List?)
            ?.map(
                (dynamic e) => ProductSizes.fromJson(e as Map<String, dynamic>))
            .toList(),
        productCustomOptions = (json['productCustomOptions'] as List?)
            ?.map((dynamic e) =>
                ProductCustomOptions.fromJson(e as Map<String, dynamic>))
            .toList(),
        productColors = (json['productColors'] as List?)
            ?.map((dynamic e) =>
                ProductColors.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'productSizes': productSizes?.map((e) => e.toJson()).toList(),
        'productCustomOptions':
            productCustomOptions?.map((e) => e.toJson()).toList(),
        'productColors': productColors?.map((e) => e.toJson()).toList()
      };
}

class ProductSizes {
  final String? typename;
  final String? sizeUnit;
  final List<OptionValues>? sizeValues;

  ProductSizes({
    this.typename,
    this.sizeUnit,
    this.sizeValues,
  });

  ProductSizes.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        sizeUnit = json['sizeUnit'] as String?,
        sizeValues = (json['sizeValues'] as List?)
            ?.map(
                (dynamic e) => OptionValues.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'sizeUnit': sizeUnit,
        'sizeValues': sizeValues?.map((e) => e.toJson()).toList()
      };
}

class OptionValues {
  final String? typename;
  final String? value;
  final bool? isValueHidden;

  OptionValues({
    this.typename,
    this.value,
    this.isValueHidden,
  });

  OptionValues.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        value = json['value'] as String?,
        isValueHidden = json['isValueHidden'] as bool?;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'value': value, 'isValueHidden': isValueHidden};
}

class ProductCustomOptions {
  final String? optionId;
  final String? optionTitle;
  final List<OptionValues>? optionValues;

  ProductCustomOptions({
    this.optionId,
    this.optionTitle,
    this.optionValues,
  });

  ProductCustomOptions.fromJson(Map<String, dynamic> json)
      : optionId = json['optionId'] as String?,
        optionTitle = json['optionTitle'] as String?,
        optionValues = (json['optionValues'] as List?)
            ?.map(
                (dynamic e) => OptionValues.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        'optionId': optionId,
        'optionTitle': optionTitle,
        'optionValues': optionValues?.map((e) => e.toJson()).toList()
      };
}

class ProductColors {
  final bool? isHidden;
  final String? id;
  final String? colorName;
  final String? colorIcon;
  final List<String>? colorImages;
  final String? colorFamily;

  ProductColors({
    this.id,
    this.isHidden,
    this.colorName,
    this.colorIcon,
    this.colorImages,
    this.colorFamily,
  });

  ProductColors.fromJson(Map<String, dynamic> json)
      : id = json['colorId'] as String?,
        isHidden = json['isHidden'] as bool?,
        colorName = json['colorName'] as String?,
        colorIcon = json['colorIcon'] as String?,
        colorImages = (json['colorImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        colorFamily = json['colorFamily'] as String?;

  Map<String, dynamic> toJson() => {
        'colorId': id,
        'colorName': colorName,
        'isHidden': isHidden,
        'colorIcon': colorIcon,
        'colorImages': colorImages,
        'colorFamily': colorFamily
      };
}

class ProductSpecs {
  final String? specsId;
  final String? specsTitle;
  final String? specsValue;

  ProductSpecs({
    this.specsId,
    this.specsTitle,
    this.specsValue,
  });

  ProductSpecs.fromJson(Map<String, dynamic> json)
      : specsId = json['specsId'] as String?,
        specsTitle = json['specsTitle'] as String?,
        specsValue = json['specsValue'] as String?;

  Map<String, dynamic> toJson() =>
      {'specsId': specsId, 'specsTitle': specsTitle, 'specsValue': specsValue};
}

class ProductVariants {
  final String? typename;
  final String? id;
  final String? variantName;
  final String? variantCode;
  final dynamic variantPrice;
  final String? variantManufacturerId;
  final int? variantQte;
  final bool? isVariantVisible;
  final List<ReturnData>? validationHistory;
  bool? isPriceReturned;
  bool? isQtyReturned;
  bool? isGtinReturned;
  final List<String>? variantImages;
  final String? variantEIN;
  final VariantAttributes? variantAttributes;
  final ProductVariants? ar;

  ProductVariants({
    this.typename,
    this.id,
    this.validationHistory,
    this.variantName,
    this.variantCode,
    this.variantPrice,
    this.isVariantVisible,
    this.variantQte,
    this.variantImages,
    this.variantEIN,
    this.variantManufacturerId,
    this.ar,
    this.variantAttributes,
    this.isPriceReturned = false,
    this.isQtyReturned = false,
    this.isGtinReturned = false,
  });

  ProductVariants.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        id = json['_id'] as String?,
        variantName = json['variantName'] as String?,
        ar = (json['ar'] as Map<String, dynamic>?) != null  
            ? ProductVariants.fromJson(json['ar'] as Map<String, dynamic>)  
            : null,
        variantManufacturerId = json['variantManufacturerId'] as String?,
        isVariantVisible = json['isVariantVisible'] as bool?,
        variantCode = json['variantCode'] as String?,
        variantPrice = json['variantPrice'],
        variantQte = json['variantQte'] as int?,
        validationHistory = (json['validationHistory'] as List?)
            ?.map((dynamic e) => ReturnData.fromJson(e as Map<String, dynamic>))
            .toList(),
        variantImages = (json['variantImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        variantEIN = json['variantEIN'] as String?,
        variantAttributes =
            (json['variantAttributes'] as Map<String, dynamic>?) != null
                ? VariantAttributes.fromJson(
                    json['variantAttributes'] as Map<String, dynamic>)
                : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        '_id': id,
        'variantName': variantName,
        'ar': ar?.toJson(),
        'validationHistory': validationHistory?.map((e) => e.toJson()).toList(),
        'variantManufacturerId': variantManufacturerId,
        'variantCode': variantCode,
        'variantPrice': variantPrice,
        'isVariantVisible': isVariantVisible,
        'variantQte': variantQte,
        'variantImages': variantImages,
        'variantEIN': variantEIN,
        'variantAttributes': variantAttributes?.toJson()
      };
}

class VariantAttributes {
  final VariantSize? variantSize;
  final VariantColor? variantColor;
  final List<VariantCustomOptions>? variantCustomOptions;

  VariantAttributes({
    this.variantSize,
    this.variantColor,
    this.variantCustomOptions,
  });

  VariantAttributes.fromJson(Map<String, dynamic> json)
      : variantSize = (((json['variantSize'] as Map<String, dynamic>?) !=
                    null) &&
                (json['variantSize']["unit"] != null))
            ? VariantSize.fromJson(json['variantSize'] as Map<String, dynamic>)
            : null,
        variantColor = (json['variantColor'] as Map<String, dynamic>?) != null
            ? VariantColor.fromJson(
                json['variantColor'] as Map<String, dynamic>)
            : null,
        variantCustomOptions = (json['variantCustomOptions'] as List?)
            ?.map((dynamic e) =>
                VariantCustomOptions.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        'variantSize': variantSize?.toJson(),
        'variantColor': variantColor?.toJson(),
        'variantCustomOptions':
            variantCustomOptions?.map((e) => e.toJson()).toList()
      };
}

class VariantSize {
  final String? size;
  final String? unit;

  VariantSize({
    this.size,
    this.unit,
  });

  VariantSize.fromJson(Map<String, dynamic> json)
      : size = json['size'] as String?,
        unit = json['unit'] as String?;

  Map<String, dynamic> toJson() => {'size': size, 'unit': unit};
}

class VariantColor {
  final String? colorName;
  String? colorId;
  String? colorIcon;
  final String? colorFamily;
  final List<String>? colorImages;

  VariantColor({
    this.colorName,
    this.colorId,
    this.colorIcon,
    this.colorFamily,
    this.colorImages,
  });

  VariantColor.fromJson(Map<String, dynamic> json)
      : colorName = json['colorName'] as String?,
        colorIcon = json['colorIcon'] as String?,
        colorFamily = json['colorFamily'] as String?,
        colorImages = (json['colorImages'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        colorId = json['colorId'] as String?;

  Map<String, dynamic> toJson() => {
        'colorName': colorName,
        'colorIcon': colorIcon,
        'colorFamily': colorFamily,
        'colorId': colorId,
        'colorImages': colorImages
      };
}

class VariantCustomOptions {
  final String? attributeTitle;
  final String? attributeValue;

  VariantCustomOptions({
    this.attributeTitle,
    this.attributeValue,
  });

  VariantCustomOptions.fromJson(Map<String, dynamic> json)
      : attributeTitle = json['attributeTitle'] as String?,
        attributeValue = json['attributeValue'] as String?;

  Map<String, dynamic> toJson() =>
      {'attributeTitle': attributeTitle, 'attributeValue': attributeValue};
}

class ProductPolicies {
  final String? typename;
  final bool? productAcceptReturn;
  final String? productReturnDuration;
  final String? productWarrantyDuration;
  final bool? productFreeReturn;
  final bool? productNotFreeReturn;
  final String? productReturnPolicy;

  ProductPolicies({
    this.typename,
    this.productAcceptReturn,
    this.productReturnDuration,
    this.productWarrantyDuration,
    this.productFreeReturn,
    this.productNotFreeReturn,
    this.productReturnPolicy,
  });

  ProductPolicies.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        productAcceptReturn = json['productAcceptReturn'] as bool?,
        productReturnDuration = json['productReturnDuration'] as String?,
        productWarrantyDuration = json['productWarrantyDuration'] as String?,
        productFreeReturn = json['productFreeReturn'] as bool?,
        productNotFreeReturn = json['productNotFreeReturn'] as bool?,
        productReturnPolicy = json['productReturnPolicy'] as String?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'productAcceptReturn': productAcceptReturn,
        'productReturnDuration': productReturnDuration,
        'productWarrantyDuration': productWarrantyDuration,
        'productFreeReturn': productFreeReturn,
        'productNotFreeReturn': productNotFreeReturn,
        'productReturnPolicy': productReturnPolicy
      };
}

class ReturnData {
  final String? typename;
  final String? id;
  final String? status;
  final String? returnMessage;
  final String? infoMessage;
  final List<String>? returnValues;
  final List<String>? updatedValues;
  final String? requestType;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ReturnData({
    this.typename,
    this.id,
    this.status,
    this.returnMessage,
    this.infoMessage,
    this.returnValues,
    this.updatedValues,
    this.requestType,
    this.createdAt,
    this.updatedAt,
  });

  factory ReturnData.fromJson(Map<String, dynamic> json) {
    return ReturnData(
      typename: json['__typename'] as String?,
      id: json['_id'] as String?,
      status: json['status'] as String?,
      returnMessage: json['returnMessage'] as String?,
      infoMessage: json['infoMessage'] as String?,
      returnValues: json['returnValues'] != null
          ? (json['returnValues'] as List).map((e) => e as String).toList()
          : null,
      updatedValues: json['updatedValues'] != null
          ? (json['updatedValues'] as List).map((e) => e as String).toList()
          : null,
      requestType: json['requestType'] as String?,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        '_id': id ?? '',
        'status': status ?? '',
        'returnMessage': returnMessage ?? '',
        'infoMessage': infoMessage ?? '',
        'returnValues': returnValues ?? [],
        'updatedValues': updatedValues ?? [],
        'requestType': requestType ?? '',
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
      };
}
