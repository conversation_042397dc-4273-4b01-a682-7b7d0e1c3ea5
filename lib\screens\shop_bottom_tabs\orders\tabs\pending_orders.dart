import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/controller/orders_controller.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/new_orders.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/return_orders.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class PendingOrder extends StatefulWidget {
  const PendingOrder({
    Key? key,
  }) : super(key: key);

  @override
  State<PendingOrder> createState() => _PendingOrderState();
}

class _PendingOrderState extends State<PendingOrder>
    with AutomaticKeepAliveClientMixin<PendingOrder> {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final applocal = AppLocalizations.of(context)!;
    final controller = Get.find<OrdersController>();
    return Padding(
      padding:
          const EdgeInsets.only(left: kLeftSpace, right: kRightSpace, top: 10),
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            toolbarHeight: 0,
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(48),
              child: ColoredBox(
                color: AppColors.backgroundColorDark,
                child: Obx(
                  () => TabBar(
                    labelColor: AppColors.colorPrimary,
                    unselectedLabelColor: Colors.white.withOpacity(0.6),
                    indicatorColor: AppColors.colorPrimary,
                    labelStyle: FontStyles.fontSemibold(),
                    unselectedLabelStyle: FontStyles.fontRegular(),
                    tabs: [
                      Tab(
                          text: applocal.newOrderTab(controller.formatNumber(
                              controller.pendingOrderCount.value))),
                      Tab(text: applocal.newReurnOrderTab("")),
                    ],
                  ),
                ),
              ),
            ),
          ),
          body: const TabBarView(
            physics: NeverScrollableScrollPhysics(),
            children: [NewOrders(), ReturnOrder()],
          ),
        ),
      ),
    );
  }
}
