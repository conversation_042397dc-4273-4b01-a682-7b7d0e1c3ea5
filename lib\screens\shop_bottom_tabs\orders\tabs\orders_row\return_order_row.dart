import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/enums/enums.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/helper/url_loader.dart';
import 'package:overolasuppliers/model/base_model.dart';
import 'package:overolasuppliers/model/orders/order_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/controller/orders_controller.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/orders/tabs/components/product_info.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';

class ReturnOrderRow extends GetView<OrdersController>
    implements ServerResponse {
  final bool isHistory;
  final OrderItems orderItems;
  ReturnOrderRow({Key? key, this.isHistory = false, required this.orderItems})
      : super(key: key);

  late GraphQlInitilize _request;

  @override
  Widget build(BuildContext context) {
    _request = GraphQlInitilize(this);
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: AppColors.headerColorDark,
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () => Get.toNamed(RouteNames.orderDetailScreen, arguments: [
          isHistory ? OrderType.returnConfirmOrder : OrderType.returnOrder,
          orderItems
        ]),
        child: Stack(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            orderItems.orderItemCode ?? "",
                            style: FontStyles.fontMedium(fontSize: 16),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            "ordered in ${GlobalMethods.convertTimeFormate(orderItems.createdAt ?? "", format: "dd MMM, yyyy hh:mm a")}",
                            style: FontStyles.fontRegular(
                                fontSize: 12, color: Colors.white.withOpacity(0.6)),
                          ),
                        ],
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: AppColors.colorTangerine,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          "RETURNED",
                          style: FontStyles.fontMedium(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Customer Info
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            SvgPicture.string(SvgStrings.iconOrderUser),
                            const SizedBox(width: 8),
                            Text(
                              orderItems.clientId?.userId?.userName ?? "",
                              style: FontStyles.fontRegular(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.8),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            SvgPicture.string(SvgStrings.iconOrderLocationPin),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                orderItems.billingAddress?.address ?? "",
                                style: FontStyles.fontRegular(
                                  fontSize: 14,
                                  color: Colors.white.withOpacity(0.8),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Documents Section
                  Row(
                    children: [
                      _buildDocumentButton(
                        icon: SvgStrings.iconInvoice,
                        label: "Invoice Summary",
                        onTap: () => _handleDocumentTap(orderItems.invoice ?? ""),
                      ),
                      const SizedBox(width: 12),
                      _buildDocumentButton(
                        icon: SvgStrings.iconInvoice,
                        label: "Shipment Info",
                        onTap: () => _handleDocumentTap(orderItems.shipmentQrCode ?? ""),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Status Timeline
                  _buildStatusTimeline(),
                  const SizedBox(height: 16),

                  // Products List
                  SizedBox(
                    height: 132,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: orderItems.items?.length ?? 0,
                      itemBuilder: (context, index) => Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: ProductInfo(item: (orderItems.items ?? [])[index]),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Footer with Price and Actions
                  _buildFooter(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusTimeline() {
    return Column(
      children: [
        _buildTimelineItem(
          icon: SvgStrings.iconDelivery,
          status: "Delivered",
          time: "",
        ),
        _buildTimelineItem(
          icon: SvgStrings.iconReturned,
          status: "Returned",
          time: "11:30 PM, 20/09/2020",
        ),
        if (isHistory)
          _buildTimelineItem(
            icon: SvgStrings.iconReturnedConfirmed,
            status: "Return Confirmed",
            time: "11:30 PM, 20/09/2020",
          ),
      ],
    );
  }

  Widget _buildTimelineItem({
    required String icon,
    required String status,
    required String time,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SvgPicture.string(icon),
          const SizedBox(width: 8),
          Text(
            status,
            style: FontStyles.fontRegular(fontSize: 12),
          ),
          Text(
            time.isNotEmpty ? " $time" : "",
            style: FontStyles.fontRegular(
              fontSize: 12,
              color: Colors.white.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                SvgPicture.string(SvgStrings.iconPriceTag),
                const SizedBox(width: 8),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "Total: ",
                        style: FontStyles.fontSemibold(fontSize: 14),
                      ),
                      TextSpan(
                        text: "${orderItems.finalCost ?? 0}",
                        style: FontStyles.fontBold(fontSize: 18),
                      ),
                      TextSpan(
                        text: " AED",
                        style: FontStyles.fontSemibold(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: AppColors.colorPrimary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                EnStrings.details,
                style: FontStyles.fontMedium(
                  fontSize: 14,
                  color: AppColors.colorPrimary,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: ElbaabButtonWidget(
                onPress: () {},
                colors: AppColors.colorSecondary_Red,
                text: EnStrings.reject,
                height: 40,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: ElbaabButtonWidget(
                onPress: () => _request.runMutation(
                  context: context,
                  query: GraphQlQuries.acceptReturnOrderItem,
                  variables: GraphQlVariables.acceptReturnOrderItem(
                    orderItemId: orderItems.id ?? "",
                  ),
                ),
                colors: AppColors.colorPrimary,
                text: EnStrings.acceptReturn,
                height: 40,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper methods
  Widget _buildDocumentButton({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.white.withOpacity(0.2)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.string(icon),
              const SizedBox(width: 8),
              Text(
                label,
                style: FontStyles.fontRegular(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 13,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleDocumentTap(String url) {
    if (Platform.isIOS) {
      Get.to(
        () => UrlLoader(url: url),
        fullscreenDialog: true,
        transition: Transition.circularReveal,
      );
    } else {
      GlobalMethods.launchInWebView(url);
    }
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    BaseModel model = BaseModel.fromJson(response);
    if (model.status == statusOK) {}
  }
}
