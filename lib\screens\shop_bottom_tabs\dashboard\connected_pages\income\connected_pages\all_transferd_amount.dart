import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/income_expense/all_withdrawals_model.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/components/row_details.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/components/row_title.dart';
import 'package:overolasuppliers/widgets/elbaab_label_container.dart';

class AllTransferdAmount extends StatefulWidget {
  const AllTransferdAmount({super.key});

  @override
  State<AllTransferdAmount> createState() => _AllTransferdAmountState();
}

class _AllTransferdAmountState extends State<AllTransferdAmount>
    implements ServerResponse {
  RxString sortByStatus = 'All'.obs;
  bool isLoading = false;

  List<String> arrstatus = ["All"];

  List<WithDrawals> allTransferdAmount = Get.arguments[0];

  RxList<WithDrawals> allTransferdAmountFilter = RxList<WithDrawals>([]);

  late GraphQlInitilize request;

  PaginatedWithDrawalModel paginatedWithDrawalModel =
      PaginatedWithDrawalModel(hasNextPage: true, page: 1);

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    request = GraphQlInitilize(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      for (var element in allTransferdAmount) {
        allTransferdAmountFilter.add(element);
        if (!arrstatus.contains(element.status)) {
          arrstatus.add(element.status ?? "");
        }
      }
    });

    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([]);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      if (isLoading ||
          (paginatedWithDrawalModel.hasNextPage ?? false) == false) {
        return;
      }
      isLoading = true;
      request.runQuery(
          context: context,
          query: GraphQlQuries.getWithDrawals,
          variables: GraphQlVariables.getPaginated(
              itemsNumber: 30,
              page: ((paginatedWithDrawalModel.page ?? 0) + 1)));
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: ElbaabLabelContainer(
                      onTap: () =>
                          BottomSheets.showListPicker(context, arrstatus)
                              .then((value) {
                        sortByStatus.value = arrstatus[value];
                        allTransferdAmountFilter.clear();
                        if (value == 0) {
                          for (var element in allTransferdAmount) {
                            allTransferdAmountFilter.add(element);
                          }
                        } else {
                          for (var element in allTransferdAmount) {
                            if (element.status == sortByStatus.value) {
                              allTransferdAmountFilter.add(element);
                            }
                          }
                        }
                      }),
                      leading: Obx(
                        () => Text(
                          sortByStatus.value,
                          style: FontStyles.fontRegular(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.6),
                          ),
                        ),
                      ),
                      label: "Status",
                      trailing: const Icon(Icons.arrow_drop_down),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30),
                    child: IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(
                        Icons.close,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              height: 50,
              margin: const EdgeInsets.only(top: 15),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(5),
                  topRight: Radius.circular(5),
                ),
                color: AppColors.colotMidGray,
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(children: <Widget>[
                  const Expanded(
                      flex: 1, child: RowTitle(title: EnStrings.withdrawalID)),
                  const SizedBox(
                    width: 10,
                  ),
                  const Expanded(flex: 1, child: RowTitle(title: "Requested Amount")),
                  const SizedBox(
                    width: 10,
                  ),
                  const Expanded(
                      flex: 1, child: RowTitle(title: "Amount You Recived")),
                  const Expanded(flex: 1, child: RowTitle(title: EnStrings.status)),
                  const Expanded(flex: 2, child: RowTitle(title: EnStrings.date)),
                  IconButton(
                    icon: const RowTitle(title: "Receipt"),
                    onPressed: () {},
                  )
                ]),
              ),
            ),
            Expanded(
              child: Obx(
                () => ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: allTransferdAmountFilter.length,
                    controller: _scrollController,
                    itemBuilder: (context, index) {
                      WithDrawals item = allTransferdAmountFilter[index];
                      return Container(
                        decoration: BoxDecoration(
                          color: AppColors.feildColorDark,
                          borderRadius:
                              index < (allTransferdAmountFilter.length - 1)
                                  ? BorderRadius.zero
                                  : const BorderRadius.only(
                                      bottomLeft: Radius.circular(5),
                                      bottomRight: Radius.circular(5),
                                    ),
                        ),
                        height: 51,
                        child: Column(
                          children: [
                            SizedBox(
                              height: 50,
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Row(children: <Widget>[
                                  Expanded(
                                    flex: 1,
                                    child: RowDetails(
                                      detail: item.withdrawalId ?? "",
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  Expanded(
                                    child: Column(
                                      children: [
                                        RowDetails(
                                            detail:
                                                "${item.requestedAmount ?? 0} AED"),
                                        Align(
                                          alignment: Alignment.centerRight,
                                          child: Text(
                                            "${item.withdrawalFee ?? 0} % Fee",
                                            style: FontStyles.fontMedium(
                                              fontSize: 10,
                                              color: AppColors.colorDanger,
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  Expanded(
                                    flex: 1,
                                    child: RowDetails(
                                      detail:
                                          "${(item.requestedAmount - (item.requestedAmount * (item.withdrawalFee / 100)))} AED",
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  Expanded(
                                      flex: 1,
                                      child: RowDetails(
                                        detail: item.status ?? "",
                                        textColor: (item.status ?? "") == ""
                                            ? AppColors.colorPrimary
                                            : (item.status ?? "") ==
                                                    "Processing"
                                                ? AppColors.colorSecondaryYellow
                                                : (item.status ?? "") ==
                                                        "Released"
                                                    ? AppColors.colorSecondary
                                                    : AppColors.colorDanger,
                                      )),
                                  Expanded(
                                    flex: 2,
                                    child: RowDetails(
                                      detail: GlobalMethods.convertTimeFormate(
                                          ((item.status ?? "") == "Processing"
                                              ? item.createdAt ?? ""
                                              : item.updatedAt ?? ""),
                                          format: "dd MMM, yyyy hh:mm a"),
                                    ),
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      ((item.status ?? "") == "Processing")
                                          ? Icons.pending_actions_rounded
                                          : (item.status == "Released")
                                              ? Icons.receipt_long_outlined
                                              : Icons.assignment_late,
                                      color:
                                          ((item.status ?? "") == "Processing")
                                              ? AppColors.colorYellow
                                              : (item.status == "Released")
                                                  ? AppColors.colorPrimary
                                                  : AppColors.colorDanger,
                                      size: 25,
                                    ),
                                    onPressed: () {
                                      if (item.status == "Released") {
                                        GlobalMethods.launchInWebView(
                                            item.receipt ?? "",
                                            inApp: false);
                                      }
                                    },
                                  )
                                ]),
                              ),
                            ),
                            Container(
                              height: 1,
                              color: Colors.white.withOpacity(0.08),
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 10),
                            )
                          ],
                        ),
                      );
                    }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  onError(error, String type) {
    isLoading = false;
  }

  @override
  onSucess(response, String type) {
    WithDrawalsModel model = WithDrawalsModel.fromJson(response);
    paginatedWithDrawalModel = model.withdrawals!;
    isLoading = false;
    if (model.status == statusOK) {
      allTransferdAmount.addAll(model.withdrawals?.items ?? []);
      allTransferdAmountFilter.addAll(model.withdrawals?.items ?? []);
    }
  }
}
