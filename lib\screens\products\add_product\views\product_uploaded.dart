import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/svg_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_gradient_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ProductUploaded extends StatelessWidget {
  const ProductUploaded({super.key});

  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: ElbaabHeader(
        title: appLocal.addProduct,
      ),
      body: Column(
        children: <Widget>[
          Padding(
            padding:
                const EdgeInsets.only(left: 20, right: 20, bottom: 20, top: 50),
            child: Container(
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                color: AppColors.headerColorDark,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    SvgPicture.string(SvgStrings.iconTick),
                    const SizedBox(height: 13),
                    Text(
                      Get.arguments[0],
                      textAlign: TextAlign.center,
                      style: FontStyles.fontBold(),
                    ),
                    const SizedBox(height: 13),
                    Image.asset('assets/images/upload_product.png')
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        height: 90,
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Center(
          child: ElbaabGradientButtonWidget(
            onPress: () => Get.offAllNamed(RouteNames.shopHomeScreen),
            text: appLocal.backToShop,
          ),
        ),
      ),
    );
  }
}
