import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/model/review_by_product.dart';
import 'package:overolasuppliers/model/reviews_model.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';

class ProductReviews extends StatefulWidget {
  final Items item;
  const ProductReviews({super.key, required this.item});

  @override
  State<ProductReviews> createState() => _ProductReviewsState();
}

class _ProductReviewsState extends State<ProductReviews> implements ServerResponse {

  RxList<UserReview> arrReviews = <UserReview>[].obs;
  late GraphQlInitilize _request;
  late ReviewsByProduct productReviews;
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    scrollController.addListener(_onScroll);
    _request = GraphQlInitilize(this);
    Future.delayed(
        Duration.zero,
        () => {
              arrReviews.clear(),
              _request.runQuery(
                context: context,
                query: GraphQlQuries.getReviewsByProductId,
                variables: GraphQlVariables.getReviewsByProductId(
                    itemsNumber: 10,
                    page: 1,
                    productId: widget.item.reviews?.first.orderItemId?.items
                                ?.first.variant !=
                            null
                        ? widget.item.reviews?.first.orderItemId?.items?.first
                                .variant?.productId?.id ??
                            ""
                        : widget.item.reviews?.first.orderItemId?.items?.first
                                .product?.id ??
                            ""),
              )
            });
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      if (productReviews.reviews?.hasNextPage ?? false) {
        _request.runQuery(
            context: context,
            query: GraphQlQuries.getReviewsByProductId,
            variables: GraphQlVariables.getReviewsByProductId(
                itemsNumber: 10,
                page: ((productReviews.reviews?.page ?? 0) + 1),
                productId: widget.item.reviews?.first.orderItemId?.items?.first
                            .variant !=
                        null
                    ? widget.item.reviews?.first.orderItemId?.items?.first
                            .variant?.productId?.id ??
                        ""
                    : widget.item.reviews?.first.orderItemId?.items?.first
                            .product?.id ??
                        ""));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const ElbaabHeader(
        title:EnStrings.productReviews,
        leadingBack: true,
      ),
      body: Column(
        children: [
          Hero(
            tag: "productReview${widget.item.reviews?.first.createdAt}",
            transitionOnUserGestures: true,
            child: Container(
              height: 150,
              padding: const EdgeInsets.all(8),
              color: AppColors.headerColorDark,
              child: Row(
                children: [
                  SizedBox(
                    height: 132,
                    width: 90,
                    child: GlobalMethods.netWorkImage(
                        widget.item.reviews?.first.orderItemId?.items?.first
                                    .variant !=
                                null
                            ? widget.item.reviews?.first.orderItemId?.items
                                    ?.first.variant?.variantImages?.first ??
                                ""
                            : widget.item.reviews?.first.orderItemId?.items
                                    ?.first.product?.productImages?.first ??
                                "",
                        BorderRadius.circular(5),
                        BoxFit.cover),
                  ),
                  const SizedBox(width: 11),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.item.reviews?.first.orderItemId?.items?.first
                                      .variant !=
                                  null
                              ? widget.item.reviews?.first.orderItemId?.items
                                      ?.first.variant?.productId?.productName ??
                                  ""
                              : widget.item.reviews?.first.orderItemId?.items
                                      ?.first.product?.productName ??
                                  "",
                          style: FontStyles.fontBold(fontSize: 12),
                        ),
                        const Spacer(),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                widget.item.reviews?.first.orderItemId?.items
                                            ?.first.variant !=
                                        null
                                    ? EnStrings.variantEin
                                    : EnStrings.productEin,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                "#${widget.item.reviews?.first.orderItemId?.items?.first.variant != null ? (widget.item.reviews?.first.orderItemId?.items?.first.variant?.variantEIN ?? "") : widget.item.reviews?.first.orderItemId?.items?.first.product?.productEIN ?? ""}",
                                style: FontStyles.fontRegular(
                                  fontSize: 12,
                                  color: Colors.white.withOpacity(0.6),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const Spacer(),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                "Shipment Code",
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                "#${(widget.item.reviews?.first.orderItemId?.orderItemCode ?? "").substring((widget.item.reviews?.first.orderItemId?.orderItemCode ?? "").length - 8)}",
                                style: FontStyles.fontRegular(
                                  fontSize: 12,
                                  color: Colors.white.withOpacity(0.6),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const Spacer(),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                EnStrings.qty,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                "${widget.item.reviews?.first.orderItemId?.items?.first.quantity ?? 0} pieces",
                                style: FontStyles.fontRegular(
                                  fontSize: 12,
                                  color: Colors.white.withOpacity(0.6),
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (widget.item.reviews?.first.orderItemId?.items?.first
                                .variant !=
                            null)
                          const Spacer(),
                        if (widget.item.reviews?.first.orderItemId?.items?.first
                                .variant !=
                            null)
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  widget
                                              .item
                                              .reviews
                                              ?.first
                                              .orderItemId
                                              ?.items
                                              ?.first
                                              .variant
                                              ?.variantAttributes
                                              ?.variantColor !=
                                          null
                                      ? EnStrings.color
                                      : widget
                                                  .item
                                                  .reviews
                                                  ?.first
                                                  .orderItemId
                                                  ?.items
                                                  ?.first
                                                  .variant
                                                  ?.variantAttributes
                                                  ?.variantSize !=
                                              null
                                          ? EnStrings.size
                                          : EnStrings.customOption,
                                  style: FontStyles.fontRegular(fontSize: 12),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: widget
                                            .item
                                            .reviews
                                            ?.first
                                            .orderItemId
                                            ?.items
                                            ?.first
                                            .variant
                                            ?.variantAttributes
                                            ?.variantColor !=
                                        null
                                    ? Row(
                                        children: [
                                          SizedBox(
                                            height: 12,
                                            width: 12,
                                            child: GlobalMethods.netWorkImage(
                                                widget
                                                        .item
                                                        .reviews
                                                        ?.first
                                                        .orderItemId
                                                        ?.items
                                                        ?.first
                                                        .variant
                                                        ?.variantAttributes
                                                        ?.variantColor
                                                        ?.colorIcon ??
                                                    "",
                                                BorderRadius.circular(6),
                                                BoxFit.cover),
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            widget
                                                    .item
                                                    .reviews
                                                    ?.first
                                                    .orderItemId
                                                    ?.items
                                                    ?.first
                                                    .variant
                                                    ?.variantAttributes
                                                    ?.variantColor
                                                    ?.colorFamily ??
                                                "",
                                            style: FontStyles.fontRegular(
                                              fontSize: 12,
                                              color:
                                                  Colors.white.withOpacity(0.6),
                                            ),
                                          ),
                                        ],
                                      )
                                    : widget
                                                .item
                                                .reviews
                                                ?.first
                                                .orderItemId
                                                ?.items
                                                ?.first
                                                .variant
                                                ?.variantAttributes
                                                ?.variantSize !=
                                            null
                                        ? Text(
                                            widget
                                                    .item
                                                    .reviews
                                                    ?.first
                                                    .orderItemId
                                                    ?.items
                                                    ?.first
                                                    .variant
                                                    ?.variantAttributes
                                                    ?.variantSize
                                                    ?.size ??
                                                "",
                                            style: FontStyles.fontRegular(
                                              fontSize: 12,
                                              color:
                                                  Colors.white.withOpacity(0.6),
                                            ),
                                          )
                                        : SizedBox(
                                            height: 20,
                                            child: ListView.builder(
                                                itemCount: widget
                                                    .item
                                                    .reviews
                                                    ?.first
                                                    .orderItemId
                                                    ?.items
                                                    ?.first
                                                    .variant
                                                    ?.variantAttributes
                                                    ?.variantCustomOptions
                                                    ?.length,
                                                scrollDirection:
                                                    Axis.horizontal,
                                                itemBuilder: (context, index) {
                                                  VariantCustomOptions options =
                                                      (widget
                                                              .item
                                                              .reviews
                                                              ?.first
                                                              .orderItemId
                                                              ?.items
                                                              ?.first
                                                              .variant
                                                              ?.variantAttributes
                                                              ?.variantCustomOptions ??
                                                          [])[index];
                                                  return Container(
                                                    height: 20,
                                                    decoration: BoxDecoration(
                                                      border: Border.all(
                                                        width: 1,
                                                        color: Colors.white24,
                                                      ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              5),
                                                    ),
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 5),
                                                    child: Text(
                                                      "${options.attributeTitle ?? ""} - ${options.attributeValue ?? ""}",
                                                      style: FontStyles
                                                          .fontRegular(
                                                        fontSize: 12,
                                                        color: Colors.white
                                                            .withOpacity(0.6),
                                                      ),
                                                    ),
                                                  );
                                                }),
                                          ),
                              ),
                            ],
                          ),
                        const Spacer(),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                EnStrings.price,
                                style: FontStyles.fontRegular(fontSize: 12),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                "${widget.item.reviews?.first.orderItemId?.items?.first.variant != null ? (widget.item.reviews?.first.orderItemId?.items?.first.variant?.variantPrice ?? 0) : (widget.item.reviews?.first.orderItemId?.items?.first.product?.productPrice ?? 0)} AED",
                                style: FontStyles.fontMedium(
                                  color: Colors.white.withOpacity(0.6),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          Expanded(
            child: Obx(
              () => ListView.builder(
                  itemCount: arrReviews.length,
                  controller: scrollController,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              SizedBox(
                                height: 24,
                                width: 24,
                                child: GlobalMethods.netWorkImage(
                                    arrReviews[index]
                                            .clientId
                                            ?.userId
                                            ?.avatar ??
                                        "",
                                    BorderRadius.circular(12),
                                    BoxFit.cover,
                                    // userName: arrReviews[index]
                                    //         .clientId
                                    //         ?.userId
                                    //         ?.userName ??
                                    //     ""
                                        ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  arrReviews[index]
                                          .clientId
                                          ?.userId
                                          ?.userName ??
                                      "",
                                  style: FontStyles.fontBold(fontSize: 10),
                                ),
                              ),
                              Text(
                                GlobalMethods.timeAgo(DateTime.parse(
                                    arrReviews[index].createdAt ?? "")),
                                style: FontStyles.fontLight(
                                  fontSize: 10,
                                  color: Colors.white.withOpacity(0.5),
                                ),
                              ),
                            ],
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 25, top: 10),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.star,
                                  color: AppColors.colorRatingStar,
                                  size: 12,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  "${arrReviews[index].rate ?? 0}",
                                  style: FontStyles.fontMedium(fontSize: 10),
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    arrReviews[index].reviewMessage ?? "",
                                    style: FontStyles.fontRegular(
                                      fontSize: 12,
                                      color: Colors.white.withOpacity(0.5),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                          Container(
                            height: 1,
                            margin: const EdgeInsets.only(left: 25),
                            color: Colors.white.withOpacity(0.4),
                          ),
                        ],
                      ),
                    );
                  }),
            ),
          )
        ],
      ),
    );
  }

  @override
  onError(error, String type) {
    print(error);
  }


   @override
  onSucess(response, String type) {
    productReviews = ReviewsByProduct.fromJson(response);
    if (productReviews.status == statusOK) {
      arrReviews.addAll(productReviews.reviews?.items ?? []);
    }
  }
}
