import 'package:flutter/material.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/widgets/elbaab_name_widget.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class VerifyNumber extends StatelessWidget {
  final ValueNotifier<int> time;

  const VerifyNumber({Key? key, required this.time}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final appLocal = AppLocalizations.of(context)!;
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          const ElbaabNameWidget(),
          const SizedBox(height: 30),
           Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'Verify mobile number',
              style: FontStyles.fontSemibold(fontSize: 25),
            ),
          ),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'To verify your mobile number, we\'ve sent a One Time Password (OTP) to your phone Number',
              style: TextStyle(
                fontFamily: 'Poppins',
                fontSize: 14,
                color: Colors.white.withOpacity(0.55),
              ),
            ),
          ),
          const SizedBox(
            height: 30,
          ),
          PinFieldAutoFill(
            decoration: UnderlineDecoration(
              textStyle: const TextStyle(fontSize: 20),
              colorBuilder:
                  const FixedColorBuilder(Color.fromRGBO(203, 208, 220, 1)),
            ),
            currentCode: "",
            onCodeChanged: (code) {
              if (code!.length == 6) {
                FocusScope.of(context).requestFocus(FocusNode());
              }
            },
          ),
          ValueListenableBuilder(
            valueListenable: time,
            builder: (context, value, child) {
              return Padding(
                padding: const EdgeInsets.only(top: 24.0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: value == 0
                      ? InkWell(
                          onTap: () {},
                          child: RichText(
                            text: TextSpan(children: <TextSpan>[
                              const TextSpan(
                                text: 'Not Received? ',
                                style: TextStyle(
                                  fontFamily: 'Poppins',
                                  fontSize: 10,
                                ),
                              ),
                              TextSpan(
                                text: 'Resend OTP',
                                style: TextStyle(
                                  fontFamily: 'Poppins',
                                  fontSize: 10,
                                  decoration: TextDecoration.underline,
                                  color: AppColors.colorPrimary,
                                ),
                              ),
                            ]),
                          ),
                        )
                      : Text(
                          appLocal.secondRemaing('$value'),
                          style: FontStyles.fontRegular(fontSize: 15),
                        ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
