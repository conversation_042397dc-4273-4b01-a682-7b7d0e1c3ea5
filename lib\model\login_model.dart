import 'package:overolasuppliers/model/shop/ViewShopModel.dart';

class LoginModel {
  final int? status;
  final String? message;
  final String? arMessage;
  final String? token;
  final User? user;

  LoginModel({
    this.status,
    this.message,
    this.token,
    this.arMessage,
    this.user,
  });

  LoginModel.fromJson(Map<String, dynamic> json)
      : status = json['status'] as int?,
        message = json['message'] as String?,
        token = json['token'] as String?,
        arMessage = json['arMessage'] as String?,
        user = (json['user'] as Map<String, dynamic>?) != null
            ? User.fromJson(json['user'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        'status': status,
        'message': message,
        'arMessage': arMessage,
        'token': token,
        'user': user?.toJson()
      };
}

class User {
  final String? id;
  final String? userPhoneNumber;
  final bool? isEmailVerified;
  final bool? isPhoneVerified;
  final List<String>? firebaseDeviceToken;
  final Supplier? supplier;

  User({
    this.id,
    this.userPhoneNumber,
    this.isEmailVerified,
    this.isPhoneVerified,
    this.firebaseDeviceToken,
    this.supplier,
  });

  User.fromJson(Map<String, dynamic> json)
      : id = json['_id'] as String?,
        userPhoneNumber = json['userPhoneNumber'] as String?,
        isEmailVerified = json['isEmailVerified'] as bool?,
        isPhoneVerified = json['isPhoneVerified'] as bool?,
        firebaseDeviceToken = (json['firebaseDeviceToken'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        supplier = (json['supplier'] as Map<String, dynamic>?) != null
            ? Supplier.fromJson(json['supplier'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        '_id': id,
        'userPhoneNumber': userPhoneNumber,
        'isEmailVerified': isEmailVerified,
        'isPhoneVerified': isPhoneVerified,
        'firebaseDeviceToken': firebaseDeviceToken,
        'supplier': supplier?.toJson()
      };
}

class Supplier {
  final String? id;
  final Shop? shopId;
  final String? bussinessStatus;
  final String? bussinessOwnerLegalId;
  final String? bussinessTradeCertificate;
  final String? bussinessName;
  final String? bussinessOwnerName;
  final String? tradeCertificateExpiryDate;
  final String? ownerLegalIdExpiryDate;
  final List<ValidationHistory>? validationHistory;

  Supplier({
    this.id,
    this.shopId,
    this.bussinessStatus,
    this.bussinessOwnerLegalId,
    this.bussinessTradeCertificate,
    this.bussinessName,
    this.bussinessOwnerName,
    this.tradeCertificateExpiryDate,
    this.ownerLegalIdExpiryDate,
    this.validationHistory,
  });

  Supplier.fromJson(Map<String, dynamic> json)
      : id = json['_id'] as String?,
        shopId = (json['shopId'] as Map<String, dynamic>?) != null
            ? Shop.fromJson(json['shopId'] as Map<String, dynamic>)
            : null,
        bussinessStatus = json['bussinessStatus'] as String?,
        bussinessOwnerLegalId = json['bussinessOwnerLegalId'] as String?,
        bussinessTradeCertificate =
            json['bussinessTradeCertificate'] as String?,
        bussinessName = json['bussinessName'] as String?,
        bussinessOwnerName = json['bussinessOwnerName'] as String?,
        tradeCertificateExpiryDate =
            json['tradeCertificateExpiryDate'] as String?,
        ownerLegalIdExpiryDate = json['ownerLegalIdExpiryDate'] as String?,
        validationHistory = (json['validationHistory'] as List?)
            ?.map((dynamic e) =>
                ValidationHistory.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        '_id': id,
        'shopId': shopId?.toJson(),
        'bussinessStatus': bussinessStatus,
        'bussinessOwnerLegalId': bussinessOwnerLegalId,
        'bussinessTradeCertificate': bussinessTradeCertificate,
        'bussinessName': bussinessName,
        'bussinessOwnerName': bussinessOwnerName,
        'tradeCertificateExpiryDate': tradeCertificateExpiryDate,
        'ownerLegalIdExpiryDate': ownerLegalIdExpiryDate,
        'validationHistory': validationHistory?.map((e) => e.toJson()).toList()
      };
}

class ValidationHistory {
  final List<dynamic>? returnValues;
  final String? returnMessage;
  final String? createdAt;

  ValidationHistory({
    this.returnValues,
    this.returnMessage,
    this.createdAt,
  });

  ValidationHistory.fromJson(Map<String, dynamic> json)
      : returnValues = json['returnValues'] as List<dynamic>?,
        createdAt = json['createdAt'] as String?,
        returnMessage = json['returnMessage'] as String?;

  Map<String, dynamic> toJson() => {
        'returnValues': returnValues,
        'returnMessage': returnMessage,
        'createdAt': createdAt
      };
}
