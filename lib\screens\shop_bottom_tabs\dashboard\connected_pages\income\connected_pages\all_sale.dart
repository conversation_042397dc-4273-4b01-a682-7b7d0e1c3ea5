
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/colors/AppColors.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/constant/global_methods.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_initilize.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_quries.dart';
import 'package:overolasuppliers/helper/graphQl/graphql_variables.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/helper/style/font_style_constants.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/components/row_details.dart';
import 'package:overolasuppliers/screens/shop_bottom_tabs/dashboard/connected_pages/income/connected_pages/components/row_title.dart';
import 'package:overolasuppliers/widgets/elbaab_button_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_feild_container_widget.dart';
import 'package:overolasuppliers/widgets/elbaab_header.dart';
import 'package:overolasuppliers/widgets/elbaab_input_textfield.dart';
import 'package:overolasuppliers/widgets/elbaab_label_container.dart';

import '../../../../../../model/income_expense/all_sales_model.dart';

class AllSales extends StatefulWidget {
  const AllSales({super.key});

  @override
  State<AllSales> createState() => _AllSalesState();
}

class _AllSalesState extends State<AllSales> implements ServerResponse {
  RxString sortByDate = 'this week '.obs, sortByStatus = 'All'.obs;

  bool isLoading = false;
  List<String> arrstatus = ["All"];

  List<String> arrDate = [
    "this week",
    "10 Day",
    "15 Days",
    "this  Month",
    "last 3 Months",
    "last 6 Months"
  ];

  List<Sales> allSales = Get.arguments[0];

  Rx<SalesModel> salesModel =
      SalesModel(sales: PaginatedSalesModel(hasNextPage: true)).obs;

  late GraphQlInitilize _request;

  var availableAmount = Get.arguments[1];
  final ScrollController _scrollController = ScrollController();

  String requestAmount = '';

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    _request = GraphQlInitilize(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      for (var element in allSales) {
        if (!arrstatus.contains(element.status)) {
          arrstatus.add(element.status ?? "");
        }
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      if (isLoading ||
          (salesModel.value.sales?.hasNextPage ?? false) == false) {
        return;
      }
      isLoading = true;
      getSales();
    }
  }

  getSales({int? page, int dateFilter = 365}) {
    try {
      page ??= (salesModel.value.sales?.page ?? 0) + 1;
    } catch (e) {
      page = 1;
    }
    if (sortByDate.value.isNotEmpty && !sortByDate.value.endsWith(" ")) {
      int indexOf = arrDate.indexOf(sortByDate.value);
      dateFilter = [7, 10, 15, 30, 90, 180][indexOf];
    }

    _request.runQuery(
      context: context,
      query: GraphQlQuries.getSalesQuery,
      logVariables: true,
      variables: GraphQlVariables.getSalesQuery(
        page: page,
        statusFilter: sortByStatus.value == "All" ? "" : sortByStatus.value,
        dateFilter: dateFilter,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const ElbaabHeader(
        title: EnStrings.sales,
        leadingBack: true,
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    flex: 1,
                    child: ElbaabLabelContainer(
                      onTap: () {
                        BottomSheets.showListPicker(context, arrstatus)
                            .then((value) => {
                                  if (sortByStatus.value != arrstatus[value])
                                    {
                                      sortByStatus.value = arrstatus[value],
                                      allSales.clear(),
                                      getSales(page: 1)
                                    }
                                });
                      },
                      leading: Obx(
                        () => Text(
                          sortByStatus.value,
                          style: FontStyles.fontRegular(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.6),
                          ),
                        ),
                      ),
                      label: EnStrings.status,
                      trailing: const Icon(Icons.arrow_drop_down),
                      leadingRightSpace: 30,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 1,
                    child: InkWell(
                      onTap: () {
                        BottomSheets.showListPicker(context, arrDate)
                            .then((value) {
                          if (sortByDate.value != arrDate[value]) {
                            sortByDate.value = arrDate[value];
                            getSales();
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: AppColors.headerColorDark,
                          border: Border.all(
                            width: 1,
                            color: Colors.white.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Obx(
                                () => Text(
                                  sortByDate.value,
                                  style: FontStyles.fontRegular(
                                    fontSize: 12,
                                    color: Colors.white.withOpacity(0.6),
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                            const SizedBox(width: 3),
                            const Icon(
                              Icons.arrow_drop_down,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (availableAmount != 0) const SizedBox(height: 24),
              if (availableAmount != 0)
                ElbaabFeildContainerWidget(
                  borderWidth: 0,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 15, vertical: 25),
                    child: Column(
                      children: [
                        Wrap(
                          children: <Widget>[
                            Text(
                              "$availableAmount",
                              style: FontStyles.fontMedium(fontSize: 32),
                            ),
                            Text(
                              ' AED',
                              style: FontStyles.fontMedium(fontSize: 12),
                            ),
                          ],
                        ),
                        Text(
                          'Total amount',
                          style: FontStyles.fontRegular(
                            color: Colors.white.withOpacity(0.5),
                          ),
                        ),
                        const SizedBox(height: 15),
                        ElbaaabInputTextField(
                          onChanged: (v) => requestAmount = v,
                          hint: 'ex : 550',
                          inputType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          inputFormatter: '[0-9.]',
                          label: EnStrings.amount,
                        ),
                        const SizedBox(height: 20),
                        ElbaabButtonWidget(
                          onPress: () {
                            BottomSheets.initateTransferRequesBottomtSheet(
                                    context, double.parse(requestAmount))
                                .then((value) {
                              if (value) {
                                Get.back(result: true);
                              }
                            });
                          },
                          colors: AppColors.colorPrimary,
                          text: 'Transers',
                          height: 40,
                          icon: const Icon(
                            Icons.chevron_right,
                            size: 20,
                          ),
                          iconOnRight: true,
                        ),
                      ],
                    ),
                  ),
                ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    EnStrings.sales,
                    style: FontStyles.fontSemibold(),
                  ),
                ),
              ),
              Container(
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(5),
                    topRight: Radius.circular(5),
                  ),
                  color: AppColors.colotMidGray,
                ),
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Row(children: <Widget>[
                    Expanded(
                        flex: 2, child: RowTitle(title: EnStrings.orderID)),
                    Expanded(flex: 3, child: RowTitle(title: EnStrings.amount)),
                    Expanded(flex: 2, child: RowTitle(title: EnStrings.date)),
                    Expanded(flex: 3, child: RowTitle(title: EnStrings.status)),
                  ]),
                ),
              ),
              Obx(
                () => ListView.builder(
                    itemCount: allSales.length,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      Sales item = allSales[index];
                      return Container(
                        height: 51,
                        decoration: BoxDecoration(
                          color: AppColors.feildColorDark,
                          borderRadius: index < 4
                              ? BorderRadius.zero
                              : const BorderRadius.only(
                                  bottomLeft: Radius.circular(5),
                                  bottomRight: Radius.circular(5),
                                ),
                        ),
                        child: Column(
                          children: [
                            SizedBox(
                              height: 50,
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  children: <Widget>[
                                    Expanded(
                                        flex: 2,
                                        child: RowDetails(
                                            detail:
                                                "#${(item.shipmentId ?? "").substring((item.shipmentId ?? "").length - 4)}")),
                                    Expanded(
                                      flex: 3,
                                      child: Column(
                                        children: [
                                          Text(
                                            '${item.supplierPay ?? 0} AED',
                                            style: FontStyles.fontRegular(
                                              fontSize: 12,
                                              color:
                                                  Colors.white.withOpacity(0.8),
                                            ),
                                            maxLines: 1,
                                            textAlign: TextAlign.center,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                right: 15),
                                            child: Align(
                                              alignment: Alignment.centerRight,
                                              child: Text(
                                                'fees',
                                                style: FontStyles.fontRegular(
                                                  fontSize: 9,
                                                  color: AppColors.colorDanger,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      flex: 3,
                                      child: RowDetails(
                                        detail:
                                            GlobalMethods.convertTimeFormate(
                                                (item.date ?? ""),
                                                format: "dd MMM, yyyy"),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 2,
                                      child: RowDetails(
                                        detail: item.status ?? "",
                                        textColor: (item.status ?? "") == ""
                                            ? AppColors.colorPrimary
                                            : (item.status ?? "") ==
                                                    "Processing"
                                                ? AppColors.colorSecondaryYellow
                                                : (item.status ?? "") ==
                                                        "Released"
                                                    ? AppColors.colorSecondary
                                                    : AppColors.colorDanger,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              height: 1,
                              color: Colors.white.withOpacity(0.08),
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 10),
                            )
                          ],
                        ),
                      );
                    }),
              ),
              Obx(() => allSales.isEmpty && salesModel.value.status != statusOK
                  ? Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 100, horizontal: 16),
                      child: Text(
                        salesModel.value.message ?? "",
                        style: FontStyles.fontMedium(),
                      ),
                    )
                  : Container())
            ],
          ),
        ),
      ),
    );
  }

  @override
  onError(error, String type) {}

  @override
  onSucess(response, String type) {
    SalesModel sales = SalesModel.fromJson(response);
    isLoading = false;
    if (sales.sales == null || sales.sales?.page == 1) {
      salesModel.value = sales;
      allSales.clear();
    }
    if (sales.status == statusOK) {
      salesModel.value = sales;
      allSales.addAll(sales.sales?.items ?? []);
      for (var element in allSales) {
        if (!arrstatus.contains(element.status)) {
          arrstatus.add(element.status ?? "");
        }
      }
    }
  }
}
