import 'dart:async';

import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:get/route_manager.dart';
import 'package:overolasuppliers/helper/bottomSheetsAndDailogs/bottom_sheets.dart';
import 'package:overolasuppliers/helper/constant/constants.dart';
import 'package:overolasuppliers/helper/routes/route_names.dart';
import 'package:overolasuppliers/helper/strings/en_strings.dart';
import 'package:overolasuppliers/main.dart';
import 'package:overolasuppliers/provider/security_session_provider.dart';
import 'package:provider/provider.dart';

class InitilizeBranchIO {
  StreamSubscription<Map>? streamSubscription;
  String deepLinkTitle = "deep_link_title";
  String controlParamsKey = '\$uri_redirect_mode';
  String deepLinkData = 'flutter deep link';
  String branchIoCanonicalIdentifier = 'flutter/branch';
  late BranchUniversalObject buo;
  late BranchLinkProperties lp;
  BranchResponse? response;

  init() {
    Future.delayed(Duration.zero, () {
      listenDeepLinkData();
    });
    initializeDeepLinkData();
    return this;
  }

  void generateLink() async {
    BranchResponse response =
        await FlutterBranchSdk.getShortUrl(buo: buo, linkProperties: lp);
    if (response.success) {
      // print(response.result);
    }
  }

  void listenDeepLinkData() {
    streamSubscription = FlutterBranchSdk.initSession().listen((data) {
      if (data.containsKey("+clicked_branch_link") &&
          data["+clicked_branch_link"] == true) {
        if (data.containsKey('~referring_link')) {
          var link = data['~referring_link'].toString();
          var isListenStream = prefs.getBool(isListenNavidationStream) ?? false;
          if (link.contains('signup') && isListenStream) {
            final signupuri = Uri.parse(link);
            String merchantid = signupuri.queryParameters["merchantid"]!;
            prefs.setBool(isListenNavidationStream, false);
            Get.offAllNamed(RouteNames.numberVerificationScreen,
                arguments: [merchantid]);
          } else if (link.contains('resetpassword') && isListenStream) {
            final signupuri = Uri.parse(link);
            String merchantid = signupuri.queryParameters["userId"]!;
            prefs.setBool(isListenNavidationStream, false);
            Get.offNamed(RouteNames.chanegPasswordScreen,
                arguments: [merchantid, true]);
          } else if (link.contains('update-email') && isListenStream) {
            BottomSheets.showAlertMessageBottomSheet(
                    "Congratulations your email chaned successfully",
                    EnStrings.alert,
                    Get.context!)
                .then((value) => Get.back());
          } else if (link.contains('settings')) {
            Provider.of<SecuritySessionProvider>(Get.context!, listen: false)
                .startSession();
            Get.back();
          }
        }
      }
    }, onError: (error) {});
  }

  void initializeDeepLinkData() {
    buo = BranchUniversalObject(
      canonicalIdentifier: 'flutter/branch',
      publiclyIndex: true,
      locallyIndex: true,
      contentMetadata: BranchContentMetaData()
        ..addCustomMetadata('custom_string', 'abc')
        ..addCustomMetadata('custom_number', 12345)
        ..addCustomMetadata('custom_bool', true)
        ..addCustomMetadata('custom_list_number', [1, 2, 3, 4, 5])
        ..addCustomMetadata('custom_list_string', ['a', 'b', 'c']),
    );

    FlutterBranchSdk.registerView(buo: buo);
    lp = BranchLinkProperties();
  }
}
