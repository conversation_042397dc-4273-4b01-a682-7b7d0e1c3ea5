class FaqsModel {
  final String? typename;
  final int? status;
  final String? message;
  final Faqs? faqs;

  FaqsModel({
    this.typename,
    this.status,
    this.message,
    this.faqs,
  });

  FaqsModel.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        status = json['status'] as int?,
        message = json['message'] as String?,
        faqs = (json['faqs'] as Map<String, dynamic>?) != null
            ? Faqs.fromJson(json['faqs'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'status': status,
        'message': message,
        'faqs': faqs?.toJson()
      };
}

class Faqs {
  final String? typename;
  final List<Items>? items;
  final int? page;
  final bool? hasNextPage;
  final int? totalItems;
  final int? totalPages;

  Faqs({
    this.typename,
    this.items,
    this.page,
    this.hasNextPage,
    this.totalItems,
    this.totalPages,
  });

  Faqs.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        items = (json['items'] as List?)
            ?.map((dynamic e) => Items.fromJson(e as Map<String, dynamic>))
            .toList(),
        page = json['page'] as int?,
        hasNextPage = json['hasNextPage'] as bool?,
        totalItems = json['totalItems'] as int?,
        totalPages = json['totalPages'] as int?;

  Map<String, dynamic> toJson() => {
        '__typename': typename,
        'items': items?.map((e) => e.toJson()).toList(),
        'page': page,
        'hasNextPage': hasNextPage,
        'totalItems': totalItems,
        'totalPages': totalPages
      };
}

class Items {
  final String? typename;
  final String? question;
  final String? answer;
  final Items? ar;
 bool? isExpanded;

  Items({this.typename,this.ar, this.question, this.answer, this.isExpanded = false});

  Items.fromJson(Map<String, dynamic> json)
      : typename = json['__typename'] as String?,
        question = json['question'] as String?,
        isExpanded = json['isExpanded'] as bool?,
        ar = (json['ar'] as Map<String, dynamic>?) != null
            ? Items.fromJson(json['ar'] as Map<String, dynamic>)
            : null,
        answer = json['answer'] as String?;

  Map<String, dynamic> toJson() =>
      {'__typename': typename, 'question': question, 'answer': answer,'ar': ar?.toJson()};
}
