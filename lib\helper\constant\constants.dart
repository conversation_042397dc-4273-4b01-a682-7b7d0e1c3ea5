import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';

const kLeftSpace = 16.0;
const kRightSpace = 16.0;
const statusOK = 200;
const status400 = 400;

// Padding
const double kPaddingS = 8.0;
const double kPaddingM = 16.0;
const double kPaddingL = 32.0;

// Spacing
const double kSpaceS = 4.0;
const double kSpaceM = 8.0;

// Animation
const Duration kButtonAnimationDuration = Duration(milliseconds: 1000);
const Duration kCardAnimationDuration = Duration(milliseconds: 800);
const Duration kRippleAnimationDuration = Duration(milliseconds: 800);
const Duration kLoginAnimationDuration = Duration(milliseconds: 1500);

String supplierID = "";
String shopId = "";
String supplierCity = "";
String supplierBusinessName = "";
String userAuthToken = "";
String firebaseDeviceToken = "";

const  FirebaseOptions firebaseOptions =   FirebaseOptions(
            apiKey: "AIzaSyAf-MKJtaxm0VcX7EcZR8zuQmYmHvptPtw",
            appId: "1:412054758878:ios:09cd75d9e9eba76ad0a94f",
            messagingSenderId: "412054758878",
            projectId: "elbaab-supplier");

LinearGradient rembowGradient = const LinearGradient(
  begin: Alignment(0.79, -0.88),
  end: Alignment(-0.73, 0.89),
  colors: [Color(0xff6500ff), Color(0xfff800cf), Color(0xffffe500)],
  stops: [0.0, 0.345, 1.0],
);

LinearGradient buttonGradient = const LinearGradient(
  begin: Alignment(0.79, -0.88),
  end: Alignment(-0.73, 0.89),
  colors: [
    Color.fromRGBO(143, 156, 244, 1),
    Color.fromRGBO(112, 112, 190, 1),
    Color.fromRGBO(79, 85, 133, 1),
  ],
  stops: [0.0, 0.345, 1.0],
);

const String requiredLogin = "RequiredLogin";
const String localAuthEnable = "LocalAuthEnable";
const String rememberMe = "rememberMe";
const String localAuthEmail = "LocalAuthEmail";
const String localAuthPassword = "LocalAuthPassword";
const String signupEmail = "Email";
const String signupPassword = "Password";
const String recentCategoriesList = "RecentCategory";
const String recentBrandsList = "RecentBrands";
const String merchantID = "MerchantID";
const String lastMerchantID = "LastMerchantID";
const String authToken = "AuthToken";
const String userPhoneNumber = "UserPhoneNumber";
const String ownerName = "OwnerName";
const String shopCity = "ShopCity";
const String isListenNavidationStream = "IsListenNavidationStream";
const String productInformation = "ProductInformation";
const String lastProductEditId = "productEditId";
const String productDetails = "ProductDetail";
const String productShipment = "ProductShipment";
const String productPolicies = "ProductPolicies";
const String shopInfo = "information";
const String shopPickUpAddress = "pickupAddress";
const String shopCustomerContact = "customerContact";
const String firebaseApiKey = "AIzaSyAf-MKJtaxm0VcX7EcZR8zuQmYmHvptPtw";
